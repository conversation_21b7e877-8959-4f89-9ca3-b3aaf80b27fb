# FlowMaster

A comprehensive sports school management system built with React, TypeScript, and Firebase.

## Prerequisites

- Node.js (v16 or higher)
- npm (v8 or higher)
- Docker and Docker Compose
- Python 3.8+ (for development tools)
- Firebase project (for production and MCP integration)

## Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd flowmaster
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env.development` file in the root directory with the following content:
```
REACT_APP_FIREBASE_API_KEY=your_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id
```

4. Start the development server:

Using npm:
```bash
npm start
```

Using Docker:
```bash
docker-compose up
```

## Available Scripts

- `npm start` - Starts the development server
- `npm test` - Runs the test suite
- `npm run build` - Creates a production build
- `npm run lint` - Runs ESLint
- `npm run lint:fix` - Runs ESLint and fixes issues
- `npm run format` - Formats code using Prettier
- `npm run type-check` - Runs TypeScript type checking

## Project Structure

```
flowmaster/
├── public/                 # Static files
├── src/
│   ├── components/        # Reusable React components
│   │   ├── layout/       # Layout components
│   │   ├── forms/        # Form components
│   │   └── shared/       # Shared components
│   ├── pages/            # Page components
│   ├── services/         # API and service layer
│   ├── hooks/            # Custom React hooks
│   ├── context/          # React context providers
│   ├── utils/            # Utility functions
│   ├── locales/          # Translation files
│   └── styles/           # Global styles and themes
├── docs/                  # Documentation
│   └── firebase-mcp-integration.md  # Firebase MCP integration guide
├── scripts/               # Utility scripts
│   ├── setup-firebase-mcp.sh  # Firebase MCP setup script
│   └── firebase-mcp-examples.js  # Firebase MCP usage examples
└── package.json
```

## Contributing

1. Create a new branch: `git checkout -b feature/your-feature-name`
2. Make your changes
3. Run tests: `npm test`
4. Run type checking: `npm run type-check`
5. Run linting: `npm run lint`
6. Commit your changes following conventional commits
7. Push to the branch
8. Create a Pull Request

## Firebase MCP Integration

FlowMaster supports integration with Firebase MCP (Model Context Protocol), which allows AI assistants like Augment Code to interact directly with your Firebase services.

### Setup

To set up Firebase MCP for FlowMaster:

```bash
chmod +x scripts/setup-firebase-mcp.sh
./scripts/setup-firebase-mcp.sh
```

This script will:
- Install Firebase MCP
- Configure it to work with your Firebase project
- Set up the necessary configuration files

### Usage

Once set up, you can use AI assistants to interact with your Firebase data. For example:

- Query Firestore collections and documents
- Upload files to Firebase Storage
- Manage user accounts in Firebase Authentication

For detailed instructions and examples, see [Firebase MCP Integration Guide](docs/firebase-mcp-integration.md).

## License

[MIT License](LICENSE)
