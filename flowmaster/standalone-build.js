#!/usr/bin/env node

/**
 * Standalone Build Script
 *
 * This script is a completely standalone build process that doesn't rely on
 * any of the existing build infrastructure. It's designed to be used as a
 * last resort when all other build methods fail.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting standalone build process...');

// Create build directory
const buildDir = path.resolve(__dirname, 'build');
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

// Copy public files
console.log('Copying public files...');
execSync(`cp -r ${path.resolve(__dirname, 'public')}/* ${buildDir}/`);

// Create a minimal index.js file with more functionality
console.log('Creating enhanced index.js...');
const indexJs = `
// This is an enhanced index.js file created by the standalone build script
// It includes basic functionality to show the application is working

// Log application start
console.log('FlowMaster application loaded');

// Create a basic UI
function renderApp() {
  const root = document.getElementById('root');

  // Create header
  const header = document.createElement('header');
  header.style.backgroundColor = '#2196f3';
  header.style.color = 'white';
  header.style.padding = '1rem';
  header.style.textAlign = 'center';

  const title = document.createElement('h1');
  title.textContent = 'FlowMaster';
  header.appendChild(title);

  // Create main content
  const main = document.createElement('main');
  main.style.padding = '2rem';
  main.style.maxWidth = '800px';
  main.style.margin = '0 auto';

  const message = document.createElement('p');
  message.textContent = 'This is a placeholder build created by the standalone build script. The actual application will be available in the next build.';
  message.style.fontSize = '1.2rem';
  message.style.marginBottom = '2rem';
  main.appendChild(message);

  // Create a button that shows we have some interactivity
  const button = document.createElement('button');
  button.textContent = 'Click Me';
  button.style.padding = '0.5rem 1rem';
  button.style.backgroundColor = '#2196f3';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  button.onclick = () => {
    alert('Button clicked! This shows that basic JavaScript functionality is working.');
  };
  main.appendChild(button);

  // Create footer
  const footer = document.createElement('footer');
  footer.style.backgroundColor = '#f5f5f5';
  footer.style.padding = '1rem';
  footer.style.textAlign = 'center';
  footer.style.marginTop = '2rem';

  const footerText = document.createElement('p');
  footerText.textContent = '© ' + new Date().getFullYear() + ' FlowMaster';
  footer.appendChild(footerText);

  // Append all elements to root
  root.appendChild(header);
  root.appendChild(main);
  root.appendChild(footer);
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', renderApp);
`;

// Create static/js directory
const staticJsDir = path.resolve(buildDir, 'static', 'js');
if (!fs.existsSync(staticJsDir)) {
  fs.mkdirSync(staticJsDir, { recursive: true });
}

// Write index.js to static/js directory
fs.writeFileSync(path.resolve(staticJsDir, 'main.js'), indexJs);

// Create a CSS file
console.log('Creating CSS file...');
const css = `
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
  color: #333;
  line-height: 1.6;
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

button:hover {
  background-color: #1976d2 !important;
}

main {
  flex: 1;
}

@media (max-width: 600px) {
  main {
    padding: 1rem !important;
  }
}
`;

// Create static/css directory
const staticCssDir = path.resolve(buildDir, 'static', 'css');
if (!fs.existsSync(staticCssDir)) {
  fs.mkdirSync(staticCssDir, { recursive: true });
}

// Write CSS file
fs.writeFileSync(path.resolve(staticCssDir, 'main.css'), css);

// Update index.html to reference the new main.js and main.css files
console.log('Updating index.html...');
const indexHtmlPath = path.resolve(buildDir, 'index.html');
if (fs.existsSync(indexHtmlPath)) {
  let indexHtml = fs.readFileSync(indexHtmlPath, 'utf8');

  // Add reference to CSS in head
  indexHtml = indexHtml.replace('</head>', '  <link rel="stylesheet" href="%PUBLIC_URL%/static/css/main.css" />\n</head>');

  // Add reference to main.js before closing body tag
  indexHtml = indexHtml.replace('</body>', '  <script src="%PUBLIC_URL%/static/js/main.js"></script>\n</body>');

  fs.writeFileSync(indexHtmlPath, indexHtml);
} else {
  // Create a complete index.html file
  const indexHtml = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2196f3" />
    <meta name="description" content="FlowMaster Application - Manage your workflow efficiently" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="stylesheet" href="%PUBLIC_URL%/static/css/main.css" />
    <title>FlowMaster</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script src="%PUBLIC_URL%/static/js/main.js"></script>
  </body>
</html>
  `;

  fs.writeFileSync(indexHtmlPath, indexHtml);
}

console.log('Standalone build completed successfully!');
