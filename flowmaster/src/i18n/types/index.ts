import 'i18next';

interface I18nResources {
  reports: {
    title: string;
    attendance: {
      title: string;
      programsTab: string;
      studentsTab: string;
      overallStatistics: string;
      programsDetail: string;
      studentsDetail: string;
      topStudents: string;
      totalSessions: string;
      totalStudents: string;
      present: string;
      absent: string;
      excused: string;
      rate: string;
      noData: string;
      totalAttendance: string;
      attendanceByProgram: string;
      attendanceByStudent: string;
      attendanceByDate: string;
      attendanceByInstructor: string;
      attendanceByDay: string;
      attendanceByMonth: string;
      attendanceByYear: string;
      attendanceByWeek: string;
      attendanceByHour: string;
      attendanceByStatus: string;
      attendanceByType: string;
      attendanceByCategory: string;
      attendanceByLocation: string;
      attendanceBySchool: string;
      attendanceByClass: string;
      attendanceByGroup: string;
      attendanceByLevel: string;
      attendanceByAge: string;
      attendanceByGender: string;
      attendanceByCountry: string;
      attendanceByCity: string;
      attendanceByRegion: string;
      attendanceByZip: string;
      attendanceByAddress: string;
      attendanceByPhone: string;
      attendanceByEmail: string;
      attendanceByPayment: string;
      attendanceByDiscount: string;
      attendanceByPrice: string;
      attendanceByDuration: string;
      attendanceByFrequency: string;
      attendanceByRecurrence: string;
      attendanceByStartDate: string;
      attendanceByEndDate: string;
      attendanceByCreationDate: string;
      attendanceByModificationDate: string;
      attendanceByDeletionDate: string;
      attendanceByArchiveDate: string;
      attendanceByRestoreDate: string;
      attendanceByExportDate: string;
      attendanceByImportDate: string;
      attendanceByPublishDate: string;
      attendanceByUnpublishDate: string;
      attendanceByApprovalDate: string;
      attendanceByRejectionDate: string;
      attendanceBySubmissionDate: string;
      attendanceByWithdrawalDate: string;
      attendanceByCompletionDate: string;
      attendanceByExpirationDate: string;
      attendanceByRenewalDate: string;
      attendanceByTerminationDate: string;
      attendanceByActivationDate: string;
      attendanceByDeactivationDate: string;
      attendanceByLastLoginDate: string;
      attendanceByLastLogoutDate: string;
      attendanceByLastActivityDate: string;
    };
    messages: {
      fetchError: string;
      filterError: string;
      exportError: string;
    };
    dashboard: {
      title: string;
      attendanceWidget: string;
      recentAbsences: string;
      attendanceTrends: string;
      equipmentStatus: string;
      overdueRentals: string;
      quickRental: string;
      createRental: string;
      continueToRental: string;
      totalItems: string;
      availability: string;
      condition: string;
      categories: string;
      noOverdueRentals: string;
      daysOverdue: string;
    };
  };
  scheduling: {
    title: string;
    unavailability: {
      title: string;
      add: string;
      edit: string;
      delete: string;
      confirm: string;
      cancel: string;
      date: string;
      time: string;
      startTime: string;
      endTime: string;
      reason: string;
      description: string;
      periods: string;
      recurring: string;
      recurrence: {
        daily: string;
        weekly: string;
        monthly: string;
        custom: string;
      };
      notifications: {
        added: string;
        updated: string;
        deleted: string;
        error: string;
      };
      validation: {
        dateRequired: string;
        startTimeRequired: string;
        endTimeRequired: string;
        endTimeAfterStart: string;
        reasonRequired: string;
      };
    };
    availability: {
      title: string;
      manage: string;
      setHours: string;
      timeSlots: string;
      recurring: string;
      exceptions: string;
    };
    calendar: {
      view: string;
      month: string;
      week: string;
      day: string;
      today: string;
    };
    timeSlot: {
      start: string;
      end: string;
      duration: string;
      repeat: string;
      until: string;
    };
    actions: {
      create: string;
      edit: string;
      delete: string;
      save: string;
      cancel: string;
    };
    status: {
      available: string;
      booked: string;
      unavailable: string;
      pending: string;
    };
  };
  dashboard: {
    welcome: string;
    welcomeSchool: string;
    unknownSchool: string;
    error: {
      fetch: string;
      noSchool: string;
    };
    stats: {
      totalPrograms: string;
      totalLessons: string;
      completedLessons: string;
      upcomingLessons: string;
    };
    actions: {
      newLesson: string;
      newProgram: string;
      manageInstructors: string;
    };
    chart: {
      lessonsTitle: string;
    };
    activities: {
      title: string;
    };
  };
  people: {
    title: string;
    categories: {
      students: string;
      instructors: string;
      clients: string;
    };
    descriptions: {
      students: string;
      instructors: string;
      clients: string;
    };
    instructors: {
      title: string;
      editInstructor: string;
      addInstructor: string;
      deleteInstructor: string;
      currentStudents: string;
      totalLessons: string;
      progress: string;
      viewProgress: string;
      fields: {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        disciplines: string;
        status: string;
      };
    };
    students: {
      title: string;
      addStudent: string;
      editStudent: string;
      fields: {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        level: string;
        disciplines: string;
        programs: string;
        enrollmentDate: string;
        status: string;
      };
    };
    clients: {
      title: string;
      addClient: string;
      editClient: string;
      deleteClient: string;
      fields: {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        disciplines: string;
        company: string;
        type: string;
        services: string;
        status: string;
      };
    };
  };
  programs: {
    title: string;
    schoolPrograms: string;
    schoolProgramsDesc: string;
    groupPrograms: string;
    groupProgramsDesc: string;
    individualPrograms: string;
    individualProgramsDesc: string;
  };
  lessons: {
    title: string;
    subtitle: string;
    categories: {
      individual: string;
      group: string;
      children: string;
    };
    descriptions: {
      individual: string;
      group: string;
      children: string;
    };
    create: {
      title: string;
    };
    edit: {
      title: string;
      duration: string;
      date: string;
      error: {
        fetch: string;
      };
    };
    form: {
      title: string;
      type: string;
      instructor: string;
      addStudent: string;
      addNewStudent: string;
      selectStudents: string;
      dateTime: string;
      endTime: string;
      startTime: string;
      duration: string;
      durationHelper: string;
      discipline: string;
      level: string;
      status: string;
      students: string;
      minutes: string;
      levels: {
        beginner: string;
        intermediate: string;
        advanced: string;
      };
      notes: string;
    };
    status: {
      upcoming: string;
      completed: string;
      'in-progress': string;
      scheduled: string;
      cancelled: string;
    };
    types: {
      individual: string;
      group: string;
      children: string;
    };
    individual: {
      title: string;
    };
    group: {
      title: string;
    };
    children: {
      title: string;
    };
  };
  common: {
    ui: {
      add: string;
      actions: string;
      loading: string;
      saving: string;
      save: string;
      cancel: string;
      delete: string;
      create: string;
      confirm: string;
      back: string;
      edit: string;
      email: string;
      signIn: string;
      signUp: string;
      language: string;
      manage: string;
      role: string;
      view: string;
      required: string;
    };
    deleteConfirmation: {
      title: string;
      message: string;
      confirm: string;
      cancel: string;
    };
    validation: {
      required: string;
      invalidEmail: string;
      invalidPhone: string;
      passwordTooShort: string;
      passwordRequirements: string;
    };
    status: {
      active: string;
      inactive: string;
    };
    menu: {
      dashboard: string;
      students: string;
      schedule: string;
      settings: string;
      lessons: string;
      programs: string;
    };
    update: {
      newVersion: string;
      refresh: string;
    };
    forgotPassword: string;
  };
  errors: {
    generic: {
      title: string;
      message: string;
      tryAgain: string;
    };
    auth: {
      resetPassword: {
        error: string;
      };
    };
  };
  auth: {
    login: {
      title: string;
      subtitle: string;
      email: string;
      password: string;
      rememberMe: string;
      forgotPassword: string;
      noAccount: string;
      signUp: string;
      signIn: string;
      errors: {
        invalidEmail: string;
        invalidPassword: string;
        userNotFound: string;
        wrongPassword: string;
        tooManyRequests: string;
        passwordRequirements: string;
        default: string;
      };
    };
    register: {
      title: string;
      subtitle: string;
      firstName: string;
      lastName: string;
      email: string;
      password: string;
      confirmPassword: string;
      hasAccount: string;
      signIn: string;
      signUp: string;
      validation: {
        required: string;
        invalidEmail: string;
        passwordTooShort: string;
        passwordRequirements: string;
      };
      errors: {
        emailInUse: string;
        weakPassword: string;
        passwordMismatch: string;
        default: string;
      };
    };
    resetPassword: {
      title: string;
      subtitle: string;
      email: string;
      submit: string;
      backToLogin: string;
      success: string;
    };
    verifyEmail: {
      title: string;
      subtitle: string;
      message: string;
      resend: string;
      success: string;
    };
  };
  profile: {
    title: string;
    role: string;
    updateSuccess: string;
    personalInfo: {
      bio: string;
    };
    fields: {
      displayName: string;
      phoneNumber: string;
      bio: string;
    };
    errors: {
      fetch: string;
      update: string;
      imageSizeLimit: string;
      imageUpload: string;
    };
  };
  settings: {
    title: string;
    appearance: {
      title: string;
      darkMode: string;
      language: string;
    };
    security: {
      title: string;
      description: string;
      twoFactorAuth: string;
      changePassword: string;
      passwordSettings: string;
      loginSettings: string;
      minPasswordLength: string;
      minPasswordLengthError: string;
      requireSpecialCharacters: string;
      requireNumbers: string;
      requireUppercase: string;
      passwordExpiryDays: string;
      passwordExpiryDaysError: string;
      maxLoginAttempts: string;
      maxLoginAttemptsError: string;
      twoFactorEnabled: string;
      sessionTimeout: string;
      minutes: string;
      days: string;
      fetchError: string;
      updateError: string;
      updateSuccess: string;
    };
    sport: {
      title: string;
      description: string;
      availableSports: string;
      customSports: string;
      enterCustomSport: string;
      selectedSports: string;
      saveChanges: string;
      disciplines: string;
      addSport: string;
      addDiscipline: string;
      sportName: string;
      disciplineName: string;
      difficulty: string;
      season: string;
      equipment: string;
      requirements: string;
      updateSuccess: string;
      updateError: string;
      deleteSuccess: string;
      deleteError: string;
      disciplineUpdateSuccess: string;
      disciplineUpdateError: string;
      disciplineDeleteSuccess: string;
      disciplineDeleteError: string;
      sports: {
        skiing: string;
        snowboarding: string;
        mountainBiking: string;
        surfing: string;
        windsurfing: string;
        kitesurfing: string;
        rockClimbing: string;
        kayaking: string;
        hiking: string;
      };
    };
    account: {
      title: string;
      email: string;
      role: string;
      deleteAccount: string;
    };
    bookings: {
      title: string;
      description: string;
      fetchError: string;
      saveError: string;
      saveSuccess: string;
      rules: string;
      availability: string;
      restrictions: string;
      cancellation: string;
      defaults: string;
      updateSuccess: string;
      updateError: string;
      allowInstantBooking: string;
      minAdvanceBooking: string;
      minAdvanceBookingError: string;
      maxAdvanceBooking: string;
      maxAdvanceBookingError: string;
      cancellationDeadline: string;
      cancellationDeadlineError: string;
      defaultLessonDuration: string;
      defaultLessonDurationError: string;
      requirePaymentUpfront: string;
    };
    system: {
      title: string;
      description: string;
    };
    finance: {
      title: string;
      description: string;
    };
    users: {
      title: string;
      description: string;
    };
    scheduling: {
      title: string;
      defaultDuration: string;
      bufferTime: string;
      advanceNotice: string;
      cancellationPolicy: string;
    };
    notifications: {
      title: string;
      emailSettings: string;
      pushSettings: string;
      lessons: string;
      programs: string;
      announcements: string;
      reminders: string;
      updateSuccess: string;
      updateError: string;
    };
  };
  courses: {
    createCourse: string;
    editCourse: string;
    courseTitle: string;
    courseType: string;
    weekly: string;
    daily: string;
    ageGroup: string;
    children: string;
    adults: string;
    date: string;
    dateRange: string;
    startDate: string;
    endDate: string;
    selectDays: string;
    timeSlots: string;
    addTimeSlot: string;
    startTime: string;
    endTime: string;
    remove: string;
    courses: string;
    coursesDesc: string;
    weeklyCourses: string;
    weeklyCoursesDesc: string;
    dailyCourses: string;
    dailyCoursesDesc: string;
    create: {
      title: string;
    };
    form: {
      title: string;
      type: string;
      days: {
        sunday: string;
        monday: string;
        tuesday: string;
        wednesday: string;
        thursday: string;
        friday: string;
        saturday: string;
      };
      ageGroup: {
        children: string;
        adults: string;
      };
      instructor: string;
      addStudent: string;
      addNewStudent: string;
      selectStudents: string;
      dateTime: string;
      startDate: string;
      endDate: string;
      startTime: string;
      duration: string;
      durationHelper: string;
      minutes: string;
      discipline: string;
      level: string;
      levels: {
        beginner: string;
        intermediate: string;
        advanced: string;
      };
      students: string;
      notes: string;
    };
    errors: {
      titleRequired: string;
      disciplineRequired: string;
      instructorRequired: string;
      daysRequired: string;
      timeSlotsRequired: string;
      endDateBeforeStartDate: string;
      endTimeBeforeStartTime: string;
    };
  };
  equipment: {
    tabs: {
      inventory: string;
      rentals: string;
      equipment: string;
    };
    inventory: {
      title: string;
      addButton: string;
      nameColumn: string;
      categoryColumn: string;
      sizeColumn: string;
      serialColumn: string;
      conditionColumn: string;
      availabilityColumn: string;
      actionsColumn: string;
      noItems: string;
      deleteTitle: string;
      deleteConfirmation: string;
    };
    form: {
      addTitle: string;
      editTitle: string;
      name: string;
      category: string;
      size: string;
      serialNumber: string;
      condition: string;
      available: string;
      additionalInfo: string;
      purchaseDate: string;
      lastMaintenanceDate: string;
      notes: string;
    };
    category: {
      kite: string;
      board: string;
      harness: string;
      wetsuit: string;
      helmet: string;
      other: string;
    };
    condition: {
      good: string;
      damaged: string;
      lost: string;
      maintenance: string;
    };
    status: {
      available: string;
      unavailable: string;
    };
    filters: {
      search: string;
      category: string;
      condition: string;
      availability: string;
      status: string;
      dateFrom: string;
      dateTo: string;
      apply: string;
    };
    rental: {
      title: string;
      addButton: string;
      dateColumn: string;
      customerColumn: string;
      itemsColumn: string;
      dueDateColumn: string;
      statusColumn: string;
      actionsColumn: string;
      itemsCount: string;
      noItems: string;
      returnAction: string;
      returnTitle: string;
      returnConfirmation: string;
      confirmReturn: string;
      customer: string;
      items: string;
      status: {
        active: string;
        returned: string;
        overdue: string;
      };
      form: {
        title: string;
        customerSection: string;
        customerType: string;
        studentType: string;
        clientType: string;
        guestType: string;
        selectStudent: string;
        selectClient: string;
        guestName: string;
        rentalDetails: string;
        rentalDate: string;
        dueDate: string;
        deposit: string;
        notes: string;
        equipmentSection: string;
        selectEquipment: string;
        addItem: string;
        selectedItems: string;
        noItemsSelected: string;
      };
      detail: {
        title: string;
        customer: string;
        dates: string;
        rentalDate: string;
        dueDate: string;
        returnDate: string;
        status: string;
        returnButton: string;
        deposit: string;
        depositReturned: string;
        depositHeld: string;
        notes: string;
        equipmentItems: string;
        condition: string;
        itemNotes: string;
        returnTitle: string;
        returnInstructions: string;
        returnCondition: string;
        returnNotes: string;
        confirmReturn: string;
        notFound: string;
        returnSuccess: string;
      };
    };
    validation: {
      nameRequired: string;
      categoryRequired: string;
      conditionRequired: string;
      customerRequired: string;
      dateRequired: string;
      dueDateRequired: string;
      itemsRequired: string;
    };
    messages: {
      fetchError: string;
      saveError: string;
      deleteError: string;
      returnError: string;
      saveSuccess: string;
      returnSuccess: string;
    };
    lessons: {
      equipmentAssignments: string;
      assignEquipment: string;
      student: string;
      equipment: string;
      assignedAt: string;
      actions: string;
      noEquipment: string;
      selectStudent: string;
      selectEquipment: string;
    };
    dashboard: {
      equipmentStatus: string;
      overdueRentals: string;
      quickRental: string;
      createRental: string;
      continueToRental: string;
      totalItems: string;
      availability: string;
      condition: string;
      categories: string;
      noOverdueRentals: string;
      daysOverdue: string;
    };
  };
}

declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: I18nResources;
  }
}

export type TranslationNamespaces = keyof I18nResources;
export type TranslationKeys<NS extends TranslationNamespaces> =
  | keyof I18nResources[NS]
  | `${NS}:${string & keyof I18nResources[NS]}`;

export type NestedTranslationKeys<NS extends TranslationNamespaces> = {
  [K in keyof I18nResources[NS]]: I18nResources[NS][K] extends Record<string, string>
    ?
        | `${K & string}.${keyof I18nResources[NS][K] & string}`
        | `${NS}:${K & string}.${keyof I18nResources[NS][K] & string}`
    : K extends string
      ? K | `${NS}:${K}`
      : never;
}[keyof I18nResources[NS]];

// Default translations for type checking
export const defaultTranslations: I18nResources = {
  reports: {
    title: 'Reports',
    attendance: {
      title: 'Attendance Reports',
      programsTab: 'Programs',
      studentsTab: 'Students',
      overallStatistics: 'Overall Statistics',
      programsDetail: 'Programs Detail',
      studentsDetail: 'Students Detail',
      topStudents: 'Top Students by Attendance',
      totalSessions: 'Total Sessions',
      totalStudents: 'Total Students',
      present: 'Present',
      absent: 'Absent',
      excused: 'Excused',
      rate: 'Attendance Rate',
      noData: 'No attendance data available for the selected filters',
      totalAttendance: 'Total Attendance',
      attendanceByProgram: 'Attendance by Program',
      attendanceByStudent: 'Attendance by Student',
      attendanceByDate: 'Attendance by Date',
      attendanceByInstructor: 'Attendance by Instructor',
      attendanceByDay: 'Attendance by Day',
      attendanceByMonth: 'Attendance by Month',
      attendanceByYear: 'Attendance by Year',
      attendanceByWeek: 'Attendance by Week',
      attendanceByHour: 'Attendance by Hour',
      attendanceByStatus: 'Attendance by Status',
      attendanceByType: 'Attendance by Type',
      attendanceByCategory: 'Attendance by Category',
      attendanceByLocation: 'Attendance by Location',
      attendanceBySchool: 'Attendance by School',
      attendanceByClass: 'Attendance by Class',
      attendanceByGroup: 'Attendance by Group',
      attendanceByLevel: 'Attendance by Level',
      attendanceByAge: 'Attendance by Age',
      attendanceByGender: 'Attendance by Gender',
      attendanceByCountry: 'Attendance by Country',
      attendanceByCity: 'Attendance by City',
      attendanceByRegion: 'Attendance by Region',
      attendanceByZip: 'Attendance by Zip',
      attendanceByAddress: 'Attendance by Address',
      attendanceByPhone: 'Attendance by Phone',
      attendanceByEmail: 'Attendance by Email',
      attendanceByPayment: 'Attendance by Payment',
      attendanceByDiscount: 'Attendance by Discount',
      attendanceByPrice: 'Attendance by Price',
      attendanceByDuration: 'Attendance by Duration',
      attendanceByFrequency: 'Attendance by Frequency',
      attendanceByRecurrence: 'Attendance by Recurrence',
      attendanceByStartDate: 'Attendance by Start Date',
      attendanceByEndDate: 'Attendance by End Date',
      attendanceByCreationDate: 'Attendance by Creation Date',
      attendanceByModificationDate: 'Attendance by Modification Date',
      attendanceByDeletionDate: 'Attendance by Deletion Date',
      attendanceByArchiveDate: 'Attendance by Archive Date',
      attendanceByRestoreDate: 'Attendance by Restore Date',
      attendanceByExportDate: 'Attendance by Export Date',
      attendanceByImportDate: 'Attendance by Import Date',
      attendanceByPublishDate: 'Attendance by Publish Date',
      attendanceByUnpublishDate: 'Attendance by Unpublish Date',
      attendanceByApprovalDate: 'Attendance by Approval Date',
      attendanceByRejectionDate: 'Attendance by Rejection Date',
      attendanceBySubmissionDate: 'Attendance by Submission Date',
      attendanceByWithdrawalDate: 'Attendance by Withdrawal Date',
      attendanceByCompletionDate: 'Attendance by Completion Date',
      attendanceByExpirationDate: 'Attendance by Expiration Date',
      attendanceByRenewalDate: 'Attendance by Renewal Date',
      attendanceByTerminationDate: 'Attendance by Termination Date',
      attendanceByActivationDate: 'Attendance by Activation Date',
      attendanceByDeactivationDate: 'Attendance by Deactivation Date',
      attendanceByLastLoginDate: 'Attendance by Last Login Date',
      attendanceByLastLogoutDate: 'Attendance by Last Logout Date',
      attendanceByLastActivityDate: 'Attendance by Last Activity Date',
    },
    messages: {
      fetchError: 'Failed to fetch data',
      filterError: 'Failed to apply filters',
      exportError: 'Failed to export data',
    },
    dashboard: {
      title: 'Dashboard',
      attendanceWidget: 'Attendance Overview',
      recentAbsences: 'Recent Absences',
      attendanceTrends: 'Attendance Trends',
      equipmentStatus: 'Equipment Status',
      overdueRentals: 'Overdue Rentals',
      quickRental: 'Quick Rental',
      createRental: 'Create Rental',
      continueToRental: 'Continue to Rental',
      totalItems: 'Total Items',
      availability: 'Availability',
      condition: 'Condition',
      categories: 'Categories',
      noOverdueRentals: 'No Overdue Rentals',
      daysOverdue: '{{days}} days overdue',
    },
  },
  scheduling: {
    title: 'Title',
    unavailability: {
      title: 'Title',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      confirm: 'Confirm',
      cancel: 'Cancel',
      date: 'Date',
      time: 'Time',
      startTime: 'Start Time',
      endTime: 'End Time',
      reason: 'Reason',
      description: 'Description',
      periods: 'Periods',
      recurring: 'Recurring',
      recurrence: {
        daily: 'Daily',
        weekly: 'Weekly',
        monthly: 'Monthly',
        custom: 'Custom',
      },
      notifications: {
        added: 'Added',
        updated: 'Updated',
        deleted: 'Deleted',
        error: 'Error',
      },
      validation: {
        dateRequired: 'Date is required',
        startTimeRequired: 'Start Time is required',
        endTimeRequired: 'End Time is required',
        endTimeAfterStart: 'End Time must be after Start Time',
        reasonRequired: 'Reason is required',
      },
    },
    availability: {
      title: 'Availability',
      manage: 'Manage Availability',
      setHours: 'Set Hours',
      timeSlots: 'Time Slots',
      recurring: 'Recurring Schedule',
      exceptions: 'Exceptions',
    },
    calendar: {
      view: 'Calendar View',
      month: 'Month',
      week: 'Week',
      day: 'Day',
      today: 'Today',
    },
    timeSlot: {
      start: 'Start Time',
      end: 'End Time',
      duration: 'Duration',
      repeat: 'Repeat',
      until: 'Until',
    },
    actions: {
      create: 'Create',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
    },
    status: {
      available: 'Available',
      booked: 'Booked',
      unavailable: 'Unavailable',
      pending: 'Pending',
    },
  },
  dashboard: {
    welcome: 'Welcome',
    welcomeSchool: 'Welcome to your school',
    unknownSchool: 'Unknown School',
    error: {
      fetch: 'Error fetching dashboard data',
      noSchool: 'No school found',
    },
    stats: {
      totalPrograms: 'Total Programs',
      totalLessons: 'Total Lessons',
      completedLessons: 'Completed Lessons',
      upcomingLessons: 'Upcoming Lessons',
    },
    actions: {
      newLesson: 'New Lesson',
      newProgram: 'New Program',
      manageInstructors: 'Manage Instructors',
    },
    chart: {
      lessonsTitle: 'Lessons Overview',
    },
    activities: {
      title: 'Recent Activities',
    },
  },
  people: {
    title: 'People',
    categories: {
      students: 'Students',
      instructors: 'Instructors',
      clients: 'Clients',
    },
    descriptions: {
      students: 'Student Description',
      instructors: 'Instructor Description',
      clients: 'Client Description',
    },
    instructors: {
      title: 'Instructors',
      editInstructor: 'Edit Instructor',
      addInstructor: 'Add Instructor',
      deleteInstructor: 'Delete Instructor',
      currentStudents: 'Current Students',
      totalLessons: 'Total Lessons',
      progress: 'Progress',
      viewProgress: 'View Progress',
      fields: {
        firstName: 'First Name',
        lastName: 'Last Name',
        email: 'Email',
        phone: 'Phone',
        disciplines: 'Disciplines',
        status: 'Status',
      },
    },
    students: {
      title: 'Students',
      addStudent: 'Add Student',
      editStudent: 'Edit Student',
      fields: {
        firstName: 'Name',
        lastName: 'Last Name',
        email: 'Email',
        phone: 'Phone',
        level: 'Level',
        programs: 'Programs',
        disciplines: 'Disciplines',
        enrollmentDate: 'Enrollment Date',
        status: 'Status',
      },
    },
    clients: {
      title: 'Clients',
      addClient: 'Add Client',
      editClient: 'Edit Client',
      deleteClient: 'Delete Client',
      fields: {
        firstName: 'Name',
        lastName: 'Last Name',
        email: 'Email',
        phone: 'Phone',
        disciplines: 'Disciplines',
        company: 'Company',
        type: 'Type',
        services: 'Services',
        status: 'Status',
      },
    },
  },
  programs: {
    title: 'Programs',
    schoolPrograms: 'School Programs',
    schoolProgramsDesc: 'Programs for schools and organized groups',
    groupPrograms: 'Group Programs',
    groupProgramsDesc: 'Group programs for schools and organized groups',
    individualPrograms: 'Individual Programs',
    individualProgramsDesc: 'Individual programs for students',
  },
  lessons: {
    title: 'Lessons',
    subtitle: "Manage individual, group, and  children's lessons",
    categories: {
      individual: 'Individual Lessons',
      group: 'Group Lessons',
      children: "children's Lessons",
    },
    descriptions: {
      individual: 'Personalized lessons tailored to your needs',
      group: 'Learning in a dynamic group setting',
      children: 'Special lessons for young learners',
    },
    create: {
      title: 'Create New Lesson',
    },
    edit: {
      title: 'Edit Lesson',
      duration: '{{minutes}} minutes',
      date: '{{date}}',
      error: {
        fetch: 'Error fetching lesson data',
      },
    },
    form: {
      title: 'Lesson',
      type: 'Lesson Type',
      instructor: 'Select Instructor',
      addStudent: 'Add Student',
      addNewStudent: 'Add New Student',
      selectStudents: 'Select Students',
      dateTime: 'Date and Time',
      endTime: 'End Time',
      startTime: 'Start Time',
      duration: 'Duration (minutes)',
      durationHelper: 'Select lesson duration',
      discipline: 'Discipline',
      level: 'Level',
      status: 'Status',
      students: 'Students',
      minutes: 'Minutes',
      levels: {
        beginner: 'Beginner',
        intermediate: 'Intermediate',
        advanced: 'Advanced',
      },
      notes: 'Notes',
    },
    status: {
      upcoming: 'Upcoming',
      completed: 'Completed',
      'in-progress': 'In Progress',
      scheduled: 'Scheduled',
      cancelled: 'Cancelled',
    },
    individual: {
      title: 'Individual Lessons',
    },
    group: {
      title: 'Group Lessons',
    },
    children: {
      title: 'children Lessons',
    },
    types: {
      individual: 'Individual Lesson',
      group: 'Group Lesson',
      children: "children's Lesson",
    },
  },
  common: {
    ui: {
      add: 'Add',
      actions: 'Actions',
      loading: 'Loading...',
      saving: 'Saving...',
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      create: 'Create',
      back: 'Back',
      confirm: 'Confirm',
      email: 'Email',
      signIn: 'Sign In',
      signUp: 'Sign Up',
      language: 'Language',
      manage: 'Manage',
      role: 'Role',
      view: 'View',
      required: 'Required',
    },
    menu: {
      dashboard: 'Dashboard',
      students: 'Students',
      schedule: 'Schedule',
      settings: 'Settings',
      lessons: 'Lessons',
      programs: 'Programs',
    },
    update: {
      newVersion: 'New Version Available',
      refresh: 'Refresh',
    },
    forgotPassword: 'Forgot Password?',
    deleteConfirmation: {
      title: 'Delete Confirmation',
      message: 'Are you sure you want to delete this item?',
      confirm: 'Delete',
      cancel: 'Cancel',
    },
    validation: {
      required: 'This field is required',
      invalidEmail: 'Invalid email address',
      invalidPhone: 'Invalid phone number',
      passwordTooShort: 'Password must be at least 6 characters long',
      passwordRequirements:
        'Password must contain at least one uppercase letter, one lowercase letter, and one number',
    },
    status: {
      active: 'Active',
      inactive: 'Inactive',
    },
  },
  errors: {
    generic: {
      title: 'Error',
      message: 'Something went wrong',
      tryAgain: 'Try Again',
    },

    auth: {
      resetPassword: {
        error: 'Failed to reset password',
      },
    },
  },
  auth: {
    login: {
      title: 'Login',
      subtitle: 'Welcome back',
      email: 'Email',
      password: 'Password',
      rememberMe: 'Remember me',
      forgotPassword: 'Forgot Password?',
      noAccount: "Don't have an account?",
      signUp: 'Sign Up',
      signIn: 'Sign In',
      errors: {
        invalidEmail: 'Invalid email address',
        invalidPassword: 'Invalid password',
        userNotFound: 'User not found',
        wrongPassword: 'Wrong password',
        tooManyRequests: 'Too many requests',
        default: 'An error occurred',
        passwordRequirements:
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      },
    },
    register: {
      title: 'Register',
      subtitle: 'Create your account',
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      hasAccount: 'Already have an account?',
      signIn: 'Sign In',
      signUp: 'Sign Up',
      validation: {
        required: 'Required field',
        invalidEmail: 'Invalid email format',
        passwordTooShort: 'Password must be at least 8 characters',
        passwordRequirements:
          'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      },
      errors: {
        emailInUse: 'Email already in use',
        weakPassword: 'Password is too weak',
        passwordMismatch: 'Passwords do not match',
        default: 'An error occurred',
      },
    },
    resetPassword: {
      title: 'Reset Password',
      subtitle: 'Enter your email',
      email: 'Email',
      submit: 'Submit',
      backToLogin: 'Back to Login',
      success: 'Password reset email sent',
    },
    verifyEmail: {
      title: 'Verify Email',
      subtitle: 'Check your email',
      message: 'Please verify your email address',
      resend: 'Resend verification email',
      success: 'Email verified successfully',
    },
  },
  profile: {
    title: 'Profile',
    role: 'Role',
    updateSuccess: 'Profile updated successfully',
    personalInfo: {
      bio: 'Bio',
    },
    fields: {
      displayName: 'Display Name',
      phoneNumber: 'Phone Number',
      bio: 'Bio',
    },
    errors: {
      fetch: 'Error fetching profile',
      update: 'Error updating profile',
      imageSizeLimit: 'Image is too large. Maximum size is 10 MB',
      imageUpload: 'Error uploading image',
    },
  },
  settings: {
    title: 'Settings',
    appearance: {
      title: 'Appearance',
      darkMode: 'Dark Mode',
      language: 'Language',
    },
    security: {
      title: 'Security',
      description: 'Manage security settings and authentication',
      twoFactorAuth: 'Two-Factor Authentication',
      changePassword: 'Change Password',
      passwordSettings: 'Password Settings',
      loginSettings: 'Login Settings',
      minPasswordLength: 'Minimum Password Length',
      minPasswordLengthError: 'Password must be at least 8 characters long',
      requireSpecialCharacters: 'Require Special Characters',
      requireNumbers: 'Require Numbers',
      requireUppercase: 'Require Uppercase Letters',
      passwordExpiryDays: 'Password Expiry Days',
      passwordExpiryDaysError: 'Password expiry days must be 0 or greater',
      maxLoginAttempts: 'Maximum Login Attempts',
      maxLoginAttemptsError: 'Maximum login attempts must be at least 1',
      twoFactorEnabled: 'Enable Two-Factor Authentication',
      sessionTimeout: 'Session Timeout',
      minutes: 'minutes',
      days: 'days',
      fetchError: 'Failed to fetch security settings',
      updateError: 'Failed to update security settings',
      updateSuccess: 'Security settings updated successfully',
    },
    sport: {
      title: 'Sport',
      description: 'Manage available sports and disciplines',
      availableSports: 'Available Sports',
      disciplines: 'Disciplines',
      addSport: 'Add Sport',
      addDiscipline: 'Add Discipline',
      sportName: 'Sport Name',
      disciplineName: 'Discipline Name',
      difficulty: 'Difficulty',
      season: 'Season',
      equipment: 'Equipment',
      requirements: 'Requirements',
      updateSuccess: 'Sport settings updated successfully',
      updateError: 'Failed to update sport settings',
      deleteSuccess: 'Sport deleted successfully',
      deleteError: 'Failed to delete sport',
      disciplineUpdateSuccess: 'Discipline updated successfully',
      disciplineUpdateError: 'Failed to update discipline',
      disciplineDeleteSuccess: 'Discipline deleted successfully',
      disciplineDeleteError: 'Failed to delete discipline',
      sports: {
        skiing: 'Skiing',
        snowboarding: 'Snowboarding',
        mountainBiking: 'Mountain Biking',
        surfing: 'Surfing',
        windsurfing: 'Windsurfing',
        kitesurfing: 'Kitesurfing',
        rockClimbing: 'Rock Climbing',
        kayaking: 'Kayaking',
        hiking: 'Hiking',
      },
      customSports: 'Custom Sports',
      enterCustomSport: 'Enter Custom Sport',
      selectedSports: 'Selected Sports',
      saveChanges: 'Save Changes',
    },
    account: {
      title: 'Account',
      email: 'Email',
      role: 'Role',
      deleteAccount: 'Delete Account',
    },
    bookings: {
      title: 'Bookings',
      description: 'Configure booking rules and availability',
      fetchError: 'Failed to fetch booking settings',
      saveError: 'Failed to save booking settings',
      saveSuccess: 'Booking settings saved successfully',
      rules: 'Booking Rules',
      availability: 'Availability Settings',
      restrictions: 'Booking Restrictions',
      cancellation: 'Cancellation Policy',
      defaults: 'Default Settings',
      updateSuccess: 'Update Success',
      updateError: 'Update Error',
      allowInstantBooking: 'Allow Instant Booking',
      minAdvanceBooking: 'Minimum Advance Booking',
      minAdvanceBookingError: 'Minimum advance booking cannot be less than 1 day',
      maxAdvanceBooking: 'Maximum Advance Booking',
      maxAdvanceBookingError: 'Maximum advance booking cannot be more than 30 days',
      cancellationDeadline: 'Cancellation Deadline',
      cancellationDeadlineError: 'Cancellation Deadline Error',
      defaultLessonDuration: 'Default Lesson Duration',
      defaultLessonDurationError: 'Default lesson duration cannot be less than 30 minutes',
      requirePaymentUpfront: 'Require Payment Upfront',
    },
    system: {
      title: 'System',
      description: 'Configure system settings',
    },
    finance: {
      title: 'Finance',
      description: 'Configure finance settings',
    },
    users: {
      title: 'Users',
      description: 'Configure user settings',
    },
    scheduling: {
      title: 'Scheduling',
      defaultDuration: 'Default Duration',
      bufferTime: 'Buffer Time',
      advanceNotice: 'Advance Notice',
      cancellationPolicy: 'Cancellation Policy',
    },
    notifications: {
      title: 'Notification Settings',
      emailSettings: 'Email Notifications',
      pushSettings: 'Push Notifications',
      lessons: 'Lesson Updates',
      programs: 'Program Updates',
      announcements: 'Announcements',
      reminders: 'Reminders',
      updateSuccess: 'Notification settings updated successfully',
      updateError: 'Failed to update notification settings',
    },
  },
  courses: {
    createCourse: 'Create Course',
    editCourse: 'Edit Course',
    courseTitle: 'Course Title',
    courseType: 'Course Type',
    weekly: 'Weekly',
    daily: 'Daily',
    ageGroup: 'Age Group',
    children: 'Children',
    adults: 'Adults',
    date: 'Date',
    dateRange: 'Date Range',
    startDate: 'Start Date',
    endDate: 'End Date',
    selectDays: 'Select Days',
    timeSlots: 'Time Slots',
    addTimeSlot: 'Add Time Slot',
    startTime: 'Start Time',
    endTime: 'End Time',
    remove: 'Remove',
    courses: 'Courses',
    coursesDesc: 'Regular courses for different age groups',
    weeklyCourses: 'Weekly Courses',
    weeklyCoursesDesc: 'Weekly courses for different age groups',
    dailyCourses: 'Daily Courses',
    dailyCoursesDesc: 'Daily courses for different age groups',
    create: {
      title: 'Create New Course',
    },
    form: {
      title: 'Course Title',
      type: 'Course Type',
      instructor: 'Select Instructor',
      addStudent: 'Add Student',
      addNewStudent: 'Add New Student',
      selectStudents: 'Select Students',
      days: {
        sunday: 'Sunday',
        monday: 'Monday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
      },
      ageGroup: {
        children: 'Children',
        adults: 'Adults',
      },
      dateTime: 'Date and Time',
      startDate: 'Start Date',
      endDate: 'End Date',
      startTime: 'Start Time',
      duration: 'Duration (minutes)',
      durationHelper: 'Select lesson duration',
      minutes: 'Minutes',
      discipline: 'Discipline',
      level: 'Level',
      levels: {
        beginner: 'Beginner',
        intermediate: 'Intermediate',
        advanced: 'Advanced',
      },
      students: 'Students',
      notes: 'Notes',
    },
    errors: {
      titleRequired: 'Title is required',
      disciplineRequired: 'Discipline is required',
      instructorRequired: 'Instructor is required',
      daysRequired: 'At least one day must be selected',
      timeSlotsRequired: 'At least one time slot is required',
      endDateBeforeStartDate: 'End date must be after start date',
      endTimeBeforeStartTime: 'Start time must be before end time',
    },
  },
  equipment: {
    tabs: {
      inventory: 'Inventory',
      rentals: 'Rentals',
      equipment: 'Equipment',
    },
    inventory: {
      title: 'Equipment Inventory',
      addButton: 'Add Equipment',
      nameColumn: 'Name',
      categoryColumn: 'Category',
      sizeColumn: 'Size',
      serialColumn: 'Serial Number',
      conditionColumn: 'Condition',
      availabilityColumn: 'Availability',
      actionsColumn: 'Actions',
      noItems: 'No equipment items found',
      deleteTitle: 'Delete Equipment Item',
      deleteConfirmation: 'Are you sure you want to delete {{name}}? This action cannot be undone.',
    },
    form: {
      addTitle: 'Add Equipment Item',
      editTitle: 'Edit Equipment Item',
      name: 'Name',
      category: 'Category',
      size: 'Size',
      serialNumber: 'Serial Number',
      condition: 'Condition',
      available: 'Available for Rental',
      additionalInfo: 'Additional Information',
      purchaseDate: 'Purchase Date',
      lastMaintenanceDate: 'Last Maintenance Date',
      notes: 'Notes',
    },
    category: {
      kite: 'Kite',
      board: 'Board',
      harness: 'Harness',
      wetsuit: 'Wetsuit',
      helmet: 'Helmet',
      other: 'Other',
    },
    condition: {
      good: 'Good',
      damaged: 'Damaged',
      lost: 'Lost',
      maintenance: 'Needs Maintenance',
    },
    status: {
      available: 'Available',
      unavailable: 'In Use',
    },
    filters: {
      search: 'Search',
      category: 'Category',
      condition: 'Condition',
      availability: 'Availability',
      status: 'Status',
      dateFrom: 'From Date',
      dateTo: 'To Date',
      apply: 'Apply Filters',
    },
    rental: {
      title: 'Equipment Rentals',
      addButton: 'New Rental',
      dateColumn: 'Date',
      customerColumn: 'Customer',
      itemsColumn: 'Items',
      dueDateColumn: 'Due Date',
      statusColumn: 'Status',
      actionsColumn: 'Actions',
      itemsCount: 'items',
      noItems: 'No rentals found',
      returnAction: 'Return',
      returnTitle: 'Return Equipment',
      returnConfirmation: 'Are you sure you want to mark this rental as returned?',
      confirmReturn: 'Confirm Return',
      customer: 'Customer',
      items: 'Items',
      status: {
        active: 'Active',
        returned: 'Returned',
        overdue: 'Overdue',
      },
      form: {
        title: 'New Equipment Rental',
        customerSection: 'Customer Information',
        customerType: 'Customer Type',
        studentType: 'Student',
        clientType: 'Client',
        guestType: 'Guest',
        selectStudent: 'Select Student',
        selectClient: 'Select Client',
        guestName: 'Guest Name',
        rentalDetails: 'Rental Details',
        rentalDate: 'Rental Date',
        dueDate: 'Due Date',
        deposit: 'Deposit Amount',
        notes: 'Notes',
        equipmentSection: 'Equipment Items',
        selectEquipment: 'Select Equipment',
        addItem: 'Add',
        selectedItems: 'Selected Items',
        noItemsSelected: 'No items selected',
      },
      detail: {
        title: 'Rental Details',
        customer: 'Customer',
        dates: 'Dates',
        rentalDate: 'Rental Date',
        dueDate: 'Due Date',
        returnDate: 'Return Date',
        status: 'Status',
        returnButton: 'Return Equipment',
        deposit: 'Deposit',
        depositReturned: 'Deposit returned',
        depositHeld: 'Deposit held',
        notes: 'Notes',
        equipmentItems: 'Equipment Items',
        condition: 'Condition',
        itemNotes: 'Notes',
        returnTitle: 'Return Equipment',
        returnInstructions: 'Please check the condition of each item and add notes if needed.',
        returnCondition: 'Condition',
        returnNotes: 'Notes',
        confirmReturn: 'Confirm Return',
        notFound: 'Rental not found',
        returnSuccess: 'Equipment returned successfully',
      },
    },
    validation: {
      nameRequired: 'Name is required',
      categoryRequired: 'Category is required',
      conditionRequired: 'Condition is required',
      customerRequired: 'Customer is required',
      dateRequired: 'Date is required',
      dueDateRequired: 'Due date is required',
      itemsRequired: 'At least one item is required',
    },
    messages: {
      fetchError: 'Failed to fetch data',
      saveError: 'Failed to save data',
      deleteError: 'Failed to delete item',
      returnError: 'Failed to return rental',
      saveSuccess: 'Data saved successfully',
      returnSuccess: 'Equipment returned successfully',
    },
    lessons: {
      equipmentAssignments: 'Equipment Assignments',
      assignEquipment: 'Assign Equipment',
      student: 'Student',
      equipment: 'Equipment',
      assignedAt: 'Assigned At',
      actions: 'Actions',
      noEquipment: 'No equipment assigned for this lesson',
      selectStudent: 'Select Student',
      selectEquipment: 'Select Equipment',
    },
    dashboard: {
      equipmentStatus: 'Equipment Status',
      overdueRentals: 'Overdue Rentals',
      quickRental: 'New Rental',
      createRental: 'Create New Rental',
      continueToRental: 'Continue to Rental',
      totalItems: 'Total Items',
      availability: 'Availability',
      condition: 'Condition',
      categories: 'Categories',
      noOverdueRentals: 'No overdue rentals',
      daysOverdue: '{{days}} days overdue',
    },
  },
};
