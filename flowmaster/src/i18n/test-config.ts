import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

i18n.use(initReactI18next).init({
  lng: 'en',
  fallbackLng: 'en',
  debug: false,
  interpolation: {
    escapeValue: false,
  },
  resources: {
    en: {
      common: require('../../../public/locales/en/common.json'),
      errors: require('../../../public/locales/en/errors.json'),
      auth: require('../../../public/locales/en/auth.json'),
      profile: require('../../../public/locales/en/profile.json'),
      settings: require('../../../public/locales/en/settings.json'),
      people: require('../../../public/locales/en/people.json'),
      dashboard: require('../../../public/locales/en/dashboard.json'),
      lessons: require('../../../public/locales/en/lessons.json'),
      programs: require('../../../public/locales/en/programs.json'),
      reports: require('../../../public/locales/en/reports.json'),
    },
    sl: {
      common: require('../../../public/locales/sl/common.json'),
      errors: require('../../../public/locales/sl/errors.json'),
      auth: require('../../../public/locales/sl/auth.json'),
      profile: require('../../../public/locales/sl/profile.json'),
      settings: require('../../../public/locales/sl/settings.json'),
      people: require('../../../public/locales/sl/people.json'),
      dashboard: require('../../../public/locales/sl/dashboard.json'),
      lessons: require('../../../public/locales/sl/lessons.json'),
      programs: require('../../../public/locales/sl/programs.json'),
      reports: require('../../../public/locales/sl/reports.json'),
    },
  },
  defaultNS: 'common',
  ns: ['common', 'auth', 'errors', 'settings'],
  initImmediate: false,
});

export default i18n;
