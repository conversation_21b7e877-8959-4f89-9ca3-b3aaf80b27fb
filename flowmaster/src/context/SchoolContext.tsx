import React, { createContext, useState, useEffect } from 'react';
import { useFirestore } from '../hooks/useFirestore';
import { useAuth } from '../hooks/useAuth';
import type { School } from '../hooks/useSchool';

export interface SchoolContextType {
  currentSchool: School | null;
  setCurrentSchool: (school: School | null) => void;
  loading: boolean;
  error: Error | null;
}

export const SchoolContext = createContext<SchoolContextType | null>(null);

export const SchoolProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentSchool, setCurrentSchool] = useState<School | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();
  const { getDocument } = useFirestore();

  useEffect(() => {
    const fetchUserSchool = async () => {
      if (!user?.uid) {
        setLoading(false);
        return;
      }

      try {
        const userData = await getDocument('users', user.uid);

        if (userData?.schoolId) {
          const schoolData = await getDocument('schools', userData.schoolId);

          if (schoolData) {
            setCurrentSchool({
              id: userData.schoolId,
              ...schoolData,
              createdAt: schoolData.createdAt?.toDate(),
              updatedAt: schoolData.updatedAt?.toDate(),
            } as School);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch school data'));
      } finally {
        setLoading(false);
      }
    };

    fetchUserSchool();
  }, [user?.uid, getDocument]);

  return (
    <SchoolContext.Provider
      value={{
        currentSchool,
        setCurrentSchool,
        loading,
        error,
      }}
    >
      {children}
    </SchoolContext.Provider>
  );
};
