import { render, screen, waitFor } from '@testing-library/react';
import { useAuth } from '../AuthContext';
import { RoleProvider, useRole } from '../RoleContext';
import { getUserRole } from '../../services/roleService';
import { Role, RoleType } from '../../types/role';

// Mock the required dependencies
jest.mock('../AuthContext');
jest.mock('../../services/role');

const mockUseAuth = useAuth as jest.Mock;
const mockGetUserRole = getUserRole as jest.Mock;

const mockRole: Role = {
  id: RoleType.ADMIN,
  name: 'Administrator',
  description: 'Administrator role with full access',
  permissions: ['manage_users', 'manage_settings'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

// Test component that uses the role context
const TestComponent = () => {
  const { role, checkPermission, loading, error } = useRole();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!role) return <div>No role</div>;

  return (
    <div>
      <div>Role: {role.name}</div>
      <div>Can manage users: {checkPermission('manage_users') ? 'Yes' : 'No'}</div>
    </div>
  );
};

describe('RoleContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should show loading state initially', () => {
    mockUseAuth.mockReturnValue({ user: { uid: 'test-uid' } });
    mockGetUserRole.mockImplementation(
      () =>
        new Promise((resolve) => {
          // Simulating a long-running operation
          setTimeout(() => resolve(mockRole), 1000);
        })
    );

    render(
      <RoleProvider>
        <TestComponent />
      </RoleProvider>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should fetch and display user role', async () => {
    mockUseAuth.mockReturnValue({ user: { uid: 'test-uid' } });
    mockGetUserRole.mockResolvedValue(mockRole);

    render(
      <RoleProvider>
        <TestComponent />
      </RoleProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Role: Administrator')).toBeInTheDocument();
      expect(screen.getByText('Can manage users: Yes')).toBeInTheDocument();
    });

    expect(mockGetUserRole).toHaveBeenCalledWith('test-uid');
  });

  it('should handle error state', async () => {
    mockUseAuth.mockReturnValue({ user: { uid: 'test-uid' } });
    mockGetUserRole.mockRejectedValue(new Error('Failed to fetch role'));

    render(
      <RoleProvider>
        <TestComponent />
      </RoleProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Error: Failed to fetch role')).toBeInTheDocument();
    });
  });

  it('should show no role when user is not authenticated', async () => {
    mockUseAuth.mockReturnValue({ user: null });

    render(
      <RoleProvider>
        <TestComponent />
      </RoleProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('No role')).toBeInTheDocument();
    });

    expect(mockGetUserRole).not.toHaveBeenCalled();
  });

  it('should correctly check permissions', async () => {
    mockUseAuth.mockReturnValue({ user: { uid: 'test-uid' } });
    mockGetUserRole.mockResolvedValue({
      ...mockRole,
      permissions: ['manage_users'],
    });

    const TestPermissions = () => {
      const { checkPermission } = useRole();
      return (
        <div>
          <div data-testid="manage-users">{checkPermission('manage_users').toString()}</div>
          <div data-testid="create-workflow">{checkPermission('manage_settings').toString()}</div>
        </div>
      );
    };

    render(
      <RoleProvider>
        <TestPermissions />
      </RoleProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('manage-users')).toHaveTextContent('true');
      expect(screen.getByTestId('create-workflow')).toHaveTextContent('false');
    });
  });
});
