import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { AuthProvider, AuthContext } from '../AuthContext';
import { auth } from '../../services/firebase';
import { getUserProfile } from '../../services/authService';

// Mock Firebase auth and services
jest.mock('../../services/firebase', () => ({
  auth: {
    onAuthStateChanged: jest.fn(),
  },
}));

jest.mock('../../services/auth', () => ({
  getUserProfile: jest.fn(),
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  resetPassword: jest.fn(),
  verifyEmail: jest.fn(),
}));

describe('AuthContext', () => {
  const mockUser = {
    uid: '123',
    email: '<EMAIL>',
    emailVerified: true,
  };

  const mockUserProfile = {
    uid: '123',
    email: '<EMAIL>',
    displayName: 'Test User',
    emailVerified: true,
    role: 'student',
    createdAt: '2025-01-23',
    updatedAt: '2025-01-23',
    settings: {
      language: 'en',
      theme: 'light',
      notifications: true,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('provides loading state while initializing', () => {
    (auth.onAuthStateChanged as jest.Mock).mockImplementation(() => () => {});

    const TestComponent = () => {
      const { isLoading } = React.useContext(AuthContext);
      return <div>{isLoading ? 'Loading' : 'Loaded'}</div>;
    };

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByText('Loading')).toBeInTheDocument();
  });

  it('updates auth state when user signs in', async () => {
    (auth.onAuthStateChanged as jest.Mock).mockImplementation((callback) => {
      callback(mockUser);
      return () => {};
    });

    (getUserProfile as jest.Mock).mockResolvedValue(mockUserProfile);

    const TestComponent = () => {
      const { user, isAuthenticated, isLoading } = React.useContext(AuthContext);
      if (isLoading) return <div>Loading</div>;
      return <div>{isAuthenticated ? `Authenticated as ${user?.email}` : 'Not authenticated'}</div>;
    };

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument();
  });

  it('handles authentication errors', async () => {
    const mockError = new Error('Auth error');
    (auth.onAuthStateChanged as jest.Mock).mockImplementation((callback) => {
      callback(mockUser);
      return () => {};
    });

    (getUserProfile as jest.Mock).mockRejectedValue(mockError);

    const TestComponent = () => {
      const { error, isLoading } = React.useContext(AuthContext);
      if (isLoading) return <div>Loading</div>;
      return <div>{error || 'No error'}</div>;
    };

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    expect(screen.getByText('Auth error')).toBeInTheDocument();
  });

  it('clears user data on sign out', async () => {
    let authCallback: (user: null | { uid: string; email: string; emailVerified: boolean }) => void;
    (auth.onAuthStateChanged as jest.Mock).mockImplementation((callback) => {
      authCallback = callback;
      callback(mockUser);
      return () => {};
    });

    (getUserProfile as jest.Mock).mockResolvedValue(mockUserProfile);

    const TestComponent = () => {
      const { isAuthenticated, isLoading } = React.useContext(AuthContext);
      if (isLoading) return <div>Loading</div>;
      return <div>{isAuthenticated ? 'Authenticated' : 'Not authenticated'}</div>;
    };

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    expect(screen.getByText('Authenticated')).toBeInTheDocument();

    // Simulate sign out
    await act(async () => {
      authCallback(null);
    });

    expect(screen.getByText('Not authenticated')).toBeInTheDocument();
  });
});
