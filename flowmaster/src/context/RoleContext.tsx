import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { getUserRole } from '../services/roleService';
import { Role, RoleContextType, Permission } from '../types/role';

const RoleContext = createContext<RoleContextType>({
  role: null,
  checkPermission: () => false,
  loading: true,
  error: null,
});

export const useRole = () => useContext(RoleContext);

export const RoleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [role, setRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchRole = async () => {
      if (!user) {
        setRole(null);
        setLoading(false);
        return;
      }

      try {
        const userRole = await getUserRole(user.uid);
        setRole(userRole);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch role'));
      } finally {
        setLoading(false);
      }
    };

    fetchRole();
  }, [user]);

  const checkPermission = (permission: Permission): boolean => {
    if (!role) return false;
    return role.permissions.includes(permission);
  };

  return (
    <RoleContext.Provider value={{ role, checkPermission, loading, error }}>
      {children}
    </RoleContext.Provider>
  );
};

export default RoleContext;
