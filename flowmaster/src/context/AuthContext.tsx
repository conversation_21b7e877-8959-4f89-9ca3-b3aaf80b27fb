import React, { createContext, useContext, useEffect, useState } from 'react';
import type { User, UserProfile } from '../types/user';
import { auth } from '../services/firebase';
import {
  getUserProfile,
  signIn,
  signUp,
  signOut,
  resetPassword,
  verifyEmail,
} from '../services/authService';
import { getIdTokenResult } from 'firebase/auth';
import { RoleType } from '../types/role';

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  signIn: typeof signIn;
  signUp: typeof signUp;
  signOut: typeof signOut;
  resetPassword: typeof resetPassword;
  verifyEmail: typeof verifyEmail;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userProfile: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
  signIn,
  signUp,
  signOut,
  resetPassword,
  verifyEmail,
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(
      async (firebaseUser) => {
        try {
          if (firebaseUser) {
            // Get custom claims from the ID token
            const tokenResult = await getIdTokenResult(firebaseUser);
            const claims = tokenResult.claims;

            // Get user profile from Firestore
            const profile = await getUserProfile(firebaseUser.uid);

            // Use claims for role and schoolId if available, otherwise fall back to Firestore data
            const role = (claims.role as RoleType) || profile.roleId;
            const schoolId = (claims.schoolId as string) || profile.schoolId;

            setUser({
              ...firebaseUser,
              ...profile,
              role,
              schoolId,
            } as unknown as User);

            setUserProfile(profile);

            // If claims are missing but Firestore data exists, update the claims
            if ((!claims.role || !claims.schoolId) && (profile.roleId || profile.schoolId)) {
              console.log('Updating missing custom claims from Firestore data');
              try {
                // This would require a server-side function call in a real app
                // For now, we'll just log it
                console.log('Would update claims to:', {
                  role: profile.roleId,
                  schoolId: profile.schoolId,
                });
              } catch (claimError) {
                console.error('Error updating claims:', claimError);
              }
            }
          } else {
            setUser(null);
            setUserProfile(null);
          }
        } catch (err) {
          setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
          setIsLoading(false);
        }
      },
      (error) => {
        setError(error.message);
        setIsLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, []);

  const value = {
    user,
    userProfile,
    isLoading,
    isAuthenticated: !!user,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    verifyEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export { AuthContext };

export default AuthProvider;
