import { Timestamp } from 'firebase/firestore';

export type CourseType = 'weekly' | 'daily';
export type AgeGroup = 'children' | 'adults';

export interface TimeSlot {
  startTime: Date | Timestamp;
  endTime: Date | Timestamp;
}

export interface CourseFormData {
  title: string;
  type: CourseType;
  discipline: string;
  instructorId: string;
  studentIds: string[];
  ageGroup: AgeGroup;
  date: Date | Timestamp;
  dateRange: Date[] | Timestamp[];
  startDate: Date | Timestamp;
  endDate: Date | Timestamp;
  timeSlots: TimeSlot[];
  selectedDays: number[];
  maxParticipants: number;
  status: 'active' | 'inactive' | 'cancelled';
  notes?: string;
  description?: string;
  schedule?: Record<string, string[]>;
  createdBy: string;
  updatedAt: Date | Timestamp;
  level: string;
}

export type Course = CourseFormData & {
  id: string;
};
