import { Timestamp } from 'firebase/firestore';
import { AttendanceStatus } from './program';

/**
 * Interface for a single attendance record
 */
export interface AttendanceRecord {
  id: string;
  studentId: string;
  programId?: string;
  sessionId?: string;
  lessonId?: string;
  date: Timestamp;
  status: AttendanceStatus;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Interface for attendance statistics
 */
export interface AttendanceStats {
  total: number;
  present: number;
  absent: number;
  excused: number;
  attendanceRate: number; // Percentage (0-100)
}

/**
 * Interface for student attendance summary
 */
export interface StudentAttendanceSummary {
  studentId: string;
  programAttendance: Record<string, AttendanceStats>; // programId -> stats
  overallAttendance: AttendanceStats;
  lastUpdated: Timestamp;
}

/**
 * Interface for attendance history item
 */
export interface AttendanceHistoryItem {
  id: string;
  date: Timestamp;
  status: AttendanceStatus;
  programId?: string;
  programName?: string;
  sessionId?: string;
  lessonId?: string;
  lessonTitle?: string;
  notes?: string;
}

/**
 * Interface for attendance filter options
 */
export interface AttendanceFilterOptions {
  startDate?: Date;
  endDate?: Date;
  programId?: string;
  status?: AttendanceStatus;
}
