import 'i18next';

declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: {
      
      courses: {
        form: {
          title: string;
          type: string;
          days: {
            sunday: string;
            monday: string;
            tuesday: string;
            wednesday: string;
            thursday: string;
            friday: string;
            saturday: string;
            [key: string]: string; // Add index signature to support dynamic keys
          };
        };
        errors: {
          endTimeBeforeStart: string;
          titleRequired: string;
          disciplineRequired: string;
          instructorRequired: string;
          daysRequired: string;
          timeSlotsRequired: string;
          endDateBeforeStart: string;
        };
          ageGroup: {
            children: string;
            adults: string;
          };
          discipline: string;
          level: string;
          startDate: string;
          endDate: string;
          // ... other form fields
        };
        // Existing flat keys
        'courses.title': string;
        'courses.schoolPrograms': string;
        'courses.schoolProgramsDesc': string;
        'courses.groupPrograms': string;
        'courses.groupProgramsDesc': string;
        'courses.individualPrograms': string;
        'courses.individualProgramsDesc': string;
        'courses.courses': string;
        'courses.coursesDesc': string;
        'courses.weeklyCourses': string;
        'courses.weeklyCoursesDesc': string;
        'courses.dailyCourses': string;
        'courses.dailyCoursesDesc': string;
        'courses.createCourse': string;
        'courses.editCourse': string;
        'courses.courseTitle': string;
        'courses.courseType': string;
        'courses.weekly': string;
        'courses.daily': string;
        'courses.ageGroup': string;
        'courses.children': string;
        'courses.adults': string;
        'courses.dateRange': string;
        'courses.startDate': string;
        'courses.endDate': string;
        'courses.selectDays': string;
        'courses.timeSlots': string;
        'courses.addTimeSlot': string;
        'courses.startTime': string;
        'courses.endTime': string;
        'courses.remove': string;
      };
      validation: {
        'validation.required': string;
        'validation.email': string;
        'validation.phone': string;
        'validation.minLength': string;
        'validation.maxLength': string;
        'validation.passwordMatch': string;
        'validation.invalidFormat': string;
        'validation.uniqueEmail': string;
        'validation.invalidCredentials': string;
      };
      scheduling: {
        'availability.title': string;
        'availability.addSlot': string;
        'availability.selectedDateSlots': string;
        'availability.noSlots': string;
        'availability.deleteSlot': string;
        'availability.editSlot': string;
        'availability.startTime': string;
        'availability.endTime': string;
        'availability.repeat': string;
        'availability.repeatOptions.none': string;
        'availability.repeatOptions.daily': string;
        'availability.repeatOptions.weekly': string;
        'availability.repeatOptions.monthly': string;
        'availability.save': string;
        'availability.cancel': string;
      };
      people: {
        'people.title': string;
        'people.categories.students': string;
        'people.categories.instructors': string;
        'people.categories.clients': string;
        'people.descriptions.students': string;
        'people.descriptions.instructors': string;
        'people.descriptions.clients': string;
        'people.instructors.title': string;
        'people.instructors.addInstructor': string;
        'people.instructors.fields.name': string;
        'people.instructors.fields.email': string;
        'people.instructors.fields.disciplines': string;
        'people.instructors.fields.status': string;
      };
      programs: {
        'programs.title': string;
        'programs.schoolPrograms': string;
        'programs.schoolProgramsDesc': string;
        'programs.groupPrograms': string;
        'programs.groupProgramsDesc': string;
        'programs.individualPrograms': string;
        'programs.individualProgramsDesc': string;
        'programs.courses': string;
        'programs.coursesDesc': string;
        'programs.weeklyCourses': string;
        'programs.weeklyCoursesDesc': string;
        'programs.dailyCourses': string;
        'programs.dailyCoursesDesc': string;
      };
      lessons: {
        'title': string;
        'categories.individual': string;
        'categories.group': string;
        'categories.children': string;
        'descriptions.individual': string;
        'descriptions.group': string;
        'descriptions.children': string;
        'status.upcoming': string;
        'status.completed': string;
        'status.in-progress': string;
        'create.title': string;
        'edit.title': string;
        'form.title': string;
        'form.type': string;
        'form.instructor': string;
        'form.startTime': string;
        'form.duration': string;
        'form.notes': string;
        'form.minutes': string;
        'form.discipline': string;
        'form.level': string;
        'form.students': string;
        'types.individual': string;
        'types.group': string;
        'types.children': string;
      };
      common: {
        'common.ui.actions': string;
        'common.ui.loading': string;
        'common.ui.saving': string;
        'common.ui.save': string;
        'common.ui.cancel': string;
        'common.ui.delete': string;
        'common.ui.back': string;
        'common.ui.edit': string;
        'common.ui.email': string;
        'common.ui.signIn': string;
        'common.ui.signUp': string;
        'common.ui.language': string;
        'common.ui.manage': string;
        'common.ui.required': string;
        'common.ui.deleteTitle': string;
        'common.ui.confirmDelete': string;
        'common.menu.dashboard': string;
        'common.menu.students': string;
        'common.menu.schedule': string;
        'common.menu.settings': string;
        'common.menu.lessons': string;
        'common.menu.programs': string;
        'common.update.newVersion': string;
        'common.update.refresh': string;
        'common.forgotPassword': string;
      };
      errors: {
        'errors.generic.title': string;
        'errors.generic.message': string;
        'errors.generic.tryAgain': string;
        'errors.profile.fetch': string;
        'errors.profile.update': string;
        'errors.auth.resetPassword.error': string;
      };
      auth: {
        'auth.login.title': string;
        'auth.login.subtitle': string;
        'auth.login.email': string;
        'auth.login.password': string;
        'auth.login.rememberMe': string;
        'auth.login.forgotPassword': string;
        'auth.login.noAccount': string;
        'auth.login.signUp': string;
        'auth.login.signIn': string;
        'auth.register.title': string;
        'auth.register.subtitle': string;
        'auth.register.firstName': string;
        'auth.register.lastName': string;
        'auth.register.email': string;
        'auth.register.password': string;
        'auth.register.confirmPassword': string;
        'auth.register.hasAccount': string;
        'auth.register.signIn': string;
        'auth.register.signUp': string;
        'auth.resetPassword.title': string;
        'auth.resetPassword.subtitle': string;
        'auth.resetPassword.email': string;
        'auth.resetPassword.submit': string;
        'auth.resetPassword.backToLogin': string;
        'auth.resetPassword.success': string;
        'auth.verifyEmail.title': string;
        'auth.verifyEmail.subtitle': string;
        'auth.verifyEmail.message': string;
        'auth.verifyEmail.resend': string;
        'auth.verifyEmail.success': string;
      };
      profile: {
        'profile.title': string;
        'profile.role': string;
        'profile.updateSuccess': string;
        'profile.fields.displayName': string;
        'profile.fields.phoneNumber': string;
        'profile.fields.bio': string;
      };
      settings: {
        'settings.title': string;
        'settings.appearance.title': string;
        'settings.appearance.darkMode': string;
        'settings.appearance.language': string;
        'settings.security.title': string;
        'settings.security.description': string;
        'settings.security.twoFactorAuth': string;
        'settings.security.changePassword': string;
        'settings.security.passwordSettings': string;
        'settings.security.loginSettings': string;
        'settings.security.minPasswordLength': string;
        'settings.security.minPasswordLengthError': string;
        'settings.security.requireSpecialCharacters': string;
        'settings.security.requireNumbers': string;
        'settings.security.requireUppercase': string;
        'settings.security.passwordExpiryDays': string;
        'settings.security.passwordExpiryDaysError': string;
        'settings.security.maxLoginAttempts': string;
        'settings.security.maxLoginAttemptsError': string;
        'settings.security.twoFactorEnabled': string;
        'settings.security.sessionTimeout': string;
        'settings.security.minutes': string;
        'settings.security.days': string;
        'settings.security.fetchError': string;
        'settings.security.updateError': string;
        'settings.security.updateSuccess': string;
        'settings.sport.title': string;
        'settings.sport.description': string;
        'settings.sport.availableSports': string;
        'settings.sport.customSports': string;
        'settings.sport.enterCustomSport': string;
        'settings.sport.selectedSports': string;
        'settings.sport.saveChanges': string;
        'settings.sport.disciplines': string;
        'settings.sport.addSport': string;
        'settings.sport.addDiscipline': string;
        'settings.sport.sportName': string;
        'settings.sport.disciplineName': string;
        'settings.sport.difficulty': string;
        'settings.sport.season': string;
        'settings.sport.equipment': string;
        'settings.sport.requirements': string;
        'settings.sport.updateSuccess': string;
        'settings.sport.updateError': string;
        'settings.sport.deleteSuccess': string;
        'settings.sport.deleteError': string;
        'settings.sport.disciplineUpdateSuccess': string;
        'settings.sport.disciplineUpdateError': string;
        'settings.sport.disciplineDeleteSuccess': string;
        'settings.sport.disciplineDeleteError': string;
        'settings.sport.sports.skiing': string;
        'settings.sport.sports.snowboarding': string;
        'settings.sport.sports.mountainBiking': string;
        'settings.sport.sports.surfing': string;
        'settings.sport.sports.windsurfing': string;
        'settings.sport.sports.kitesurfing': string;
        'settings.sport.sports.rockClimbing': string;
        'settings.sport.sports.kayaking': string;
        'settings.sport.sports.hiking': string;
        'settings.account.title': string;
        'settings.account.email': string;
        'settings.account.role': string;
        'settings.account.deleteAccount': string;
        'settings.bookings.title': string;
        'settings.bookings.description': string;
        'settings.bookings.fetchError': string;
        'settings.bookings.saveError': string;
        'settings.bookings.saveSuccess': string;
        'settings.bookings.rules': string;
        'settings.bookings.availability': string;
        'settings.bookings.restrictions': string;
        'settings.bookings.cancellation': string;
        'settings.bookings.defaults': string;
        'settings.bookings.updateSuccess': string;
        'settings.bookings.updateError': string;
        'settings.bookings.allowInstantBooking': string;
        'settings.bookings.minAdvanceBooking': string;
        'settings.bookings.minAdvanceBookingError': string;
        'settings.bookings.maxAdvanceBooking': string;
        'settings.bookings.maxAdvanceBookingError': string;
        'settings.bookings.cancellationDeadline': string;
        'settings.bookings.cancellationDeadlineError': string;
        'settings.bookings.defaultLessonDuration': string;
        'settings.bookings.defaultLessonDurationError': string;
        'settings.bookings.requirePaymentUpfront': string;
        'settings.system.title': string;
        'settings.system.description': string;
        'settings.scheduling.title': string;
        'settings.scheduling.defaultDuration': string;
        'settings.scheduling.bufferTime': string;
        'settings.scheduling.advanceNotice': string;
        'settings.scheduling.cancellationPolicy': string;
        'settings.notifications.title': string;
        'settings.notifications.emailSettings': string;
        'settings.notifications.pushSettings': string;
        'settings.notifications.lessons': string;
        'settings.notifications.programs': string;
        'settings.notifications.announcements': string;
        'settings.notifications.reminders': string;
        'settings.notifications.updateSuccess': string;
        'settings.notifications.updateError': string;
        'settings.finance.title': string;
        'settings.finance.description': string;
        'settings.users.title': string;
        'settings.users.description': string;
        'settings.messages.success': string;
        'settings.messages.error': string;
      };
      common: {
        'common.ui.back': string;
        'common.ui.save': string;
        'common.ui.cancel': string;
        'common.ui.delete': string;
        'common.ui.back': string;
        'common.ui.edit': string;
        'common.ui.email': string;
        'common.ui.signIn': string;
        'common.ui.signUp': string;
        'common.ui.language': string;
        'common.ui.manage': string;
        'common.ui.required': string;
        'common.menu.dashboard': string;
        'common.menu.students': string;
        'common.menu.schedule': string;
        'common.menu.settings': string;
        'common.menu.lessons': string;
        'common.menu.programs': string;
        'common.update.newVersion': string;
        'common.update.refresh': string;
        'common.forgotPassword': string;
      };
    };
  }

