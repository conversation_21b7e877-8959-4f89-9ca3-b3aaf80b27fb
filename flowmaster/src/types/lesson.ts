import { Timestamp } from 'firebase/firestore';

// Define the lesson status type
export type LessonStatus = 'scheduled' | 'completed' | 'cancelled';

// Define and export the base interface for form data
export interface LessonFormData {
  title: string;
  type: 'individual' | 'group' | 'children';
  discipline: string;
  instructorId: string;
  studentIds: string[];
  startTime: Date | Timestamp;
  duration: number; // in minutes
  level: string;
  status: LessonStatus;
  notes?: string;
  createdBy: string;
  updatedAt: Date | Timestamp;
  programId?: string; // Reference to the program if this is a program lesson
  programSessionId?: string; // Reference to the program session if this is a program lesson
}

// Define and export the lesson interface with ID
export type Lesson = LessonFormData & {
  id: string;
};
