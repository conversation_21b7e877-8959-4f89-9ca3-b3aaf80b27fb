import { Timestamp } from 'firebase/firestore';

/**
 * Equipment condition status
 */
export type EquipmentCondition = 'good' | 'damaged' | 'lost' | 'maintenance';

/**
 * Equipment category
 */
export type EquipmentCategory = 'kite' | 'board' | 'harness' | 'wetsuit' | 'helmet' | 'other';

/**
 * Equipment item interface
 */
export interface EquipmentItem {
  id: string;
  name: string;
  category: EquipmentCategory;
  size?: string;
  serialNumber?: string;
  condition: EquipmentCondition;
  available: boolean;
  notes?: string;
  purchaseDate?: Timestamp;
  lastMaintenanceDate?: Timestamp;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Rental status
 */
export type RentalStatus = 'active' | 'returned' | 'overdue';

/**
 * Rental item interface (equipment item in a rental)
 */
export interface RentalItem {
  equipmentId: string;
  equipmentName: string;
  condition: EquipmentCondition;
  notes?: string;
}

/**
 * Equipment assignment for lessons
 */
export interface EquipmentAssignment {
  id: string;
  lessonId: string;
  studentId: string;
  studentName: string;
  equipmentItems: RentalItem[];
  assignedAt: Date;
  rentalId?: string;
}

/**
 * Rental record interface
 */
export interface RentalRecord {
  id: string;
  date: Timestamp;
  dueDate: Timestamp;
  customerId: string;
  customerName: string;
  customerType: 'student' | 'client' | 'guest';
  items: RentalItem[];
  relatedLessonId?: string;
  returned: boolean;
  returnDate?: Timestamp;
  status: RentalStatus;
  notes?: string;
  deposit?: number;
  depositReturned?: boolean;
  createdBy: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Equipment assignment in a lesson
 */
export interface LessonEquipmentAssignment {
  lessonId: string;
  studentId: string;
  studentName: string;
  equipmentItems: RentalItem[];
  issued: boolean;
  returned: boolean;
  rentalId?: string;
}

/**
 * Equipment filter options
 */
export interface EquipmentFilterOptions {
  category?: EquipmentCategory;
  condition?: EquipmentCondition;
  available?: boolean;
  search?: string;
}

/**
 * Rental filter options
 */
export interface RentalFilterOptions {
  status?: RentalStatus;
  customerId?: string;
  lessonId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}
