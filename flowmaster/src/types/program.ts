import { Timestamp } from 'firebase/firestore';

/**
 * Types of programs offered by the school
 */
export type ProgramType = 'yearly' | 'seasonal' | 'camp';

/**
 * Status of a program
 */
export type ProgramStatus = 'active' | 'completed' | 'archived';

/**
 * Days of the week for scheduling
 */
export type WeekDay =
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'
  | 'sunday';

/**
 * Attendance status for a student in a session
 */
export type AttendanceStatus = 'present' | 'absent' | 'excused';

/**
 * Payment status for a student in a program
 */
export type PaymentStatus = 'paid' | 'partial' | 'pending';

/**
 * Payment method types
 */
export type PaymentMethod = 'cash' | 'card' | 'transfer' | 'check' | 'other';

/**
 * Payment interface for tracking individual payments
 */
export interface Payment {
  id: string;
  studentId: string;
  programId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  date: Timestamp;
  notes?: string;
  receiptNumber?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Schedule configuration for a program
 */
export interface ProgramSchedule {
  days: WeekDay[];
  startTime: string; // Format: "HH:MM" (24-hour)
  endTime: string; // Format: "HH:MM" (24-hour)
  startDate: Timestamp;
  endDate: Timestamp;
}

/**
 * Pricing information for a program
 */
export interface ProgramPricing {
  totalFee: number;
  currency: string;
  paymentStatus: Record<string, PaymentStatus>; // studentId -> status
  installments?: number; // Number of installments allowed
  dueDate?: Timestamp | null; // When payment is due
  earlyBirdDiscount?: number; // Discount amount for early payment
  discounts?: Record<string, number>; // studentId -> discount amount
  paidAmounts?: Record<string, number>; // studentId -> amount paid so far
}

/**
 * Progress tracking for a program
 */
export interface ProgramProgress {
  skills: string[];
  goals: string;
}

/**
 * Main Program interface
 */
export interface Program {
  id: string;
  name: string;
  type: ProgramType;
  description: string;
  schedule: ProgramSchedule;
  location?: string;
  participants: string[]; // studentIds
  instructors: string[]; // instructorIds
  pricing: ProgramPricing;
  progress: ProgramProgress;
  status: ProgramStatus;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Session interface for individual program sessions
 */
export interface ProgramSession {
  id: string;
  programId: string;
  date: Timestamp;
  attendance: Record<string, AttendanceStatus>; // studentId -> status
  notes: string;
  skills: Record<string, boolean>; // skillId -> completed
  isMakeup: boolean;
  makeupDetails?: {
    originalSessionId?: string; // ID of the original session this makeup is for
    forStudents: string[]; // List of student IDs this makeup session is for
    reason: string; // Reason for the makeup session
  };
  missedStudents?: string[]; // Students who missed this session and need makeup
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Form data for creating/editing a program
 */
export interface ProgramFormData {
  name: string;
  type: ProgramType;
  description: string;
  schedule: {
    days: WeekDay[];
    startTime: string;
    endTime: string;
    startDate: Date;
    endDate: Date;
  };
  location?: string;
  participants: string[];
  instructors: string[];
  pricing: {
    totalFee: number;
    currency: string;
    installments?: number;
    dueDate?: Date | null;
    earlyBirdDiscount?: number;
    discounts?: Record<string, number>;
  };
  progress: {
    skills: string[];
    goals: string;
  };
}
