import { Timestamp } from 'firebase/firestore';

import { RoleType } from './role';

export interface User {
  uid: string;
  email: string;
  displayName: string;
  emailVerified: boolean;
  role: RoleType;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  schoolId: string;
  profile: {
    photoURL?: string;
    bio?: string;
    skills: string[];
    certifications: string[];
  };
  settings: {
    language: string;
    notifications: boolean;
    theme: 'light' | 'dark';
  };
}

export interface Lesson {
  type: 'individual' | 'group' | 'children';
  date: Timestamp;
  duration: number;
  instructorId: string;
  studentIds: string[];
  equipment: string[];
  notes?: string;
  status: 'scheduled' | 'completed' | 'canceled';
}

export interface Program {
  id: string;
  name: string;
  type: 'yearly' | 'seasonal' | 'camp';
  description: string;
  schedule: {
    days: string[];
    startTime: string;
    endTime: string;
    startDate: Timestamp;
    endDate: Timestamp;
  };
  location?: string;
  participants: string[]; // studentIds
  instructors: string[]; // instructorIds
  pricing: {
    totalFee: number;
    currency: string;
    paymentStatus: Record<string, 'paid' | 'partial' | 'pending'>;
  };
  progress: {
    skills: string[];
    goals: string;
  };
  status: 'active' | 'completed' | 'archived';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Schedule {
  date: Timestamp;
  lessons: string[];
  instructorId: string;
  notes?: string;
}

export interface School {
  name: string;
  sports: string[];
  programs: string[];
  settings: {
    language: string;
    currency: string;
    timezone: string;
  };
  createdAt: Timestamp;
}

export interface Attendance {
  studentId: string;
  referenceId: string;
  referenceType: 'lesson' | 'program';
  date: Timestamp;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  createdAt: Timestamp;
}

export interface ProgramSession {
  id: string;
  programId: string;
  date: Timestamp;
  attendance: Record<string, 'present' | 'absent' | 'excused'>;
  notes: string;
  skills: Record<string, boolean>;
  isMakeup: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
