// Types from Firebase Auth
import type { User as FirebaseUser } from 'firebase/auth';

export enum RoleType {
  ADMIN = 'admin',
  MANAGER = 'manager',
  INSTRUCTOR = 'instructor',
  STUDENT = 'student',
  CLIENT = 'CLIENT',
}

export type Permission =
  | 'manage_users'
  | 'manage_lessons'
  | 'manage_programs'
  | 'manage_settings'
  | 'view_lessons'
  | 'view_programs'
  | 'view_users'
  | 'view_people'
  | 'edit_profile'
  | 'manage_attendance'
  | 'manage_equipment';

export interface Role {
  id: RoleType;
  name: string;
  description: string;
  permissions: Permission[];
  createdAt: string;
  updatedAt: string;
}

export interface RoleContextType {
  role: Role | null;
  checkPermission: (permission: Permission) => boolean;
  loading: boolean;
  error: Error | null;
}
// Map of permission IDs to their display names and descriptions
export const PERMISSION_DETAILS = {
  manage_users: {
    name: 'Manage Users',
    description: 'Can manage user accounts',
  },
  manage_lessons: {
    name: 'Manage Lessons',
    description: 'Can create and edit lessons',
  },
  manage_programs: {
    name: 'Manage Programs',
    description: 'Can create and edit programs',
  },
  manage_settings: {
    name: 'Manage Settings',
    description: 'Can manage application settings',
  },
  view_lessons: {
    name: 'View Lessons',
    description: 'Can view lessons',
  },
  view_programs: {
    name: 'View Programs',
    description: 'Can view programs',
  },
  view_users: {
    name: 'View Users',
    description: 'Can view users',
  },
  edit_profile: {
    name: 'Edit Profile',
    description: 'Can edit profile',
  },
  manage_equipment: {
    name: 'Manage Equipment',
    description: 'Can manage equipment inventory and rentals',
  },
} as const;

export type UserRole = 'admin' | 'manager' | 'instructor' | 'student';

// Extend the Firebase User type to include role and profile fields
export interface FlowMasterUser extends FirebaseUser {
  role?: UserRole;
  bio?: string;
  phoneNumber: string | null;
  schoolId?: string;
  updateProfile: (profile: {
    displayName?: string | null;
    photoURL?: string | null;
  }) => Promise<void>;
  updateEmail: (email: string) => Promise<void>;
  updatePhoneNumber: (phoneNumber: string) => Promise<void>;
}

// Predefined roles with their permissions
export const DEFAULT_ROLES: { [key in RoleType]: Role } = {
  [RoleType.ADMIN]: {
    id: RoleType.ADMIN,
    name: 'Administrator',
    description: 'Full system access',
    permissions: [
      'manage_users',
      'manage_lessons',
      'manage_programs',
      'manage_settings',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
      'manage_equipment',
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  [RoleType.CLIENT]: {
    id: RoleType.CLIENT,
    name: 'Client',
    description: 'Client access',
    permissions: ['view_lessons'], // adjust permissions as needed
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  [RoleType.INSTRUCTOR]: {
    id: RoleType.INSTRUCTOR,
    name: 'Instructor',
    description: 'Can manage programs and lessons',
    permissions: [
      'manage_lessons',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  [RoleType.MANAGER]: {
    id: RoleType.MANAGER,
    name: 'Manager',
    description: 'School management access',
    permissions: [
      'manage_users',
      'manage_lessons',
      'manage_programs',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
      'manage_equipment',
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  [RoleType.STUDENT]: {
    id: RoleType.STUDENT,
    name: 'Student',
    description: 'Student access',
    permissions: ['view_lessons', 'view_programs', 'edit_profile'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
};
