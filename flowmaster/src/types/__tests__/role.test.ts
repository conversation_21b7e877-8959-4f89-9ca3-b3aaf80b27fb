import { RoleType, Permission, Role, RoleContextType, DEFAULT_ROLES } from '../role';

describe('Role Types', () => {
  describe('RoleType Enum', () => {
    it('should have all required role types', () => {
      expect(RoleType.ADMIN).toBe('admin');
      expect(RoleType.MANAGER).toBe('manager');
      expect(RoleType.INSTRUCTOR).toBe('instructor');
      expect(RoleType.CLIENT).toBe('client');
    });
  });

  describe('Role Interface', () => {
    it('should have correct structure', () => {
      const role: Role = {
        id: RoleType.ADMIN,
        name: 'Administrator',
        description: 'Full system access',
        permissions: ['manage_users', 'manage_lessons'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      expect(role.id).toBeDefined();
      expect(role.name).toBeDefined();
      expect(role.description).toBeDefined();
      expect(Array.isArray(role.permissions)).toBe(true);
      expect(role.createdAt).toBeDefined();
      expect(role.updatedAt).toBeDefined();
    });
  });

  describe('RoleContextType', () => {
    it('should have all required properties', () => {
      const mockRole: Role = DEFAULT_ROLES[RoleType.ADMIN];
      const roleContext: RoleContextType = {
        role: mockRole,
        checkPermission: (permission: Permission) => {
          expect(permission).toBeDefined();
          return true;
        },
        loading: false,
        error: null,
      };

      expect(roleContext.role).toBeDefined();
      expect(roleContext.checkPermission).toBeDefined();
      expect(roleContext.loading).toBeDefined();
      expect(roleContext.error).toBeDefined();

      // Test the checkPermission function
      expect(roleContext.checkPermission('manage_users')).toBe(true);
    });
  });

  describe('DEFAULT_ROLES', () => {
    it('should have all role types defined', () => {
      expect(DEFAULT_ROLES[RoleType.ADMIN]).toBeDefined();
      expect(DEFAULT_ROLES[RoleType.MANAGER]).toBeDefined();
      expect(DEFAULT_ROLES[RoleType.INSTRUCTOR]).toBeDefined();
      expect(DEFAULT_ROLES[RoleType.CLIENT]).toBeDefined();
    });

    it('should have correct permissions for each role', () => {
      expect(DEFAULT_ROLES[RoleType.ADMIN].permissions).toContain('manage_users');
      expect(DEFAULT_ROLES[RoleType.MANAGER].permissions).toContain('manage_programs');
      expect(DEFAULT_ROLES[RoleType.INSTRUCTOR].permissions).toContain('manage_lessons');
      expect(DEFAULT_ROLES[RoleType.CLIENT].permissions).toContain('view_lessons');
    });
  });
});
