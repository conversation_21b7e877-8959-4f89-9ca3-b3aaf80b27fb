import { AuthError, AuthContextType, AuthState } from '../auth';
import { User } from 'firebase/auth';
import { UserProfile } from '../user';

describe('Auth Types', () => {
  describe('AuthError', () => {
    it('should have correct structure', () => {
      const error: AuthError = {
        code: 'auth/error-code',
        message: 'Error message',
      };
      expect(error.code).toBeDefined();
      expect(error.message).toBeDefined();
    });
  });

  describe('AuthContextType', () => {
    it('should have all required properties', () => {
      const mockUser = {} as User;
      const mockProfile = {} as UserProfile;
      const mockError = new Error('test error');

      const authContext: AuthContextType = {
        user: mockUser,
        profile: mockProfile,
        loading: true,
        error: mockError,
        signIn: async () => Promise.resolve(),
        signUp: async () => Promise.resolve(),
        signOut: async () => Promise.resolve(),
        resetPassword: async () => Promise.resolve(),
      };

      expect(authContext.user).toBeDefined();
      expect(authContext.profile).toBeDefined();
      expect(authContext.loading).toBeDefined();
      expect(authContext.error).toBeDefined();
      expect(authContext.signIn).toBeDefined();
      expect(authContext.signUp).toBeDefined();
      expect(authContext.signOut).toBeDefined();
      expect(authContext.resetPassword).toBeDefined();
    });
  });

  describe('AuthState', () => {
    it('should have all required properties', () => {
      const mockUser = null;
      const mockProfile = null;
      const mockError = null;

      const authState: AuthState = {
        user: mockUser,
        profile: mockProfile,
        loading: false,
        error: mockError,
      };

      expect(authState.user).toBeDefined();
      expect(authState.profile).toBeDefined();
      expect(authState.loading).toBeDefined();
      expect(authState.error).toBeDefined();
    });
  });
});
