import { UserSettings, UserProfile, User } from '../user';
import { Timestamp } from 'firebase/firestore';
import { RoleType } from '../role';

describe('User Types', () => {
  describe('UserSettings', () => {
    it('should have correct structure', () => {
      const settings: UserSettings = {
        language: 'en',
        theme: 'light',
        notifications: true,
      };

      expect(settings.language).toBeDefined();
      expect(settings.theme).toBeDefined();
      expect(settings.notifications).toBeDefined();
      expect(['light', 'dark']).toContain(settings.theme);
    });
  });

  describe('UserProfile', () => {
    it('should have all required properties', () => {
      const profile: UserProfile = {
        photoURL: 'https://example.com/photo.jpg',
        bio: 'Test bio',
        skills: ['JavaScript', 'TypeScript'],
        certifications: ['AWS', 'Azure'],
      };

      expect(profile.skills).toBeDefined();
      expect(profile.certifications).toBeDefined();
      expect(profile.photoURL).toBeDefined();
      expect(profile.bio).toBeDefined();
      expect(Array.isArray(profile.skills)).toBe(true);
      expect(Array.isArray(profile.certifications)).toBe(true);
    });

    it('should handle optional properties', () => {
      const profile: UserProfile = {
        skills: [],
        certifications: [],
      };

      expect(profile.photoURL).toBeUndefined();
      expect(profile.bio).toBeUndefined();
      expect(profile.skills).toHaveLength(0);
      expect(profile.certifications).toHaveLength(0);
    });
  });

  describe('User', () => {
    it('should have all required properties', () => {
      const user: User = {
        uid: '123',
        email: '<EMAIL>',
        displayName: 'Test User',
        emailVerified: true,
        roleId: RoleType.CLIENT,
        schoolId: 'school123',
        createdAt: Timestamp.fromDate(new Date()),
        updatedAt: Timestamp.fromDate(new Date()),
        profile: {
          skills: [],
          certifications: [],
        },
        settings: {
          language: 'en',
          theme: 'light',
          notifications: true,
        },
      };

      expect(user.uid).toBeDefined();
      expect(user.email).toBeDefined();
      expect(user.displayName).toBeDefined();
      expect(user.emailVerified).toBeDefined();
      expect(user.roleId).toBeDefined();
      expect(user.schoolId).toBeDefined();
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();
      expect(user.profile).toBeDefined();
      expect(user.settings).toBeDefined();
    });
  });
});
