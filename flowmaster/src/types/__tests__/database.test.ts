import { RoleType } from '../role';
import { User, Lesson, Program, Schedule, School, Attendance } from '../database';

// Mock Firebase Timestamp
const mockTimestamp = {
  seconds: 0,
  nanoseconds: 0,
  toDate: () => new Date(0),
  toMillis: () => 0,
  isEqual: () => true,
  toJSON: () => ({ seconds: 0, nanoseconds: 0 }),
  valueOf: () => 'timestamp',
};

jest.mock('firebase/firestore', () => ({
  Timestamp: jest.fn(() => mockTimestamp),
}));

describe('Database Types', () => {
  describe('User Interface', () => {
    it('should create a valid User object', () => {
      const user: User = {
        uid: '123',
        email: '<EMAIL>',
        displayName: 'Test User',
        emailVerified: true,
        role: 'instructor' as RoleType,
        createdAt: mockTimestamp,
        updatedAt: mockTimestamp,
        schoolId: 'school123',
        profile: {
          skills: [],
          certifications: [],
        },
        settings: {
          language: 'en',
          notifications: true,
          theme: 'light',
        },
      };

      expect(user).toBeDefined();
      expect(user.uid).toBe('123');
    });
  });

  describe('Lesson Interface', () => {
    it('should create a valid Lesson object', () => {
      const lesson: Lesson = {
        type: 'individual',
        date: mockTimestamp,
        duration: 60,
        instructorId: 'instructor123',
        studentIds: ['student123'],
        equipment: ['board', 'sail'],
        status: 'scheduled',
      };

      expect(lesson).toBeDefined();
      expect(lesson.type).toBe('individual');
    });
  });

  describe('Program Interface', () => {
    it('should create a valid Program object', () => {
      const program: Program = {
        type: 'school',
        name: 'Summer Program',
        startDate: mockTimestamp,
        endDate: mockTimestamp,
        instructorIds: ['instructor123'],
        studentIds: ['student123'],
        goals: ['Learn basics'],
        status: 'active',
      };

      expect(program).toBeDefined();
      expect(program.type).toBe('school');
    });
  });

  describe('Schedule Interface', () => {
    it('should create a valid Schedule object', () => {
      const schedule: Schedule = {
        date: mockTimestamp,
        lessons: ['lesson123'],
        instructorId: 'instructor123',
        notes: 'Test schedule',
      };

      expect(schedule).toBeDefined();
      expect(schedule.instructorId).toBe('instructor123');
    });
  });

  describe('School Interface', () => {
    it('should create a valid School object', () => {
      const school: School = {
        name: 'Test School',
        sports: ['windsurfing'],
        programs: ['program123'],
        settings: {
          language: 'en',
          currency: 'USD',
          timezone: 'UTC',
        },
        createdAt: mockTimestamp,
      };

      expect(school).toBeDefined();
      expect(school.name).toBe('Test School');
    });
  });

  describe('Attendance Interface', () => {
    it('should create a valid Attendance object', () => {
      const attendance: Attendance = {
        studentId: 'student123',
        referenceId: 'lesson123',
        referenceType: 'lesson',
        date: mockTimestamp,
        status: 'present',
        notes: 'On time',
        createdAt: mockTimestamp,
      };

      expect(attendance).toBeDefined();
      expect(attendance.status).toBe('present');
    });
  });
});
