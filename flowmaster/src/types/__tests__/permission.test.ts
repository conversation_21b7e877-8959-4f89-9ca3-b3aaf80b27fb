import { Permission, PermissionType } from '../permission';

describe('Permission Types', () => {
  describe('Permission Enum', () => {
    it('should have all required permissions', () => {
      expect(Permission.READ_CONTENT).toBe('read:content');
      expect(Permission.WRITE_CONTENT).toBe('write:content');
      expect(Permission.MANAGE_USERS).toBe('manage:users');
      expect(Permission.VIEW_USERS).toBe('view:users');
      expect(Permission.ADMIN).toBe('admin');
    });
  });

  describe('PermissionType', () => {
    it('should accept both Permission enum and string values', () => {
      const enumPermission: PermissionType = Permission.READ_CONTENT;
      const stringPermission: PermissionType = 'custom:permission';

      expect(typeof enumPermission).toBe('string');
      expect(typeof stringPermission).toBe('string');
    });

    it('should work with Permission enum values', () => {
      const permissions: PermissionType[] = [
        Permission.READ_CONTENT,
        Permission.WRITE_CONTENT,
        Permission.MANAGE_USERS,
        Permission.VIEW_USERS,
        Permission.ADMIN,
      ];

      permissions.forEach((permission) => {
        expect(typeof permission).toBe('string');
        expect(permission).toBeDefined();
      });
    });
  });
});
