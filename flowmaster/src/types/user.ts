import { RoleType } from './role';

import { Timestamp } from 'firebase/firestore';

export interface UserSettings {
  language: string;
  theme: 'light' | 'dark';
  notifications: boolean;
}

export interface UserProfile {
  photoURL?: string;
  bio?: string;
  skills: string[];
  certifications: string[];
  roleId?: RoleType;
  schoolId?: string;
}

import { User as FirebaseUser } from 'firebase/auth';

export interface User extends FirebaseUser {
  roleId: RoleType;
  schoolId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  profile: UserProfile;
  settings: UserSettings;
}
