// Define the student status type (legacy)
export type StudentStatus = 'active' | 'inactive';

// Define the student type
export type StudentType = 'program' | 'lesson' | 'both';

// Define and export the base interface
export interface StudentFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  enrollmentDate: string;
  level: string;
  specialties: string[];
  status: StudentStatus; // Keeping for backward compatibility
  type?: StudentType; // New field to replace status
}

// Define and export the student interface
export type Student = StudentFormData & {
  id: string;
};
