import React, { Suspense } from 'react';
import Layout from './components/layout/Layout';
import AppRoutes from './routes';
import { AuthProvider } from './context/AuthContext';
import { RoleProvider } from './context/RoleContext';
import { SchoolProvider } from './context/SchoolContext';
import { ServiceWorkerUpdate } from './components/shared/ServiceWorkerUpdate';
import { LoadingScreen } from './components/shared/LoadingScreen';
import './i18n/config';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

const App: React.FC = () => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <AuthProvider>
        <RoleProvider>
          <SchoolProvider>
            <Layout>
              <Suspense fallback={<LoadingScreen />}>
                <AppRoutes />
              </Suspense>
            </Layout>
            <ServiceWorkerUpdate />
          </SchoolProvider>
        </RoleProvider>
      </AuthProvider>
    </LocalizationProvider>
  );
};

export default App;
