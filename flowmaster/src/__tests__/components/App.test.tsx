import React from 'react';
import { render, screen } from '@testing-library/react';
import App from '../../App';

// Mock the required components and providers
jest.mock('../../components/layout/Layout', () => {
  return function MockLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="mock-layout">{children}</div>;
  };
});

jest.mock('../../routes', () => {
  return function MockAppRoutes() {
    return <div data-testid="mock-routes">Routes Content</div>;
  };
});

jest.mock('../../components/shared/ServiceWorkerUpdate', () => ({
  ServiceWorkerUpdate: function MockServiceWorkerUpdate() {
    return <div data-testid="mock-service-worker-update">Service Worker Update</div>;
  },
}));

// Mock the context providers
jest.mock('../../context/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-auth-provider">{children}</div>
  ),
}));

jest.mock('../../context/RoleContext', () => ({
  RoleProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-role-provider">{children}</div>
  ),
}));

describe('App Component', () => {
  it('renders with all required providers and components', () => {
    render(<App />);

    // Verify that all providers are rendered
    expect(screen.getByTestId('mock-auth-provider')).toBeInTheDocument();
    expect(screen.getByTestId('mock-role-provider')).toBeInTheDocument();

    // Verify that Layout component is rendered
    expect(screen.getByTestId('mock-layout')).toBeInTheDocument();

    // Verify that AppRoutes component is rendered
    expect(screen.getByTestId('mock-routes')).toBeInTheDocument();

    // Verify that ServiceWorkerUpdate component is rendered
    expect(screen.getByTestId('mock-service-worker-update')).toBeInTheDocument();
  });
});
