import { lazy, ComponentType } from 'react';

type Factory<T extends ComponentType<unknown>> = () => Promise<{ default: T }>;

interface LazyComponent<T extends ComponentType<unknown>> extends React.LazyExoticComponent<T> {
  preload?: () => Promise<{ default: T }>;
}

/**
 * Creates a lazy-loaded component with preload capability
 * @param factory - The import factory function
 * @returns A lazy component with preload method
 *
 * @example
 * const MyComponent = lazyImportWithPreload(() => import('./MyComponent'));
 * // Later, preload the component
 * MyComponent.preload?.();
 */
export function lazyImportWithPreload<T extends ComponentType<unknown>>(
  factory: Factory<T>
): LazyComponent<T> {
  const Component = lazy(factory);
  (Component as LazyComponent<T>).preload = factory;
  return Component as LazyComponent<T>;
}

/**
 * Preloads multiple components in parallel
 * @param components - Array of lazy components to preload
 * @returns Promise that resolves when all components are preloaded
 *
 * @example
 * preloadComponents([MyComponent, OtherComponent]);
 */
export function preloadComponents(
  components: LazyComponent<ComponentType<unknown>>[]
): Promise<void> {
  return Promise.all(
    components.filter((component) => component.preload).map((component) => component.preload?.())
  ).then(() => undefined);
}

/**
 * Preloads components based on route patterns
 * @param currentPath - Current application path
 * @param routes - Route configuration object
 *
 * @example
 * preloadRouteComponents('/lessons', routes);
 */
export function preloadRouteComponents(
  currentPath: string,
  routes: Record<string, { component: LazyComponent<ComponentType<unknown>>; path: string }>
): void {
  // First try exact match
  const exactMatch = Object.values(routes).find((route) => route.path === currentPath);
  if (exactMatch?.component.preload) {
    exactMatch.component.preload();
    return;
  }

  // If no exact match, try pattern matches (e.g., /lessons/:id)
  const patternMatches = Object.values(routes).filter((route) => {
    const pattern = route.path.replace(/:[^/]+/g, '[^/]+');
    return new RegExp(`^${pattern}$`).test(currentPath);
  });

  if (patternMatches.length > 0) {
    preloadComponents(patternMatches.map((route) => route.component));
  }
}
