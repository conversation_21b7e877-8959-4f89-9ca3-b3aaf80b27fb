/**
 * Interface for rate limiter options
 */
interface RateLimiterOptions {
  maxAttempts: number;
  timeWindow: number;
  storageKey?: string;
}

/**
 * Interface for a rate limit attempt
 */
interface RateLimitAttempt {
  timestamp: number;
  key: string;
}

/**
 * Class for managing rate limiting in the application
 */
export class RateLimiter {
  private attempts: Map<string, RateLimitAttempt[]>;
  private maxAttempts: number;
  private timeWindow: number;
  private storageKey: string;

  /**
   * Creates a new RateLimiter instance
   * @param options - Configuration options for the rate limiter
   */
  constructor(options: RateLimiterOptions) {
    this.maxAttempts = options.maxAttempts;
    this.timeWindow = options.timeWindow;
    this.storageKey = options.storageKey || 'rateLimiter';
    this.attempts = new Map();

    // Load saved attempts from localStorage
    this.loadAttempts();
  }

  /**
   * Checks if an action is allowed for a given key
   * @param key - Unique identifier for the rate-limited action
   * @returns boolean indicating if the action is allowed
   */
  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempts = this.getAttempts(key);

    // Remove expired attempts
    const recentAttempts = attempts.filter((attempt) => now - attempt.timestamp < this.timeWindow);

    if (recentAttempts.length >= this.maxAttempts) {
      return false;
    }

    // Add new attempt
    recentAttempts.push({ timestamp: now, key });
    this.setAttempts(key, recentAttempts);

    return true;
  }

  /**
   * Gets the number of remaining attempts for a key
   * @param key - Unique identifier for the rate-limited action
   * @returns number of remaining attempts
   */
  getRemainingAttempts(key: string): number {
    const now = Date.now();
    const attempts = this.getAttempts(key);
    const recentAttempts = attempts.filter((attempt) => now - attempt.timestamp < this.timeWindow);

    return Math.max(0, this.maxAttempts - recentAttempts.length);
  }

  /**
   * Gets the time until the next attempt is allowed
   * @param key - Unique identifier for the rate-limited action
   * @returns milliseconds until next attempt is allowed, or 0 if attempts are available
   */
  getTimeUntilNextAttempt(key: string): number {
    const now = Date.now();
    const attempts = this.getAttempts(key);

    if (attempts.length === 0) {
      return 0;
    }

    const oldestAttempt = attempts[0].timestamp;
    const timeUntilReset = this.timeWindow - (now - oldestAttempt);

    return Math.max(0, timeUntilReset);
  }

  /**
   * Resets attempts for a given key
   * @param key - Unique identifier for the rate-limited action
   */
  reset(key: string): void {
    this.attempts.delete(key);
    this.saveAttempts();
  }

  /**
   * Resets all rate limiting attempts
   */
  resetAll(): void {
    this.attempts.clear();
    this.saveAttempts();
  }

  private getAttempts(key: string): RateLimitAttempt[] {
    return this.attempts.get(key) || [];
  }

  private setAttempts(key: string, attempts: RateLimitAttempt[]): void {
    this.attempts.set(key, attempts);
    this.saveAttempts();
  }

  private loadAttempts(): void {
    try {
      const saved = localStorage.getItem(this.storageKey);
      if (saved) {
        const parsed = JSON.parse(saved);
        this.attempts = new Map(Object.entries(parsed));
      }
    } catch (error) {
      console.error('Failed to load rate limiter attempts:', error);
      this.attempts = new Map();
    }
  }

  private saveAttempts(): void {
    try {
      const attemptsObj = Object.fromEntries(this.attempts);
      localStorage.setItem(this.storageKey, JSON.stringify(attemptsObj));
    } catch (error) {
      console.error('Failed to save rate limiter attempts:', error);
    }
  }
}

/**
 * Creates a rate limiter instance for authentication attempts
 */
export const authRateLimiter = new RateLimiter({
  maxAttempts: 5,
  timeWindow: 5 * 60 * 1000, // 5 minutes
  storageKey: 'authRateLimiter',
});

/**
 * Creates a rate limiter instance for API requests
 */
export const apiRateLimiter = new RateLimiter({
  maxAttempts: 100,
  timeWindow: 60 * 1000, // 1 minute
  storageKey: 'apiRateLimiter',
});
