import {
  LogLevel,
  logDebug,
  logInfo,
  logWarn,
  logError,
  getLogEntries,
  clearLogEntries,
  getLogEntriesByLevel,
  getRecentLogEntries,
} from '../logger';

describe('Logger', () => {
  beforeEach(() => {
    clearLogEntries();
    jest.spyOn(console, 'debug').mockImplementation();
    jest.spyOn(console, 'info').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should log debug messages in development environment', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    logDebug('Debug message', { detail: 'test' });
    expect(console.debug).toHaveBeenCalled();
    const entries = getLogEntries();
    expect(entries[0].level).toBe(LogLevel.DEBUG);
    expect(entries[0].message).toBe('Debug message');

    process.env.NODE_ENV = originalEnv;
  });

  it('should not log debug messages in production environment', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    logDebug('Debug message');
    expect(console.debug).not.toHaveBeenCalled();
    expect(getLogEntries()).toHaveLength(0);

    process.env.NODE_ENV = originalEnv;
  });

  it('should log info messages', () => {
    logInfo('Info message', { detail: 'test' });
    expect(console.info).toHaveBeenCalled();
    const entries = getLogEntries();
    expect(entries[0].level).toBe(LogLevel.INFO);
    expect(entries[0].message).toBe('Info message');
  });

  it('should log warning messages', () => {
    logWarn('Warning message', { detail: 'test' });
    expect(console.warn).toHaveBeenCalled();
    const entries = getLogEntries();
    expect(entries[0].level).toBe(LogLevel.WARN);
    expect(entries[0].message).toBe('Warning message');
  });

  it('should log error messages with Error object', () => {
    const error = new Error('Test error');
    logError('Error message', error);
    expect(console.error).toHaveBeenCalled();
    const entries = getLogEntries();
    expect(entries[0].level).toBe(LogLevel.ERROR);
    expect(entries[0].message).toBe('Error message');
    expect(entries[0].details).toHaveProperty('name', 'Error');
    expect(entries[0].details).toHaveProperty('message', 'Test error');
    expect(entries[0].details).toHaveProperty('stack');
  });

  it('should filter log entries by level', () => {
    logInfo('Info 1');
    logWarn('Warning 1');
    logError('Error 1');
    logInfo('Info 2');

    const infoEntries = getLogEntriesByLevel(LogLevel.INFO);
    expect(infoEntries).toHaveLength(2);
    expect(infoEntries[0].message).toBe('Info 1');
    expect(infoEntries[1].message).toBe('Info 2');
  });

  it('should get recent log entries', () => {
    for (let i = 0; i < 15; i++) {
      logInfo(`Message ${i}`);
    }

    const recentEntries = getRecentLogEntries(5);
    expect(recentEntries).toHaveLength(5);
    expect(recentEntries[4].message).toBe('Message 14');
  });

  it('should clear all log entries', () => {
    logInfo('Test message');
    expect(getLogEntries()).toHaveLength(1);

    clearLogEntries();
    expect(getLogEntries()).toHaveLength(0);
  });
});
