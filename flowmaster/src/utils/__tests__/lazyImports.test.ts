import React, { ComponentType } from 'react';
import { lazyImportWithPreload, preloadComponents, preloadRouteComponents } from '../lazyImports';

describe('Lazy Imports', () => {
  const mockComponent: ComponentType<unknown> = () =>
    React.createElement('div', null, 'Test Component');
  const mockFactory = jest.fn().mockResolvedValue({ default: mockComponent });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('lazyImportWithPreload', () => {
    it('should create a lazy component with preload capability', async () => {
      const LazyComponent = lazyImportWithPreload(mockFactory);
      expect(LazyComponent).toBeDefined();
      expect(LazyComponent.preload).toBeDefined();

      // Test preloading
      await LazyComponent.preload?.();
      expect(mockFactory).toHaveBeenCalled();
    });
  });

  describe('preloadComponents', () => {
    it('should preload multiple components in parallel', async () => {
      const components = [lazyImportWithPreload(mockFactory), lazyImportWithPreload(mockFactory)];

      await preloadComponents(components);
      expect(mockFactory).toHaveBeenCalledTimes(2);
    });

    it('should handle components without preload method', async () => {
      const components = [
        lazyImportWithPreload(mockFactory),
        lazyImportWithPreload(() => Promise.resolve({ default: mockComponent })),
      ];

      await preloadComponents(components);
      expect(mockFactory).toHaveBeenCalledTimes(1);
    });
  });

  describe('preloadRouteComponents', () => {
    const routes = {
      home: {
        path: '/home',
        component: lazyImportWithPreload(mockFactory),
      },
      lesson: {
        path: '/lessons/:id',
        component: lazyImportWithPreload(mockFactory),
      },
    };

    it('should preload exact match routes', () => {
      preloadRouteComponents('/home', routes);
      expect(mockFactory).toHaveBeenCalledTimes(1);
    });

    it('should preload pattern match routes', () => {
      preloadRouteComponents('/lessons/123', routes);
      expect(mockFactory).toHaveBeenCalledTimes(1);
    });

    it('should not preload non-matching routes', () => {
      preloadRouteComponents('/unknown', routes);
      expect(mockFactory).not.toHaveBeenCalled();
    });
  });
});
