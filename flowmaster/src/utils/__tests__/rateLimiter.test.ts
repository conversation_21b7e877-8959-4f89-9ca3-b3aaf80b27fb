import { RateLimiter, authRateLimiter, apiRateLimiter } from '../rateLimiter';

describe('RateLimiter', () => {
  let rateLimiter: RateLimiter;

  beforeEach(() => {
    localStorage.clear();
    rateLimiter = new RateLimiter({
      maxAttempts: 3,
      timeWindow: 1000, // 1 second
      storageKey: 'testRateLimiter',
    });
  });

  it('should allow attempts within limits', () => {
    expect(rateLimiter.isAllowed('test')).toBe(true);
    expect(rateLimiter.isAllowed('test')).toBe(true);
    expect(rateLimiter.isAllowed('test')).toBe(true);
    expect(rateLimiter.isAllowed('test')).toBe(false);
  });

  it('should track remaining attempts correctly', () => {
    expect(rateLimiter.getRemainingAttempts('test')).toBe(3);
    rateLimiter.isAllowed('test');
    expect(rateLimiter.getRemainingAttempts('test')).toBe(2);
  });

  it('should calculate time until next attempt', () => {
    for (let i = 0; i < 3; i++) {
      rateLimiter.isAllowed('test');
    }
    expect(rateLimiter.getTimeUntilNextAttempt('test')).toBeGreaterThan(0);
  });

  it('should reset attempts for a specific key', () => {
    rateLimiter.isAllowed('test');
    rateLimiter.reset('test');
    expect(rateLimiter.getRemainingAttempts('test')).toBe(3);
  });

  it('should reset all attempts', () => {
    rateLimiter.isAllowed('test1');
    rateLimiter.isAllowed('test2');
    rateLimiter.resetAll();
    expect(rateLimiter.getRemainingAttempts('test1')).toBe(3);
    expect(rateLimiter.getRemainingAttempts('test2')).toBe(3);
  });

  it('should persist attempts in localStorage', () => {
    rateLimiter.isAllowed('test');
    const newRateLimiter = new RateLimiter({
      maxAttempts: 3,
      timeWindow: 1000,
      storageKey: 'testRateLimiter',
    });
    expect(newRateLimiter.getRemainingAttempts('test')).toBe(2);
  });
});

describe('Predefined Rate Limiters', () => {
  it('should configure authRateLimiter correctly', () => {
    expect(authRateLimiter.getRemainingAttempts('test')).toBe(5);
  });

  it('should configure apiRateLimiter correctly', () => {
    expect(apiRateLimiter.getRemainingAttempts('test')).toBe(100);
  });
});
