enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  details?: Record<string, unknown>;
}

type LogMessage = string | Error;
type LogData = Record<string, unknown>;

// Initialize the log entries array
const logEntries: LogEntry[] = [];

export class Logger {
  private static instance: Logger;
  private logs: Array<{
    level: LogLevel;
    message: LogMessage;
    data?: LogData;
    timestamp: Date;
  }> = [];

  private constructor() {}

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private log(level: LogLevel, message: LogMessage, data?: LogData): void {
    const entry = {
      level,
      message,
      data,
      timestamp: new Date(),
    };
    this.logs.push(entry);
    if (this.logs.length > MAX_LOG_ENTRIES) {
      this.logs.shift();
    }
    this.printLogEntry(entry);
  }

  private printLogEntry(entry: {
    level: LogLevel;
    message: LogMessage;
    data?: LogData;
    timestamp: Date;
  }): void {
    const { level, message, data, timestamp } = entry;
    const formattedMessage = `[${timestamp.toISOString()}] ${level.toUpperCase()}: ${message}`;
    const formattedData = data ? `\n${JSON.stringify(data, null, 2)}` : '';
    console.log(`${formattedMessage}${formattedData}`);
  }

  public debug(message: LogMessage, data?: LogData): void {
    this.log(LogLevel.DEBUG, message, data);
  }

  public info(message: LogMessage, data?: LogData): void {
    this.log(LogLevel.INFO, message, data);
  }

  public warn(message: LogMessage, data?: LogData): void {
    this.log(LogLevel.WARN, message, data);
  }

  public error(message: LogMessage, data?: LogData): void {
    this.log(LogLevel.ERROR, message, data);
  }

  public getLogs(): Array<{
    level: LogLevel;
    message: LogMessage;
    data?: LogData;
    timestamp: Date;
  }> {
    return [...this.logs];
  }

  public clearLogs(): void {
    this.logs.length = 0;
  }

  public getLogsByLevel(level: LogLevel): Array<{
    level: LogLevel;
    message: LogMessage;
    data?: LogData;
    timestamp: Date;
  }> {
    return this.logs.filter((entry) => entry.level === level);
  }

  public getRecentLogs(count = 10): Array<{
    level: LogLevel;
    message: LogMessage;
    data?: LogData;
    timestamp: Date;
  }> {
    return this.logs.slice(-count);
  }
}

/**
 * Maximum number of log entries to keep in memory
 */
const MAX_LOG_ENTRIES = 1000;

/**
 * Creates a new log entry
 */
function createLogEntry(
  level: LogLevel,
  message: string,
  details?: Record<string, unknown>
): LogEntry {
  return {
    timestamp: new Date().toISOString(),
    level,
    message,
    details,
  };
}

/**
 * Stores a log entry and manages the log entries array size
 */
function storeLogEntry(entry: LogEntry): void {
  logEntries.push(entry);
  if (logEntries.length > MAX_LOG_ENTRIES) {
    logEntries.shift();
  }
}

/**
 * Formats a log entry for console output
 */
function formatLogEntry(entry: LogEntry): string {
  const { timestamp, level, message, details } = entry;
  return `[${timestamp}] ${level.toUpperCase()}: ${message}${
    details ? '\n' + JSON.stringify(details, null, 2) : ''
  }`;
}

/**
 * Logs a debug message
 */
export function logDebug(message: string, details?: Record<string, unknown>): void {
  if (process.env.NODE_ENV === 'development') {
    const entry = createLogEntry(LogLevel.DEBUG, message, details);
    storeLogEntry(entry);
    console.debug(formatLogEntry(entry));
  }
}

/**
 * Logs an info message
 */
export function logInfo(message: string, details?: Record<string, unknown>): void {
  const entry = createLogEntry(LogLevel.INFO, message, details);
  storeLogEntry(entry);
  console.info(formatLogEntry(entry));
}

/**
 * Logs a warning message
 */
export function logWarn(message: string, details?: Record<string, unknown>): void {
  const entry = createLogEntry(LogLevel.WARN, message, details);
  storeLogEntry(entry);
  console.warn(formatLogEntry(entry));
}

/**
 * Logs an error message
 */
export function logError(message: string, error?: Error | Record<string, unknown>): void {
  const details =
    error instanceof Error
      ? {
          name: error.name,
          message: error.message,
          stack: error.stack,
        }
      : error;

  const entry = createLogEntry(LogLevel.ERROR, message, details);
  storeLogEntry(entry);
  console.error(formatLogEntry(entry));

  if (process.env.REACT_APP_ERROR_MONITORING_ENABLED === 'true') {
    // TODO: Implement error monitoring service integration
  }
}

/**
 * Gets all stored log entries
 */
export function getLogEntries(): LogEntry[] {
  return [...logEntries];
}

/**
 * Clears all stored log entries
 */
export function clearLogEntries(): void {
  logEntries.length = 0;
}

/**
 * Gets log entries filtered by level
 */
export function getLogEntriesByLevel(level: LogLevel): LogEntry[] {
  return logEntries.filter((entry) => entry.level === level);
}

/**
 * Gets the most recent log entries
 */
export function getRecentLogEntries(count = 10): LogEntry[] {
  return logEntries.slice(-count);
}
