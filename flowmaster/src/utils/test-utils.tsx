import React from 'react';
import { render as rtlRender } from '@testing-library/react';
import { I18nextProvider, initReactI18next } from 'react-i18next';
import i18n from 'i18next';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import AuthProvider from '../context/AuthContext';
import { RoleProvider } from '../context/RoleContext';

// Initialize i18next for tests
i18n.use(initReactI18next).init({
  lng: 'en',
  fallbackLng: 'en',
  ns: ['common', 'auth', 'errors'],
  defaultNS: 'common',
  resources: {
    en: {
      common: {
        'auth.login.title': 'Welcome Back',
        'auth.emailVerification.title': 'Verify Your Email',
        'auth.permissions.protected_content': 'Protected Content',
        'auth.permissions.unauthorized': "You don't have permission to access this content",
        'auth.permissions.loading': 'Loading...',
      },
    },
  },
  interpolation: {
    escapeValue: false,
  },
});

interface WrapperProps {
  children: React.ReactNode;
}

const AllTheProviders: React.FC<WrapperProps> = ({ children }) => {
  return (
    <I18nextProvider i18n={i18n}>
      <BrowserRouter>
        <AuthProvider>
          <RoleProvider>{children}</RoleProvider>
        </AuthProvider>
      </BrowserRouter>
    </I18nextProvider>
  );
};

const render = (ui: React.ReactElement, options = {}) =>
  rtlRender(ui, { wrapper: AllTheProviders, ...options });

// re-export everything
export * from '@testing-library/react';

// override render method
export { render };
