import { renderHook, act } from '@testing-library/react';
import { useTheme } from '../useTheme';

describe('useTheme', () => {
  let mockMatchMedia: jest.Mock;
  let mockLocalStorage: { [key: string]: string };

  beforeEach(() => {
    // Mock localStorage
    mockLocalStorage = {};
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key) => mockLocalStorage[key]),
        setItem: jest.fn((key, value) => {
          mockLocalStorage[key] = value;
        }),
      },
      writable: true,
    });

    // Mock matchMedia with a proper MediaQueryList-like object
    mockMatchMedia = jest.fn().mockImplementation((query) => ({
      matches: query === '(prefers-color-scheme: dark)',
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));

    Object.defineProperty(window, 'matchMedia', {
      value: mockMatchMedia,
      writable: true,
    });
  });

  it('should initialize with system preference when no saved theme', () => {
    mockMatchMedia.mockImplementation((query) => ({
      matches: query === '(prefers-color-scheme: dark)',
      media: query,
    }));

    const { result } = renderHook(() => useTheme());

    expect(result.current.isDarkMode).toBe(true);
    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('should initialize with saved theme from localStorage', () => {
    mockLocalStorage['theme'] = 'light';

    const { result } = renderHook(() => useTheme());

    expect(result.current.isDarkMode).toBe(false);
    expect(localStorage.getItem).toHaveBeenCalledWith('theme');
  });

  it('should toggle theme when toggleTheme is called', () => {
    const { result } = renderHook(() => useTheme());
    const initialIsDarkMode = result.current.isDarkMode;

    act(() => {
      result.current.toggleTheme();
    });

    expect(result.current.isDarkMode).toBe(!initialIsDarkMode);
    expect(localStorage.setItem).toHaveBeenCalledWith('theme', expect.any(String));
  });

  it('should update document theme attribute when theme changes', () => {
    const { result } = renderHook(() => useTheme());

    act(() => {
      result.current.toggleTheme();
    });

    expect(document.documentElement.getAttribute('data-theme')).toBe(
      result.current.isDarkMode ? 'dark' : 'light'
    );
  });
});
