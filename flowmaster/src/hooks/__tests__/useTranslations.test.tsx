import { renderHook } from '@testing-library/react';
import { useTranslations } from '../useTranslations';
import { useTranslation } from 'react-i18next';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));

describe('useTranslations', () => {
  const mockT = jest.fn();
  const mockI18n = {
    language: 'en',
    changeLanguage: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useTranslation as jest.Mock).mockReturnValue({
      t: mockT,
      i18n: mockI18n,
    });
  });

  it('should initialize with correct namespaces', () => {
    renderHook(() => useTranslations());

    expect(useTranslation).toHaveBeenCalledWith([
      'common',
      'errors',
      'auth',
      'profile',
      'settings',
    ]);
  });

  it('should return translation function and i18n instance', () => {
    const { result } = renderHook(() => useTranslations());

    expect(result.current.t).toBe(mockT);
    expect(result.current.i18n).toBe(mockI18n);
  });

  it('should handle translation calls', () => {
    const { result } = renderHook(() => useTranslations());
    const key = 'common:ui.loading';
    const expectedTranslation = 'Loading...';

    mockT.mockReturnValue(expectedTranslation);
    const translation = result.current.t(key);

    expect(mockT).toHaveBeenCalledWith(key);
    expect(translation).toBe(expectedTranslation);
  });

  it('should provide access to i18n functions', () => {
    const { result } = renderHook(() => useTranslations());
    const newLang = 'fr';

    result.current.i18n.changeLanguage(newLang);

    expect(mockI18n.changeLanguage).toHaveBeenCalledWith(newLang);
  });
});
