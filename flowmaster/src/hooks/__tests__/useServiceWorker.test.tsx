import { renderHook, act } from '@testing-library/react';
import { useServiceWorker } from '../useServiceWorker';
import { Workbox } from 'workbox-window';

// Mock workbox-window
jest.mock('workbox-window', () => ({
  Workbox: jest.fn(),
}));

describe('useServiceWorker', () => {
  const mockRegistration = {
    waiting: {
      postMessage: jest.fn(),
    },
  };

  let mockWorkbox: jest.Mocked<Workbox>;
  let addEventListenerMap: { [key: string]: (event: { type: string }) => void };
  let originalEnv: NodeJS.ProcessEnv;
  let originalNavigator: Navigator;

  beforeEach(() => {
    // Store original environment
    originalEnv = process.env;
    originalNavigator = window.navigator;

    // Mock process.env
    process.env = {
      ...process.env,
      NODE_ENV: 'production',
    };

    // Mock navigator.serviceWorker and window.location
    const mockLocation = { reload: jest.fn() };
    Object.defineProperty(window, 'navigator', {
      value: { serviceWorker: {} },
      writable: true,
    });
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    });

    // Setup event listener map
    addEventListenerMap = {};

    // Setup Workbox mock
    mockWorkbox = {
      addEventListener: jest.fn((event, cb) => {
        addEventListenerMap[event] = cb;
      }),
      removeEventListener: jest.fn(),
      register: jest.fn().mockResolvedValue(mockRegistration),
    } as unknown as jest.Mocked<Workbox>;

    (Workbox as jest.Mock).mockImplementation(() => mockWorkbox);
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    Object.defineProperty(window, 'navigator', {
      value: originalNavigator,
      writable: true,
    });
    jest.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useServiceWorker());

    expect(result.current.isRegistered).toBe(false);
    expect(result.current.needsRefresh).toBe(false);
    expect(typeof result.current.update).toBe('function');
  });

  it('should register service worker in production', async () => {
    const { result } = renderHook(() => useServiceWorker());

    expect(Workbox).toHaveBeenCalledWith('/service-worker.js');
    expect(mockWorkbox.register).toHaveBeenCalled();
    expect(result.current.isRegistered).toBe(false);

    // Wait for registration promise to resolve
    await act(async () => {
      await mockWorkbox.register();
    });
  });

  it('should not register service worker in development', () => {
    process.env.NODE_ENV = 'development';
    renderHook(() => useServiceWorker());

    expect(Workbox).not.toHaveBeenCalled();
    expect(mockWorkbox.register).not.toHaveBeenCalled();
  });

  it('should handle service worker activation', async () => {
    const { result } = renderHook(() => useServiceWorker());

    // Wait for registration
    await act(async () => {
      await mockWorkbox.register();
    });

    // Simulate service worker activation
    await act(async () => {
      addEventListenerMap['activated']({ type: 'activated' });
    });

    expect(result.current.isRegistered).toBe(true);
  });

  it('should handle update available', async () => {
    const { result } = renderHook(() => useServiceWorker());

    // Wait for registration
    await act(async () => {
      await mockWorkbox.register();
    });

    // Simulate update available
    await act(async () => {
      addEventListenerMap['waiting']({ type: 'waiting' });
    });

    expect(result.current.needsRefresh).toBe(true);
  });

  it('should handle update function', async () => {
    const { result } = renderHook(() => useServiceWorker());
    const reloadSpy = jest.spyOn(window.location, 'reload');

    // Register service worker to set up registration
    await act(async () => {
      await mockWorkbox.register();
    });

    // Trigger update
    await act(async () => {
      await result.current.update();
    });

    expect(mockRegistration.waiting.postMessage).toHaveBeenCalledWith({
      type: 'SKIP_WAITING',
    });
    expect(reloadSpy).toHaveBeenCalled();

    reloadSpy.mockRestore();
  });

  it('should handle registration error', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    const error = new Error('Registration failed');
    mockWorkbox.register.mockRejectedValueOnce(error);

    renderHook(() => useServiceWorker());

    // Wait for registration promise to reject
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(consoleSpy).toHaveBeenCalledWith('Service Worker registration failed:', error);

    consoleSpy.mockRestore();
  });
});
