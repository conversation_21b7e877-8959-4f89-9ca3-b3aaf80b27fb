import { renderHook, act } from '@testing-library/react';
import { useAuth } from '../useAuth';
import { useNavigate } from 'react-router-dom';
import {
  getAuth,
  signInWithEmailAndPassword,
  onAuthStateChanged,
  browserLocalPersistence,
  browserSessionPersistence,
  setPersistence,
} from 'firebase/auth';

// Mock dependencies
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
  signInWithEmailAndPassword: jest.fn(),
  onAuthStateChanged: jest.fn(),
  browserLocalPersistence: 'local',
  browserSessionPersistence: 'session',
  setPersistence: jest.fn(),
}));

describe('useAuth', () => {
  const mockNavigate = jest.fn();
  const mockAuth = {
    signOut: jest.fn(),
  };
  const mockUser = {
    uid: '123',
    email: '<EMAIL>',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (getAuth as jest.Mock).mockReturnValue(mockAuth);
    (onAuthStateChanged as jest.Mock).mockImplementation((_, callback) => {
      callback(null);
      return () => {
        /* Cleanup subscription */
      };
    });
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useAuth());

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
    expect(typeof result.current.signIn).toBe('function');
    expect(typeof result.current.signOut).toBe('function');
  });

  describe('signIn', () => {
    it('should sign in successfully with remember me', async () => {
      (signInWithEmailAndPassword as jest.Mock).mockResolvedValueOnce({});
      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.signIn('<EMAIL>', 'password', true);
      });

      expect(setPersistence).toHaveBeenCalledWith(mockAuth, browserLocalPersistence);
      expect(signInWithEmailAndPassword).toHaveBeenCalledWith(
        mockAuth,
        '<EMAIL>',
        'password'
      );
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
      expect(result.current.error).toBeNull();
    });

    it('should sign in with session persistence when remember me is false', async () => {
      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.signIn('<EMAIL>', 'password', false);
      });

      expect(setPersistence).toHaveBeenCalledWith(mockAuth, browserSessionPersistence);
    });

    it('should handle sign in error', async () => {
      const error = new Error('Invalid credentials');
      (signInWithEmailAndPassword as jest.Mock).mockRejectedValueOnce(error);
      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.signIn('<EMAIL>', 'password');
      });

      expect(result.current.error).toBe(error);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('signOut', () => {
    it('should sign out successfully', async () => {
      mockAuth.signOut.mockResolvedValueOnce({});
      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.signOut();
      });

      expect(mockAuth.signOut).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/login');
      expect(result.current.error).toBeNull();
    });

    it('should handle sign out error', async () => {
      const error = new Error('Sign out failed');
      mockAuth.signOut.mockRejectedValueOnce(error);
      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.signOut();
      });

      expect(result.current.error).toBe(error);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('auth state changes', () => {
    it('should update user state when auth state changes', () => {
      (onAuthStateChanged as jest.Mock).mockImplementation((_, callback) => {
        callback(mockUser);
        return () => {
          /* Cleanup subscription */
        };
      });

      const { result } = renderHook(() => useAuth());

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should handle auth state change error', () => {
      const error = new Error('Auth state change error');
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      (onAuthStateChanged as jest.Mock).mockImplementation(() => {
        throw error;
      });

      const { result } = renderHook(() => useAuth());

      expect(consoleSpy).toHaveBeenCalledWith('Auth state change error:', error);
      expect(result.current.isLoading).toBe(false);

      consoleSpy.mockRestore();
    });
  });
});
