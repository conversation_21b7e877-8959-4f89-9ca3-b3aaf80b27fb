import { renderHook, act } from '@testing-library/react';
import { useAuthForm } from '../useAuthForm';
import { useAuth } from '../../context/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

// Mock dependencies
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
  useLocation: jest.fn(),
}));

jest.mock('../../context/AuthContext', () => ({
  useAuth: jest.fn(),
}));

describe('useAuthForm', () => {
  const mockNavigate = jest.fn();
  const mockSignIn = jest.fn();
  const mockSignUp = jest.fn();
  const mockLocation = { state: { from: { pathname: '/dashboard' } } };

  beforeEach(() => {
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (useLocation as jest.Mock).mockReturnValue(mockLocation);
    (useAuth as jest.Mock).mockReturnValue({
      signIn: mockSignIn,
      signUp: mockSignUp,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Login Form', () => {
    it('should initialize with empty form state for login', () => {
      const { result } = renderHook(() => useAuthForm('login'));

      expect(result.current.formState).toEqual({
        email: '',
        password: '',
      });
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should update form state on input change', () => {
      const { result } = renderHook(() => useAuthForm('login'));

      act(() => {
        result.current.handleChange({
          target: { name: 'email', value: '<EMAIL>' },
        } as React.ChangeEvent<HTMLInputElement>);
      });

      expect(result.current.formState.email).toBe('<EMAIL>');
    });

    it('should handle successful login submission', async () => {
      mockSignIn.mockResolvedValueOnce({});
      const { result } = renderHook(() => useAuthForm('login'));

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as unknown as React.FormEvent<HTMLFormElement>);
      });

      expect(mockSignIn).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
      expect(result.current.error).toBeNull();
    });

    it('should handle login error', async () => {
      const error = new Error('Invalid credentials');
      mockSignIn.mockRejectedValueOnce(error);
      const { result } = renderHook(() => useAuthForm('login'));

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as unknown as React.FormEvent<HTMLFormElement>);
      });

      expect(result.current.error).toBe(error.message);
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Register Form', () => {
    it('should initialize with empty form state for register', () => {
      const { result } = renderHook(() => useAuthForm('register'));

      expect(result.current.formState).toEqual({
        email: '',
        password: '',
        displayName: '',
      });
    });

    it('should handle successful registration', async () => {
      mockSignUp.mockResolvedValueOnce({});
      const { result } = renderHook(() => useAuthForm('register'));

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as unknown as React.FormEvent<HTMLFormElement>);
      });

      expect(mockSignUp).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
      expect(result.current.error).toBeNull();
    });

    it('should handle registration error', async () => {
      const error = new Error('Email already exists');
      mockSignUp.mockRejectedValueOnce(error);
      const { result } = renderHook(() => useAuthForm('register'));

      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn(),
        } as unknown as React.FormEvent<HTMLFormElement>);
      });

      expect(result.current.error).toBe(error.message);
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });
});
