import { renderHook } from '@testing-library/react';
import { usePermission } from '../usePermission';
import { useRole } from '../../context/RoleContext';
import { Permission } from '../../types/role';

// Mock the useRole hook
jest.mock('../../context/RoleContext', () => ({
  useRole: jest.fn(),
}));

describe('usePermission', () => {
  const mockRole = {
    id: 'admin',
    name: 'Administrator',
    permissions: ['manage_users', 'manage_lessons'],
  };

  const mockCheckPermission = jest.fn();

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup default mock implementation
    (useRole as jest.Mock).mockReturnValue({
      role: mockRole,
      checkPermission: mockCheckPermission,
    });
  });

  it('returns all expected properties', () => {
    const { result } = renderHook(() => usePermission());

    expect(result.current).toHaveProperty('role');
    expect(result.current).toHaveProperty('hasPermission');
    expect(result.current).toHaveProperty('hasAllPermissions');
    expect(result.current).toHaveProperty('hasAnyPermission');
    expect(typeof result.current.hasPermission).toBe('function');
    expect(typeof result.current.hasAllPermissions).toBe('function');
    expect(typeof result.current.hasAnyPermission).toBe('function');
  });

  describe('hasPermission', () => {
    it('calls checkPermission with the correct permission', () => {
      const { result } = renderHook(() => usePermission());
      mockCheckPermission.mockReturnValue(true);

      const permission: Permission = 'manage_users';
      const hasPermission = result.current.hasPermission(permission);

      expect(mockCheckPermission).toHaveBeenCalledWith(permission);
      expect(hasPermission).toBe(true);
    });

    it('returns false when checkPermission returns false', () => {
      const { result } = renderHook(() => usePermission());
      mockCheckPermission.mockReturnValue(false);

      const permission: Permission = 'manage_lessons';
      const hasPermission = result.current.hasPermission(permission);

      expect(mockCheckPermission).toHaveBeenCalledWith(permission);
      expect(hasPermission).toBe(false);
    });
  });

  describe('hasAllPermissions', () => {
    it('returns true when user has all permissions', () => {
      const { result } = renderHook(() => usePermission());
      mockCheckPermission.mockReturnValue(true);

      const permissions: Permission[] = ['manage_users', 'manage_lessons'];
      const hasAll = result.current.hasAllPermissions(permissions);

      expect(mockCheckPermission).toHaveBeenCalledTimes(2);
      expect(hasAll).toBe(true);
    });

    it('returns false when user lacks any permission', () => {
      const { result } = renderHook(() => usePermission());
      mockCheckPermission.mockReturnValueOnce(true).mockReturnValueOnce(false);

      const permissions: Permission[] = ['manage_users', 'manage_lessons'];
      const hasAll = result.current.hasAllPermissions(permissions);

      expect(mockCheckPermission).toHaveBeenCalledTimes(2);
      expect(hasAll).toBe(false);
    });

    it('returns true for empty permissions array', () => {
      const { result } = renderHook(() => usePermission());
      const hasAll = result.current.hasAllPermissions([]);
      expect(hasAll).toBe(true);
    });
  });

  describe('hasAnyPermission', () => {
    it('returns true when user has at least one permission', () => {
      const { result } = renderHook(() => usePermission());
      mockCheckPermission.mockReturnValueOnce(false).mockReturnValueOnce(true);

      const permissions: Permission[] = ['manage_users', 'manage_lessons'];
      const hasAny = result.current.hasAnyPermission(permissions);

      expect(mockCheckPermission).toHaveBeenCalledTimes(2);
      expect(hasAny).toBe(true);
    });

    it('returns false when user has no permissions', () => {
      const { result } = renderHook(() => usePermission());
      mockCheckPermission.mockReturnValue(false);

      const permissions: Permission[] = ['manage_users', 'manage_lessons'];
      const hasAny = result.current.hasAnyPermission(permissions);

      expect(mockCheckPermission).toHaveBeenCalledTimes(2);
      expect(hasAny).toBe(false);
    });

    it('returns false for empty permissions array', () => {
      const { result } = renderHook(() => usePermission());
      const hasAny = result.current.hasAnyPermission([]);
      expect(hasAny).toBe(false);
    });
  });
});
