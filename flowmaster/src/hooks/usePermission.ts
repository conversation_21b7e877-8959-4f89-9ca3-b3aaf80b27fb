import { useRole } from '../context/RoleContext';
import { Permission, Role } from '../types/role';

interface UsePermissionReturn {
  role: Role | null;
  hasPermission: (permission: Permission) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
}

/**
 * Hook for checking user permissions
 * @returns Object with permission check functions
 */
export const usePermission = (): UsePermissionReturn => {
  const { role, checkPermission } = useRole();

  const hasPermission = (permission: Permission): boolean => {
    return checkPermission(permission);
  };

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every((permission) => checkPermission(permission));
  };

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some((permission) => checkPermission(permission));
  };

  return {
    role,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
  };
};

export default usePermission;
