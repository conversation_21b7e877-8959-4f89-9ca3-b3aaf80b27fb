import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FirebaseError } from 'firebase/app';
import {
  signInWithEmailAndPassword,
  onAuthStateChanged,
  browserLocalPersistence,
  browserSessionPersistence,
  setPersistence,
} from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import type { FlowMasterUser } from '../types/role';
import type { User } from '../types/user';

interface UseAuthReturn {
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (data: {
    displayName?: string;
    email?: string;
    phoneNumber?: string;
    bio?: string;
  }) => Promise<void>;
  error: Error | null;
  user: FlowMasterUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export const useAuth = (): UseAuthReturn => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [user, setUser] = useState<FlowMasterUser | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    let unsubscribe = () => {
      return;
    };
    try {
      unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          try {
            const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
            if (userDoc.exists()) {
              const userData = userDoc.data() as User;
              setUser({
                ...firebaseUser,
                role: userData.roleId,
                schoolId: userData.schoolId,
                bio: userData.profile?.bio,
              } as FlowMasterUser);
            } else {
              console.error(
                `User document not found in Firestore for uid: ${firebaseUser.uid}. This might happen if the user registration process was interrupted.`
              );
              setError(new Error('User profile not found. Please try signing out and in again.'));
              setUser(null);
            }
          } catch (err) {
            console.error('Error fetching user data:', err);
            setError(new Error('Failed to load user profile. Please try again later.'));
            setUser(null);
          }
        } else {
          setUser(null);
        }
        setIsLoading(false);
      });
    } catch (err) {
      console.error('Auth state change error:', err);
      setIsLoading(false);
    }
    return () => {
      try {
        unsubscribe();
      } catch (err) {
        console.error('Unsubscribe error:', err);
      }
    };
  }, []);

  const signIn = async (email: string, password: string, rememberMe = false) => {
    try {
      setIsLoading(true);
      setError(null);
      const persistenceType = rememberMe ? browserLocalPersistence : browserSessionPersistence;
      await setPersistence(auth, persistenceType);
      const { user: firebaseUser } = await signInWithEmailAndPassword(auth, email, password);
      // Fetch user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        throw new Error('User data not found');
      }
      const userData = userDoc.data() as User;
      setUser({
        ...firebaseUser,
        role: userData.roleId,
        schoolId: userData.schoolId,
        bio: userData.profile?.bio,
      } as FlowMasterUser);

      navigate('/dashboard');
    } catch (err) {
      const error = err as FirebaseError;
      // Handle Firebase Auth errors with user-friendly messages
      if (error.code === 'auth/wrong-password' || error.code === 'auth/user-not-found') {
        setError(new Error('Invalid email or password'));
      } else if (error.code === 'auth/too-many-requests') {
        setError(new Error('Too many failed login attempts. Please try again later'));
      } else {
        setError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await auth.signOut();
      navigate('/login');
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: {
    displayName?: string;
    email?: string;
    phoneNumber?: string;
    bio?: string;
  }) => {
    try {
      setIsLoading(true);
      setError(null);
      if (!user) throw new Error('No user logged in');

      // Update user profile in Firebase Auth
      if (data.displayName || data.email) {
        await user.updateProfile({
          displayName: data.displayName || user.displayName,
        });
        if (data.email) {
          await user.updateEmail(data.email);
        }
      }

      // Update phone number if provided
      if (data.phoneNumber) {
        await user.updatePhoneNumber(data.phoneNumber);
      }

      // Update custom claims or additional user data in your backend/database
      // You would typically make an API call here to update the bio and other custom fields

      setUser({ ...user, ...data } as FlowMasterUser);
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return { signIn, signOut, updateProfile, error, user, isLoading, isAuthenticated: !!user };
};
