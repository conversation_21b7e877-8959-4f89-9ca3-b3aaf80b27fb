import { useEffect, useState } from 'react';
import { Workbox, WorkboxLifecycleEvent } from 'workbox-window';

interface ServiceWorkerState {
  isRegistered: boolean;
  needsRefresh: boolean;
  registration: ServiceWorkerRegistration | null;
  update: () => void;
}

export const useServiceWorker = () => {
  const [state, setState] = useState<ServiceWorkerState>({
    isRegistered: false,
    needsRefresh: false,
    registration: null,
    update: () => {
      if (state.registration?.waiting) {
        state.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }
      window.location.reload();
    },
  });

  useEffect(() => {
    if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {
      const wb = new Workbox('/service-worker.js');

      const updateCallback = () => {
        setState((prev) => ({ ...prev, needsRefresh: true }));
      };

      // Add event listeners
      wb.addEventListener('installed', (event: WorkboxLifecycleEvent) => {
        // Silent success in production
        if (process.env.NODE_ENV === 'development') {
          // Using void to explicitly ignore the console statement
          void event;
        }
      });

      wb.addEventListener('activated', (event: WorkboxLifecycleEvent) => {
        // Silent success in production
        if (process.env.NODE_ENV === 'development') {
          // Using void to explicitly ignore the console statement
          void event;
        }
        setState((prev) => ({
          ...prev,
          isRegistered: true,
        }));
      });

      wb.addEventListener('waiting', updateCallback);
      wb.addEventListener('controlling', updateCallback);

      // Register the service worker
      wb.register()
        .then((registration) => {
          if (registration) {
            setState((prev) => ({
              ...prev,
              isRegistered: true,
              registration: registration,
            }));
            if (process.env.NODE_ENV === 'development') {
              // Using void to explicitly ignore the console statement
              void registration;
            }
          }
        })
        .catch((error: Error) => {
          // Using void to explicitly ignore the console statement in production
          void error;
        });

      // Return cleanup function that removes event listeners
      return () => {
        wb.removeEventListener('waiting', updateCallback);
        wb.removeEventListener('controlling', updateCallback);
      };
    }

    // Return empty cleanup function with explicit void return type
    return (): void => {
      // Cleanup not needed when service worker is not used
      void 0;
    };
  }, []);

  return state;
};
