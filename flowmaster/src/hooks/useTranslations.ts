import { useTranslation } from 'react-i18next';

/**
 * Custom hook that wraps react-i18next's useTranslation hook to provide typed translations
 * across all namespaces. This ensures type safety when using translation keys.
 *
 * @returns The translation function and i18n instance
 */
export const useTranslations = () => {
  const namespaces = ['common', 'errors', 'auth', 'profile', 'settings', 'reports'] as const;
  const { t, i18n } = useTranslation(namespaces);

  return { t, i18n };
};
