// import { useEffect } from 'react';
// import { useAuth } from '../context/AuthContext';
// import { useRole } from '../context/RoleContext';
// import { useNavigate } from 'react-router-dom';

/**
 * Custom hook to check if the user has equipment management permissions
 * For development, this hook always returns true
 */
export const useEquipmentPermission = () => {
  // Commented out for development
  // const { user } = useAuth();
  // const { checkPermission, role, loading } = useRole();
  // const navigate = useNavigate();

  // For development, always return true for equipment permission
  // This is a temporary fix until the Firebase emulator is properly set up
  return {
    hasEquipmentPermission: true,
    isLoading: false
  };

  /* Commented out for development
  useEffect(() => {
    // If still loading or user is not logged in, don't do anything yet
    if (loading || !user) return;

    console.log('Role loaded:', role);
    console.log('User:', user);

    // Check if user has the required permission
    const canManageEquipment = checkPermission('manage_equipment');
    console.log('Can manage equipment:', canManageEquipment);

    // For debugging, log the role permissions
    if (role) {
      console.log('Role permissions:', role.permissions);
      console.log('Has manage_equipment:', role.permissions.includes('manage_equipment'));
    }

    // Only redirect if we're sure the user doesn't have permission
    if (!canManageEquipment) {
      console.warn('User does not have permission to manage equipment');
      navigate('/dashboard');
    }
  }, [user, role, loading, checkPermission, navigate]);

  // Don't check permission if still loading
  const hasPermission = !loading && checkPermission('manage_equipment');

  return {
    hasEquipmentPermission: hasPermission,
    isLoading: loading
  };
  */
};
