import { useContext } from 'react';
import { SchoolContext, SchoolContextType } from '../context/SchoolContext';

export interface School {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  logo?: string;
  settings?: {
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
      logo?: string;
    };
    notifications?: {
      email?: boolean;
      sms?: boolean;
    };
    preferences?: {
      language?: string;
      timezone?: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

export const useSchool = (): SchoolContextType => {
  const context = useContext(SchoolContext);

  if (!context) {
    throw new Error('useSchool must be used within a SchoolProvider');
  }

  return context;
};
