import { doc, getDoc, updateDoc, collection, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useCallback } from 'react';

export const useFirestore = () => {
  const getDocument = useCallback(async (collection: string, id: string) => {
    try {
      const docRef = doc(db, collection, id);
      const docSnap = await getDoc(docRef);
      return docSnap.exists() ? docSnap.data() : null;
    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  }, []);

  const updateDocument = useCallback(
    async <T extends Record<string, unknown>>(collection: string, id: string, data: T) => {
      try {
        const docRef = doc(db, collection, id);
        await updateDoc(docRef, data);
      } catch (error) {
        console.error('Error updating document:', error);
        throw error;
      }
    },
    []
  );

  const getCollection = useCallback(async (collectionName: string) => {
    try {
      const collectionRef = collection(db, collectionName);
      const querySnapshot = await getDocs(collectionRef);
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
    } catch (error) {
      console.error('Error getting collection:', error);
      throw error;
    }
  }, []);

  return {
    getDocument,
    updateDocument,
    getCollection,
  };
};
