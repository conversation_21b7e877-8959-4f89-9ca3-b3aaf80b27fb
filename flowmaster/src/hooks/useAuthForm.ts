import { useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { RoleType } from '../types/role';

interface LocationState {
  from?: {
    pathname: string;
  };
}

interface AuthFormState {
  email: string;
  password: string;
  displayName?: string;
  role?: RoleType;
  schoolName?: string;
  schoolDescription?: string;
  schoolCode?: string;
}

interface UseAuthFormReturn {
  formState: AuthFormState;
  loading: boolean;
  error: string | null;
  handleChange: (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<{ name?: string; value: unknown }>
      | { target: { name: string; value: string } }
  ) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => Promise<void>;
}

type AuthFormType = 'login' | 'register';

export const useAuthForm = (type: AuthFormType): UseAuthFormReturn => {
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, signUp } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formState, setFormState] = useState<AuthFormState>({
    email: '',
    password: '',
    ...(type === 'register' && { displayName: '' }),
  });

  const handleChange = useCallback(
    (
      e:
        | React.ChangeEvent<HTMLInputElement>
        | React.ChangeEvent<{ name?: string; value: unknown }>
        | { target: { name: string; value: string } }
    ) => {
      const { name, value } = e.target;
      if (name) {
        setFormState((prev) => ({ ...prev, [name]: value }));
      }
    },
    []
  );

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (type === 'login') {
        await signIn(formState.email, formState.password);
      } else {
        if (!formState.displayName) {
          throw new Error('Display name is required');
        }
        if (formState.schoolCode) {
          // Join existing school flow
          if (!formState.role) {
            throw new Error('Role selection is required');
          }
          await signUp(formState.email, formState.password, formState.displayName, {
            schoolCode: formState.schoolCode,
            role: formState.role,
          });
        } else {
          // Create new school flow
          if (!formState.schoolName) {
            throw new Error('School name is required');
          }
          await signUp(formState.email, formState.password, formState.displayName, {
            schoolName: formState.schoolName,
            schoolDescription: formState.schoolDescription,
          });
        }
      }

      // Get the redirect path from location state, or default to dashboard
      const state = location.state as LocationState;
      const from = state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return {
    formState,
    loading,
    error,
    handleChange,
    handleSubmit,
  };
};
