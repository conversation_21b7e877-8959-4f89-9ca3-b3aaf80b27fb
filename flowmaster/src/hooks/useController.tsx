import {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
  UseControllerProps as RHFUseControllerProps,
  useController as useRHFController,
} from 'react-hook-form';

interface UseControllerProps<T extends FieldValues>
  extends Omit<RHFUseControllerProps<T>, 'rules'> {
  name: Path<T>;
  control: Control<T>;
  rules?: Omit<
    RegisterOptions<T, Path<T>>,
    'disabled' | 'setValueAs' | 'valueAsNumber' | 'valueAsDate'
  >;
  defaultValue?: T[Path<T>];
}

export const useController = <T extends FieldValues>({
  name,
  control,
  rules,
  defaultValue,
}: UseControllerProps<T>) => {
  const { field, fieldState, formState } = useRHFController({
    name,
    control,
    rules,
    defaultValue,
  });

  return {
    field,
    fieldState,
    formState,
  };
};
