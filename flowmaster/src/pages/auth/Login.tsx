import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { FirebaseError } from 'firebase/app';
import {
  Box,
  Button,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  Link as MuiLink,
  TextField,
} from '@mui/material';
import { useAuth } from '../../hooks/useAuth';
import AuthLayout from '../../components/auth/AuthLayout';
import { ErrorAlert } from '../../components/shared/ErrorAlert';

const Login = () => {
  const { t } = useTranslation(['auth', 'errors']);
  const { signIn, isLoading, error, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError(t('auth:login.errors.invalidEmail'));
      return false;
    }
    setEmailError('');
    return true;
  };

  const validatePassword = (password: string) => {
    const hasMinLength = password.length >= 8;
    const hasNumber = /[0-9]/.test(password);

    if (!hasMinLength || !hasNumber) {
      setPasswordError(t('auth:login.errors.passwordRequirements'));
      return false;
    }
    setPasswordError('');
    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (validateEmail(email) && validatePassword(password)) {
      try {
        await signIn(email, password, rememberMe);
      } catch (err: unknown) {
        if (err instanceof FirebaseError) {
          if (err.code === 'auth/user-not-found') {
            setEmailError(t('auth:login.errors.userNotFound'));
          } else if (err.code === 'auth/wrong-password') {
            setPasswordError(t('auth:login.errors.wrongPassword'));
          } else if (err.code === 'auth/too-many-requests') {
            setPasswordError(t('auth:login.errors.tooManyRequests'));
          }
        }
      }
    }
  };

  const handleEmailBlur = () => {
    validateEmail(email);
  };

  const handlePasswordBlur = () => {
    validatePassword(password);
  };

  return (
    <AuthLayout title={t('auth:login.title')} subtitle={t('auth:login.subtitle')}>
      <form onSubmit={handleSubmit} noValidate data-testid="login-form">
        <ErrorAlert error={error} />

        <TextField
          required
          fullWidth
          id="email"
          label={t('auth:login.email')}
          name="email"
          autoComplete="email"
          autoFocus
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          onBlur={handleEmailBlur}
          error={!!emailError}
          helperText={emailError}
          sx={{ mb: 2 }}
        />

        <TextField
          required
          fullWidth
          name="password"
          label={t('auth:login.password')}
          type="password"
          id="password"
          autoComplete="current-password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          onBlur={handlePasswordBlur}
          error={!!passwordError}
          helperText={passwordError}
        />

        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                color="primary"
              />
            }
            label={t('auth:login.rememberMe')}
          />
          <MuiLink
            component={RouterLink}
            to="/auth/reset-password"
            variant="body2"
            sx={{ textDecoration: 'none' }}
          >
            {t('auth:login.forgotPassword')}
          </MuiLink>
        </Box>

        <Button
          type="submit"
          fullWidth
          variant="contained"
          disabled={isLoading}
          sx={{ mt: 3, mb: 2 }}
          size="large"
        >
          {isLoading ? <CircularProgress size={24} color="inherit" /> : t('auth:login.signIn')}
        </Button>

        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <MuiLink
            component={RouterLink}
            to="/auth/register"
            variant="body2"
            sx={{ textDecoration: 'none' }}
          >
            {t('auth:login.noAccount')} {t('auth:login.signUp')}
          </MuiLink>
        </Box>
      </form>
    </AuthLayout>
  );
};

export default Login;
