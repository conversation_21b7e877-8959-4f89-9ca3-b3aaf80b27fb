import {
  <PERSON>,
  <PERSON>ton,
  CircularP<PERSON>ress,
  Divider,
  Link as <PERSON>i<PERSON><PERSON>,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
} from '@mui/material';
import { Link } from 'react-router-dom';
import AuthLayout from '../../components/auth/AuthLayout';
import TextField from '../../components/forms/TextField';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { useAuthForm } from '../../hooks/useAuthForm';
import React from 'react';

const Register = () => {
  const { formState, loading, error, handleChange, handleSubmit } = useAuthForm('register');
  const [registrationType, setRegistrationType] = React.useState('school');

  // Initialize form state with default role
  React.useEffect(() => {
    if (registrationType === 'user' && !formState.role) {
      handleChange({
        target: { name: 'role', value: 'client' },
      } as React.ChangeEvent<HTMLInputElement>);
    }
  }, [registrationType, formState.role, handleChange]);

  const handleRegistrationTypeChange = (_event: React.SyntheticEvent, newValue: string) => {
    setRegistrationType(newValue);
  };

  return (
    <AuthLayout title="Create Account" subtitle="Join FlowMaster to manage your sports school">
      <Tabs
        value={registrationType}
        onChange={handleRegistrationTypeChange}
        centered
        sx={{ mb: 3 }}
      >
        <Tab value="school" label="New School" />
        <Tab value="user" label="Join Existing School" />
      </Tabs>

      <form onSubmit={handleSubmit} noValidate data-testid="register-form">
        <ErrorAlert error={error} />

        <TextField
          required
          fullWidth
          id="displayName"
          label="Full Name"
          name="displayName"
          autoComplete="name"
          autoFocus
          value={formState.displayName}
          onChange={handleChange}
          disabled={loading}
        />

        <TextField
          required
          fullWidth
          id="email"
          label="Email Address"
          name="email"
          autoComplete="email"
          value={formState.email}
          onChange={handleChange}
          disabled={loading}
          sx={{ mt: 2 }}
        />

        <TextField
          required
          fullWidth
          name="password"
          label="Password"
          type="password"
          id="password"
          autoComplete="new-password"
          value={formState.password}
          onChange={handleChange}
          disabled={loading}
          helperText="Password must be at least 8 characters long"
          sx={{ mt: 2 }}
        />

        {registrationType === 'school' && (
          <>
            <TextField
              required
              fullWidth
              id="schoolName"
              label="School Name"
              name="schoolName"
              value={formState.schoolName || ''}
              onChange={handleChange}
              disabled={loading}
              sx={{ mt: 2 }}
            />

            <TextField
              fullWidth
              id="schoolDescription"
              label="School Description"
              name="schoolDescription"
              multiline
              rows={2}
              value={formState.schoolDescription || ''}
              onChange={handleChange}
              disabled={loading}
              sx={{ mt: 2 }}
            />
          </>
        )}

        {registrationType === 'user' && (
          <>
            <TextField
              required
              fullWidth
              id="schoolCode"
              label="School Invitation Code"
              name="schoolCode"
              value={formState.schoolCode || ''}
              onChange={handleChange}
              disabled={loading}
              helperText="Enter the invitation code provided by your school"
              sx={{ mt: 2 }}
            />

            <FormControl fullWidth required sx={{ mt: 2 }}>
              <InputLabel id="role-label">Role</InputLabel>
              <Select
                labelId="role-label"
                id="role"
                name="role"
                value={formState.role || ''}
                label="Role"
                onChange={handleChange}
                disabled={loading}
              >
                <MenuItem value="instructor">Instructor</MenuItem>
                <MenuItem value="client">Client</MenuItem>
              </Select>
            </FormControl>
          </>
        )}

        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="large"
          disabled={loading}
          sx={{ mt: 3, mb: 2 }}
        >
          {loading ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
        </Button>

        <Divider sx={{ my: 2 }}>
          <Typography variant="body2" color="text.secondary">
            OR
          </Typography>
        </Divider>

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2">
            Already have an account?{' '}
            <MuiLink component={Link} to="/login" sx={{ textDecoration: 'none' }}>
              Sign in
            </MuiLink>
          </Typography>
        </Box>
      </form>
    </AuthLayout>
  );
};

export default Register;
