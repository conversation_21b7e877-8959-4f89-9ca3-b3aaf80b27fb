import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ForgotPassword from '../ForgotPassword';
import { useAuth } from '../../../context/AuthContext';

jest.mock('../../../context/AuthContext');

const mockUseAuth = useAuth as jest.Mock;

describe('ForgotPassword Component', () => {
  const mockResetPassword = jest.fn();

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      resetPassword: mockResetPassword,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders forgot password form correctly', () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );

    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reset password/i })).toBeInTheDocument();
    expect(screen.getByText(/remember your password/i)).toBeInTheDocument();
  });

  it('handles form submission successfully', async () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    const submitButton = screen.getByRole('button', { name: /reset password/i });
    fireEvent.click(submitButton);

    expect(mockResetPassword).toHaveBeenCalledWith('<EMAIL>');
  });

  it('displays loading state during submission', async () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    const submitButton = screen.getByRole('button', { name: /reset password/i });
    fireEvent.click(submitButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(emailInput).toBeDisabled();
    expect(submitButton).toBeDisabled();
  });

  it('displays error message on failed submission', async () => {
    const errorMessage = 'Failed to reset password';
    mockResetPassword.mockRejectedValueOnce(new Error(errorMessage));

    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    fireEvent.click(screen.getByRole('button', { name: /reset password/i }));

    expect(await screen.findByText(errorMessage)).toBeInTheDocument();
  });

  it('displays success message after successful submission', async () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    fireEvent.click(screen.getByRole('button', { name: /reset password/i }));

    expect(await screen.findByText(/password reset email sent/i)).toBeInTheDocument();
  });

  it('clears email input after successful submission', async () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    fireEvent.click(screen.getByRole('button', { name: /reset password/i }));

    await waitFor(() => {
      expect(emailInput).toHaveValue('');
    });
  });
});
