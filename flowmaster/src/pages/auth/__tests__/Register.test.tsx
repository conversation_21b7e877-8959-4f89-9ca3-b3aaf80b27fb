import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Register from '../Register';
import { useAuthForm } from '../../../hooks/useAuthForm';

// Mock the useAuthForm hook
jest.mock('../../../hooks/useAuthForm');

const mockUseAuthForm = useAuthForm as jest.Mock;

describe('Register Component', () => {
  const defaultMockValues = {
    formState: {
      displayName: '',
      email: '',
      password: '',
    },
    loading: false,
    error: null,
    handleChange: jest.fn(),
    handleSubmit: jest.fn((e) => e.preventDefault()),
  };

  beforeEach(() => {
    mockUseAuthForm.mockReturnValue(defaultMockValues);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders register form correctly', () => {
    render(
      <BrowserRouter>
        <Register />
      </BrowserRouter>
    );

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
    expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
  });

  it('handles form submission', async () => {
    const mockHandleSubmit = jest.fn((e) => e.preventDefault());
    mockUseAuthForm.mockReturnValue({
      ...defaultMockValues,
      handleSubmit: mockHandleSubmit,
    });

    render(
      <BrowserRouter>
        <Register />
      </BrowserRouter>
    );

    fireEvent.submit(screen.getByTestId('register-form'));

    await waitFor(() => {
      expect(mockHandleSubmit).toHaveBeenCalled();
    });
  });

  it('handles input changes', () => {
    const mockHandleChange = jest.fn();
    mockUseAuthForm.mockReturnValue({
      ...defaultMockValues,
      handleChange: mockHandleChange,
    });

    render(
      <BrowserRouter>
        <Register />
      </BrowserRouter>
    );

    fireEvent.change(screen.getByLabelText(/full name/i), {
      target: { value: 'John Doe' },
    });

    expect(mockHandleChange).toHaveBeenCalled();
  });

  it('displays loading state', () => {
    mockUseAuthForm.mockReturnValue({
      ...defaultMockValues,
      loading: true,
    });

    render(
      <BrowserRouter>
        <Register />
      </BrowserRouter>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /create account/i })).not.toBeInTheDocument();
  });

  it('displays error message when error exists', async () => {
    const errorMessage = 'Registration failed';
    mockUseAuthForm.mockReturnValue({
      ...defaultMockValues,
      error: errorMessage,
    });

    render(
      <BrowserRouter>
        <Register />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('disables form inputs while loading', () => {
    mockUseAuthForm.mockReturnValue({
      ...defaultMockValues,
      loading: true,
    });

    render(
      <BrowserRouter>
        <Register />
      </BrowserRouter>
    );

    expect(screen.getByLabelText(/full name/i)).toBeDisabled();
    expect(screen.getByLabelText(/email address/i)).toBeDisabled();
    expect(screen.getByLabelText(/password/i)).toBeDisabled();
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
