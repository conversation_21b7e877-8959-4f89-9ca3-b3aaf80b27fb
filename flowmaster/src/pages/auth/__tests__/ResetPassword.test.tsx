import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import ResetPassword from '../ResetPassword';
import { sendPasswordResetEmail } from 'firebase/auth';
import { jest } from '@jest/globals';

// Mock Firebase auth
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({
    currentUser: null,
  })),
  sendPasswordResetEmail: jest.fn(),
}));

// Mock react-router-dom's useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => {
  const actual = jest.requireActual('react-router-dom') as object;
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock translations
jest.mock('../../../hooks/useTranslations', () => ({
  useTranslations: () => ({
    t: (key: string) => {
      // Return the actual text that should be displayed
      const translations: Record<string, string> = {
        'common:ui.email': 'Email',
        'auth:resetPassword.submit': 'Reset Password',
        'auth:resetPassword.backToLogin': 'Back to Login',
        'common:ui.loading': 'Loading...',
        'auth:resetPassword.success': 'Password reset email sent',
        'errors:auth.resetPassword.error': 'Failed to reset password',
      };
      return Object.prototype.hasOwnProperty.call(translations, key) ? translations[key] : key;
    },
  }),
}));

describe('ResetPassword Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <BrowserRouter>
        <ResetPassword />
      </BrowserRouter>
    );
  };

  it('renders reset password form correctly', () => {
    renderComponent();

    expect(screen.getByRole('textbox', { name: 'Email' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Reset Password' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Back to Login' })).toBeInTheDocument();
  });

  it('handles successful password reset request', async () => {
    jest.mocked(sendPasswordResetEmail).mockResolvedValueOnce();
    renderComponent();

    const emailInput = screen.getByRole('textbox', { name: 'Email' });
    const submitButton = screen.getByRole('button', { name: 'Reset Password' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    expect(submitButton).toBeDisabled();
    expect(screen.getByText('Loading...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Password reset email sent')).toBeInTheDocument();
    });

    expect(sendPasswordResetEmail).toHaveBeenCalledWith(expect.anything(), '<EMAIL>');

    // Wait for navigation after success
    await waitFor(
      () => {
        expect(mockNavigate).toHaveBeenCalledWith('/login');
      },
      { timeout: 3500 }
    );
  });

  it('handles password reset error', async () => {
    jest.mocked(sendPasswordResetEmail).mockRejectedValueOnce(new Error('Failed to reset'));
    renderComponent();

    const emailInput = screen.getByRole('textbox', { name: 'Email' });
    const submitButton = screen.getByRole('button', { name: 'Reset Password' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to reset password')).toBeInTheDocument();
    });

    expect(submitButton).not.toBeDisabled();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('navigates to login page when back button is clicked', () => {
    renderComponent();

    const backButton = screen.getByRole('button', { name: 'Back to Login' });
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });

  it('disables form elements during submission', async () => {
    jest.mocked(sendPasswordResetEmail).mockImplementationOnce(
      () =>
        new Promise(() => {
          /* Pending promise */
        })
    );
    renderComponent();

    const emailInput = screen.getByRole('textbox', { name: 'Email' });
    const submitButton = screen.getByRole('button', { name: 'Reset Password' });
    const backButton = screen.getByRole('button', { name: 'Back to Login' });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    expect(emailInput).toBeDisabled();
    expect(submitButton).toBeDisabled();
    expect(backButton).toBeDisabled();
  });
});
