import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import EmailVerification from '../EmailVerification';
import { useAuth } from '../../../context/AuthContext';
import * as authService from '../../../services/authService';

jest.mock('../../../context/AuthContext');
jest.mock('../../../services/authService');

const mockUseAuth = useAuth as jest.Mock;
const mockVerifyEmail = jest.spyOn(authService, 'verifyEmail');
const mockNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('EmailVerification Component', () => {
  const mockUser = {
    email: '<EMAIL>',
    emailVerified: false,
  };

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: mockUser,
      isLoading: false,
    });
    mockVerifyEmail.mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders email verification page correctly', () => {
    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    expect(screen.getByText(/verify your email/i)).toBeInTheDocument();
    expect(screen.getByText(mockUser.email)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /i've verified my email/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /resend email/i })).toBeInTheDocument();
  });

  it('redirects to login if user is not logged in', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
    });

    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    expect(mockNavigate).toHaveBeenCalledWith('/login', { replace: true });
  });

  it('redirects to dashboard if email is already verified', () => {
    mockUseAuth.mockReturnValue({
      user: { ...mockUser, emailVerified: true },
      isLoading: false,
    });

    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
  });

  it('shows loading state when checking auth status', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
    });

    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('handles resend verification email successfully', async () => {
    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    fireEvent.click(screen.getByRole('button', { name: /resend email/i }));

    expect(mockVerifyEmail).toHaveBeenCalledWith(mockUser);
    expect(await screen.findByText(/verification email sent/i)).toBeInTheDocument();
  });

  it('handles resend verification email error', async () => {
    const errorMessage = 'Failed to send verification email';
    mockVerifyEmail.mockRejectedValueOnce(new Error(errorMessage));

    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    fireEvent.click(screen.getByRole('button', { name: /resend email/i }));

    expect(await screen.findByText(errorMessage)).toBeInTheDocument();
  });

  it('refreshes page when verify button is clicked', () => {
    const mockReload = jest.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: mockReload },
      writable: true,
    });

    render(
      <BrowserRouter>
        <EmailVerification />
      </BrowserRouter>
    );

    fireEvent.click(screen.getByRole('button', { name: /i've verified my email/i }));

    expect(mockReload).toHaveBeenCalled();
  });
});
