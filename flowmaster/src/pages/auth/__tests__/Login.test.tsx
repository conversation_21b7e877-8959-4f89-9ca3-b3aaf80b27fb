import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18next from 'i18next';
import Login from '../Login';
import enErrors from '../../../../public/locales/en/errors.json';
import slErrors from '../../../../public/locales/sl/errors.json';
import enAuth from '../../../../public/locales/en/auth.json';
import slAuth from '../../../../public/locales/sl/auth.json';
import { AuthContext } from '../../../context/AuthContext';

const mockNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const defaultAuthContext = {
  user: null,
  userProfile: null,
  isLoading: false,
  isAuthenticated: false,
  signIn: jest.fn(),
  signOut: jest.fn(),
  signUp: jest.fn(),
  verifyEmail: jest.fn(),
  resetPassword: jest.fn(),
  error: null,
};

const renderWithProviders = (ui: React.ReactElement, authContext = {}, language = 'en') => {
  i18next.init({
    lng: language,
    fallbackLng: language,
    ns: ['auth', 'errors', 'validation'],
    defaultNS: 'auth',
    interpolation: {
      escapeValue: false,
    },
    resources: {
      en: {
        errors: enErrors,
        auth: enAuth,
        validation: enAuth.login.errors,
      },
      sl: {
        errors: slErrors,
        auth: slAuth,
        validation: slAuth.login.errors,
      },
    },
  });
  return render(
    <I18nextProvider i18n={i18next}>
      <BrowserRouter>
        <AuthContext.Provider value={{ ...defaultAuthContext, ...authContext }}>
          {ui}
        </AuthContext.Provider>
      </BrowserRouter>
    </I18nextProvider>
  );
};

describe('Login', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form', () => {
    renderWithProviders(<Login />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('handles form submission with remember me', async () => {
    const mockSignIn = jest.fn().mockResolvedValue(undefined);
    renderWithProviders(<Login />, { signIn: mockSignIn });

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const rememberMeCheckbox = screen.getByLabelText(/remember me/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(rememberMeCheckbox);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123', true);
    });
  });

  it('handles form submission without remember me', async () => {
    const mockSignIn = jest.fn().mockResolvedValue(undefined);
    renderWithProviders(<Login />, { signIn: mockSignIn });

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123', false);
    });
  });

  it('displays validation errors for empty fields in English', async () => {
    renderWithProviders(<Login />, {}, 'en');

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(enAuth.login.errors.default)).toBeInTheDocument();
      expect(screen.getByText(enAuth.login.errors.default)).toBeInTheDocument();
    });
  });

  it('displays validation errors for empty fields in Slovenian', async () => {
    renderWithProviders(<Login />, {}, 'sl');

    const submitButton = screen.getByRole('button', { name: /prijava/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(slAuth.login.errors.default)).toBeInTheDocument();
      expect(screen.getByText(slAuth.login.errors.default)).toBeInTheDocument();
    });
  });

  it('displays error message when login fails in English', async () => {
    renderWithProviders(<Login />, { error: enAuth.login.errors.wrongPassword }, 'en');
    await waitFor(() => {
      const errorElement = screen.getByText(enAuth.login.errors.wrongPassword);
      expect(errorElement).toBeInTheDocument();
    });
  });

  it('displays error message when login fails in Slovenian', async () => {
    renderWithProviders(<Login />, { error: slAuth.login.errors.wrongPassword }, 'sl');
    await waitFor(() => {
      const errorElement = screen.getByText(slAuth.login.errors.wrongPassword);
      expect(errorElement).toBeInTheDocument();
    });
  });

  it('redirects to dashboard when already authenticated', async () => {
    renderWithProviders(<Login />, { isAuthenticated: true });
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('shows loading state when authentication is in progress', () => {
    renderWithProviders(<Login />, { isLoading: true });
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('validates email format in English', async () => {
    renderWithProviders(<Login />, {}, 'en');

    const emailInput = screen.getByLabelText(/email/i);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.blur(emailInput);

    await waitFor(() => {
      expect(screen.getByText(enAuth.login.errors.invalidEmail)).toBeInTheDocument();
    });
  });

  it('validates email format in Slovenian', async () => {
    renderWithProviders(<Login />, {}, 'sl');

    const emailInput = screen.getByLabelText(/e-poštni naslov/i);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.blur(emailInput);

    await waitFor(() => {
      expect(screen.getByText(slAuth.login.errors.invalidEmail)).toBeInTheDocument();
    });
  });

  it('validates password length', async () => {
    renderWithProviders(<Login />);

    const passwordInput = screen.getByLabelText(/password/i);
    fireEvent.change(passwordInput, { target: { value: '123' } });
    fireEvent.blur(passwordInput);

    await waitFor(() => {
      expect(screen.getByText(enAuth.login.errors.passwordRequirements)).toBeInTheDocument();
    });
  });

  it('navigates to signup page', async () => {
    renderWithProviders(<Login />);

    const signupLink = screen.getByRole('link', {
      name: new RegExp(`${enAuth.login.noAccount}.*${enAuth.login.signUp}`),
    });
    fireEvent.click(signupLink);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/auth/register');
    });
  });

  it('navigates to forgot password page', async () => {
    renderWithProviders(<Login />);

    const forgotPasswordLink = screen.getByRole('link', {
      name: new RegExp(enAuth.login.forgotPassword),
    });
    fireEvent.click(forgotPasswordLink);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/auth/reset-password');
    });
  });
});
