import { useEffect, useState } from 'react';
import { Box, Button, CircularProgress, Typography, Alert } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AuthLayout from '../../components/auth/AuthLayout';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { useAuth } from '../../context/AuthContext';
import * as authService from '../../services/authService';

const EmailVerification = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // If user is not logged in, redirect to login
    if (!isLoading && !user) {
      navigate('/login', { replace: true });
    }
    // If user is verified, redirect to dashboard
    if (user?.emailVerified) {
      navigate('/dashboard', { replace: true });
    }
  }, [user, isLoading, navigate]);

  const handleResendVerification = async () => {
    setError(null);
    setSuccess(false);

    try {
      if (user) {
        await authService.verifyEmail(user);
        setSuccess(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (isLoading) {
    return (
      <AuthLayout
        title="Verify Your Email"
        subtitle="Please check your email and click the verification link"
      >
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <CircularProgress />
        </Box>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Verify Your Email"
      subtitle="Please check your email and click the verification link"
    >
      <Box sx={{ textAlign: 'center', mt: 2 }}>
        <ErrorAlert error={error} />

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Verification email sent! Check your inbox.
          </Alert>
        )}

        <Typography variant="body1" sx={{ mb: 3 }}>
          We sent a verification email to: <strong>{user?.email}</strong>
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          <Button variant="contained" onClick={handleRefresh} disabled={isLoading}>
            I&apos;ve Verified My Email
          </Button>

          <Button variant="outlined" onClick={handleResendVerification} disabled={isLoading}>
            {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Resend Email'}
          </Button>
        </Box>
      </Box>
    </AuthLayout>
  );
};

export default EmailVerification;
