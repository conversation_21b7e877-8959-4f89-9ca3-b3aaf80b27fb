import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import { getProgram, getProgramSessions } from '../../services/programService';
import { Program, ProgramSession } from '../../types/program';
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Archive as ArchiveIcon,
  Event as EventIcon,
  School as SchoolIcon,
  EmojiEvents as CampIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import ProgramOverview from '../../components/programs/ProgramOverview';
import ProgramSessions from '../../components/programs/ProgramSessions';
import ProgramParticipants from '../../components/programs/ProgramParticipants';
import EnhancedProgramAttendance from '../../components/programs/EnhancedProgramAttendance';
import ProgramProgress from '../../components/programs/ProgramProgress';
import ProgramReports from '../../components/programs/ProgramReports';
import ProgramLessons from '../../components/programs/ProgramLessons';
import ProgramPayments from '../../components/programs/ProgramPayments';
import ProgramMakeupSessions from '../../components/programs/ProgramMakeupSessions';
import ProgramStudentAssignment from '../../components/programs/ProgramStudentAssignment';
import ProgramScheduleChange from '../../components/programs/ProgramScheduleChange';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`program-tabpanel-${index}`}
      aria-labelledby={`program-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3, px: 3 }}>{children}</Box>}
    </div>
  );
};

const a11yProps = (index: number) => {
  return {
    id: `program-tab-${index}`,
    'aria-controls': `program-tabpanel-${index}`,
  };
};

const ProgramDetail: React.FC = () => {
  const { t } = useTranslation('programs');
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentSchool } = useSchool();

  const [program, setProgram] = useState<Program | null>(null);
  const [sessions, setSessions] = useState<ProgramSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    const fetchProgramData = async () => {
      if (!currentSchool?.id || !id) return;

      try {
        setLoading(true);

        // Fetch program details
        const programData = await getProgram(currentSchool.id, id);
        if (!programData) {
          setError(t('messages.programNotFound', 'Program not found'));
          setLoading(false);
          return;
        }

        setProgram(programData);

        // Fetch program sessions
        const sessionsData = await getProgramSessions(currentSchool.id, id);
        setSessions(sessionsData);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching program data:', err);
        setError(t('messages.fetchError', 'Failed to fetch program data'));
        setLoading(false);
      }
    };

    fetchProgramData();
  }, [currentSchool?.id, id, t]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleBack = () => {
    navigate('/programs');
  };

  const handleEdit = () => {
    navigate(`/programs/${id}/edit`);
  };

  const getProgramTypeIcon = (type: string) => {
    switch (type) {
      case 'yearly':
        return <SchoolIcon />;
      case 'seasonal':
        return <EventIcon />;
      case 'camp':
        return <CampIcon />;
      default:
        return <EventIcon />;
    }
  };

  const getProgramTypeColor = (type: string): 'primary' | 'success' | 'warning' | 'default' => {
    switch (type) {
      case 'yearly':
        return 'primary';
      case 'seasonal':
        return 'success';
      case 'camp':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getProgramStatusColor = (status: string): 'success' | 'info' | 'default' => {
    switch (status) {
      case 'active':
        return 'success';
      case 'completed':
        return 'info';
      case 'archived':
        return 'default';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !program) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Button startIcon={<ArrowBackIcon />} onClick={handleBack} sx={{ mb: 2 }}>
          {t('actions.back', 'Back to Programs')}
        </Button>

        <Alert severity="error" sx={{ my: 2 }}>
          {error || t('messages.programNotFound', 'Program not found')}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box display="flex" alignItems="center" mb={2}>
        <Button startIcon={<ArrowBackIcon />} onClick={handleBack} sx={{ mr: 2 }}>
          {t('actions.back', 'Back')}
        </Button>

        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          {program.name}
        </Typography>

        <Box display="flex" gap={1}>
          <Button variant="outlined" startIcon={<EditIcon />} onClick={handleEdit}>
            {t('actions.edit', 'Edit')}
          </Button>

          {program.status !== 'archived' && (
            <Button variant="outlined" color="warning" startIcon={<ArchiveIcon />}>
              {t('actions.archive', 'Archive')}
            </Button>
          )}

          <Button variant="outlined" color="error" startIcon={<DeleteIcon />}>
            {t('actions.delete', 'Delete')}
          </Button>
        </Box>
      </Box>

      <Box display="flex" alignItems="center" mb={3} gap={2}>
        <Chip
          icon={getProgramTypeIcon(program.type)}
          label={t(`programTypes.${program.type}`, program.type)}
          color={getProgramTypeColor(program.type)}
        />

        <Chip
          label={t(`status.${program.status}`, program.status)}
          color={getProgramStatusColor(program.status)}
          variant="outlined"
        />

        <Typography variant="body2" color="text.secondary">
          {format(program.schedule.startDate.toDate(), 'MMM d, yyyy')} -{' '}
          {format(program.schedule.endDate.toDate(), 'MMM d, yyyy')}
        </Typography>
      </Box>

      <Paper sx={{ width: '100%', mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label={t('tabs.overview', 'Overview')} {...a11yProps(0)} />
          <Tab label={t('tabs.sessions', 'Sessions')} {...a11yProps(1)} />
          <Tab label={t('tabs.participants', 'Participants')} {...a11yProps(2)} />
          <Tab label={t('tabs.attendance', 'Attendance')} {...a11yProps(3)} />
          <Tab label={t('tabs.progress', 'Progress')} {...a11yProps(4)} />
          <Tab label={t('tabs.lessons', 'Lessons')} {...a11yProps(5)} />
          <Tab label={t('tabs.makeups', 'Makeups')} {...a11yProps(6)} />
          <Tab label={t('tabs.payments', 'Payments')} {...a11yProps(7)} />
          <Tab label={t('tabs.reports', 'Reports')} {...a11yProps(8)} />
          <Tab label={t('tabs.assignment', 'Assignment')} {...a11yProps(9)} />
          <Tab label={t('tabs.schedule', 'Schedule')} {...a11yProps(10)} />
        </Tabs>

        <Divider />

        <TabPanel value={tabValue} index={0}>
          <ProgramOverview program={program} />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <ProgramSessions program={program} sessions={sessions} />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <ProgramParticipants program={program} />
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <EnhancedProgramAttendance
            program={program}
            sessions={sessions}
            onSessionUpdate={() => {
              // Refresh sessions data when attendance is updated
              if (currentSchool?.id && id) {
                getProgramSessions(currentSchool.id, id).then((sessionsData) => {
                  setSessions(sessionsData);
                });
              }
            }}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <ProgramProgress program={program} />
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          <ProgramLessons program={program} sessions={sessions} />
        </TabPanel>

        <TabPanel value={tabValue} index={6}>
          <ProgramMakeupSessions program={program} sessions={sessions} />
        </TabPanel>

        <TabPanel value={tabValue} index={7}>
          <ProgramPayments program={program} />
        </TabPanel>

        <TabPanel value={tabValue} index={8}>
          <ProgramReports program={program} sessions={sessions} />
        </TabPanel>

        <TabPanel value={tabValue} index={9}>
          <ProgramStudentAssignment program={program} onUpdate={() => window.location.reload()} />
        </TabPanel>

        <TabPanel value={tabValue} index={10}>
          <ProgramScheduleChange
            program={program}
            sessions={sessions}
            onUpdate={() => window.location.reload()}
          />
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default ProgramDetail;
