import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import { Container, Typography, Box, CircularProgress, Alert } from '@mui/material';
import ProgramForm from '../../components/programs/ProgramForm';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../services/firebase';

const ProgramCreate: React.FC = () => {
  const { t } = useTranslation('programs');
  const navigate = useNavigate();
  const { currentSchool } = useSchool();

  const [students, setStudents] = useState<Student[]>([]);
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch students
        const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
        const studentsQuery = query(studentsRef, where('status', '==', 'active'));
        const studentsSnapshot = await getDocs(studentsQuery);
        const studentsData = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Student[];

        // Fetch instructors
        const instructorsRef = collection(db, 'schools', currentSchool.id, 'instructors');
        const instructorsQuery = query(instructorsRef, where('status', '==', 'active'));
        const instructorsSnapshot = await getDocs(instructorsQuery);
        const instructorsData = instructorsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Instructor[];

        setStudents(studentsData);
        setInstructors(instructorsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id, t]);

  const handleSuccess = (programId: string) => {
    navigate(`/programs/${programId}`);
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ my: 2 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {t('actions.create', 'Create Program')}
      </Typography>

      <ProgramForm students={students} instructors={instructors} onSuccess={handleSuccess} />
    </Container>
  );
};

export default ProgramCreate;
