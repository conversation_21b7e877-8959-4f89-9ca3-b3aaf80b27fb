import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Container, Paper, Tabs, Tab } from '@mui/material';
import { School } from '@mui/icons-material';
import ProgramList from '../../components/programs/ProgramList';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`program-tabpanel-${index}`}
      aria-labelledby={`program-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3, px: 3 }}>{children}</Box>}
    </div>
  );
};

const a11yProps = (index: number) => {
  return {
    id: `program-tab-${index}`,
    'aria-controls': `program-tabpanel-${index}`,
  };
};

const Programs: React.FC = () => {
  const { t } = useTranslation('programs');
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box display="flex" alignItems="center" mb={3}>
        <School sx={{ fontSize: '2rem', mr: 1 }} />
        <Typography variant="h4" component="h1">
          {t('title', 'Programs')}
        </Typography>
      </Box>

      <Paper sx={{ width: '100%', mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label={t('programTypes.yearly', 'Yearly Program')} {...a11yProps(0)} />
          <Tab label={t('programTypes.seasonal', 'Seasonal Program')} {...a11yProps(1)} />
          <Tab label={t('programTypes.camp', 'Camp')} {...a11yProps(2)} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <ProgramList />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <ProgramList />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <ProgramList />
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default Programs;
