import { render, screen } from '@testing-library/react';
import Programs from '../Programs';

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (_key: string, fallback: string) => fallback,
  }),
}));

describe('Programs Component', () => {
  beforeEach(() => {
    render(<Programs />);
  });

  it('renders the school programs section', () => {
    // Check section title
    expect(screen.getByText('School Programs')).toBeInTheDocument();
    expect(screen.getByText('Programs for schools and organized groups')).toBeInTheDocument();

    // Check program cards
    expect(screen.getByText('Group Programs')).toBeInTheDocument();
    expect(screen.getByText('Group programs for schools and organized groups')).toBeInTheDocument();
    expect(screen.getByText('Individual Programs')).toBeInTheDocument();
    expect(screen.getByText('Individual programs for students')).toBeInTheDocument();
  });

  it('renders the courses section', () => {
    // Check section title
    expect(screen.getByText('Courses')).toBeInTheDocument();
    expect(screen.getByText('Regular courses for different age groups')).toBeInTheDocument();

    // Check course cards
    expect(screen.getByText('Weekly Courses')).toBeInTheDocument();
    expect(screen.getByText('Weekly courses for different age groups')).toBeInTheDocument();
    expect(screen.getByText('Daily Courses')).toBeInTheDocument();
    expect(screen.getByText('Daily courses for different age groups')).toBeInTheDocument();
  });

  it('renders all icons', () => {
    // Check for the presence of specific icons
    expect(screen.getByTestId('SchoolIcon')).toBeInTheDocument();
    expect(screen.getAllByTestId('PersonIcon')).toHaveLength(2);
    expect(screen.getAllByTestId('CalendarMonthIcon')).toHaveLength(2);
    expect(screen.getByTestId('CalendarTodayIcon')).toBeInTheDocument();
  });

  it('renders all cards with proper structure', () => {
    const cards = screen.getAllByRole('button'); // CardActionArea makes cards clickable
    expect(cards).toHaveLength(4); // 2 program cards + 2 course cards

    cards.forEach((card) => {
      expect(card).toBeInTheDocument();
      expect(card).toHaveStyle({ cursor: 'pointer' }); // Verify cards are clickable
    });
  });
});
