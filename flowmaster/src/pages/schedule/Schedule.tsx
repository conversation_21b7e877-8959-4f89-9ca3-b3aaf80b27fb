import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Tabs, Tab, CircularProgress, Paper } from '@mui/material';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import DailyTimelineView from '../../components/scheduling/DailyTimelineView';
import WeeklyTimelineView from '../../components/scheduling/WeeklyTimelineView';
import MonthlyCalendarView from '../../components/scheduling/MonthlyCalendarView';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`schedule-tabpanel-${index}`}
      aria-labelledby={`schedule-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `schedule-tab-${index}`,
    'aria-controls': `schedule-tabpanel-${index}`,
  };
}

const Schedule: React.FC = () => {
  const { t } = useTranslation();
  const [value, setValue] = React.useState(0);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchScheduleData = async () => {
      try {
        // TODO: Implement API call to fetch schedule data
        // For now, just simulating loading
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (err) {
        setError(t('schedule.error.fetch', 'Failed to fetch schedule data'));
      } finally {
        setLoading(false);
      }
    };

    fetchScheduleData();
  }, [t]);

  const handleChange = (_: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        {t('schedule.title', 'Schedule')}
      </Typography>

      {error && <ErrorAlert error={error} />}

      <Box
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'center',
          mb: 3,
        }}
      >
        <Tabs value={value} onChange={handleChange} aria-label="schedule views" centered>
          <Tab label={t('schedule.tabs.daily', 'Daily Schedule')} {...a11yProps(0)} />
          <Tab label={t('schedule.tabs.weekly', 'Weekly Overview')} {...a11yProps(1)} />
          <Tab label={t('schedule.tabs.monthly', 'Monthly Overview')} {...a11yProps(2)} />
        </Tabs>
      </Box>

      <Paper sx={{ width: '100%' }}>
        <TabPanel value={value} index={0}>
          <DailyTimelineView
            onLessonClick={(lesson) => {
              // TODO: Implement lesson click handler
              console.log('Lesson clicked:', lesson);
            }}
          />
        </TabPanel>

        <TabPanel value={value} index={1}>
          <WeeklyTimelineView
            onLessonClick={(lesson) => {
              // TODO: Implement lesson click handler
              console.log('Lesson clicked:', lesson);
            }}
          />
        </TabPanel>

        <TabPanel value={value} index={2}>
          <MonthlyCalendarView
            onLessonClick={(lesson) => {
              // TODO: Implement lesson click handler
              console.log('Lesson clicked:', lesson);
            }}
          />
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default Schedule;
