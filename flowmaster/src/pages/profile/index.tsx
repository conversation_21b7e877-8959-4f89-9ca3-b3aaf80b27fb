import React from 'react';
import Profile from './Profile';

/**
 * ProfilePage serves as the main entry point for the profile management section.
 * It wraps the Profile component with the consistent page-level structure.
 */
const ProfilePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <Profile />
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
