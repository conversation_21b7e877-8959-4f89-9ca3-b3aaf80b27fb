import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useAuth } from '../../../hooks/useAuth';
import { usePermission } from '../../../hooks/usePermission';
import { getDoc, updateDoc } from 'firebase/firestore';
import Profile from '../Profile';

// Mock the required hooks and Firebase functions
jest.mock('../../../hooks/useAuth');
jest.mock('../../../hooks/usePermission');
jest.mock('firebase/firestore');
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'profile:title': 'Profile',
        'profile:fields.displayName': 'Display Name',
        'profile:fields.phoneNumber': 'Phone Number',
        'profile:fields.bio': 'Bio',
        'profile:role': 'Role',
        'profile:updateSuccess': 'Profile updated successfully',
        'common:ui.email': 'Email',
        'common:ui.save': 'Save',
        'common:ui.saving': 'Saving',
        'errors:profile.fetch': 'Failed to fetch profile',
        'errors:profile.update': 'Failed to update profile',
      };
      return translations[key] || key;
    },
  }),
}));

describe('Profile Component', () => {
  const mockUser = {
    uid: 'test-uid',
    email: '<EMAIL>',
    photoURL: 'https://example.com/photo.jpg',
  };

  const mockRole = {
    name: 'Admin',
    description: 'Administrator role',
  };

  const mockProfileData = {
    displayName: 'Test User',
    bio: 'Test bio',
    phoneNumber: '1234567890',
  };

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock useAuth hook
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
    });

    // Mock usePermission hook
    (usePermission as jest.Mock).mockReturnValue({
      role: mockRole,
    });

    // Mock Firebase getDoc
    (getDoc as jest.Mock).mockResolvedValue({
      exists: () => true,
      data: () => mockProfileData,
    });

    // Mock Firebase updateDoc
    (updateDoc as jest.Mock).mockResolvedValue(undefined);
  });

  it('renders loading state initially', () => {
    render(<Profile />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('loads and displays user profile data', async () => {
    render(<Profile />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByRole('textbox', { name: 'Display Name' })).toHaveValue('Test User');
    expect(screen.getByRole('textbox', { name: 'Email' })).toHaveValue('<EMAIL>');
    expect(screen.getByRole('textbox', { name: 'Phone Number' })).toHaveValue('1234567890');
    expect(screen.getByRole('textbox', { name: 'Bio' })).toHaveValue('Test bio');
  });

  it('displays role information', async () => {
    render(<Profile />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByText(/Admin/)).toBeInTheDocument();
    expect(screen.getByText(/Administrator role/)).toBeInTheDocument();
  });

  it('handles form submission successfully', async () => {
    render(<Profile />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Update form fields
    fireEvent.change(screen.getByRole('textbox', { name: 'Display Name' }), {
      target: { value: 'Updated Name' },
    });

    fireEvent.change(screen.getByRole('textbox', { name: 'Phone Number' }), {
      target: { value: '0987654321' },
    });

    fireEvent.change(screen.getByRole('textbox', { name: 'Bio' }), {
      target: { value: 'Updated bio' },
    });

    // Submit form
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    // Verify updateDoc was called with correct data
    await waitFor(() => {
      expect(updateDoc).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          displayName: 'Updated Name',
          phoneNumber: '0987654321',
          bio: 'Updated bio',
        })
      );
    });

    // Check success message
    await waitFor(() => {
      expect(screen.getByText('Profile updated successfully')).toBeInTheDocument();
    });
  });

  it('handles profile fetch error', async () => {
    (getDoc as jest.Mock).mockRejectedValueOnce(new Error('Fetch error'));

    render(<Profile />);

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch profile')).toBeInTheDocument();
    });
  });

  it('handles profile update error', async () => {
    (updateDoc as jest.Mock).mockRejectedValueOnce(new Error('Update error'));

    render(<Profile />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Submit form
    fireEvent.click(screen.getByText('Save'));

    await waitFor(() => {
      expect(screen.getByText('Failed to update profile')).toBeInTheDocument();
    });
  });

  it('disables form fields while saving', async () => {
    (updateDoc as jest.Mock).mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );
    render(<Profile />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Submit form to trigger saving state
    fireEvent.click(screen.getByRole('button', { name: 'Save' }));

    // Verify fields are disabled during saving
    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Display Name' })).toBeDisabled();
      expect(screen.getByRole('textbox', { name: 'Phone Number' })).toBeDisabled();
      expect(screen.getByRole('textbox', { name: 'Bio' })).toBeDisabled();
      expect(screen.getByRole('button', { name: 'Saving' })).toBeDisabled();
    });
  });
});
