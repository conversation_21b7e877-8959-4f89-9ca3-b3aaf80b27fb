import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useTranslation } from 'react-i18next';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from '../../services/firebase';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Avatar,
  Alert,
  CircularProgress,
  Paper,
  IconButton,
  Badge,
} from '@mui/material';
import { usePermission } from '../../hooks/usePermission';
import PhotoCamera from '@mui/icons-material/PhotoCamera';

interface UserProfile {
  displayName: string;
  email: string;
  photoURL?: string;
  bio?: string;
  phoneNumber?: string;
}

/**
 * Profile component allows users to view and edit their profile information.
 * Features:
 * - Display and edit user information
 * - Upload profile picture
 * - Update contact information
 * - View role and permissions
 */
const Profile: React.FC = () => {
  const { t } = useTranslation(['profile', 'common']);
  const { user } = useAuth();
  const { role } = usePermission();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [profile, setProfile] = useState<UserProfile>({
    displayName: '',
    email: '',
  });
  const [imageLoading, setImageLoading] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user?.uid) return;

      try {
        const docRef = doc(db, 'users', user.uid);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          setProfile({
            ...(docSnap.data() as UserProfile),
            email: user.email || '',
          });
        }
      } catch (err) {
        setError(t('errors.fetch', 'Failed to fetch profile data'));
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user, t]);

  const handleImageUpload = useCallback(
    async (file: File) => {
      if (!user?.uid) return;
      setImageLoading(true);
      setError(null);

      try {
        const storageRef = ref(storage, `profile-images/${user.uid}`);
        await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(storageRef);

        const docRef = doc(db, 'users', user.uid);
        await updateDoc(docRef, {
          photoURL: downloadURL,
        });

        // Update local state
        setProfile((prev) => ({ ...prev, photoURL: downloadURL }));
        setSuccess(true);
      } catch (err) {
        setError(t('errors.imageUpload', 'Failed to upload profile image'));
      } finally {
        setImageLoading(false);
      }
    },
    [user, t]
  );

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        setError(t('errors.imageSizeLimit', 'Image size must be less than 5MB'));
        return;
      }
      handleImageUpload(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.uid) return;

    setSaving(true);
    setError(null);
    setSuccess(false);

    try {
      const docRef = doc(db, 'users', user.uid);
      await updateDoc(docRef, {
        displayName: profile.displayName,
        bio: profile.bio,
        phoneNumber: profile.phoneNumber,
      });
      setSuccess(true);
    } catch (err) {
      setError(t('profile:errors.update', 'Failed to update profile'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ mt: 4, p: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center">
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
              <IconButton
                color="primary"
                aria-label="upload picture"
                component="label"
                disabled={imageLoading}
                sx={{
                  bgcolor: 'white',
                  ':hover': { bgcolor: '#f5f5f5' },
                  width: 32,
                  height: 32,
                }}
              >
                <input hidden accept="image/*" type="file" onChange={handleFileChange} />
                {imageLoading ? <CircularProgress size={24} /> : <PhotoCamera />}
              </IconButton>
            }
          >
            <Avatar
              src={profile.photoURL || user?.photoURL || undefined}
              sx={{
                width: 100,
                height: 100,
                mb: 2,
                border: '4px solid white',
                boxShadow: '0 0 8px rgba(0,0,0,0.1)',
              }}
            />
          </Badge>
          <Typography component="h1" variant="h5" gutterBottom>
            {t('title', 'Profile')}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 2, width: '100%' }}>
              {t('actions.save', 'Profile updated successfully')}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%', mt: 2 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              label={t('personalInfo.firstName', 'First Name')}
              value={profile.displayName}
              onChange={(e) => setProfile({ ...profile, displayName: e.target.value })}
              disabled={saving}
            />
            <TextField
              margin="normal"
              fullWidth
              label={t('personalInfo.email', 'Email')}
              value={profile.email}
              disabled
            />
            <TextField
              margin="normal"
              fullWidth
              label={t('personalInfo.phone', 'Phone Number')}
              value={profile.phoneNumber || ''}
              onChange={(e) => setProfile({ ...profile, phoneNumber: e.target.value })}
              disabled={saving}
            />
            <TextField
              margin="normal"
              fullWidth
              multiline
              rows={4}
              label={t('personalInfo.bio', 'Bio')}
              value={profile.bio || ''}
              onChange={(e) => setProfile({ ...profile, bio: e.target.value })}
              disabled={saving}
            />

            <Box mt={3} p={2} bgcolor="background.paper" borderRadius={1}>
              <Typography variant="subtitle1" gutterBottom>
                {t('common:ui.role', 'Role')}
              </Typography>
              <Typography color="textSecondary">
                {role?.name ?? t('common:ui.loading', 'Loading...')} - {role?.description ?? ''}
              </Typography>
            </Box>

            <Button type="submit" fullWidth variant="contained" sx={{ mt: 3 }} disabled={saving}>
              {saving ? t('actions.save', 'Saving...') : t('actions.save', 'Save')}
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default Profile;
