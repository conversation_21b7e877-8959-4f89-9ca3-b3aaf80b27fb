import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Grid,
  CircularProgress,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import {
  Add as AddIcon,
  Event as EventIcon,
  School as SchoolIcon,
  Group as GroupIcon,
  ShoppingCart as ShoppingCartIcon,
} from '@mui/icons-material';
import AttendanceWidget from '../../components/dashboard/AttendanceWidget';
import AttendanceTrendsWidget from '../../components/dashboard/AttendanceTrendsWidget';
import EquipmentStatusWidget from '../../components/dashboard/EquipmentStatusWidget';
import OverdueRentalsWidget from '../../components/dashboard/OverdueRentalsWidget';
import QuickRentalButton from '../../components/dashboard/QuickRentalButton';
import { db } from '../../services/firebase';
import { doc, getDoc } from 'firebase/firestore';

interface SchoolData {
  name: string;
  // Add other school fields as needed
}

interface DashboardStats {
  totalPrograms: number;
  totalLessons: number;
  completedLessons: number;
  upcomingLessons: number;
  recentActivities: Array<{
    id: string;
    type: string;
    description: string;
    date: string;
  }>;
  lessonsTrend: Array<{
    date: string;
    lessons: number;
  }>;
}

const Dashboard: React.FC = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const { user } = useAuth();
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [schoolData, setSchoolData] = React.useState<SchoolData | null>(null);
  const [stats, setStats] = React.useState<DashboardStats>({
    totalPrograms: 0,
    totalLessons: 0,
    completedLessons: 0,
    upcomingLessons: 0,
    recentActivities: [],
    lessonsTrend: [],
  });

  React.useEffect(() => {
    const fetchSchoolData = async () => {
      if (!user?.schoolId) {
        setError(
          t('error.noSchool', 'No school associated with this account', { ns: 'dashboard' })
        );
        setLoading(false);
        return;
      }

      try {
        const schoolRef = doc(db, 'schools', user.schoolId);
        const schoolDoc = await getDoc(schoolRef);

        if (schoolDoc.exists()) {
          const data = schoolDoc.data() as SchoolData;
          setSchoolData(data);
        } else {
          setError(t('error.schoolNotFound', 'School data not found', { ns: 'dashboard' }));
        }
      } catch (err) {
        setError(t('error.fetch', 'Failed to fetch school data', { ns: 'dashboard' }));
      } finally {
        setLoading(false);
      }
    };

    fetchSchoolData();
    const fetchDashboardData = async () => {
      try {
        // TODO: Implement API call to fetch dashboard data
        // For now, using mock data
        setStats({
          totalPrograms: 5,
          totalLessons: 25,
          completedLessons: 15,
          upcomingLessons: 10,
          recentActivities: [
            {
              id: '1',
              type: 'lesson',
              description: 'New lesson scheduled with John Doe',
              date: '2024-01-15',
            },
            {
              id: '2',
              type: 'program',
              description: 'Winter Program 2024 created',
              date: '2024-01-14',
            },
            {
              id: '3',
              type: 'lesson',
              description: 'Lesson completed with Sarah Smith',
              date: '2024-01-13',
            },
          ],
          lessonsTrend: [
            { date: '2024-01-10', lessons: 4 },
            { date: '2024-01-11', lessons: 6 },
            { date: '2024-01-12', lessons: 5 },
            { date: '2024-01-13', lessons: 8 },
            { date: '2024-01-14', lessons: 7 },
            { date: '2024-01-15', lessons: 9 },
          ],
        });
      } catch (err) {
        setError(t('dashboard.error.fetch', 'Failed to fetch dashboard data'));
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [t, user]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        {t('welcomeSchool', 'Welcome to {{schoolName}}', {
          schoolName: schoolData?.name || t('unknownSchool', 'Unknown School', { ns: 'dashboard' }),
          ns: 'dashboard',
        })}
      </Typography>

      {error && <ErrorAlert error={error} />}

      <Grid container spacing={3} mt={2}>
        {/* Quick Actions */}
        <Grid item xs={12}>
          <Box display="flex" gap={2} mb={3}>
            <Button variant="contained" startIcon={<AddIcon />} color="primary">
              {t('actions.newLesson', 'New Lesson', { ns: 'dashboard' })}
            </Button>
            <Button variant="contained" startIcon={<AddIcon />} color="secondary">
              {t('actions.newProgram', 'New Program', { ns: 'dashboard' })}
            </Button>
            <Button variant="contained" startIcon={<GroupIcon />}>
              {t('actions.manageInstructors', 'Manage Instructors', { ns: 'dashboard' })}
            </Button>
            <QuickRentalButton />
          </Box>
        </Grid>

        {/* Stats Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('stats.totalPrograms', 'Total Programs', { ns: 'dashboard' })}
              </Typography>
              <Typography variant="h5">{stats.totalPrograms}</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('stats.totalLessons', 'Total Lessons', { ns: 'dashboard' })}
              </Typography>
              <Typography variant="h5">{stats.totalLessons}</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('stats.completedLessons', 'Completed Lessons', { ns: 'dashboard' })}
              </Typography>
              <Typography variant="h5">{stats.completedLessons}</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('stats.upcomingLessons', 'Upcoming Lessons', { ns: 'dashboard' })}
              </Typography>
              <Typography variant="h5">{stats.upcomingLessons}</Typography>
            </CardContent>
          </Card>
        </Grid>
        {/* Lessons Trend Chart */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('chart.lessonsTitle', 'Lessons Trend', { ns: 'dashboard' })}
              </Typography>
              <Box height={300}></Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('activities.title', 'Recent Activities', { ns: 'dashboard' })}
              </Typography>
              <List>
                {stats.recentActivities.map((activity) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemIcon>
                        {activity.type === 'lesson' ? <EventIcon /> : <SchoolIcon />}
                      </ListItemIcon>
                      <ListItemText primary={activity.description} secondary={activity.date} />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Attendance Overview Widget */}
        <Grid item xs={12} md={6}>
          <AttendanceWidget />
        </Grid>

        {/* Attendance Trends Widget */}
        <Grid item xs={12} md={6}>
          <AttendanceTrendsWidget />
        </Grid>

        {/* Equipment Status Widget */}
        <Grid item xs={12} md={6}>
          <EquipmentStatusWidget />
        </Grid>

        {/* Overdue Rentals Widget */}
        <Grid item xs={12} md={6}>
          <OverdueRentalsWidget />
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
