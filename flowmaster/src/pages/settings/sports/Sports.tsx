import React, { useState, useEffect, useMemo } from 'react';
import { Box, Typography, Chip, TextField, Button, IconButton } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { useFirestore } from '../../../hooks/useFirestore';
import { useAuth } from '../../../hooks/useAuth';
import { LoadingScreen } from '../../../components/shared/LoadingScreen';
import { useTranslation } from 'react-i18next';
interface Sport {
  id: string;
  name: string;
  isCustom: boolean;
}

interface Level {
  id: string;
  name: string;
  isCustom: boolean;
}

const Sports: React.FC = () => {
  const { t } = useTranslation('settings');
  const { user } = useAuth();
  const { getDocument, updateDocument } = useFirestore();
  const [loading, setLoading] = useState(true);
  const [selectedSports, setSelectedSports] = useState<Sport[]>([]);
  const [selectedLevels, setSelectedLevels] = useState<Level[]>([]);
  const [newCustomSport, setNewCustomSport] = useState('');
  const [newCustomLevel, setNewCustomLevel] = useState('');
  const [error, setError] = useState('');
  const [levelError, setLevelError] = useState('');

  const predefinedSports: Omit<Sport, 'id'>[] = [
    { name: t('sport.sports.skiing', 'Skiing'), isCustom: false },
    { name: t('sport.sports.snowboarding', 'Snowboarding'), isCustom: false },
    { name: t('sport.sports.mountainBiking', 'Mountain Biking'), isCustom: false },
    { name: t('sport.sports.surfing', 'Surfing'), isCustom: false },
    { name: t('sport.sports.windsurfing', 'Windsurfing'), isCustom: false },
    { name: t('sport.sports.kitesurfing', 'Kitesurfing'), isCustom: false },
    { name: t('sport.sports.rockClimbing', 'Rock Climbing'), isCustom: false },
    { name: t('sport.sports.kayaking', 'Kayaking'), isCustom: false },
    { name: t('sport.sports.hiking', 'Hiking'), isCustom: false },
  ];

  const predefinedLevels = useMemo<Omit<Level, 'id'>[]>(
    () => [
      { name: t('sport.levels.beginner', 'Beginner'), isCustom: false },
      { name: t('sport.levels.intermediate', 'Intermediate'), isCustom: false },
      { name: t('sport.levels.advanced', 'Advanced'), isCustom: false },
    ],
    [t]
  );

  useEffect(() => {
    const fetchData = async () => {
      if (!user?.schoolId) {
        // Initialize with predefined levels even when there's no school ID
        const defaultLevels = predefinedLevels.map((level) => ({
          id: Math.random().toString(36).substr(2, 9),
          ...level,
        }));
        setSelectedLevels(defaultLevels);
        setLoading(false);
        return;
      }

      try {
        const schoolDoc = await getDocument('schools', user.schoolId);
        if (schoolDoc?.sports) {
          setSelectedSports(schoolDoc.sports);
        }
        if (schoolDoc?.levels) {
          setSelectedLevels(schoolDoc.levels);
        } else {
          // Initialize with predefined levels if none exist
          const defaultLevels = predefinedLevels.map((level) => ({
            id: Math.random().toString(36).substr(2, 9),
            ...level,
          }));
          setSelectedLevels(defaultLevels);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
        // Set default levels even on error
        const defaultLevels = predefinedLevels.map((level) => ({
          id: Math.random().toString(36).substr(2, 9),
          ...level,
        }));
        setSelectedLevels(defaultLevels);
      } finally {
        setLoading(false);
      }
    };

    setLoading(true);
    fetchData();
  }, [user, getDocument, predefinedLevels]);

  const handleSportToggle = (sport: Omit<Sport, 'id'>) => {
    const sportExists = selectedSports.some((s) => s.name === sport.name);

    if (sportExists) {
      setSelectedSports(selectedSports.filter((s) => s.name !== sport.name));
    } else {
      const newSport: Sport = {
        id: Math.random().toString(36).substr(2, 9),
        ...sport,
      };
      setSelectedSports([...selectedSports, newSport]);
    }
  };

  const handleLevelToggle = (level: Omit<Level, 'id'>) => {
    const levelExists = selectedLevels.some((l) => l.name === level.name);

    if (levelExists) {
      setSelectedLevels(selectedLevels.filter((l) => l.name !== level.name));
    } else {
      const newLevel: Level = {
        id: Math.random().toString(36).substr(2, 9),
        ...level,
      };
      setSelectedLevels([...selectedLevels, newLevel]);
    }
  };

  const handleAddCustomSport = () => {
    if (!newCustomSport.trim()) return;

    const sportExists = selectedSports.some(
      (sport) => sport.name.toLowerCase() === newCustomSport.trim().toLowerCase()
    );

    if (sportExists) {
      setError(t('sport.errors.sportExists', 'Sport already exists'));
      return;
    }

    const newSport: Sport = {
      id: Math.random().toString(36).substr(2, 9),
      name: newCustomSport.trim(),
      isCustom: true,
    };

    setSelectedSports([...selectedSports, newSport]);
    setNewCustomSport('');
    setError('');
  };

  const handleAddCustomLevel = () => {
    if (!newCustomLevel.trim()) return;

    const levelExists = selectedLevels.some(
      (level) => level.name.toLowerCase() === newCustomLevel.trim().toLowerCase()
    );

    if (levelExists) {
      setLevelError(t('sport.errors.levelExists', 'Level already exists'));
      return;
    }

    const newLevel: Level = {
      id: Math.random().toString(36).substr(2, 9),
      name: newCustomLevel.trim(),
      isCustom: true,
    };

    setSelectedLevels([...selectedLevels, newLevel]);
    setNewCustomLevel('');
    setLevelError('');
  };

  const handleRemoveSport = (sportToRemove: Sport) => {
    setSelectedSports(selectedSports.filter((sport) => sport.id !== sportToRemove.id));
  };

  const handleRemoveLevel = (levelToRemove: Level) => {
    setSelectedLevels(selectedLevels.filter((level) => level.id !== levelToRemove.id));
  };

  const handleSave = async () => {
    if (!user?.schoolId) return;

    try {
      // Let's add console.log to see what we're saving
      console.log('Saving sports:', selectedSports);

      await updateDocument('schools', user.schoolId, {
        sports: selectedSports,
        levels: selectedLevels,
      });
    } catch (err) {
      console.error('Error saving data:', err);
      setError(t('sport.errors.saveFailed', 'Save failed'));
    }
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('sport.title')}
      </Typography>

      <Typography variant="body1" sx={{ mb: 3 }}>
        {t('sport.description')}
      </Typography>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {t('sport.availableSports')}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {predefinedSports.map((sport) => (
            <Chip
              key={sport.name}
              label={sport.name}
              onClick={() => handleSportToggle(sport)}
              color={selectedSports.some((s) => s.name === sport.name) ? 'primary' : 'default'}
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {t('sport.customSports', 'Custom Sports')}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <TextField
            value={newCustomSport}
            onChange={(e) => setNewCustomSport(e.target.value)}
            placeholder={t('sport.enterCustomSport', 'Enter custom sport')}
            error={!!error}
            helperText={error}
            size="small"
          />
          <IconButton
            onClick={handleAddCustomSport}
            color="primary"
            disabled={!newCustomSport.trim()}
          >
            <AddIcon />
          </IconButton>
        </Box>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {t('sport.selectedSports', 'Selected Sports')}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {selectedSports.map((sport) => (
            <Chip
              key={sport.id}
              label={sport.name}
              onDelete={() => handleRemoveSport(sport)}
              color="primary"
              deleteIcon={<DeleteIcon />}
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {t('sport.availableLevels', 'Available Levels')}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {predefinedLevels.map((level) => (
            <Chip
              key={level.name}
              label={level.name}
              onClick={() => handleLevelToggle(level)}
              color={selectedLevels.some((l) => l.name === level.name) ? 'primary' : 'default'}
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {t('sport.customLevels', 'Custom Levels')}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <TextField
            value={newCustomLevel}
            onChange={(e) => setNewCustomLevel(e.target.value)}
            placeholder={t('sport.enterCustomLevel', 'Enter custom level')}
            error={!!levelError}
            helperText={levelError}
            size="small"
          />
          <IconButton
            onClick={handleAddCustomLevel}
            color="primary"
            disabled={!newCustomLevel.trim()}
          >
            <AddIcon />
          </IconButton>
        </Box>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {t('sport.selectedLevels', 'Selected Levels')}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {selectedLevels.map((level) => (
            <Chip
              key={level.id}
              label={level.name}
              onDelete={() => handleRemoveLevel(level)}
              color="primary"
              deleteIcon={<DeleteIcon />}
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>
      </Box>

      <Button
        variant="contained"
        color="primary"
        onClick={handleSave}
        disabled={selectedSports.length === 0 || selectedLevels.length === 0}
      >
        {t('sport.saveChanges', 'Save changes')}
      </Button>
    </Box>
  );
};

export default Sports;
