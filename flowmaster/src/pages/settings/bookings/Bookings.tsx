import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Paper,
} from '@mui/material';
import { CalendarMonth, ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';

const BookingsSettings: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();
  const { user } = useAuth();

  const [loading] = useState(false);

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">
          {t('bookings.unauthorized', 'You must be logged in to access booking settings')}
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Button startIcon={<ArrowBack />} onClick={() => navigate('/settings')} sx={{ mb: 2 }}>
          {t('ui.back', 'Back', { ns: 'common' })}
        </Button>
        <Box display="flex" alignItems="center" mb={3}>
          <CalendarMonth sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('bookings.title', 'Booking Settings')}
          </Typography>
        </Box>
      </Box>

      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="body1" color="text.secondary">
              {t(
                'bookings.placeholder',
                'Booking settings and configurations will be available here soon.'
              )}
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default BookingsSettings;
