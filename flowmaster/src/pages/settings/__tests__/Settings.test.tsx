/* eslint-disable prettier/prettier */
import { render, screen, fireEvent } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Settings from '../Settings';

// Mock the required hooks
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

describe('Settings Component', () => {
  const mockTranslate = jest.fn((key: string) => {
    const translations: Record<string, string> = {
      // eslint-disable-next-line prettier/prettier
      'title': 'Settings',
      'subtitle': 'Manage your account and application settings',
      'sections.account': 'Account Settings',
      'sections.school': 'School Management',
      'sections.security': 'Security & Access',
      'sections.system': 'System Settings',
      'categories.profile': 'Profile',
      'categories.language': 'Language',
      'categories.notifications': 'Notifications',
      'categories.school': 'School',
      'categories.sport': 'Sport',
      'categories.booking': 'Booking',
      'categories.security': 'Security',
      'categories.users': 'Users',
      'categories.financial': 'Financial',
      'categories.system': 'System',
    };
    return translations[key] || key;
  });
  const mockNavigate = jest.fn();

  beforeEach(() => {
    (useTranslation as jest.Mock).mockReturnValue({
      t: mockTranslate,
    });
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the main settings title and subtitle', () => {
    render(<Settings />);

    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Manage your account and application settings')).toBeInTheDocument();
  });

  it('renders all setting sections with correct titles', () => {
    render(<Settings />);

    expect(screen.getByText('Account Settings')).toBeInTheDocument();
    expect(screen.getByText('School Management')).toBeInTheDocument();
    expect(screen.getByText('Security & Access')).toBeInTheDocument();
    expect(screen.getByText('System Settings')).toBeInTheDocument();
  });

  it('renders all setting categories with correct titles and descriptions', () => {
    render(<Settings />);

    // Account section categories
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Language')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();

    // School Management section categories
    expect(screen.getByText('School')).toBeInTheDocument();
    expect(screen.getByText('Sport')).toBeInTheDocument();
    expect(screen.getByText('Booking')).toBeInTheDocument();

    // Security & Access section categories
    expect(screen.getByText('Security')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();

    // System Settings section categories
    expect(screen.getByText('Financial')).toBeInTheDocument();
    expect(screen.getByText('System')).toBeInTheDocument();
  });

  it('navigates to the correct path when clicking on a category card', () => {
    render(<Settings />);

    // Test navigation for each category
    const testCases = [
      { title: 'Profile', path: '/settings/profile' },
      { title: 'Language', path: '/settings/language' },
      { title: 'Notifications', path: '/settings/notifications' },
      { title: 'School', path: '/settings/school' },
      { title: 'Sport', path: '/settings/sport' },
      { title: 'Booking', path: '/settings/booking' },
      { title: 'Security', path: '/settings/security' },
      { title: 'Users', path: '/settings/users' },
      { title: 'Financial', path: '/settings/financial' },
      { title: 'System', path: '/settings/system' },
    ];

    testCases.forEach(({ title, path }) => {
      const card = screen.getByText(title).closest('button');
      fireEvent.click(card!);
      expect(mockNavigate).toHaveBeenCalledWith(path);
    });
  });

  it('uses translation hook for all text content', () => {
    render(<Settings />);

    // Verify that the translation function was called for main title and subtitle
    expect(mockTranslate).toHaveBeenCalledWith('title', 'Settings');
    expect(mockTranslate).toHaveBeenCalledWith(
      'subtitle',
      'Manage your account and application settings'
    );

    // Verify translations for sections
    expect(mockTranslate).toHaveBeenCalledWith('sections.account', 'Account Settings');
    expect(mockTranslate).toHaveBeenCalledWith('sections.school', 'School Management');
    expect(mockTranslate).toHaveBeenCalledWith('sections.security', 'Security & Access');
    expect(mockTranslate).toHaveBeenCalledWith('sections.system', 'System Settings');

    // Verify translations for categories
    expect(mockTranslate).toHaveBeenCalledWith('categories.profile', 'Profile');
    expect(mockTranslate).toHaveBeenCalledWith('categories.language', 'Language');
    expect(mockTranslate).toHaveBeenCalledWith('categories.notifications', 'Notifications');
    expect(mockTranslate).toHaveBeenCalledWith('categories.school', 'School');
    expect(mockTranslate).toHaveBeenCalledWith('categories.sport', 'Sport');
    expect(mockTranslate).toHaveBeenCalledWith('categories.booking', 'Booking');
    expect(mockTranslate).toHaveBeenCalledWith('categories.security', 'Security');
    expect(mockTranslate).toHaveBeenCalledWith('categories.users', 'Users');
    expect(mockTranslate).toHaveBeenCalledWith('categories.financial', 'Financial');
    expect(mockTranslate).toHaveBeenCalledWith('categories.system', 'System');
  });
});
