import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { Language as LanguageIcon, ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface LanguageOption {
  code: string;
  name: string;
}

const LanguageSettings: React.FC = () => {
  const { t, i18n } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const languages: LanguageOption[] = [
    { code: 'en', name: t('languages.english', 'English') },
    { code: 'es', name: t('languages.spanish', 'Spanish') },
    { code: 'fr', name: t('languages.french', 'French') },
    { code: 'de', name: t('languages.german', 'German') },
  ];

  const handleLanguageChange = async (event: SelectChangeEvent) => {
    const langCode = event.target.value;
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await i18n.changeLanguage(langCode);
      setSelectedLanguage(langCode);
      setSuccess(t('language.updateSuccess', 'Language updated successfully'));
    } catch (err) {
      setError(t('language.updateError', 'Failed to update language'));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Button startIcon={<ArrowBack />} onClick={() => navigate('/settings')} sx={{ mb: 2 }}>
          {t('ui.back', 'Back', { ns: 'common' })}
        </Button>
        <Box display="flex" alignItems="center" mb={3}>
          <LanguageIcon sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('language.title', 'Language Settings')}
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel id="language-select-label">
              {t('language.select', 'Select Language')}
            </InputLabel>
            <Select
              labelId="language-select-label"
              value={selectedLanguage}
              onChange={handleLanguageChange}
              label={t('language.select', 'Select Language')}
            >
              {languages.map((lang) => (
                <MenuItem key={lang.code} value={lang.code}>
                  {lang.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Box mt={4}>
        <Typography variant="body1" color="text.secondary" paragraph>
          {t(
            'language.description',
            'Choose your preferred language. This will change the language across the entire application.'
          )}
        </Typography>
      </Box>
    </Container>
  );
};

export default LanguageSettings;
