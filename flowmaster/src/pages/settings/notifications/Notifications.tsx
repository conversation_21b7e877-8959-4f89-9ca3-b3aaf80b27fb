import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  CircularProgress,
  <PERSON>ert,
  FormControlLabel,
  Switch,
  Paper,
} from '@mui/material';
import { NotificationsActive, ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface NotificationSettings {
  email: {
    lessons: boolean;
    programs: boolean;
    announcements: boolean;
    reminders: boolean;
  };
  push: {
    lessons: boolean;
    programs: boolean;
    announcements: boolean;
    reminders: boolean;
  };
}

const NotificationsSettings: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [settings, setSettings] = useState<NotificationSettings>({
    email: {
      lessons: true,
      programs: true,
      announcements: true,
      reminders: true,
    },
    push: {
      lessons: true,
      programs: true,
      announcements: true,
      reminders: true,
    },
  });

  const handleToggleChange = (type: 'email' | 'push', setting: string) => {
    setSettings((prev) => ({
      ...prev,
      [type]: {
        ...prev[type],
        [setting]: !prev[type][setting as keyof typeof prev.email],
      },
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // TODO: Implement API call to save notification settings
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setSuccess(t('notifications.updateSuccess', 'Notification settings updated successfully'));
    } catch (err) {
      setError(t('notifications.updateError', 'Failed to update notification settings'));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Button startIcon={<ArrowBack />} onClick={() => navigate('/settings')} sx={{ mb: 2 }}>
          {t('ui.back', 'Back', { ns: 'common' })}
        </Button>
        <Box display="flex" alignItems="center" mb={3}>
          <NotificationsActive sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('notifications.title', 'Notification Settings')}
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('notifications.emailSettings', 'Email Notifications')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.email.lessons}
                    onChange={() => handleToggleChange('email', 'lessons')}
                  />
                }
                label={t('notifications.lessons', 'Lesson Updates')}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.email.programs}
                    onChange={() => handleToggleChange('email', 'programs')}
                  />
                }
                label={t('notifications.programs', 'Program Updates')}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.email.announcements}
                    onChange={() => handleToggleChange('email', 'announcements')}
                  />
                }
                label={t('notifications.announcements', 'Announcements')}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.email.reminders}
                    onChange={() => handleToggleChange('email', 'reminders')}
                  />
                }
                label={t('notifications.reminders', 'Reminders')}
              />
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('notifications.pushSettings', 'Push Notifications')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.push.lessons}
                    onChange={() => handleToggleChange('push', 'lessons')}
                  />
                }
                label={t('notifications.lessons', 'Lesson Updates')}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.push.programs}
                    onChange={() => handleToggleChange('push', 'programs')}
                  />
                }
                label={t('notifications.programs', 'Program Updates')}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.push.announcements}
                    onChange={() => handleToggleChange('push', 'announcements')}
                  />
                }
                label={t('notifications.announcements', 'Announcements')}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.push.reminders}
                    onChange={() => handleToggleChange('push', 'reminders')}
                  />
                }
                label={t('notifications.reminders', 'Reminders')}
              />
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end" mt={2}>
            <Button variant="contained" color="primary" onClick={handleSave} disabled={loading}>
              {t('ui.save', 'Save Changes', { ns: 'common' })}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
};

export default NotificationsSettings;
