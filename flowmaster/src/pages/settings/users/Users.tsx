import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Container, Paper, IconButton } from '@mui/material';
import { PeopleAlt as UsersIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import UserManagement from '../../../components/settings/users/UserManagement';

const Users: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/settings');
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={0} sx={{ p: 3, mt: 3 }}>
        <Box display="flex" alignItems="center" mb={3}>
          <IconButton
            onClick={handleBack}
            sx={{ mr: 2 }}
            aria-label={t('ui.back', 'Back', { ns: 'common' })}
          >
            <ArrowBackIcon />
          </IconButton>
          <UsersIcon sx={{ mr: 2 }} />
          <Typography variant="h5" component="h1">
            {t('users.title', 'Users Settings')}
          </Typography>
        </Box>
        <Typography variant="body1" paragraph>
          {t('users.description', 'Manage user accounts and permissions')}
        </Typography>

        <UserManagement />
      </Paper>
    </Container>
  );
};

export default Users;
