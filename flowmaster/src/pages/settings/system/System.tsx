import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Container, Typography, Button, Paper } from '@mui/material';
import { Settings as SystemIcon, ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const System: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Button startIcon={<ArrowBack />} onClick={() => navigate('/settings')} sx={{ mb: 2 }}>
          {t('ui.back', 'Back', { ns: 'common' })}
        </Button>
        <Box display="flex" alignItems="center" mb={3}>
          <SystemIcon sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('system.title', 'System Settings')}
          </Typography>
        </Box>
      </Box>

      <Paper sx={{ p: 3 }}>
        <Typography variant="body1">
          {t('system.description', 'Configure system settings and preferences')}
        </Typography>
      </Paper>
    </Container>
  );
};

export default System;
