import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Paper,
} from '@mui/material';
import { School as SchoolIcon, ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';

interface SchoolFormData {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  description: string;
}

const SchoolSettings: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState<SchoolFormData>({
    name: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    description: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  useEffect(() => {
    const fetchSchoolData = async () => {
      if (!user) return;
      setLoading(true);
      try {
        const schoolRef = doc(db, 'schools', user.uid);
        const schoolDoc = await getDoc(schoolRef);
        if (schoolDoc.exists()) {
          const data = schoolDoc.data() as SchoolFormData;
          setFormData(data);
        }
      } catch (err) {
        setError(t('school.fetchError', 'Failed to fetch school settings'));
      } finally {
        setLoading(false);
      }
    };
    fetchSchoolData();
  }, [user, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      setError(t('school.unauthorized', 'You must be logged in to update school settings'));
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const schoolRef = doc(db, 'schools', user.uid);
      await setDoc(schoolRef, formData);
      setSuccess(t('school.updateSuccess', 'School settings updated successfully'));
    } catch (err) {
      setError(t('school.updateError', 'Failed to update school settings'));
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">
          {t('school.unauthorized', 'You must be logged in to access school settings')}
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Button startIcon={<ArrowBack />} onClick={() => navigate('/settings')} sx={{ mb: 2 }}>
          {t('ui.back', 'Back', { ns: 'common' })}
        </Button>
        <Box display="flex" alignItems="center" mb={3}>
          <SchoolIcon sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('school.title', 'School Settings')}
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('school.name', 'School Name')}
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('school.address', 'Address')}
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                required
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('school.phone', 'Phone Number')}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('school.email', 'Email')}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('school.website', 'Website')}
                name="website"
                type="url"
                value={formData.website}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('school.description', 'Description')}
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                multiline
                rows={4}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                disabled={loading}
              >
                {t('ui.save', 'Save Changes', { ns: 'common' })}
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Container>
  );
};

export default SchoolSettings;
