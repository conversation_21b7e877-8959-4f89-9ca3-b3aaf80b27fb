import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Container, Typography, Grid, Card, CardContent, CardActionArea } from '@mui/material';
import {
  Person,
  Language,
  NotificationsActive,
  Security,
  School,
  SportsHandball,
  EventAvailable,
  AccountBalance,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

/**
 * Settings component serves as a landing page for managing different setting categories.
 * Features:
 * - Profile settings
 * - Language preferences
 * - Notification settings
 * - Security settings
 * - School settings
 * - Sport settings
 * - Booking settings
 * - Financial settings
 * - User management
 * - System settings
 */
interface SettingCategory {
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  color: string;
}

interface SettingSection {
  title: string;
  description: string;
  icon: React.ReactNode;
  categories: SettingCategory[];
}

const Settings: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();

  const sections: SettingSection[] = [
    {
      title: t('sections.account', 'Account Settings'),
      description: t('sections.accountDesc', 'Manage your personal account preferences'),
      icon: <Person sx={{ fontSize: '2rem' }} />,
      categories: [
        {
          title: t('categories.profile', 'Profile'),
          description: t(
            'descriptions.profile',
            'Manage your personal profile and account settings'
          ),
          icon: <Person sx={{ fontSize: 40 }} />,
          path: '/settings/profile',
          color: '#1976d2',
        },
        {
          title: t('categories.language', 'Language'),
          description: t('descriptions.language', 'Change language and localization preferences'),
          icon: <Language sx={{ fontSize: 40 }} />,
          path: '/settings/language',
          color: '#2e7d32',
        },
        {
          title: t('categories.notifications', 'Notifications'),
          description: t('descriptions.notifications', 'Configure your notification preferences'),
          icon: <NotificationsActive sx={{ fontSize: 40 }} />,
          path: '/settings/notifications',
          color: '#ed6c02',
        },
      ],
    },
    {
      title: t('sections.school', 'School Management'),
      description: t('sections.schoolDesc', 'Configure school operations and activities'),
      icon: <School sx={{ fontSize: '2rem' }} />,
      categories: [
        {
          title: t('categories.school', 'School'),
          description: t(
            'descriptions.school',
            'Configure your ski school settings and preferences'
          ),
          icon: <School sx={{ fontSize: 40 }} />,
          path: '/settings/school',
          color: '#d32f2f',
        },
        {
          title: t('categories.sport', 'Sport'),
          description: t('descriptions.sport', 'Manage available sports and disciplines'),
          icon: <SportsHandball sx={{ fontSize: 40 }} />,
          path: '/settings/sports',
          color: '#0288d1',
        },
        {
          title: t('categories.booking', 'Booking'),
          description: t('descriptions.booking', 'Configure booking rules and availability'),
          icon: <EventAvailable sx={{ fontSize: 40 }} />,
          path: '/settings/bookings',
          color: '#7b1fa2',
        },
      ],
    },
    {
      title: t('sections.security', 'Security & Access'),
      description: t('sections.securityDesc', 'Manage security and user access settings'),
      icon: <Security sx={{ fontSize: '2rem' }} />,
      categories: [
        {
          title: t('categories.security', 'Security'),
          description: t('descriptions.security', 'Manage security settings and authentication'),
          icon: <Security sx={{ fontSize: 40 }} />,
          path: '/settings/security',
          color: '#d32f2f',
        },
        {
          title: t('categories.users', 'Users'),
          description: t('descriptions.users', 'Manage user accounts and permissions'),
          icon: <Person sx={{ fontSize: 40 }} />,
          path: '/settings/users',
          color: '#0288d1',
        },
      ],
    },
    {
      title: t('sections.system', 'System Settings'),
      description: t('sections.systemDesc', 'Manage system-wide configurations'),
      icon: <SettingsIcon sx={{ fontSize: '2rem' }} />,
      categories: [
        {
          title: t('categories.financial', 'Financial'),
          description: t('descriptions.financial', 'Manage financial settings and payment options'),
          icon: <AccountBalance sx={{ fontSize: 40 }} />,
          path: '/settings/financial',
          color: '#1976d2',
        },
        {
          title: t('categories.system', 'System'),
          description: t('descriptions.system', 'Configure system-wide settings and preferences'),
          icon: <SettingsIcon sx={{ fontSize: 40 }} />,
          path: '/settings/system',
          color: '#ed6c02',
        },
      ],
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box display="flex" alignItems="center" mb={1}>
          <SettingsIcon sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('title', 'Settings')}
          </Typography>
        </Box>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {t('subtitle', 'Manage your account and application settings')}
        </Typography>
      </Box>
      {sections.map((section) => (
        <Box key={section.title} sx={{ mb: 4 }}>
          <Box display="flex" alignItems="center" mb={2}>
            {section.icon}
            <Box ml={1}>
              <Typography variant="h5" component="h2">
                {section.title}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                {section.description}
              </Typography>
            </Box>
          </Box>
          <Grid container spacing={3}>
            {section.categories.map((category) => (
              <Grid item xs={12} sm={6} md={4} key={category.path}>
                <Card>
                  <CardActionArea onClick={() => navigate(category.path)}>
                    <CardContent>
                      <Box sx={{ color: category.color, mb: 2 }}>{category.icon}</Box>
                      <Typography variant="h6" component="div" gutterBottom>
                        {category.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {category.description}
                      </Typography>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      ))}
    </Container>
  );
};

export default Settings;
