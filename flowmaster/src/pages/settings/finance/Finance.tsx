import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Container, Paper, Typography, IconButton } from '@mui/material';
import { AccountBalance as FinanceIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const Finance: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/settings');
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={0} sx={{ p: 3, mt: 3 }}>
        <Box display="flex" alignItems="center" mb={3}>
          <IconButton
            onClick={handleBack}
            size="large"
            sx={{ mr: 2 }}
            aria-label={t('ui.back', 'Back', { ns: 'common' })}
          >
            <ArrowBackIcon />
          </IconButton>
          <FinanceIcon sx={{ mr: 2 }} />
          <Typography variant="h5" component="h1">
            {t('finance.title', 'Finance Settings')}
          </Typography>
        </Box>
        <Typography variant="body1">
          {t('finance.description', 'Manage financial settings and payment options')}
        </Typography>
      </Paper>
    </Container>
  );
};

export default Finance;
