import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  CircularProgress,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  AssignmentReturn as ReturnIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Inventory as InventoryIcon,
  AttachMoney as MoneyIcon,
  Notes as NotesIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { getRentalRecord, returnRental } from '../../services/rentalService';
import { RentalRecord, RentalStatus, EquipmentCondition } from '../../types/equipment';
import { ErrorAlert } from '../../components/shared/ErrorAlert';

const RentalDetail: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // State
  const [rental, setRental] = useState<RentalRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [returnDialogOpen, setReturnDialogOpen] = useState(false);
  const [itemConditions, setItemConditions] = useState<Record<string, EquipmentCondition>>({});
  const [itemNotes, setItemNotes] = useState<Record<string, string>>({});
  const [returning, setReturning] = useState(false);
  const [success, setSuccess] = useState(false);

  // Fetch rental data
  useEffect(() => {
    const fetchRental = async () => {
      if (!currentSchool?.id || !id) return;

      setLoading(true);
      try {
        const rentalData = await getRentalRecord(currentSchool.id, id);
        setRental(rentalData);

        // Initialize item conditions
        const conditions: Record<string, EquipmentCondition> = {};
        const notes: Record<string, string> = {};
        rentalData.items.forEach((item) => {
          conditions[item.equipmentId] = item.condition;
          notes[item.equipmentId] = item.notes || '';
        });
        setItemConditions(conditions);
        setItemNotes(notes);

        setError(null);
      } catch (err) {
        console.error('Error fetching rental:', err);
        setError(t('messages.fetchError', 'Failed to fetch rental data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchRental();
  }, [currentSchool?.id, id, t]);

  // Handle return dialog
  const handleReturnClick = () => {
    setReturnDialogOpen(true);
  };

  const handleReturnCancel = () => {
    setReturnDialogOpen(false);
  };

  const handleConditionChange = (equipmentId: string, condition: EquipmentCondition) => {
    setItemConditions({
      ...itemConditions,
      [equipmentId]: condition,
    });
  };

  const handleNotesChange = (equipmentId: string, notes: string) => {
    setItemNotes({
      ...itemNotes,
      [equipmentId]: notes,
    });
  };

  const handleReturnConfirm = async () => {
    if (!rental || !currentSchool?.id) return;

    setReturning(true);
    try {
      // Prepare item updates
      const itemUpdates = rental.items.map((item) => ({
        equipmentId: item.equipmentId,
        condition: itemConditions[item.equipmentId],
        notes: itemNotes[item.equipmentId] || undefined,
      }));

      await returnRental(currentSchool.id, rental.id, new Date(), itemUpdates);

      // Update rental in state
      setRental({
        ...rental,
        returned: true,
        status: 'returned',
        returnDate: new Date() as any, // Type hack for Timestamp
      });

      setReturnDialogOpen(false);
      setSuccess(true);
    } catch (err) {
      console.error('Error returning rental:', err);
      setError(t('messages.returnError', 'Failed to return rental', { ns: 'common' }));
    } finally {
      setReturning(false);
    }
  };

  // Handle back
  const handleBack = () => {
    navigate('/equipment/rentals');
  };

  // Render status chip
  const renderStatusChip = (status: RentalStatus) => {
    switch (status) {
      case 'active':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('rental.status.active', 'Active')}
            color="primary"
            size="small"
          />
        );
      case 'returned':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('rental.status.returned', 'Returned')}
            color="success"
            size="small"
          />
        );
      case 'overdue':
        return (
          <Chip
            icon={<WarningIcon />}
            label={t('rental.status.overdue', 'Overdue')}
            color="error"
            size="small"
          />
        );
      default:
        return null;
    }
  };

  // Format date - handles different date formats
  const formatDate = (date: any) => {
    try {
      if (
        date &&
        typeof date === 'object' &&
        'toDate' in date &&
        typeof date.toDate === 'function'
      ) {
        // Firebase Timestamp
        return format(date.toDate(), 'PPP');
      } else if (date instanceof Date) {
        // JavaScript Date object
        return format(date, 'PPP');
      } else if (typeof date === 'string') {
        // ISO string or other date string
        return format(new Date(date), 'PPP');
      } else if (date && typeof date === 'number') {
        // Unix timestamp (milliseconds)
        return format(new Date(date), 'PPP');
      }
      return 'Invalid date';
    } catch (error) {
      console.error('Error formatting date:', error, date);
      return 'Invalid date';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button startIcon={<ArrowBackIcon />} onClick={handleBack} sx={{ mr: 2 }}>
          {t('actions.back', 'Back', { ns: 'common' })}
        </Button>
        <Typography variant="h4" component="h1">
          {t('rental.detail.title', 'Rental Details')}
        </Typography>
      </Box>

      {error && <ErrorAlert message={error} />}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {t('rental.detail.returnSuccess', 'Equipment returned successfully')}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : rental ? (
        <>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">{t('rental.detail.customer', 'Customer')}</Typography>
                </Box>
                <Typography variant="body1">{rental.customerName}</Typography>
                <Typography variant="body2" color="textSecondary">
                  {rental.customerType === 'student'
                    ? t('rental.form.studentType', 'Student')
                    : t('rental.form.guestType', 'Guest')}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">{t('rental.detail.dates', 'Dates')}</Typography>
                </Box>
                <Typography variant="body1">
                  {t('rental.detail.rentalDate', 'Rental Date')}: {formatDate(rental.date)}
                </Typography>
                <Typography variant="body1">
                  {t('rental.detail.dueDate', 'Due Date')}: {formatDate(rental.dueDate)}
                </Typography>
                {rental.returnDate && (
                  <Typography variant="body1">
                    {t('rental.detail.returnDate', 'Return Date')}: {formatDate(rental.returnDate)}
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <InventoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">{t('rental.detail.status', 'Status')}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {renderStatusChip(rental.status)}
                  {!rental.returned && (
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<ReturnIcon />}
                      onClick={handleReturnClick}
                      sx={{ ml: 2 }}
                    >
                      {t('rental.detail.returnButton', 'Return Equipment')}
                    </Button>
                  )}
                </Box>
              </Grid>

              {rental.deposit !== undefined && (
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">{t('rental.detail.deposit', 'Deposit')}</Typography>
                  </Box>
                  <Typography variant="body1">${rental.deposit.toFixed(2)}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {rental.depositReturned
                      ? t('rental.detail.depositReturned', 'Deposit returned')
                      : t('rental.detail.depositHeld', 'Deposit held')}
                  </Typography>
                </Grid>
              )}

              {rental.notes && (
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <NotesIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">{t('rental.detail.notes', 'Notes')}</Typography>
                  </Box>
                  <Typography variant="body1">{rental.notes}</Typography>
                </Grid>
              )}
            </Grid>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('rental.detail.equipmentItems', 'Equipment Items')}
            </Typography>

            <List>
              {rental.items.map((item, index) => (
                <ListItem key={index} divider={index < rental.items.length - 1}>
                  <ListItemIcon>
                    <InventoryIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={item.equipmentName}
                    secondary={
                      <>
                        {t('rental.detail.condition', 'Condition')}:{' '}
                        {t(`condition.${item.condition}`, item.condition)}
                        {item.notes && (
                          <>
                            <br />
                            {t('rental.detail.itemNotes', 'Notes')}: {item.notes}
                          </>
                        )}
                      </>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>

          {/* Return Dialog */}
          <Dialog open={returnDialogOpen} onClose={handleReturnCancel} maxWidth="md" fullWidth>
            <DialogTitle>{t('rental.detail.returnTitle', 'Return Equipment')}</DialogTitle>
            <DialogContent>
              <Typography paragraph>
                {t(
                  'rental.detail.returnInstructions',
                  'Please check the condition of each item and add notes if needed.'
                )}
              </Typography>

              <List>
                {rental.items.map((item, index) => (
                  <ListItem
                    key={index}
                    divider={index < rental.items.length - 1}
                    alignItems="flex-start"
                  >
                    <ListItemText
                      primary={item.equipmentName}
                      secondary={
                        <Grid container spacing={2} sx={{ mt: 1 }}>
                          <Grid item xs={12} md={6}>
                            <FormControl fullWidth size="small">
                              <InputLabel id={`condition-label-${item.equipmentId}`}>
                                {t('rental.detail.returnCondition', 'Condition')}
                              </InputLabel>
                              <Select
                                labelId={`condition-label-${item.equipmentId}`}
                                value={itemConditions[item.equipmentId] || 'good'}
                                label={t('rental.detail.returnCondition', 'Condition')}
                                onChange={(e) =>
                                  handleConditionChange(
                                    item.equipmentId,
                                    e.target.value as EquipmentCondition
                                  )
                                }
                              >
                                <MenuItem value="good">{t('condition.good', 'Good')}</MenuItem>
                                <MenuItem value="damaged">
                                  {t('condition.damaged', 'Damaged')}
                                </MenuItem>
                                <MenuItem value="lost">{t('condition.lost', 'Lost')}</MenuItem>
                                <MenuItem value="maintenance">
                                  {t('condition.maintenance', 'Needs Maintenance')}
                                </MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              label={t('rental.detail.returnNotes', 'Notes')}
                              value={itemNotes[item.equipmentId] || ''}
                              onChange={(e) => handleNotesChange(item.equipmentId, e.target.value)}
                              fullWidth
                              size="small"
                              multiline
                              rows={2}
                            />
                          </Grid>
                        </Grid>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleReturnCancel}>
                {t('actions.cancel', 'Cancel', { ns: 'common' })}
              </Button>
              <Button
                onClick={handleReturnConfirm}
                color="primary"
                variant="contained"
                disabled={returning}
              >
                {returning ? (
                  <CircularProgress size={24} />
                ) : (
                  t('rental.detail.confirmReturn', 'Confirm Return')
                )}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Paper sx={{ p: 3 }}>
          <Typography>{t('rental.detail.notFound', 'Rental not found')}</Typography>
        </Paper>
      )}
    </Box>
  );
};

export default RentalDetail;
