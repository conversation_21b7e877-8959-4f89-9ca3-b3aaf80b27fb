import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { Box, Tabs, Tab, Container, Paper, CircularProgress } from '@mui/material';
import { Inventory as InventoryIcon, ShoppingCart as RentalIcon } from '@mui/icons-material';
import EquipmentList from './EquipmentList';
import EquipmentForm from './EquipmentForm';
import RentalList from './RentalList';
import RentalForm from './RentalForm';
import RentalDetail from './RentalDetail';
import { useEquipmentPermission } from '../../hooks/useEquipmentPermission';

const EquipmentModule: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const navigate = useNavigate();
  const location = useLocation();
  // This hook will redirect if the user doesn't have permission
  const { hasEquipmentPermission, isLoading } = useEquipmentPermission();

  // Determine active tab based on URL
  const getActiveTab = () => {
    if (location.pathname.includes('/equipment/rentals')) {
      return 1;
    }
    return 0;
  };

  const [activeTab, setActiveTab] = useState(getActiveTab());

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    if (newValue === 0) {
      navigate('/equipment');
    } else {
      navigate('/equipment/rentals');
    }
  };

  // If the permission check is still in progress, show loading indicator
  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If we have permission, render the content
  return (
    <Container maxWidth="lg">
      <Box sx={{ width: '100%', mb: 3 }}>
        <Paper sx={{ borderRadius: '4px 4px 0 0' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab
              icon={<InventoryIcon />}
              label={t('tabs.inventory', 'Inventory')}
              iconPosition="start"
            />
            <Tab icon={<RentalIcon />} label={t('tabs.rentals', 'Rentals')} iconPosition="start" />
          </Tabs>
        </Paper>
      </Box>

      {hasEquipmentPermission ? (
        <Routes>
          {/* Inventory Routes */}
          <Route path="/" element={<EquipmentList />} />
          <Route path="/new" element={<EquipmentForm />} />
          <Route path="/:id" element={<EquipmentForm />} />

          {/* Rental Routes */}
          <Route path="/rentals" element={<RentalList />} />
          <Route path="/rentals/new" element={<RentalForm />} />
          <Route path="/rentals/:id" element={<RentalDetail />} />
        </Routes>
      ) : (
        // This should not happen as the hook should redirect, but just in case
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      )}
    </Container>
  );
};

export default EquipmentModule;
