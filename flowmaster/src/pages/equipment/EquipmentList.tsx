import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  Chip,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Tooltip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Build as BuildIcon,
} from '@mui/icons-material';
import {
  getEquipmentItems,
  getEquipmentCategories,
  getEquipmentConditions,
  deleteEquipmentItem,
} from '../../services/equipmentService';
import {
  EquipmentItem,
  EquipmentCategory,
  EquipmentCondition,
  EquipmentFilterOptions,
} from '../../types/equipment';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { useNavigate } from 'react-router-dom';

const EquipmentList: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  // State
  const [equipment, setEquipment] = useState<EquipmentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState<EquipmentFilterOptions>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<EquipmentItem | null>(null);

  // Get categories and conditions
  const categories = getEquipmentCategories();
  const conditions = getEquipmentConditions();

  // Fetch equipment data
  useEffect(() => {
    const fetchEquipment = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        const items = await getEquipmentItems(currentSchool.id, filters);
        setEquipment(items);
        setError(null);
      } catch (err) {
        console.error('Error fetching equipment:', err);
        setError(t('messages.fetchError', 'Failed to fetch equipment data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchEquipment();
  }, [currentSchool?.id, filters, t]);

  // Handle search
  const handleSearch = () => {
    setFilters({ ...filters, search: searchTerm });
    setPage(0);
  };

  // Handle filter changes
  const handleCategoryChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setFilters({
      ...filters,
      category: value === 'all' ? undefined : (value as EquipmentCategory),
    });
    setPage(0);
  };

  const handleConditionChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setFilters({
      ...filters,
      condition: value === 'all' ? undefined : (value as EquipmentCondition),
    });
    setPage(0);
  };

  const handleAvailabilityChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setFilters({
      ...filters,
      available: value === 'all' ? undefined : value === 'available',
    });
    setPage(0);
  };

  // Handle pagination
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle add/edit navigation
  const handleAddEquipment = () => {
    navigate('/equipment/new');
  };

  const handleEditEquipment = (id: string) => {
    navigate(`/equipment/${id}`);
  };

  // Handle delete
  const handleDeleteClick = (item: EquipmentItem) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!itemToDelete || !currentSchool?.id) return;

    try {
      await deleteEquipmentItem(currentSchool.id, itemToDelete.id);
      setEquipment(equipment.filter((item) => item.id !== itemToDelete.id));
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    } catch (err) {
      console.error('Error deleting equipment:', err);
      setError(t('messages.deleteError', 'Failed to delete equipment item', { ns: 'common' }));
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  // Render condition chip
  const renderConditionChip = (condition: EquipmentCondition) => {
    switch (condition) {
      case 'good':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('condition.good', 'Good')}
            color="success"
            size="small"
          />
        );
      case 'damaged':
        return (
          <Chip
            icon={<WarningIcon />}
            label={t('condition.damaged', 'Damaged')}
            color="warning"
            size="small"
          />
        );
      case 'lost':
        return (
          <Chip
            icon={<CancelIcon />}
            label={t('condition.lost', 'Lost')}
            color="error"
            size="small"
          />
        );
      case 'maintenance':
        return (
          <Chip
            icon={<BuildIcon />}
            label={t('condition.maintenance', 'Maintenance')}
            color="info"
            size="small"
          />
        );
      default:
        return null;
    }
  };

  // Render availability chip
  const renderAvailabilityChip = (available: boolean) => {
    return available ? (
      <Chip label={t('status.available', 'Available')} color="success" size="small" />
    ) : (
      <Chip label={t('status.unavailable', 'In Use')} color="default" size="small" />
    );
  };

  // Calculate pagination
  const emptyRows = rowsPerPage - Math.min(rowsPerPage, equipment.length - page * rowsPerPage);
  const paginatedEquipment = equipment.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('inventory.title', 'Equipment Inventory')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddEquipment}
        >
          {t('inventory.addButton', 'Add Equipment')}
        </Button>
      </Box>

      {error && <ErrorAlert message={error} />}

      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, maxWidth: 300 }}>
            <TextField
              label={t('filters.search', 'Search')}
              variant="outlined"
              size="small"
              fullWidth
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <IconButton onClick={handleSearch}>
              <SearchIcon />
            </IconButton>
          </Box>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="category-filter-label">{t('filters.category', 'Category')}</InputLabel>
            <Select
              labelId="category-filter-label"
              value={filters.category || 'all'}
              label={t('filters.category', 'Category')}
              onChange={handleCategoryChange}
            >
              <MenuItem value="all">{t('filters.all', 'All', { ns: 'common' })}</MenuItem>
              {categories.map((category) => (
                <MenuItem key={category} value={category}>
                  {t(`category.${category}`, category)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="condition-filter-label">
              {t('filters.condition', 'Condition')}
            </InputLabel>
            <Select
              labelId="condition-filter-label"
              value={filters.condition || 'all'}
              label={t('filters.condition', 'Condition')}
              onChange={handleConditionChange}
            >
              <MenuItem value="all">{t('filters.all', 'All', { ns: 'common' })}</MenuItem>
              {conditions.map((condition) => (
                <MenuItem key={condition} value={condition}>
                  {t(`condition.${condition}`, condition)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="availability-filter-label">
              {t('filters.availability', 'Availability')}
            </InputLabel>
            <Select
              labelId="availability-filter-label"
              value={
                filters.available === undefined
                  ? 'all'
                  : filters.available
                    ? 'available'
                    : 'unavailable'
              }
              label={t('filters.availability', 'Availability')}
              onChange={handleAvailabilityChange}
            >
              <MenuItem value="all">{t('filters.all', 'All', { ns: 'common' })}</MenuItem>
              <MenuItem value="available">{t('status.available', 'Available')}</MenuItem>
              <MenuItem value="unavailable">{t('status.unavailable', 'In Use')}</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>

      <TableContainer component={Paper}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('inventory.nameColumn', 'Name')}</TableCell>
                  <TableCell>{t('inventory.categoryColumn', 'Category')}</TableCell>
                  <TableCell>{t('inventory.sizeColumn', 'Size')}</TableCell>
                  <TableCell>{t('inventory.serialColumn', 'Serial Number')}</TableCell>
                  <TableCell>{t('inventory.conditionColumn', 'Condition')}</TableCell>
                  <TableCell>{t('inventory.availabilityColumn', 'Availability')}</TableCell>
                  <TableCell align="right">{t('inventory.actionsColumn', 'Actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedEquipment.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{t(`category.${item.category}`, item.category)}</TableCell>
                    <TableCell>{item.size || '-'}</TableCell>
                    <TableCell>{item.serialNumber || '-'}</TableCell>
                    <TableCell>{renderConditionChip(item.condition)}</TableCell>
                    <TableCell>{renderAvailabilityChip(item.available)}</TableCell>
                    <TableCell align="right">
                      <Tooltip title={t('actions.edit', 'Edit', { ns: 'common' })}>
                        <IconButton onClick={() => handleEditEquipment(item.id)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('actions.delete', 'Delete', { ns: 'common' })}>
                        <IconButton onClick={() => handleDeleteClick(item)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
                {emptyRows > 0 && (
                  <TableRow style={{ height: 53 * emptyRows }}>
                    <TableCell colSpan={7} />
                  </TableRow>
                )}
                {equipment.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      {t('inventory.noItems', 'No equipment items found')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={equipment.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </TableContainer>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
        <DialogTitle>{t('inventory.deleteTitle', 'Delete Equipment Item')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t(
              'inventory.deleteConfirmation',
              'Are you sure you want to delete {{name}}? This action cannot be undone.',
              {
                name: itemToDelete?.name,
              }
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>
            {t('actions.cancel', 'Cancel', { ns: 'common' })}
          </Button>
          <Button onClick={handleDeleteConfirm} color="error">
            {t('actions.delete', 'Delete', { ns: 'common' })}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EquipmentList;
