import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  CircularProgress,
  Divider,
  Switch,
  FormControlLabel,
  Alert,
  SelectChangeEvent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { Save as SaveIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import {
  getEquipmentItem,
  createEquipmentItem,
  updateEquipmentItem,
  getEquipmentCategories,
  getEquipmentConditions,
} from '../../services/equipmentService';
import { EquipmentItem } from '../../types/equipment';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { Timestamp } from 'firebase/firestore';

const EquipmentForm: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id && id !== 'new';

  // State
  const [equipment, setEquipment] = useState<Partial<EquipmentItem>>({
    name: '',
    category: 'other',
    condition: 'good',
    available: true,
  });
  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Get categories and conditions
  const categories = getEquipmentCategories();
  const conditions = getEquipmentConditions();

  // Fetch equipment data if in edit mode
  useEffect(() => {
    const fetchEquipment = async () => {
      if (!isEditMode || !currentSchool?.id) return;

      setLoading(true);
      try {
        const item = await getEquipmentItem(currentSchool.id, id);
        setEquipment(item);
        setError(null);
      } catch (err) {
        console.error('Error fetching equipment:', err);
        setError(t('messages.fetchError', 'Failed to fetch equipment data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchEquipment();
  }, [currentSchool?.id, id, isEditMode, t]);

  // Handle form changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEquipment({ ...equipment, [name]: value });

    // Clear validation error when field is updated
    if (validationErrors[name]) {
      setValidationErrors({ ...validationErrors, [name]: '' });
    }
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const name = e.target.name;
    const value = e.target.value;
    setEquipment({ ...equipment, [name]: value });

    // Clear validation error when field is updated
    if (validationErrors[name]) {
      setValidationErrors({ ...validationErrors, [name]: '' });
    }
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setEquipment({ ...equipment, [name]: checked });
  };

  const handleDateChange = (name: string, date: Date | null) => {
    if (date) {
      setEquipment({ ...equipment, [name]: Timestamp.fromDate(date) });
    } else {
      const updatedEquipment = { ...equipment };
      delete updatedEquipment[name as keyof EquipmentItem];
      setEquipment(updatedEquipment);
    }

    // Clear validation error when field is updated
    if (validationErrors[name]) {
      setValidationErrors({ ...validationErrors, [name]: '' });
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!equipment.name?.trim()) {
      errors.name = t('validation.nameRequired', 'Name is required', { ns: 'common' });
    }

    if (!equipment.category) {
      errors.category = t('validation.categoryRequired', 'Category is required');
    }

    if (!equipment.condition) {
      errors.condition = t('validation.conditionRequired', 'Condition is required');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !currentSchool?.id) return;

    setSaving(true);
    try {
      if (isEditMode && id) {
        await updateEquipmentItem(currentSchool.id, id, equipment);
      } else {
        await createEquipmentItem(
          currentSchool.id,
          equipment as Omit<EquipmentItem, 'id' | 'createdAt' | 'updatedAt'>
        );
      }
      setSuccess(true);
      setError(null);

      // Navigate back to list after a short delay
      setTimeout(() => {
        navigate('/equipment');
      }, 1500);
    } catch (err) {
      console.error('Error saving equipment:', err);
      setError(t('messages.saveError', 'Failed to save equipment item', { ns: 'common' }));
      setSuccess(false);
    } finally {
      setSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate('/equipment');
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button startIcon={<ArrowBackIcon />} onClick={handleCancel} sx={{ mr: 2 }}>
          {t('actions.back', 'Back', { ns: 'common' })}
        </Button>
        <Typography variant="h4" component="h1">
          {isEditMode
            ? t('form.editTitle', 'Edit Equipment Item')
            : t('form.addTitle', 'Add Equipment Item')}
        </Typography>
      </Box>

      {error && <ErrorAlert error={error} />}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {t('messages.saveSuccess', 'Equipment item saved successfully', { ns: 'common' })}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper sx={{ p: 3 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="name"
                  label={t('form.name', 'Name')}
                  value={equipment.name || ''}
                  onChange={handleChange}
                  fullWidth
                  required
                  error={!!validationErrors.name}
                  helperText={validationErrors.name}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!validationErrors.category}>
                  <InputLabel id="category-label">{t('form.category', 'Category')}</InputLabel>
                  <Select
                    labelId="category-label"
                    name="category"
                    value={equipment.category || ''}
                    onChange={handleSelectChange}
                    label={t('form.category', 'Category')}
                    required
                  >
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {t(`category.${category}`, category)}
                      </MenuItem>
                    ))}
                  </Select>
                  {validationErrors.category && (
                    <FormHelperText>{validationErrors.category}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  name="size"
                  label={t('form.size', 'Size')}
                  value={equipment.size || ''}
                  onChange={handleChange}
                  fullWidth
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  name="serialNumber"
                  label={t('form.serialNumber', 'Serial Number')}
                  value={equipment.serialNumber || ''}
                  onChange={handleChange}
                  fullWidth
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!validationErrors.condition}>
                  <InputLabel id="condition-label">{t('form.condition', 'Condition')}</InputLabel>
                  <Select
                    labelId="condition-label"
                    name="condition"
                    value={equipment.condition || ''}
                    onChange={handleSelectChange}
                    label={t('form.condition', 'Condition')}
                    required
                  >
                    {conditions.map((condition) => (
                      <MenuItem key={condition} value={condition}>
                        {t(`condition.${condition}`, condition)}
                      </MenuItem>
                    ))}
                  </Select>
                  {validationErrors.condition && (
                    <FormHelperText>{validationErrors.condition}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      name="available"
                      checked={equipment.available ?? true}
                      onChange={handleSwitchChange}
                      color="primary"
                    />
                  }
                  label={t('form.available', 'Available for Rental')}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {t('form.additionalInfo', 'Additional Information')}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label={t('form.purchaseDate', 'Purchase Date')}
                    value={equipment.purchaseDate ? equipment.purchaseDate.toDate() : null}
                    onChange={(date) => handleDateChange('purchaseDate', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined',
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label={t('form.lastMaintenanceDate', 'Last Maintenance Date')}
                    value={
                      equipment.lastMaintenanceDate ? equipment.lastMaintenanceDate.toDate() : null
                    }
                    onChange={(date) => handleDateChange('lastMaintenanceDate', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined',
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  name="notes"
                  label={t('form.notes', 'Notes')}
                  value={equipment.notes || ''}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={4}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                  <Button variant="outlined" onClick={handleCancel} disabled={saving}>
                    {t('actions.cancel', 'Cancel', { ns: 'common' })}
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    disabled={saving}
                  >
                    {saving ? (
                      <CircularProgress size={24} />
                    ) : (
                      t('actions.save', 'Save', { ns: 'common' })
                    )}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>
      )}
    </Box>
  );
};

export default EquipmentForm;
