import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  CircularProgress,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Alert,
  Autocomplete,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { createRentalRecord } from '../../services/rentalService';
import { getEquipmentItems } from '../../services/equipmentService';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { RentalRecord, RentalItem, EquipmentItem } from '../../types/equipment';
import { Student } from '../../types/student';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { Timestamp } from 'firebase/firestore';
import { addDays } from 'date-fns';

interface CustomerOption {
  id: string;
  name: string;
  type: 'student' | 'client' | 'guest';
}

const RentalForm: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we have a pre-selected client from navigation state
  const preSelectedClientId = location.state?.clientId;

  // State
  const [rental, setRental] = useState<Partial<RentalRecord>>({
    date: Timestamp.fromDate(new Date()),
    dueDate: Timestamp.fromDate(addDays(new Date(), 1)),
    items: [],
    returned: false,
    status: 'active',
    customerType: 'student',
  });
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [equipment, setEquipment] = useState<EquipmentItem[]>([]);
  const [availableEquipment, setAvailableEquipment] = useState<EquipmentItem[]>([]);
  const [selectedEquipment, setSelectedEquipment] = useState<EquipmentItem | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [guestName, setGuestName] = useState('');

  // Fetch customers and equipment
  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch students
        const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
        const studentsQuery = query(studentsRef, orderBy('firstName'));
        const studentsSnapshot = await getDocs(studentsQuery);
        const studentsData = studentsSnapshot.docs.map((doc) => {
          const data = doc.data() as Student;
          return {
            id: doc.id,
            name: `${data.firstName} ${data.lastName}`,
            type: 'student' as const,
          };
        });

        // Fetch clients
        const clientsRef = collection(db, 'schools', currentSchool.id, 'clients');
        const clientsQuery = query(clientsRef, orderBy('firstName'));
        const clientsSnapshot = await getDocs(clientsQuery);
        const clientsData = clientsSnapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            name: `${data.firstName} ${data.lastName}`,
            type: 'client' as const,
          };
        });

        // Combine students and clients
        setCustomers([...studentsData, ...clientsData]);

        // If we have a pre-selected client, find and select them
        if (preSelectedClientId) {
          const allCustomers = [...studentsData, ...clientsData];
          const selectedCustomer = allCustomers.find(
            (customer) => customer.id === preSelectedClientId
          );
          if (selectedCustomer) {
            setRental({
              ...rental,
              customerId: selectedCustomer.id,
              customerName: selectedCustomer.name,
              customerType: selectedCustomer.type,
            });
          }
        }

        // Fetch equipment
        const equipmentItems = await getEquipmentItems(currentSchool.id, { available: true });
        setEquipment(equipmentItems);
        setAvailableEquipment(equipmentItems);

        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id, t, preSelectedClientId]);

  // Handle form changes
  const handleCustomerChange = (_event: React.SyntheticEvent, value: CustomerOption | null) => {
    if (value) {
      setRental({
        ...rental,
        customerId: value.id,
        customerName: value.name,
        customerType: value.type,
      });

      // Clear validation error
      if (validationErrors.customerId) {
        setValidationErrors({ ...validationErrors, customerId: '' });
      }
    }
  };

  const handleGuestNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setGuestName(name);

    if (name.trim()) {
      setRental({
        ...rental,
        customerId: `guest-${Date.now()}`,
        customerName: name,
        customerType: 'guest',
      });

      // Clear validation error
      if (validationErrors.customerId) {
        setValidationErrors({ ...validationErrors, customerId: '' });
      }
    }
  };

  const handleDateChange = (name: string, date: Date | null) => {
    if (date) {
      setRental({ ...rental, [name]: Timestamp.fromDate(date) });

      // Clear validation error
      if (validationErrors[name]) {
        setValidationErrors({ ...validationErrors, [name]: '' });
      }
    }
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRental({ ...rental, notes: e.target.value });
  };

  const handleDepositChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setRental({ ...rental, deposit: isNaN(value) ? undefined : value });
  };

  // Handle equipment selection
  const handleEquipmentChange = (_event: React.SyntheticEvent, value: EquipmentItem | null) => {
    setSelectedEquipment(value);
  };

  const handleAddEquipment = () => {
    if (!selectedEquipment) return;

    // Add equipment to rental items
    const newItem: RentalItem = {
      equipmentId: selectedEquipment.id,
      equipmentName: selectedEquipment.name,
      condition: selectedEquipment.condition,
    };

    const updatedItems = [...(rental.items || []), newItem];
    setRental({ ...rental, items: updatedItems });

    // Remove from available equipment
    setAvailableEquipment(availableEquipment.filter((item) => item.id !== selectedEquipment.id));
    setSelectedEquipment(null);

    // Clear validation error
    if (validationErrors.items) {
      setValidationErrors({ ...validationErrors, items: '' });
    }
  };

  const handleRemoveEquipment = (index: number) => {
    const items = [...(rental.items || [])];
    const removedItem = items[index];

    // Add back to available equipment if it exists in our original list
    const originalItem = equipment.find((item) => item.id === removedItem.equipmentId);
    if (originalItem) {
      setAvailableEquipment([...availableEquipment, originalItem]);
    }

    // Remove from rental items
    items.splice(index, 1);
    setRental({ ...rental, items });
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!rental.customerId) {
      errors.customerId = t('validation.customerRequired', 'Customer is required');
    }

    if (!rental.date) {
      errors.date = t('validation.dateRequired', 'Date is required');
    }

    if (!rental.dueDate) {
      errors.dueDate = t('validation.dueDateRequired', 'Due date is required');
    }

    if (!rental.items || rental.items.length === 0) {
      errors.items = t('validation.itemsRequired', 'At least one item is required');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !currentSchool?.id) return;

    setSaving(true);
    try {
      const rentalId = await createRentalRecord(
        currentSchool.id,
        rental as Omit<RentalRecord, 'id' | 'status' | 'createdAt' | 'updatedAt'>
      );
      setSuccess(true);
      setError(null);

      // Navigate to rental detail after a short delay
      setTimeout(() => {
        navigate(`/equipment/rentals/${rentalId}`);
      }, 1500);
    } catch (err) {
      console.error('Error creating rental:', err);
      setError(t('messages.saveError', 'Failed to create rental', { ns: 'common' }));
      setSuccess(false);
    } finally {
      setSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate('/equipment/rentals');
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button startIcon={<ArrowBackIcon />} onClick={handleCancel} sx={{ mr: 2 }}>
          {t('actions.back', 'Back', { ns: 'common' })}
        </Button>
        <Typography variant="h4" component="h1">
          {t('rental.form.title', 'New Equipment Rental')}
        </Typography>
      </Box>

      {error && <ErrorAlert message={error} />}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {t('messages.saveSuccess', 'Rental created successfully', { ns: 'common' })}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper sx={{ p: 3 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  {t('rental.form.customerSection', 'Customer Information')}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!validationErrors.customerId}>
                  <InputLabel id="customer-type-label">
                    {t('rental.form.customerType', 'Customer Type')}
                  </InputLabel>
                  <Select
                    labelId="customer-type-label"
                    value={rental.customerType || 'student'}
                    label={t('rental.form.customerType', 'Customer Type')}
                    onChange={(e) =>
                      setRental({
                        ...rental,
                        customerType: e.target.value as 'student' | 'client' | 'guest',
                      })
                    }
                  >
                    <MenuItem value="student">{t('rental.form.studentType', 'Student')}</MenuItem>
                    <MenuItem value="client">{t('rental.form.clientType', 'Client')}</MenuItem>
                    <MenuItem value="guest">{t('rental.form.guestType', 'Guest')}</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                {rental.customerType === 'guest' ? (
                  <TextField
                    label={t('rental.form.guestName', 'Guest Name')}
                    value={guestName}
                    onChange={handleGuestNameChange}
                    fullWidth
                    error={!!validationErrors.customerId}
                    helperText={validationErrors.customerId}
                  />
                ) : (
                  <Autocomplete
                    options={customers.filter((c) => c.type === rental.customerType)}
                    getOptionLabel={(option) => option.name}
                    onChange={handleCustomerChange}
                    value={
                      rental.customerId
                        ? customers.find((c) => c.id === rental.customerId) || null
                        : null
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={
                          rental.customerType === 'student'
                            ? t('rental.form.selectStudent', 'Select Student')
                            : t('rental.form.selectClient', 'Select Client')
                        }
                        error={!!validationErrors.customerId}
                        helperText={validationErrors.customerId}
                      />
                    )}
                  />
                )}
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {t('rental.form.rentalDetails', 'Rental Details')}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label={t('rental.form.rentalDate', 'Rental Date')}
                    value={rental.date ?
                      (typeof rental.date.toDate === 'function' ? rental.date.toDate() :
                       (typeof rental.date === 'string' ? new Date(rental.date) :
                        (rental.date instanceof Date ? rental.date : null)))
                      : null}
                    onChange={(date) => handleDateChange('date', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined',
                        error: !!validationErrors.date,
                        helperText: validationErrors.date,
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label={t('rental.form.dueDate', 'Due Date')}
                    value={rental.dueDate ?
                      (typeof rental.dueDate.toDate === 'function' ? rental.dueDate.toDate() :
                       (typeof rental.dueDate === 'string' ? new Date(rental.dueDate) :
                        (rental.dueDate instanceof Date ? rental.dueDate : null)))
                      : null}
                    onChange={(date) => handleDateChange('dueDate', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined',
                        error: !!validationErrors.dueDate,
                        helperText: validationErrors.dueDate,
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  label={t('rental.form.deposit', 'Deposit Amount')}
                  type="number"
                  value={rental.deposit || ''}
                  onChange={handleDepositChange}
                  fullWidth
                  InputProps={{
                    startAdornment: <span>$</span>,
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label={t('rental.form.notes', 'Notes')}
                  value={rental.notes || ''}
                  onChange={handleNotesChange}
                  fullWidth
                  multiline
                  rows={2}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {t('rental.form.equipmentSection', 'Equipment Items')}
                </Typography>
                {validationErrors.items && (
                  <FormHelperText error>{validationErrors.items}</FormHelperText>
                )}
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Autocomplete
                    options={availableEquipment}
                    getOptionLabel={(option) =>
                      `${option.name}${option.size ? ` (${option.size})` : ''}`
                    }
                    value={selectedEquipment}
                    onChange={handleEquipmentChange}
                    sx={{ flexGrow: 1, mr: 2 }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={t('rental.form.selectEquipment', 'Select Equipment')}
                      />
                    )}
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleAddEquipment}
                    disabled={!selectedEquipment}
                  >
                    {t('rental.form.addItem', 'Add')}
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('rental.form.selectedItems', 'Selected Items')}
                  </Typography>
                  {rental.items && rental.items.length > 0 ? (
                    <List>
                      {rental.items.map((item, index) => (
                        <ListItem key={index} divider={index < rental.items!.length - 1}>
                          <ListItemText
                            primary={item.equipmentName}
                            secondary={t(`condition.${item.condition}`, item.condition)}
                          />
                          <ListItemSecondaryAction>
                            <IconButton edge="end" onClick={() => handleRemoveEquipment(index)}>
                              <DeleteIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      {t('rental.form.noItemsSelected', 'No items selected')}
                    </Typography>
                  )}
                </Paper>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                  <Button variant="outlined" onClick={handleCancel} disabled={saving}>
                    {t('actions.cancel', 'Cancel', { ns: 'common' })}
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    disabled={saving}
                  >
                    {saving ? (
                      <CircularProgress size={24} />
                    ) : (
                      t('actions.create', 'Create', { ns: 'common' })
                    )}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>
      )}
    </Box>
  );
};

export default RentalForm;
