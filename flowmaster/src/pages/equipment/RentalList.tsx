import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  Chip,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Tooltip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as VisibilityIcon,
  AssignmentReturn as ReturnIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format } from 'date-fns';
import { getRentalRecords, returnRental } from '../../services/rentalService';
import { RentalRecord, RentalStatus, RentalFilterOptions } from '../../types/equipment';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { useNavigate } from 'react-router-dom';

const RentalList: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  // State
  const [rentals, setRentals] = useState<RentalRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState<RentalFilterOptions>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [returnDialogOpen, setReturnDialogOpen] = useState(false);
  const [rentalToReturn, setRentalToReturn] = useState<RentalRecord | null>(null);
  const [dateFrom, setDateFrom] = useState<Date | null>(null);
  const [dateTo, setDateTo] = useState<Date | null>(null);

  // Fetch rental data
  useEffect(() => {
    const fetchRentals = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        const records = await getRentalRecords(currentSchool.id, filters);
        setRentals(records);
        setError(null);
      } catch (err) {
        console.error('Error fetching rentals:', err);
        setError(t('messages.fetchError', 'Failed to fetch rental data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchRentals();
  }, [currentSchool?.id, filters, t]);

  // Handle search
  const handleSearch = () => {
    setFilters({ ...filters, search: searchTerm });
    setPage(0);
  };

  // Handle filter changes
  const handleStatusChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setFilters({
      ...filters,
      status: value === 'all' ? undefined : (value as RentalStatus),
    });
    setPage(0);
  };

  const handleDateChange = () => {
    setFilters({
      ...filters,
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined,
    });
    setPage(0);
  };

  // Handle pagination
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle add navigation
  const handleAddRental = () => {
    navigate('/equipment/rentals/new');
  };

  // Handle view navigation
  const handleViewRental = (id: string) => {
    navigate(`/equipment/rentals/${id}`);
  };

  // Handle return
  const handleReturnClick = (rental: RentalRecord) => {
    setRentalToReturn(rental);
    setReturnDialogOpen(true);
  };

  const handleReturnConfirm = async () => {
    if (!rentalToReturn || !currentSchool?.id) return;

    try {
      await returnRental(currentSchool.id, rentalToReturn.id);

      // Update the rental in the list
      setRentals(
        rentals.map((rental) =>
          rental.id === rentalToReturn.id
            ? { ...rental, returned: true, status: 'returned' }
            : rental
        )
      );

      setReturnDialogOpen(false);
      setRentalToReturn(null);
    } catch (err) {
      console.error('Error returning rental:', err);
      setError(t('messages.returnError', 'Failed to return rental', { ns: 'common' }));
    }
  };

  const handleReturnCancel = () => {
    setReturnDialogOpen(false);
    setRentalToReturn(null);
  };

  // Render status chip
  const renderStatusChip = (status: RentalStatus) => {
    switch (status) {
      case 'active':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('rental.status.active', 'Active')}
            color="primary"
            size="small"
          />
        );
      case 'returned':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('rental.status.returned', 'Returned')}
            color="success"
            size="small"
          />
        );
      case 'overdue':
        return (
          <Chip
            icon={<WarningIcon />}
            label={t('rental.status.overdue', 'Overdue')}
            color="error"
            size="small"
          />
        );
      default:
        return null;
    }
  };

  // Format date - handles different date formats
  const formatDate = (date: any) => {
    try {
      if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {
        // Firebase Timestamp
        return format(date.toDate(), 'PPP');
      } else if (date instanceof Date) {
        // JavaScript Date object
        return format(date, 'PPP');
      } else if (typeof date === 'string') {
        // ISO string or other date string
        return format(new Date(date), 'PPP');
      } else if (date && typeof date === 'number') {
        // Unix timestamp (milliseconds)
        return format(new Date(date), 'PPP');
      }
      return 'Invalid date';
    } catch (error) {
      console.error('Error formatting date:', error, date);
      return 'Invalid date';
    }
  };

  // Calculate pagination
  const emptyRows = rowsPerPage - Math.min(rowsPerPage, rentals.length - page * rowsPerPage);
  const paginatedRentals = rentals.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('rental.title', 'Equipment Rentals')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddRental}
        >
          {t('rental.addButton', 'New Rental')}
        </Button>
      </Box>

      {error && <ErrorAlert message={error} />}

      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, maxWidth: 300 }}>
            <TextField
              label={t('filters.search', 'Search')}
              variant="outlined"
              size="small"
              fullWidth
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <IconButton onClick={handleSearch}>
              <SearchIcon />
            </IconButton>
          </Box>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="status-filter-label">{t('filters.status', 'Status')}</InputLabel>
            <Select
              labelId="status-filter-label"
              value={filters.status || 'all'}
              label={t('filters.status', 'Status')}
              onChange={handleStatusChange}
            >
              <MenuItem value="all">{t('filters.all', 'All', { ns: 'common' })}</MenuItem>
              <MenuItem value="active">{t('rental.status.active', 'Active')}</MenuItem>
              <MenuItem value="returned">{t('rental.status.returned', 'Returned')}</MenuItem>
              <MenuItem value="overdue">{t('rental.status.overdue', 'Overdue')}</MenuItem>
            </Select>
          </FormControl>

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={t('filters.dateFrom', 'From Date')}
              value={dateFrom}
              onChange={setDateFrom}
              slotProps={{
                textField: {
                  size: 'small',
                  sx: { width: 150 },
                },
              }}
            />

            <DatePicker
              label={t('filters.dateTo', 'To Date')}
              value={dateTo}
              onChange={setDateTo}
              slotProps={{
                textField: {
                  size: 'small',
                  sx: { width: 150 },
                },
              }}
            />
          </LocalizationProvider>

          <Button
            variant="outlined"
            size="small"
            onClick={handleDateChange}
            startIcon={<FilterListIcon />}
          >
            {t('filters.apply', 'Apply Filters')}
          </Button>
        </Box>
      </Paper>

      <TableContainer component={Paper}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('rental.dateColumn', 'Date')}</TableCell>
                  <TableCell>{t('rental.customerColumn', 'Customer')}</TableCell>
                  <TableCell>{t('rental.itemsColumn', 'Items')}</TableCell>
                  <TableCell>{t('rental.dueDateColumn', 'Due Date')}</TableCell>
                  <TableCell>{t('rental.statusColumn', 'Status')}</TableCell>
                  <TableCell align="right">{t('rental.actionsColumn', 'Actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedRentals.map((rental) => (
                  <TableRow key={rental.id}>
                    <TableCell>{formatDate(rental.date)}</TableCell>
                    <TableCell>{rental.customerName}</TableCell>
                    <TableCell>
                      {rental.items.length} {t('rental.itemsCount', 'items')}
                    </TableCell>
                    <TableCell>{formatDate(rental.dueDate)}</TableCell>
                    <TableCell>{renderStatusChip(rental.status)}</TableCell>
                    <TableCell align="right">
                      <Tooltip title={t('actions.view', 'View', { ns: 'common' })}>
                        <IconButton onClick={() => handleViewRental(rental.id)}>
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      {!rental.returned && (
                        <Tooltip title={t('rental.returnAction', 'Return')}>
                          <IconButton onClick={() => handleReturnClick(rental)}>
                            <ReturnIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                {emptyRows > 0 && (
                  <TableRow style={{ height: 53 * emptyRows }}>
                    <TableCell colSpan={6} />
                  </TableRow>
                )}
                {rentals.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      {t('rental.noItems', 'No rentals found')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={rentals.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </TableContainer>

      {/* Return Confirmation Dialog */}
      <Dialog open={returnDialogOpen} onClose={handleReturnCancel}>
        <DialogTitle>{t('rental.returnTitle', 'Return Equipment')}</DialogTitle>
        <DialogContent>
          <Typography>
            {t(
              'rental.returnConfirmation',
              'Are you sure you want to mark this rental as returned?'
            )}
          </Typography>
          {rentalToReturn && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2">
                {t('rental.customer', 'Customer')}: {rentalToReturn.customerName}
              </Typography>
              <Typography variant="subtitle2">{t('rental.items', 'Items')}:</Typography>
              <ul>
                {rentalToReturn.items.map((item, index) => (
                  <li key={index}>{item.equipmentName}</li>
                ))}
              </ul>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleReturnCancel}>
            {t('actions.cancel', 'Cancel', { ns: 'common' })}
          </Button>
          <Button onClick={handleReturnConfirm} color="primary" variant="contained">
            {t('rental.confirmReturn', 'Confirm Return')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RentalList;
