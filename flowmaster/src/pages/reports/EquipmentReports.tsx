import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  FileDownload as FileDownloadIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import { getEquipmentItems, getEquipmentCategories } from '../../services/equipmentService';
import { getRentalRecords } from '../../services/rentalService';
import {
  EquipmentI<PERSON>,
  RentalR<PERSON>ord,
  EquipmentCategory,
  EquipmentCondition,
} from '../../types/equipment';
import { ErrorAlert } from '../../components/shared/ErrorAlert';
import { format, subDays, isAfter } from 'date-fns';

// Chart colors
const COLORS = ['#4caf50', '#ff9800', '#f44336', '#9e9e9e', '#2196f3', '#673ab7', '#795548'];

interface EquipmentStats {
  total: number;
  available: number;
  unavailable: number;
  conditions: Record<EquipmentCondition, number>;
  categories: Record<string, number>;
}

interface RentalStats {
  total: number;
  active: number;
  returned: number;
  overdue: number;
  byCategory: Record<string, number>;
  byCustomerType: Record<string, number>;
  rentalTrend: { date: string; count: number }[];
}

const EquipmentReports: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common', 'reports']);
  const { currentSchool } = useSchool();

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30');
  const [equipmentStats, setEquipmentStats] = useState<EquipmentStats>({
    total: 0,
    available: 0,
    unavailable: 0,
    conditions: {
      good: 0,
      damaged: 0,
      maintenance: 0,
      lost: 0,
    },
    categories: {},
  });
  const [rentalStats, setRentalStats] = useState<RentalStats>({
    total: 0,
    active: 0,
    returned: 0,
    overdue: 0,
    byCategory: {},
    byCustomerType: {},
    rentalTrend: [],
  });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch equipment items
        const items = await getEquipmentItems(currentSchool.id);

        // Calculate equipment stats
        const eStats: EquipmentStats = {
          total: items.length,
          available: items.filter((item) => item.available).length,
          unavailable: items.filter((item) => !item.available).length,
          conditions: {
            good: 0,
            damaged: 0,
            maintenance: 0,
            lost: 0,
          },
          categories: {},
        };

        // Count by condition and category
        items.forEach((item) => {
          eStats.conditions[item.condition]++;

          if (!eStats.categories[item.category]) {
            eStats.categories[item.category] = 0;
          }
          eStats.categories[item.category]++;
        });

        setEquipmentStats(eStats);

        // Fetch rental records
        const rentals = await getRentalRecords(currentSchool.id);

        // Calculate rental stats
        const rStats: RentalStats = {
          total: rentals.length,
          active: rentals.filter((r) => r.status === 'active').length,
          returned: rentals.filter((r) => r.status === 'returned').length,
          overdue: rentals.filter((r) => r.status === 'overdue').length,
          byCategory: {},
          byCustomerType: {},
          rentalTrend: [],
        };

        // Count by customer type
        rentals.forEach((rental) => {
          if (!rStats.byCustomerType[rental.customerType]) {
            rStats.byCustomerType[rental.customerType] = 0;
          }
          rStats.byCustomerType[rental.customerType]++;

          // Count by category (for each item in the rental)
          rental.items.forEach((item) => {
            // We don't have category in rental items, so we need to find the item in the equipment list
            const equipment = items.find((e) => e.id === item.equipmentId);
            if (equipment) {
              if (!rStats.byCategory[equipment.category]) {
                rStats.byCategory[equipment.category] = 0;
              }
              rStats.byCategory[equipment.category]++;
            }
          });
        });

        // Calculate rental trend (last 30/60/90 days)
        const days = parseInt(timeRange);
        const startDate = subDays(new Date(), days);

        // Create a map of dates to rental counts
        const dateMap: Record<string, number> = {};
        for (let i = 0; i < days; i++) {
          const date = subDays(new Date(), i);
          dateMap[format(date, 'yyyy-MM-dd')] = 0;
        }

        // Count rentals by date
        rentals.forEach((rental) => {
          const rentalDate = rental.date.toDate();
          if (isAfter(rentalDate, startDate)) {
            const dateStr = format(rentalDate, 'yyyy-MM-dd');
            if (dateMap[dateStr] !== undefined) {
              dateMap[dateStr]++;
            }
          }
        });

        // Convert to array for chart
        const trend = Object.entries(dateMap)
          .map(([date, count]) => ({
            date: format(new Date(date), 'MMM dd'),
            count,
          }))
          .reverse();

        rStats.rentalTrend = trend;

        setRentalStats(rStats);
        setError(null);
      } catch (err) {
        console.error('Error fetching report data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id, t, timeRange]);

  // Handle time range change
  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  // Prepare chart data
  const availabilityData = [
    { name: t('status.available', 'Available'), value: equipmentStats.available },
    { name: t('status.unavailable', 'In Use'), value: equipmentStats.unavailable },
  ];

  const conditionData = [
    { name: t('condition.good', 'Good'), value: equipmentStats.conditions.good },
    { name: t('condition.damaged', 'Damaged'), value: equipmentStats.conditions.damaged },
    {
      name: t('condition.maintenance', 'Needs Maintenance'),
      value: equipmentStats.conditions.maintenance,
    },
    { name: t('condition.lost', 'Lost'), value: equipmentStats.conditions.lost },
  ];

  const categoryData = Object.entries(equipmentStats.categories).map(([category, count]) => ({
    name: t(`category.${category}`, category),
    value: count,
  }));

  const rentalStatusData = [
    { name: t('rental.status.active', 'Active'), value: rentalStats.active },
    { name: t('rental.status.returned', 'Returned'), value: rentalStats.returned },
    { name: t('rental.status.overdue', 'Overdue'), value: rentalStats.overdue },
  ];

  const customerTypeData = Object.entries(rentalStats.byCustomerType).map(([type, count]) => ({
    name: t(`rental.form.${type}Type`, type),
    value: count,
  }));

  return (
    <Box p={3}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('reports.equipment.title', 'Equipment Reports')}
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="time-range-label">
              {t('common:filters.timeRange', 'Time Range')}
            </InputLabel>
            <Select
              labelId="time-range-label"
              value={timeRange}
              onChange={handleTimeRangeChange}
              label={t('common:filters.timeRange', 'Time Range')}
            >
              <MenuItem value="30">{t('common:filters.30days', '30 Days')}</MenuItem>
              <MenuItem value="60">{t('common:filters.60days', '60 Days')}</MenuItem>
              <MenuItem value="90">{t('common:filters.90days', '90 Days')}</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<FileDownloadIcon />}
            onClick={() => alert('Export functionality coming soon')}
          >
            {t('reports.export', 'Export', { ns: 'reports' })}
          </Button>
        </Box>
      </Box>

      {error && <ErrorAlert message={error} />}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Equipment Overview */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                {t('reports.equipment.overview', 'Equipment Overview')}
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Typography variant="h6" align="center" gutterBottom>
                    {t('dashboard.availability', 'Availability')}
                  </Typography>
                  <Box sx={{ height: 250 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={availabilityData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={90}
                          paddingAngle={2}
                          dataKey="value"
                          label={({ name, percent }: { name: string; percent: number }) =>
                            `${name}: ${(percent * 100).toFixed(0)}%`
                          }
                          labelLine={false}
                        >
                          {availabilityData.map((_entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={index === 0 ? '#4caf50' : '#ff9800'}
                            />
                          ))}
                        </Pie>
                        <RechartsTooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="h6" align="center" gutterBottom>
                    {t('dashboard.condition', 'Condition')}
                  </Typography>
                  <Box sx={{ height: 250 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={conditionData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={90}
                          paddingAngle={2}
                          dataKey="value"
                          label={({ name, percent }: { name: string; percent: number }) =>
                            percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                          }
                          labelLine={false}
                        >
                          {conditionData.map((_entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <RechartsTooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="h6" align="center" gutterBottom>
                    {t('dashboard.categories', 'Categories')}
                  </Typography>
                  <Box sx={{ height: 250 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={categoryData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={90}
                          paddingAngle={2}
                          dataKey="value"
                          label={({ name, percent }: { name: string; percent: number }) =>
                            percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                          }
                          labelLine={false}
                        >
                          {categoryData.map((_entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <RechartsTooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Rental Trends */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                {t('reports.equipment.rentalTrends', 'Rental Trends')}
              </Typography>
              <Box sx={{ height: 300, mt: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={rentalStats.rentalTrend}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar
                      dataKey="count"
                      name={t('reports.equipment.rentalCount', 'Rental Count')}
                      fill="#8884d8"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </Grid>

          {/* Rental Statistics */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h5" gutterBottom>
                {t('reports.equipment.rentalStatus', 'Rental Status')}
              </Typography>
              <Box sx={{ height: 250, mt: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={rentalStatusData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={90}
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }: { name: string; percent: number }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                      labelLine={false}
                    >
                      <Cell fill="#4caf50" /> {/* Active */}
                      <Cell fill="#2196f3" /> {/* Returned */}
                      <Cell fill="#f44336" /> {/* Overdue */}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </Grid>

          {/* Customer Types */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h5" gutterBottom>
                {t('reports.equipment.customerTypes', 'Customer Types')}
              </Typography>
              <Box sx={{ height: 250, mt: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={customerTypeData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={90}
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }: { name: string; percent: number }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                      labelLine={false}
                    >
                      {customerTypeData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default EquipmentReports;
