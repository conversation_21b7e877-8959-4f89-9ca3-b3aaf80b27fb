import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import AttendanceReports from './AttendanceReports';
import { useSchool } from '../../hooks/useSchool';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';

// Mock the hooks and Firebase functions
jest.mock('../../hooks/useSchool');
jest.mock('firebase/firestore');
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, fallback: string) => fallback,
    i18n: {
      changeLanguage: jest.fn(),
    },
  }),
}));

// Mock the recharts components
jest.mock('recharts', () => {
  const OriginalModule = jest.requireActual('recharts');
  return {
    ...OriginalModule,
    ResponsiveContainer: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="responsive-container">{children}</div>
    ),
    BarChart: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="bar-chart">{children}</div>
    ),
    PieChart: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="pie-chart">{children}</div>
    ),
    LineChart: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="line-chart">{children}</div>
    ),
    Bar: () => <div data-testid="bar" />,
    Pie: () => <div data-testid="pie" />,
    Line: () => <div data-testid="line" />,
    XAxis: () => <div data-testid="x-axis" />,
    YAxis: () => <div data-testid="y-axis" />,
    CartesianGrid: () => <div data-testid="cartesian-grid" />,
    Legend: () => <div data-testid="legend" />,
    Cell: () => <div data-testid="cell" />,
  };
});

describe('AttendanceReports', () => {
  beforeEach(() => {
    // Mock useSchool hook
    (useSchool as jest.Mock).mockReturnValue({
      currentSchool: { id: 'school-1', name: 'Test School' },
    });

    // Mock Firebase functions
    (collection as jest.Mock).mockReturnValue({});
    (query as jest.Mock).mockReturnValue({});
    (orderBy as jest.Mock).mockReturnValue({});
    (getDocs as jest.Mock).mockResolvedValue({
      docs: [],
    });
  });

  it('renders loading state initially', () => {
    render(<AttendanceReports />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('displays attendance reports title', async () => {
    render(<AttendanceReports />);
    await waitFor(() => {
      expect(screen.getByText('Attendance Reports')).toBeInTheDocument();
    });
  });

  it('displays tabs for programs and students', async () => {
    render(<AttendanceReports />);
    await waitFor(() => {
      expect(screen.getByText('Programs')).toBeInTheDocument();
      expect(screen.getByText('Students')).toBeInTheDocument();
    });
  });

  it('displays no data message when no attendance data is available', async () => {
    render(<AttendanceReports />);
    await waitFor(() => {
      expect(screen.getByText('No attendance data available for the selected filters')).toBeInTheDocument();
    });
  });
});
