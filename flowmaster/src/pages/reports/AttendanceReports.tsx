import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Program, ProgramSession } from '../../types/program';
import { Student } from '../../types/student';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  LinearProgress,
  SelectChangeEvent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  <PERSON>,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'recharts';
import {
  Download as DownloadIcon,
  FilterList as FilterIcon,
  Person as PersonIcon,
  School as SchoolIcon,
} from '@mui/icons-material';

// Interface for TabPanel props
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// TabPanel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`attendance-tabpanel-${index}`}
      aria-labelledby={`attendance-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Interface for attendance data
interface AttendanceData {
  programId: string;
  programName: string;
  totalSessions: number;
  totalStudents: number;
  attendanceRate: number;
  presentCount: number;
  absentCount: number;
  excusedCount: number;
  studentAttendance: Record<
    string,
    { present: number; absent: number; excused: number; total: number }
  >;
}

// Interface for student attendance data
interface StudentAttendanceData {
  studentId: string;
  studentName: string;
  totalSessions: number;
  presentCount: number;
  absentCount: number;
  excusedCount: number;
  attendanceRate: number;
  programs: string[];
}

// Interface for filter options
interface FilterOptions {
  startDate: Date | null;
  endDate: Date | null;
  programId: string;
  studentId: string;
}

const AttendanceReports: React.FC = () => {
  const { t } = useTranslation(['common', 'programs', 'reports']);
  const { currentSchool } = useSchool();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([]);
  const [studentAttendanceData, setStudentAttendanceData] = useState<StudentAttendanceData[]>([]);
  const [filters, setFilters] = useState<FilterOptions>({
    startDate: null,
    endDate: null,
    programId: 'all',
    studentId: 'all',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  // COLORS for charts
  const COLORS = ['#4caf50', '#f44336', '#ff9800'];

  // Fetch attendance data
  const fetchAttendanceData = useCallback(
    async (programsList: Program[], studentsList: Student[]) => {
      if (!currentSchool?.id) return;

      try {
        const attendanceResults: AttendanceData[] = [];
        const studentResults: Record<string, StudentAttendanceData> = {};

        // Initialize student attendance data
        studentsList.forEach((student) => {
          studentResults[student.id] = {
            studentId: student.id,
            studentName: `${student.firstName} ${student.lastName}`,
            totalSessions: 0,
            presentCount: 0,
            absentCount: 0,
            excusedCount: 0,
            attendanceRate: 0,
            programs: [],
          };
        });

        // Process each program
        for (const program of programsList) {
          // Skip if program filter is applied and doesn't match
          if (filters.programId !== 'all' && filters.programId !== program.id) {
            continue;
          }

          const sessionsRef = collection(
            db,
            'schools',
            currentSchool.id,
            'programs',
            program.id,
            'sessions'
          );

          // Apply date filters if provided
          let sessionsQuery = query(sessionsRef, orderBy('date'));
          if (filters.startDate) {
            sessionsQuery = query(sessionsQuery, where('date', '>=', filters.startDate));
          }
          if (filters.endDate) {
            sessionsQuery = query(sessionsQuery, where('date', '<=', filters.endDate));
          }

          const sessionsSnapshot = await getDocs(sessionsQuery);
          const sessions = sessionsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as ProgramSession[];

          // Skip if no sessions found
          if (sessions.length === 0) {
            continue;
          }

          // Initialize program attendance data
          const programAttendance: AttendanceData = {
            programId: program.id,
            programName: program.name,
            totalSessions: sessions.length,
            totalStudents: program.participants?.length || 0,
            attendanceRate: 0,
            presentCount: 0,
            absentCount: 0,
            excusedCount: 0,
            studentAttendance: {},
          };

          // Initialize student attendance for this program
          program.participants?.forEach((studentId) => {
            // Skip if student filter is applied and doesn't match
            if (filters.studentId !== 'all' && filters.studentId !== studentId) {
              return;
            }

            programAttendance.studentAttendance[studentId] = {
              present: 0,
              absent: 0,
              excused: 0,
              total: sessions.length,
            };

            // Add program to student's programs list
            if (studentResults[studentId]) {
              studentResults[studentId].programs.push(program.name);
            }
          });

          // Process each session
          sessions.forEach((session) => {
            if (!session.attendance) return;

            // Process attendance for each student
            Object.entries(session.attendance).forEach(([studentId, status]) => {
              // Skip if student filter is applied and doesn't match
              if (filters.studentId !== 'all' && filters.studentId !== studentId) {
                return;
              }

              // Update program attendance counts
              if (status === 'present') {
                programAttendance.presentCount++;
                if (programAttendance.studentAttendance[studentId]) {
                  programAttendance.studentAttendance[studentId].present++;
                }
              } else if (status === 'absent') {
                programAttendance.absentCount++;
                if (programAttendance.studentAttendance[studentId]) {
                  programAttendance.studentAttendance[studentId].absent++;
                }
              } else if (status === 'excused') {
                programAttendance.excusedCount++;
                if (programAttendance.studentAttendance[studentId]) {
                  programAttendance.studentAttendance[studentId].excused++;
                }
              }

              // Update student attendance data
              if (studentResults[studentId]) {
                studentResults[studentId].totalSessions++;
                if (status === 'present') {
                  studentResults[studentId].presentCount++;
                } else if (status === 'absent') {
                  studentResults[studentId].absentCount++;
                } else if (status === 'excused') {
                  studentResults[studentId].excusedCount++;
                }
              }
            });
          });

          // Calculate program attendance rate
          const totalAttendanceRecords =
            programAttendance.presentCount +
            programAttendance.absentCount +
            programAttendance.excusedCount;

          programAttendance.attendanceRate =
            totalAttendanceRecords > 0
              ? (programAttendance.presentCount / totalAttendanceRecords) * 100
              : 0;

          attendanceResults.push(programAttendance);
        }

        // Calculate attendance rates for students
        Object.values(studentResults).forEach((student) => {
          student.attendanceRate =
            student.totalSessions > 0 ? (student.presentCount / student.totalSessions) * 100 : 0;
        });

        // Filter out students with no sessions if needed
        const filteredStudentResults = Object.values(studentResults).filter(
          (student) => student.totalSessions > 0
        );

        setAttendanceData(attendanceResults);
        setStudentAttendanceData(filteredStudentResults);
      } catch (err) {
        console.error('Error fetching attendance data:', err);
        setError(t('messages.fetchError', 'Failed to fetch attendance data', { ns: 'common' }));
      }
    },
    [currentSchool?.id, filters.programId, filters.studentId, filters.startDate, filters.endDate, t]
  );

  // Fetch programs and students
  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch programs
        const programsRef = collection(db, 'schools', currentSchool.id, 'programs');
        const programsQuery = query(programsRef, orderBy('name'));
        const programsSnapshot = await getDocs(programsQuery);
        const programsData = programsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Program[];
        setPrograms(programsData);

        // Fetch students
        const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
        const studentsQuery = query(studentsRef, orderBy('firstName'));
        const studentsSnapshot = await getDocs(studentsQuery);
        const studentsData = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Student[];
        setStudents(studentsData);

        // Fetch attendance data
        await fetchAttendanceData(programsData, studentsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id, t, fetchAttendanceData]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle filter changes
  const handleFilterChange = (field: keyof FilterOptions, value: string | Date | null) => {
    setFilters({
      ...filters,
      [field]: value,
    });
  };

  // Apply filters
  const applyFilters = async () => {
    if (!currentSchool?.id) return;

    setLoading(true);
    try {
      await fetchAttendanceData(programs, students);
    } catch (err) {
      console.error('Error applying filters:', err);
      setError(t('messages.filterError', 'Failed to apply filters', { ns: 'common' }));
    } finally {
      setLoading(false);
    }
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      startDate: null,
      endDate: null,
      programId: 'all',
      studentId: 'all',
    });
  };

  // Export attendance data to CSV
  const exportAttendanceData = () => {
    setExportLoading(true);

    try {
      let csvContent = '';

      // Different export format based on active tab
      if (tabValue === 0) {
        // Program attendance export
        csvContent =
          'Program,Total Sessions,Total Students,Present,Absent,Excused,Attendance Rate\n';

        attendanceData.forEach((program) => {
          csvContent += `"${program.programName}",${program.totalSessions},${program.totalStudents},${program.presentCount},${program.absentCount},${program.excusedCount},${program.attendanceRate.toFixed(2)}%\n`;
        });
      } else {
        // Student attendance export
        csvContent = 'Student,Total Sessions,Present,Absent,Excused,Attendance Rate,Programs\n';

        studentAttendanceData.forEach((student) => {
          csvContent += `"${student.studentName}",${student.totalSessions},${student.presentCount},${student.absentCount},${student.excusedCount},${student.attendanceRate.toFixed(2)}%,"${student.programs.join(', ')}"\n`;
        });
      }

      // Create download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `attendance_report_${new Date().toISOString().split('T')[0]}.csv`
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error('Error exporting data:', err);
      setError(t('messages.exportError', 'Failed to export data', { ns: 'common' }));
    } finally {
      setExportLoading(false);
    }
  };

  // Prepare chart data for programs
  const programChartData = useMemo(() => {
    return attendanceData.map((program) => ({
      name: program.programName,
      present: program.presentCount,
      absent: program.absentCount,
      excused: program.excusedCount,
      attendanceRate: program.attendanceRate,
    }));
  }, [attendanceData]);

  // Prepare pie chart data for overall attendance
  const overallPieData = useMemo(() => {
    const totalPresent = attendanceData.reduce((sum, program) => sum + program.presentCount, 0);
    const totalAbsent = attendanceData.reduce((sum, program) => sum + program.absentCount, 0);
    const totalExcused = attendanceData.reduce((sum, program) => sum + program.excusedCount, 0);

    return [
      { name: t('programs:attendance.present', 'Present'), value: totalPresent },
      { name: t('programs:attendance.absent', 'Absent'), value: totalAbsent },
      { name: t('programs:attendance.excused', 'Excused'), value: totalExcused },
    ];
  }, [attendanceData, t]);

  // Prepare chart data for students
  const studentChartData = useMemo(() => {
    return studentAttendanceData
      .sort((a, b) => b.attendanceRate - a.attendanceRate)
      .slice(0, 10) // Top 10 students
      .map((student) => ({
        name: student.studentName,
        present: student.presentCount,
        absent: student.absentCount,
        excused: student.excusedCount,
        attendanceRate: student.attendanceRate,
      }));
  }, [studentAttendanceData]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
            }}
          >
            {t('reports.attendance.title', 'Attendance Reports')}
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
            >
              {t('common:actions.filter', 'Filter')}
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={exportAttendanceData}
              disabled={exportLoading || loading || attendanceData.length === 0}
            >
              {exportLoading ? (
                <CircularProgress size={24} />
              ) : (
                t('common:actions.export', 'Export')
              )}
            </Button>
          </Box>
        </Box>

        {/* Filters */}
        {showFilters && (
          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common:filters.startDate', 'Start Date')}
                  value={filters.startDate}
                  onChange={(date) => handleFilterChange('startDate', date)}
                  slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common:filters.endDate', 'End Date')}
                  value={filters.endDate}
                  onChange={(date) => handleFilterChange('endDate', date)}
                  slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel id="program-filter-label">
                    {t('common:filters.program', 'Program')}
                  </InputLabel>
                  <Select
                    labelId="program-filter-label"
                    id="program-filter"
                    value={filters.programId}
                    label={t('common:filters.program', 'Program')}
                    onChange={(e: SelectChangeEvent) =>
                      handleFilterChange('programId', e.target.value)
                    }
                  >
                    <MenuItem value="all">{t('common:filters.all', 'All')}</MenuItem>
                    {programs.map((program) => (
                      <MenuItem key={program.id} value={program.id}>
                        {program.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel id="student-filter-label">
                    {t('common:filters.student', 'Student')}
                  </InputLabel>
                  <Select
                    labelId="student-filter-label"
                    id="student-filter"
                    value={filters.studentId}
                    label={t('common:filters.student', 'Student')}
                    onChange={(e: SelectChangeEvent) =>
                      handleFilterChange('studentId', e.target.value)
                    }
                  >
                    <MenuItem value="all">{t('common:filters.all', 'All')}</MenuItem>
                    {students.map((student) => (
                      <MenuItem key={student.id} value={student.id}>
                        {`${student.firstName} ${student.lastName}`}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button variant="contained" color="primary" onClick={applyFilters} fullWidth>
                    {t('common:actions.apply', 'Apply')}
                  </Button>
                  <Button variant="outlined" onClick={resetFilters}>
                    {t('common:actions.reset', 'Reset')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="attendance report tabs">
            <Tab
              label={t('reports.attendance.programsTab', 'Programs')}
              icon={<SchoolIcon />}
              iconPosition="start"
            />
            <Tab
              label={t('reports.attendance.studentsTab', 'Students')}
              icon={<PersonIcon />}
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        ) : (
          <>
            {/* Programs Tab */}
            <TabPanel value={tabValue} index={0}>
              {attendanceData.length === 0 ? (
                <Alert severity="info">
                  {t(
                    'reports.attendance.noData',
                    'No attendance data available for the selected filters'
                  )}
                </Alert>
              ) : (
                <Box>
                  {/* Overall Statistics */}
                  <Paper sx={{ p: 2, mb: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      {t('reports.attendance.overallStatistics', 'Overall Statistics')}
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={8}>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart
                            data={programChartData}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <RechartsTooltip />
                            <Legend />
                            <Bar
                              dataKey="present"
                              name={t('programs:attendance.present', 'Present')}
                              fill="#4caf50"
                            />
                            <Bar
                              dataKey="absent"
                              name={t('programs:attendance.absent', 'Absent')}
                              fill="#f44336"
                            />
                            <Bar
                              dataKey="excused"
                              name={t('programs:attendance.excused', 'Excused')}
                              fill="#ff9800"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={overallPieData}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }: { name: string; percent: number }) =>
                                `${name}: ${(percent * 100).toFixed(0)}%`
                              }
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {overallPieData.map((_entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <RechartsTooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Programs Table */}
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      {t('reports.attendance.programsDetail', 'Programs Detail')}
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow sx={{ bgcolor: 'primary.main' }}>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:fields.name', 'Program')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('reports.attendance.totalSessions', 'Total Sessions')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('reports.attendance.totalStudents', 'Total Students')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:attendance.present', 'Present')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:attendance.absent', 'Absent')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:attendance.excused', 'Excused')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('reports.attendance.rate', 'Attendance Rate')}
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {attendanceData.map((program) => (
                            <TableRow key={program.programId}>
                              <TableCell>{program.programName}</TableCell>
                              <TableCell>{program.totalSessions}</TableCell>
                              <TableCell>{program.totalStudents}</TableCell>
                              <TableCell>{program.presentCount}</TableCell>
                              <TableCell>{program.absentCount}</TableCell>
                              <TableCell>{program.excusedCount}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Box sx={{ width: '100%', mr: 1 }}>
                                    <LinearProgress
                                      variant="determinate"
                                      value={program.attendanceRate}
                                      color={
                                        program.attendanceRate >= 90
                                          ? 'success'
                                          : program.attendanceRate >= 75
                                            ? 'info'
                                            : program.attendanceRate >= 60
                                              ? 'warning'
                                              : 'error'
                                      }
                                      sx={{ height: 10, borderRadius: 5 }}
                                    />
                                  </Box>
                                  <Box sx={{ minWidth: 35 }}>
                                    <Typography variant="body2" color="text.secondary">
                                      {`${Math.round(program.attendanceRate)}%`}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Paper>
                </Box>
              )}
            </TabPanel>

            {/* Students Tab */}
            <TabPanel value={tabValue} index={1}>
              {studentAttendanceData.length === 0 ? (
                <Alert severity="info">
                  {t(
                    'reports.attendance.noData',
                    'No attendance data available for the selected filters'
                  )}
                </Alert>
              ) : (
                <Box>
                  {/* Top Students Chart */}
                  <Paper sx={{ p: 2, mb: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      {t('reports.attendance.topStudents', 'Top Students by Attendance')}
                    </Typography>
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart
                        data={studentChartData}
                        layout="vertical"
                        margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" domain={[0, 100]} />
                        <YAxis type="category" dataKey="name" width={100} />
                        <RechartsTooltip />
                        <Legend />
                        <Bar
                          dataKey="attendanceRate"
                          name={t('reports.attendance.rate', 'Attendance Rate')}
                          fill="#2196f3"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </Paper>

                  {/* Students Table */}
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      {t('reports.attendance.studentsDetail', 'Students Detail')}
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow sx={{ bgcolor: 'primary.main' }}>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('students.fields.firstName', 'Student', { ns: 'people' })}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('reports.attendance.totalSessions', 'Total Sessions')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:attendance.present', 'Present')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:attendance.absent', 'Absent')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('programs:attendance.excused', 'Excused')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('reports.attendance.rate', 'Attendance Rate')}
                            </TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                              {t('students.fields.programs', 'Programs', { ns: 'people' })}
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {studentAttendanceData.map((student) => (
                            <TableRow key={student.studentId}>
                              <TableCell>{student.studentName}</TableCell>
                              <TableCell>{student.totalSessions}</TableCell>
                              <TableCell>{student.presentCount}</TableCell>
                              <TableCell>{student.absentCount}</TableCell>
                              <TableCell>{student.excusedCount}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Box sx={{ width: '100%', mr: 1 }}>
                                    <LinearProgress
                                      variant="determinate"
                                      value={student.attendanceRate}
                                      color={
                                        student.attendanceRate >= 90
                                          ? 'success'
                                          : student.attendanceRate >= 75
                                            ? 'info'
                                            : student.attendanceRate >= 60
                                              ? 'warning'
                                              : 'error'
                                      }
                                      sx={{ height: 10, borderRadius: 5 }}
                                    />
                                  </Box>
                                  <Box sx={{ minWidth: 35 }}>
                                    <Typography variant="body2" color="text.secondary">
                                      {`${Math.round(student.attendanceRate)}%`}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                                  {student.programs.map((program, index) => (
                                    <Chip
                                      key={index}
                                      label={program}
                                      size="small"
                                      sx={{
                                        bgcolor: 'primary.light',
                                        color: 'white',
                                        fontSize: '0.75rem',
                                      }}
                                    />
                                  ))}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Paper>
                </Box>
              )}
            </TabPanel>
          </>
        )}
      </Box>
    </Container>
  );
};

export default AttendanceReports;
