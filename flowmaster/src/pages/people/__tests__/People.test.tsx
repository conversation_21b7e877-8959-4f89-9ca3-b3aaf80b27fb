import { render, screen, fireEvent } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import People from '../People';

// Mock the required hooks
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'categories.students': 'Students',
        'categories.instructors': 'Instructors',
        'categories.clients': 'Clients',
        'descriptions.students': 'Manage student profiles and progress',
        'descriptions.instructors': 'Manage instructor assignments and schedules',
        'descriptions.clients': 'Manage client information and preferences',
        title: 'People Management',
        subtitle: 'Manage students, instructors, and clients',
      };
      return translations[key] || key;
    },
  }),
}));

describe('People Component', () => {
  let mockNavigate: jest.Mock;

  beforeEach(() => {
    mockNavigate = jest.fn();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
  });

  it('renders the component with correct title and subtitle', () => {
    render(<People />);

    expect(screen.getByText('People Management')).toBeInTheDocument();
    expect(screen.getByText('Manage students, instructors, and clients')).toBeInTheDocument();
  });

  it('displays all three category cards with correct information', () => {
    render(<People />);

    // Check if all category titles are present
    expect(screen.getByText('Students')).toBeInTheDocument();
    expect(screen.getByText('Instructors')).toBeInTheDocument();
    expect(screen.getByText('Clients')).toBeInTheDocument();

    // Check if all category descriptions are present
    expect(screen.getByText('Manage student profiles and progress')).toBeInTheDocument();
    expect(screen.getByText('Manage instructor assignments and schedules')).toBeInTheDocument();
    expect(screen.getByText('Manage client information and preferences')).toBeInTheDocument();
  });

  it('navigates to correct paths when cards are clicked', () => {
    render(<People />);

    // Click on Students card and verify navigation
    fireEvent.click(screen.getByText('Students'));
    expect(mockNavigate).toHaveBeenCalledWith('/people/students');

    // Click on Instructors card and verify navigation
    fireEvent.click(screen.getByText('Instructors'));
    expect(mockNavigate).toHaveBeenCalledWith('/people/instructors');

    // Click on Clients card and verify navigation
    fireEvent.click(screen.getByText('Clients'));
    expect(mockNavigate).toHaveBeenCalledWith('/people/clients');
  });

  it('renders icons for each category', () => {
    render(<People />);

    // Check if the correct number of icons are rendered
    const icons = document.querySelectorAll('svg');
    // 4 icons: 1 header icon + 3 category icons
    expect(icons).toHaveLength(4);
  });
});
