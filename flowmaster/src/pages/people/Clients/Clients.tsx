import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import ClientForm from './ClientForm';
import ConfirmationDialog from '../../../components/common/ConfirmationDialog';
import type { Client } from '../../../types/client';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Tooltip,
  Chip,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useAuth } from '../../../hooks/useAuth';
import { useSchool } from '../../../hooks/useSchool';
import { useNavigate } from 'react-router-dom';

const Clients: React.FC = () => {
  const { t } = useTranslation(['people', 'common']);
  const { user } = useAuth();
  const { currentSchool, loading: schoolLoading } = useSchool();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Check if user has admin or manager role
  const canManageClients = user?.role === 'admin' || user?.role === 'manager';

  const fetchClients = useCallback(async () => {
    if (!currentSchool?.id) return;

    setLoading(true);
    try {
      const clientsCollection = collection(db, 'schools', currentSchool.id, 'clients');
      const clientsSnapshot = await getDocs(clientsCollection);
      const clientsData = clientsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Client[];

      setClients(clientsData);
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setLoading(false);
    }
  }, [currentSchool?.id]);

  useEffect(() => {
    if (!schoolLoading) {
      fetchClients();
    }
  }, [fetchClients, schoolLoading]);

  const [selectedClient, setSelectedClient] = useState<Client | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<Client | null>(null);

  const handleAddClient = () => {
    if (!canManageClients) {
      console.warn('Unauthorized access attempt to add client');
      return;
    }
    setSelectedClient(undefined);
    setIsFormOpen(true);
  };

  const handleEditClient = (client: Client) => {
    if (!canManageClients) {
      console.warn('Unauthorized access attempt to edit client:', client.id);
      return;
    }
    const { id, ...clientData } = client;
    setSelectedClient({ ...clientData, id });
    setIsFormOpen(true);
  };

  const handleDeleteClick = (client: Client) => {
    if (!canManageClients) {
      console.warn('Unauthorized access attempt to delete client:', client.id);
      return;
    }
    setClientToDelete(client);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!canManageClients || !currentSchool?.id || !clientToDelete) {
      return;
    }
    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'clients', clientToDelete.id));
      setClients(clients.filter((c) => c.id !== clientToDelete.id));
      setDeleteConfirmOpen(false);
      setClientToDelete(null);
    } catch (error) {
      console.error('Error deleting client:', error);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
            }}
          >
            {t('clients.title')}
          </Typography>
          {canManageClients && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddClient}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
              }}
            >
              {t('clients.addClient')}
            </Button>
          )}
        </Box>
        {loading || schoolLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer
            component={Paper}
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              overflow: 'hidden',
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('clients.fields.firstName')} / {t('clients.fields.lastName')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('clients.fields.email')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('clients.fields.phone')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('clients.fields.disciplines')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('clients.fields.status')}
                  </TableCell>
                  {canManageClients && (
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>
                      {t('ui.actions', { ns: 'common' })}
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {clients.map((client) => (
                  <TableRow
                    key={client.id}
                    sx={{
                      '&:nth-of-type(odd)': { bgcolor: 'action.hover' },
                      '&:hover': { bgcolor: 'action.selected' },
                      transition: 'background-color 0.2s ease',
                    }}
                  >
                    <TableCell>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          cursor: 'pointer',
                          '&:hover': {
                            textDecoration: 'underline',
                            color: 'primary.main',
                          },
                        }}
                        onClick={() => navigate(`/people/clients/${client.id}`)}
                      >
                        {`${client.firstName} ${client.lastName}`}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ color: 'primary.main' }}>
                        {client.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{client.phone || '-'}</Typography>
                    </TableCell>
                    <TableCell>
                      {client.specialties?.length ? (
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                          {client.specialties.map((specialty) => (
                            <Chip
                              key={specialty}
                              label={specialty}
                              size="small"
                              sx={{
                                bgcolor: 'primary.light',
                                color: 'white',
                                fontSize: '0.75rem',
                              }}
                            />
                          ))}
                        </Box>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={client.status}
                        size="small"
                        sx={{
                          bgcolor: client.status === 'active' ? 'success.light' : 'error.light',
                          color: 'white',
                          textTransform: 'capitalize',
                          fontWeight: 500,
                        }}
                      />
                    </TableCell>
                    {canManageClients && (
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                          <Tooltip title={t('ui.edit', 'Edit', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleEditClient(client)}
                              size="small"
                              sx={{
                                color: 'primary.main',
                                '&:hover': { bgcolor: 'primary.light', color: 'white' },
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={t('ui.delete', 'Delete', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleDeleteClick(client)}
                              size="small"
                              sx={{
                                color: 'error.main',
                                '&:hover': { bgcolor: 'error.light', color: 'white' },
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
      {isFormOpen && (
        <ClientForm
          open={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setSelectedClient(undefined);
            // Refresh the clients list after closing
            if (currentSchool?.id) {
              fetchClients();
            }
          }}
          client={selectedClient}
        />
      )}
      <ConfirmationDialog
        open={deleteConfirmOpen}
        title={t('deleteConfirmation.title', { ns: 'common' })}
        message={t('deleteConfirmation.message', {
          ns: 'common',
          item: clientToDelete ? `${clientToDelete.firstName} ${clientToDelete.lastName}` : '',
        })}
        onConfirm={handleDeleteConfirm}
        onCancel={() => {
          setDeleteConfirmOpen(false);
          setClientToDelete(null);
        }}
      />
    </Container>
  );
};

export default Clients;
