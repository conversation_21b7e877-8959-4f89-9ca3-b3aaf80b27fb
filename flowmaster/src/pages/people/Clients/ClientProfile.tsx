import React from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { doc, getDoc } from 'firebase/firestore';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Divider,
  Avatar,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { db } from '../../../services/firebase';
import { useSchool } from '../../../hooks/useSchool';
import type { Client } from '../../../types/client';
import ClientRentals from './ClientRentals';

const ClientProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['people', 'common']);
  const { currentSchool, loading: schoolLoading } = useSchool();
  const [client, setClient] = React.useState<Client | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchClient = async () => {
      if (!currentSchool?.id || !id) return;

      try {
        setLoading(true);
        const clientRef = doc(db, 'schools', currentSchool.id, 'clients', id);
        const clientDoc = await getDoc(clientRef);

        if (clientDoc.exists()) {
          setClient({ id: clientDoc.id, ...clientDoc.data() } as Client);
        } else {
          setError(t('clients.notFound', 'Client not found'));
        }
      } catch (err) {
        console.error('Error fetching client:', err);
        setError(t('common:error.fetch', 'Failed to fetch client data'));
      } finally {
        setLoading(false);
      }
    };

    if (!schoolLoading) {
      fetchClient();
    }
  }, [currentSchool?.id, id, t, schoolLoading]);

  if (loading || schoolLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !client) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          {t('common:ui.back')}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mb: 4 }}>
        {t('common:ui.back')}
      </Button>

      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '3rem',
                }}
              >
                {client.firstName?.[0]}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {`${client.firstName} ${client.lastName}`}
              </Typography>
              <Chip
                label={client.status}
                size="small"
                sx={{
                  bgcolor: client.status === 'active' ? 'success.light' : 'error.light',
                  color: 'white',
                  textTransform: 'capitalize',
                  mb: 2,
                }}
              />
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                {t('clients.fields.email')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {client.email}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('clients.fields.phone')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {client.phone || '-'}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('clients.fields.disciplines')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {client.specialties?.length ? (
                  client.specialties.map((specialty) => (
                    <Chip
                      key={specialty}
                      label={specialty}
                      size="small"
                      sx={{
                        bgcolor: 'primary.light',
                        color: 'white',
                        fontSize: '0.75rem',
                      }}
                    />
                  ))
                ) : (
                  <Typography variant="body1">-</Typography>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('clients.upcomingLessons', 'Upcoming Lessons')}
            </Typography>
            {/* TODO: Add upcoming lessons section */}
            <Typography variant="body1" color="text.secondary">
              {t('common:ui.comingSoon', 'Coming soon')}
            </Typography>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('clients.equipmentRentals', 'Equipment Rentals')}
            </Typography>
            <ClientRentals clientId={id} />
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('clients.recentActivity', 'Recent Activity')}
            </Typography>
            {/* TODO: Add recent activity section */}
            <Typography variant="body1" color="text.secondary">
              {t('common:ui.comingSoon', 'Coming soon')}
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ClientProfile;
