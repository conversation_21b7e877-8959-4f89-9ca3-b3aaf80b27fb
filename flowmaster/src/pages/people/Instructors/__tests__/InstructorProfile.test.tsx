import { render, screen, waitFor } from '@testing-library/react';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc } from 'firebase/firestore';
import InstructorProfile from '../InstructorProfile';
import { useSchool } from '../../../../hooks/useSchool';
import type { Instructor } from '../../../../types/instructor';

// Mock Firebase services
jest.mock('../../../../services/firebase', () => ({
  db: {},
  auth: {},
  storage: {},
}));

// Mock the required hooks and Firebase functions
jest.mock('react-router-dom', () => ({
  useParams: jest.fn(),
  useNavigate: jest.fn(),
}));

jest.mock('../../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../../hooks/useSchool', () => ({
  useSchool: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  doc: jest.fn(),
  getDoc: jest.fn(),
}));

// Mock console.error to prevent noise in test output
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});

// Mock the i18next translation
jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (key: string) => {
        // Handle namespace-specific translations
        if (key.startsWith('common:')) {
          const commonTranslations: { [key: string]: string } = {
            'common:ui.back': 'Back',
            'common:error.fetch': 'Failed to fetch instructor data',
          };
          return commonTranslations[key] || key;
        }
        // Default translations
        const translations: { [key: string]: string } = {
          'instructors.notFound': 'Instructor not found',
          'instructors.fields.email': 'Email',
          'instructors.fields.phone': 'Phone',
          'instructors.fields.specialties': 'Specialties',
          'instructors.totalLessons': 'Total Lessons',
          'instructors.lessonStatistics': 'Lesson Statistics',
          'instructors.assignedLessons': 'Assigned Lessons',
        };
        return translations[key] || key;
      },
    };
  },
}));

// Mock the components we don't want to test
jest.mock('../../../../components/scheduling/UnavailabilityCalendar', () => {
  return function MockUnavailabilityCalendar() {
    return <div data-testid="unavailability-calendar">Calendar Mock</div>;
  };
});

jest.mock('../../../../components/scheduling/StudentRelationships', () => {
  return function MockStudentRelationships() {
    return <div data-testid="student-relationships">Student Relationships Mock</div>;
  };
});

describe('InstructorProfile', () => {
  const mockNavigate = jest.fn();
  const mockInstructor: Instructor = {
    id: 'instructor-1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '************',
    specialties: ['Yoga', 'Pilates'],
    status: 'active',
  };

  const setupComponent = () => {
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (useParams as jest.Mock).mockReturnValue({ id: 'instructor-1' });
    (useSchool as jest.Mock).mockReturnValue({
      currentSchool: { id: 'school-1' },
    });

    const mockDocRef = {};
    (doc as jest.Mock).mockReturnValue(mockDocRef);
  };

  beforeEach(() => {
    jest.clearAllMocks();
    setupComponent();
  });

  it('renders loading state initially', () => {
    (getDoc as jest.Mock).mockImplementation(() => new Promise(() => {}));
    render(<InstructorProfile />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders instructor details after loading', async () => {
    const mockDocSnap = {
      exists: () => true,
      data: () => ({ ...mockInstructor }),
      id: mockInstructor.id,
    };
    (getDoc as jest.Mock).mockResolvedValueOnce(mockDocSnap);

    render(<InstructorProfile />);

    // Wait for loading to complete and verify loading indicator is gone
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Verify instructor details using test-ids or specific text content
    await waitFor(() => {
      // Basic information
      expect(screen.getByTestId('instructor-name')).toHaveTextContent(
        `${mockInstructor.firstName} ${mockInstructor.lastName}`
      );
      expect(screen.getByTestId('instructor-email')).toHaveTextContent(mockInstructor.email);
      expect(screen.getByTestId('instructor-phone')).toHaveTextContent(mockInstructor.phone);

      // Status chip
      const statusElement = screen.getByTestId('instructor-status');
      expect(statusElement).toHaveTextContent(mockInstructor.status);
      expect(statusElement).toHaveStyle({ backgroundColor: expect.stringContaining('success') });

      // Specialties
      const specialtiesContainer = screen.getByTestId('instructor-specialties');
      mockInstructor.specialties.forEach((specialty) => {
        expect(specialtiesContainer).toHaveTextContent(specialty);
      });
    });

    // Verify sub-components are rendered
    expect(screen.getByTestId('unavailability-calendar')).toBeInTheDocument();
    expect(screen.getByTestId('student-relationships')).toBeInTheDocument();
  });

  it('handles instructor not found error', async () => {
    const mockDocSnap = {
      exists: () => false,
    };
    (getDoc as jest.Mock).mockResolvedValueOnce(mockDocSnap);

    render(<InstructorProfile />);

    await waitFor(() => {
      expect(screen.getByText('Instructor not found')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back/i })).toBeInTheDocument();
    });
  });

  it('handles fetch error', async () => {
    (getDoc as jest.Mock).mockRejectedValueOnce(new Error('Fetch failed'));

    render(<InstructorProfile />);

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch instructor data')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back/i })).toBeInTheDocument();
    });
  });

  it('navigates back when back button is clicked', async () => {
    const mockDocSnap = {
      exists: () => true,
      data: () => ({ ...mockInstructor }),
      id: mockInstructor.id,
    };
    (getDoc as jest.Mock).mockResolvedValueOnce(mockDocSnap);

    render(<InstructorProfile />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    const backButton = screen.getByRole('button', { name: /back/i });
    backButton.click();

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });
});
