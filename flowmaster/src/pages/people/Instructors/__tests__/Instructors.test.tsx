import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import Instructors from '../Instructors';
import { useAuth } from '../../../../hooks/useAuth';
import { useSchool } from '../../../../hooks/useSchool';

// Mock Firebase services
jest.mock('../../../../services/firebase', () => ({
  db: {},
  auth: {},
  storage: {},
}));

// Mock the required hooks and Firebase functions
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('../../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../../hooks/useSchool', () => ({
  useSchool: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  getDocs: jest.fn(),
  deleteDoc: jest.fn(),
  doc: jest.fn(),
  getFirestore: jest.fn(),
}));

jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
}));

jest.mock('firebase/storage', () => ({
  getStorage: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (key: string, options?: { ns?: string; item?: string }) => {
        // Handle namespace-specific translations
        if (options?.ns === 'common') {
          const commonTranslations: { [key: string]: string } = {
            'deleteConfirmation.title': 'Delete Confirmation',
            'deleteConfirmation.message': options?.item
              ? `Are you sure you want to delete ${options.item}?`
              : 'Are you sure you want to delete this item?',
            'ui.confirm': 'Delete', // Changed to match the actual UI text
            'ui.cancel': 'Cancel',
            'ui.actions': 'Actions',
            'ui.edit': 'Edit',
            'ui.delete': 'Delete',
          };
          return commonTranslations[key] || key;
        }

        // Default translations (people namespace)
        const translations: { [key: string]: string } = {
          'instructors.title': 'Instructors',
          'instructors.addInstructor': 'Add Instructor',
          'instructors.fields.firstName': 'First Name',
          'instructors.fields.lastName': 'Last Name',
          'instructors.fields.email': 'Email',
          'instructors.fields.phone': 'Phone',
          'instructors.fields.specialties': 'Specialties', // Changed from disciplines
          'instructors.fields.status': 'Status',
        };
        return translations[key] || key;
      },
    };
  },
}));

const mockInstructors = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '1234567890',
    specialties: ['Yoga', 'Pilates'], // Changed from disciplines to specialties
    status: 'active',
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '0987654321',
    specialties: ['CrossFit'], // Changed from disciplines to specialties
    status: 'inactive',
  },
];

describe('Instructors Component', () => {
  const mockNavigate = jest.fn();
  const mockSchoolId = 'school-123';

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (collection as jest.Mock).mockReturnValue({});
    (getDocs as jest.Mock).mockResolvedValue({
      docs: mockInstructors.map((instructor) => ({
        id: instructor.id,
        data: () => instructor,
      })),
    });
  });

  const setupComponent = (role: string | null = 'admin') => {
    (useAuth as jest.Mock).mockReturnValue({
      user: role ? { role } : null,
    });
    (useSchool as jest.Mock).mockReturnValue({
      currentSchool: { id: mockSchoolId },
    });
  };

  it('renders loading state initially', () => {
    setupComponent();
    render(<Instructors />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders instructors list after loading', async () => {
    setupComponent();
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('1234567890')).toBeInTheDocument();
  });

  it('shows add instructor button for admin users', async () => {
    setupComponent('admin');
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Add Instructor')).toBeInTheDocument();
  });

  it('hides add instructor button for non-admin users', async () => {
    setupComponent('user');
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.queryByText('Add Instructor')).not.toBeInTheDocument();
  });

  it('navigates to instructor details when clicking on instructor name', async () => {
    setupComponent();
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('John Doe'));
    expect(mockNavigate).toHaveBeenCalledWith('/people/instructors/1');
  });

  it('shows edit and delete buttons for admin users', async () => {
    setupComponent('admin');
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Look for buttons by their aria-label or role
    const editButtons = screen.getAllByLabelText('Edit');
    const deleteButtons = screen.getAllByLabelText('Delete');

    expect(editButtons).toHaveLength(mockInstructors.length);
    expect(deleteButtons).toHaveLength(mockInstructors.length);
  });

  it('handles instructor deletion', async () => {
    setupComponent('admin');
    const mockDocRef = {}; // Create a mock document reference
    (doc as jest.Mock).mockReturnValue(mockDocRef);
    (deleteDoc as jest.Mock).mockResolvedValueOnce(undefined);

    render(<Instructors />);

    // Wait for the initial loading to complete
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Click delete button for first instructor
    const deleteButtons = screen.getAllByLabelText('Delete');
    fireEvent.click(deleteButtons[0]);

    // Wait for and find the confirmation dialog
    const dialog = await screen.findByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Find and click the confirm button within the dialog
    const confirmButton = await screen.findByText('Delete');
    fireEvent.click(confirmButton);

    // Wait for the deletion to complete and verify
    await waitFor(() => {
      expect(doc).toHaveBeenCalledWith(
        expect.any(Object),
        'schools',
        'school-123',
        'instructors',
        '1'
      );
      expect(deleteDoc).toHaveBeenCalledWith(mockDocRef);
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    });
  });

  it('displays specialties chips', async () => {
    setupComponent();
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Wait for the specialties to be rendered
    await waitFor(() => {
      expect(screen.getByText('Yoga')).toBeInTheDocument();
      expect(screen.getByText('Pilates')).toBeInTheDocument();
      expect(screen.getByText('CrossFit')).toBeInTheDocument();
    });
  });

  it('displays status with correct color', async () => {
    setupComponent();
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    const activeStatus = screen.getByText('active');
    const inactiveStatus = screen.getByText('inactive');

    expect(activeStatus).toHaveStyle({ backgroundColor: expect.stringContaining('success') });
    expect(inactiveStatus).toHaveStyle({ backgroundColor: expect.stringContaining('error') });
  });

  it('opens instructor form when add button is clicked', async () => {
    setupComponent('admin');
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Add Instructor'));
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('opens instructor form with data when edit is clicked', async () => {
    setupComponent('admin');
    render(<Instructors />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Click edit button for first instructor
    const editButtons = screen.getAllByLabelText('Edit');
    fireEvent.click(editButtons[0]);

    // Wait for the dialog to appear and check its contents
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Check if the form is populated with the correct data
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });
});

// Add this mock for the InstructorForm component
jest.mock('../InstructorForm', () => {
  return function MockInstructorForm({
    open,
    instructor,
    onClose,
  }: {
    open: boolean;
    instructor?: { firstName?: string; lastName?: string; email?: string };
    onClose: () => void;
  }) {
    return open ? (
      <div role="dialog">
        <input type="text" value={instructor?.firstName || ''} readOnly />
        <input type="text" value={instructor?.lastName || ''} readOnly />
        <input type="text" value={instructor?.email || ''} readOnly />
        <button onClick={onClose}>Close</button>
      </div>
    ) : null;
  };
});
