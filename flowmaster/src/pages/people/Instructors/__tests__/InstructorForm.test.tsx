import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { doc, setDoc } from 'firebase/firestore';
import InstructorForm from '../InstructorForm';
import { useSchool } from '../../../../hooks/useSchool';
import { fetchSports } from '../../../../services/sports';
import type { Instructor } from '../../../../types/instructor';

// Mock Firebase
jest.mock('../../../../services/firebase', () => ({
  db: {},
}));

// Mock hooks and services
jest.mock('../../../../hooks/useSchool', () => ({
  useSchool: jest.fn(),
}));

jest.mock('../../../../services/sports', () => ({
  fetchSports: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  doc: jest.fn(),
  setDoc: jest.fn(),
  collection: jest.fn(),
}));

// Mock translations
jest.mock('react-i18next', () => ({
  // Trans component mock
  Trans: ({ children }: { children: React.ReactNode }) => children,
  // useTranslation hook mock
  useTranslation: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'common:validation.required': 'This field is required',
        'common:validation.invalidEmail': 'Invalid email address',
        'common:validation.invalidPhone': 'Invalid phone number',
        'instructors.addInstructor': 'Add Instructor',
        'instructors.editInstructor': 'Edit Instructor',
        'instructors.fields.firstName': 'First Name',
        'instructors.fields.lastName': 'Last Name',
        'instructors.fields.email': 'Email',
        'instructors.fields.phone': 'Phone',
        'instructors.fields.disciplines': 'Disciplines',
        'common:status.active': 'Active',
        'common:status.inactive': 'Inactive',
      };
      return translations[key] || key;
    },
    i18n: {
      changeLanguage: () => new Promise(() => {}),
    },
  }),
}));

describe('InstructorForm', () => {
  const mockOnClose = jest.fn();
  const mockSchoolId = 'school-123';

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
  };

  const mockSports = [
    { name: 'Yoga', id: '1' },
    { name: 'Pilates', id: '2' },
    { name: 'CrossFit', id: '3' },
  ];

  // Following template guidelines for console error mocking
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalError;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (useSchool as jest.Mock).mockReturnValue({ currentSchool: { id: mockSchoolId } });
    (fetchSports as jest.Mock).mockResolvedValue(mockSports);
  });

  it('renders form in add mode', async () => {
    render(<InstructorForm {...defaultProps} />);

    // Wait for the dialog to be rendered
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Check form title
    expect(screen.getByText('Add Instructor')).toBeInTheDocument();

    // Check form fields are present
    expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Phone')).toBeInTheDocument();

    // Wait for and check specialties
    await waitFor(() => {
      mockSports.forEach((sport) => {
        expect(screen.getByLabelText(sport.name)).toBeInTheDocument();
      });
    });
  });

  it('renders form in edit mode with instructor data', async () => {
    const mockInstructor: Instructor = {
      id: 'instructor-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '************',
      specialties: ['Yoga', 'Pilates'],
      status: 'active',
    };

    render(<InstructorForm {...defaultProps} instructor={mockInstructor} />);

    // Wait for the dialog to be rendered
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Check form title
    expect(screen.getByText('Edit Instructor')).toBeInTheDocument();

    // Check pre-filled values
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('************')).toBeInTheDocument();
  });

  it('handles form validation', async () => {
    render(<InstructorForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Find and click the submit button without filling any fields
    const submitButton = screen.getByRole('button', { name: /add|save/i });
    fireEvent.click(submitButton);

    // Wait for and check required field validations
    await waitFor(() => {
      const requiredMessages = screen.getAllByText('This field is required');
      expect(requiredMessages.length).toBeGreaterThanOrEqual(2);
    });

    // Test email validation
    const emailInput = screen.getByLabelText('Email');
    fireEvent.change(emailInput, {
      target: { value: 'invalid-email' },
    });

    // Submit form again
    fireEvent.click(submitButton);

    // Check for invalid email message
    await waitFor(() => {
      expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    });

    // Test phone validation (optional)
    const phoneInput = screen.getByLabelText('Phone');
    fireEvent.change(phoneInput, {
      target: { value: 'invalid-phone' },
    });

    // Submit form again
    fireEvent.click(submitButton);

    // Check for invalid phone message
    await waitFor(() => {
      expect(screen.getByText('Invalid phone number')).toBeInTheDocument();
    });
  });

  it('handles specialty selection', async () => {
    render(<InstructorForm {...defaultProps} />);

    // Wait for specialties to load
    await waitFor(() => {
      expect(screen.getByLabelText('Yoga')).toBeInTheDocument();
    });

    // Select specialties
    fireEvent.click(screen.getByLabelText('Yoga'));
    fireEvent.click(screen.getByLabelText('Pilates'));

    // Verify selections
    expect(screen.getByLabelText('Yoga')).toBeChecked();
    expect(screen.getByLabelText('Pilates')).toBeChecked();
    expect(screen.getByLabelText('CrossFit')).not.toBeChecked();
  });

  it('handles form submission successfully', async () => {
    const mockDocRef = {};
    (doc as jest.Mock).mockReturnValue(mockDocRef);
    (setDoc as jest.Mock).mockResolvedValueOnce(undefined);

    render(<InstructorForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Fill in required fields
    fireEvent.change(screen.getByLabelText('First Name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByLabelText('Last Name'), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByLabelText('Email'), {
      target: { value: '<EMAIL>' },
    });

    // Submit form
    const submitButton = screen.getByRole('button', { name: /add|save/i });
    fireEvent.click(submitButton);

    // Wait for submission to complete
    await waitFor(() => {
      expect(setDoc).toHaveBeenCalledWith(
        mockDocRef,
        expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
        }),
        expect.any(Object)
      );
    });

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles form submission error', async () => {
    const mockError = new Error('Submission failed');
    (setDoc as jest.Mock).mockRejectedValueOnce(mockError);

    render(<InstructorForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Fill in required fields
    fireEvent.change(screen.getByLabelText('First Name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByLabelText('Last Name'), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByLabelText('Email'), {
      target: { value: '<EMAIL>' },
    });

    // Submit form
    const submitButton = screen.getByRole('button', { name: /add|save/i });
    fireEvent.click(submitButton);

    // Wait for error handling
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith('Error saving instructor:', mockError);
    });

    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('handles sports loading error', async () => {
    const mockError = new Error('Failed to load sports');
    (fetchSports as jest.Mock).mockRejectedValueOnce(mockError);

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    render(<InstructorForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load sports')).toBeInTheDocument();
      expect(consoleSpy).toHaveBeenCalledWith('Error loading sports:', mockError);
    });

    consoleSpy.mockRestore();
  });

  it('handles form cancellation', async () => {
    render(<InstructorForm {...defaultProps} />);

    // Wait for the dialog to be rendered
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Look for the cancel button and click it
    const cancelButton = await screen.findByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });
});
