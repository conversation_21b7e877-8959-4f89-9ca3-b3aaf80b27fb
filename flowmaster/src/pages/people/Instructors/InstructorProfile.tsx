import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { doc, getDoc } from 'firebase/firestore';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Divider,
  Avatar,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { db } from '../../../services/firebase';
import { useSchool } from '../../../hooks/useSchool';
import type { Instructor } from '../../../types/instructor';
import UnavailabilityCalendar from '../../../components/scheduling/UnavailabilityCalendar';
import StudentRelationships from '../../../components/scheduling/StudentRelationships';
import InstructorPrograms from '../../../components/instructors/InstructorPrograms';

const InstructorProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['people', 'common']);
  const { currentSchool } = useSchool();
  const [instructor, setInstructor] = React.useState<Instructor | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchInstructor = async () => {
      if (!currentSchool?.id || !id) return;

      try {
        setLoading(true);
        const instructorRef = doc(db, 'schools', currentSchool.id, 'instructors', id);
        const instructorDoc = await getDoc(instructorRef);

        if (instructorDoc.exists()) {
          setInstructor({ id: instructorDoc.id, ...instructorDoc.data() } as Instructor);
        } else {
          setError(t('instructors.notFound', 'Instructor not found'));
        }
      } catch (err) {
        console.error('Error fetching instructor:', err);
        setError(t('common:error.fetch', 'Failed to fetch instructor data'));
      } finally {
        setLoading(false);
      }
    };

    fetchInstructor();
  }, [currentSchool?.id, id, t]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !instructor) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          {t('common:ui.back')}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mb: 4 }}>
        {t('common:ui.back')}
      </Button>

      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '3rem',
                }}
              >
                {instructor.firstName?.[0]}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {`${instructor.firstName} ${instructor.lastName}`}
              </Typography>
              <Chip
                label={instructor.status}
                size="small"
                sx={{
                  bgcolor: instructor.status === 'active' ? 'success.light' : 'error.light',
                  color: 'white',
                  textTransform: 'capitalize',
                  mb: 2,
                }}
              />
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                {t('instructors.fields.email')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {instructor.email}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('instructors.fields.phone')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {instructor.phone || '-'}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('instructors.fields.disciplines')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {instructor.specialties?.length ? (
                  instructor.specialties.map((specialty) => (
                    <Chip
                      key={specialty}
                      label={specialty}
                      size="small"
                      sx={{
                        bgcolor: 'primary.light',
                        color: 'white',
                        fontSize: '0.75rem',
                      }}
                    />
                  ))
                ) : (
                  <Typography variant="body1">-</Typography>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <UnavailabilityCalendar
            instructorId={instructor.id}
            unavailablePeriods={[]}
            onAddPeriod={(period) => {
              // TODO: Implement period addition
              console.log('Add period:', period);
            }}
            onEditPeriod={(period) => {
              // TODO: Implement period editing
              console.log('Edit period:', period);
            }}
            onDeletePeriod={(periodId) => {
              // TODO: Implement period deletion
              console.log('Delete period:', periodId);
            }}
            isManager={true}
          />

          <Paper sx={{ p: 3, mt: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('instructors.lessonStatistics', 'Lesson Statistics')}
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="primary.main">
                    0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('instructors.totalLessons', 'Total Lessons')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="success.main">
                    0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('instructors.completedLessons', 'Completed')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="error.main">
                    0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('instructors.cancelledLessons', 'Cancelled')}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <InstructorPrograms instructor={instructor} />
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('instructors.assignedLessons', 'Assigned Lessons')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('common:ui.comingSoon', 'Coming soon')}
            </Typography>
          </Paper>

          <Box sx={{ mt: 3 }}>
            <StudentRelationships
              instructorId={instructor.id}
              relationships={[]}
              onStudentClick={(studentId) => {
                // TODO: Implement student profile navigation
                console.log('Navigate to student:', studentId);
              }}
            />
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
};

export default InstructorProfile;
