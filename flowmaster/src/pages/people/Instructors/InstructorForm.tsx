import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { doc, setDoc, collection } from 'firebase/firestore';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  CircularProgress,
  TextField,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Paper,
  MenuItem,
} from '@mui/material';
import { useSchool } from '../../../hooks/useSchool';
import { db } from '../../../services/firebase';
import { fetchSports } from '../../../services/sports';
import type { Instructor, InstructorFormData } from '../../../types/instructor';

interface InstructorFormProps {
  open: boolean;
  onClose: () => void;
  instructor?: Instructor;
}

function InstructorForm({ open, onClose, instructor }: InstructorFormProps) {
  const { t } = useTranslation(['people', 'common']);
  const { currentSchool } = useSchool();
  const [loading, setLoading] = React.useState(false);
  const [specialtyOptions, setSpecialtyOptions] = React.useState<
    Array<{ value: string; label: string }>
  >([]);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadSports = async () => {
      if (!currentSchool?.id) {
        console.log('No school ID available');
        return;
      }

      try {
        setLoading(true);
        const sports = await fetchSports(currentSchool.id);

        if (sports.length === 0) {
          setError('No sports configured for this school');
        } else {
          const options = sports.map((sport) => ({
            value: sport.name,
            label: sport.name,
          }));
          setSpecialtyOptions(options);
          setError(null);
        }
      } catch (err) {
        console.error('Error loading sports:', err);
        setError('Failed to load sports');
      } finally {
        setLoading(false);
      }
    };

    loadSports();
  }, [currentSchool?.id]);

  // Add error display in the form
  React.useEffect(() => {
    console.log('Current specialty options:', specialtyOptions);
  }, [specialtyOptions]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    watch,
  } = useForm<InstructorFormData>({
    defaultValues: {
      firstName: instructor?.firstName || '',
      lastName: instructor?.lastName || '',
      email: instructor?.email || '',
      phone: instructor?.phone || '',
      specialties: instructor?.specialties || [],
      status: instructor?.status || 'active',
    },
  });

  const specialties = watch('specialties');

  const handleSpecialtyChange = (specialty: string) => {
    const currentSpecialties = specialties || [];
    const newSpecialties = currentSpecialties.includes(specialty)
      ? currentSpecialties.filter((s) => s !== specialty)
      : [...currentSpecialties, specialty];
    setValue('specialties', newSpecialties);
  };

  const onSubmit = async (data: InstructorFormData) => {
    if (!currentSchool?.id) return;

    setLoading(true);
    try {
      const instructorsRef = collection(db, 'schools', currentSchool.id, 'instructors');
      const instructorDoc = instructor?.id
        ? doc(instructorsRef, instructor.id)
        : doc(instructorsRef);

      await setDoc(instructorDoc, data, { merge: true });
      onClose();
      reset();
    } catch (error) {
      console.error('Error saving instructor:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const statusOptions = [
    { value: 'active', label: t('common:status.active') },
    { value: 'inactive', label: t('common:status.inactive') },
  ];

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white' }}>
        {instructor ? t('instructors.editInstructor') : t('instructors.addInstructor')}
      </DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Paper elevation={0} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
              <TextField
                {...register('firstName', { required: t('common:validation.required') })}
                label={t('instructors.fields.firstName')}
                error={!!errors.firstName}
                helperText={errors.firstName?.message}
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              />

              <TextField
                {...register('lastName', { required: t('common:validation.required') })}
                label={t('instructors.fields.lastName')}
                error={!!errors.lastName}
                helperText={errors.lastName?.message}
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              />

              <TextField
                {...register('email', {
                  required: t('common:validation.required'),
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: t('common:validation.invalidEmail'),
                  },
                })}
                label={t('instructors.fields.email')}
                error={!!errors.email}
                helperText={errors.email?.message}
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              />

              <TextField
                {...register('phone', {
                  pattern: {
                    value: /^[+]?[0-9-().\s]+$/,
                    message: t('common:validation.invalidPhone'),
                  },
                })}
                label={t('instructors.fields.phone')}
                error={!!errors.phone}
                helperText={errors.phone?.message}
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              />

              <FormControl component="fieldset" variant="standard">
                <FormLabel component="legend">{t('instructors.fields.disciplines')}</FormLabel>
                <FormGroup row sx={{ mt: 1 }}>
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : error ? (
                    <div style={{ color: 'red' }}>{error}</div>
                  ) : specialtyOptions.length === 0 ? (
                    <div>No disciplines available</div>
                  ) : (
                    specialtyOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        control={
                          <Checkbox
                            checked={specialties?.includes(option.value)}
                            onChange={() => handleSpecialtyChange(option.value)}
                            color="primary"
                          />
                        }
                        label={option.label}
                      />
                    ))
                  )}
                </FormGroup>
              </FormControl>

              <TextField
                {...register('status')}
                select
                label={t('instructors.fields.status')}
                defaultValue="active"
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              >
                {statusOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Box>
          </Paper>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'background.default' }}>
          <Button onClick={handleClose} variant="outlined">
            {t('common:ui.cancel')}
          </Button>
          <Button type="submit" variant="contained" disabled={loading}>
            {loading ? (
              <CircularProgress size={24} />
            ) : instructor ? (
              t('common:ui.save')
            ) : (
              t('common:ui.add')
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}

export default InstructorForm;
