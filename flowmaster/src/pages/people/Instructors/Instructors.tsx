import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import InstructorForm from './InstructorForm';
import ConfirmationDialog from '../../../components/common/ConfirmationDialog';
import type { Instructor } from '../../../types/instructor';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Tooltip,
  Chip,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';
import { useSchool } from '../../../hooks/useSchool';

const Instructors: React.FC = () => {
  const { t } = useTranslation(['people', 'common']);
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Check if user has admin or manager role
  const canManageInstructors = user?.role === 'admin' || user?.role === 'manager';

  const fetchInstructors = useCallback(async () => {
    if (!currentSchool?.id) return;

    setLoading(true);
    try {
      const instructorsCollection = collection(db, 'schools', currentSchool.id, 'instructors');
      const instructorsSnapshot = await getDocs(instructorsCollection);
      const instructorsData = instructorsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Instructor[];

      setInstructors(instructorsData);
    } catch (error) {
      console.error('Error fetching instructors:', error);
    } finally {
      setLoading(false);
    }
  }, [currentSchool?.id]);

  useEffect(() => {
    fetchInstructors();
  }, [fetchInstructors]);

  const [selectedInstructor, setSelectedInstructor] = useState<Instructor | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [instructorToDelete, setInstructorToDelete] = useState<Instructor | null>(null);

  const handleAddInstructor = () => {
    if (!canManageInstructors) {
      console.warn('Unauthorized access attempt to add instructor');
      return;
    }
    setSelectedInstructor(undefined);
    setIsFormOpen(true);
  };

  const handleEditInstructor = (instructor: Instructor) => {
    if (!canManageInstructors) {
      console.warn('Unauthorized access attempt to edit instructor:', instructor.id);
      return;
    }
    const { id, ...instructorData } = instructor;
    setSelectedInstructor({ ...instructorData, id });
    setIsFormOpen(true);
  };

  const handleDeleteClick = (instructor: Instructor) => {
    if (!canManageInstructors) {
      console.warn('Unauthorized access attempt to delete instructor:', instructor.id);
      return;
    }
    setInstructorToDelete(instructor);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!canManageInstructors || !currentSchool?.id || !instructorToDelete) {
      return;
    }
    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'instructors', instructorToDelete.id));
      setInstructors(instructors.filter((i) => i.id !== instructorToDelete.id));
      setDeleteConfirmOpen(false);
      setInstructorToDelete(null);
    } catch (error) {
      console.error('Error deleting instructor:', error);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
            }}
          >
            {t('instructors.title')}
          </Typography>
          {canManageInstructors && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddInstructor}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
              }}
            >
              {t('instructors.addInstructor')}
            </Button>
          )}
        </Box>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer
            component={Paper}
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              overflow: 'hidden',
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('instructors.fields.firstName')} / {t('instructors.fields.lastName')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('instructors.fields.email')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('instructors.fields.phone')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('instructors.fields.disciplines')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('instructors.fields.status')}
                  </TableCell>
                  {canManageInstructors && (
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>
                      {t('ui.actions', { ns: 'common' })}
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {instructors.map((instructor) => (
                  <TableRow
                    key={instructor.id}
                    sx={{
                      '&:nth-of-type(odd)': { bgcolor: 'action.hover' },
                      '&:hover': { bgcolor: 'action.selected' },
                      transition: 'background-color 0.2s ease',
                    }}
                  >
                    <TableCell>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          cursor: 'pointer',
                          '&:hover': {
                            textDecoration: 'underline',
                            color: 'primary.main',
                          },
                        }}
                        onClick={() => navigate(`/people/instructors/${instructor.id}`)}
                      >
                        {`${instructor.firstName} ${instructor.lastName}`}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ color: 'primary.main' }}>
                        {instructor.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{instructor.phone || '-'}</Typography>
                    </TableCell>
                    <TableCell>
                      {instructor.specialties?.length ? (
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                          {instructor.specialties.map((specialty) => (
                            <Chip
                              key={specialty}
                              label={specialty}
                              size="small"
                              sx={{
                                bgcolor: 'primary.light',
                                color: 'white',
                                fontSize: '0.75rem',
                              }}
                            />
                          ))}
                        </Box>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={instructor.status}
                        size="small"
                        sx={{
                          bgcolor: instructor.status === 'active' ? 'success.light' : 'error.light',
                          color: 'white',
                          textTransform: 'capitalize',
                          fontWeight: 500,
                        }}
                      />
                    </TableCell>
                    {canManageInstructors && (
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                          <Tooltip title={t('ui.edit', 'Edit', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleEditInstructor(instructor)}
                              size="small"
                              aria-label="Edit"
                              sx={{
                                color: 'primary.main',
                                '&:hover': { bgcolor: 'primary.light', color: 'white' },
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={t('ui.delete', 'Delete', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleDeleteClick(instructor)}
                              size="small"
                              aria-label="Delete"
                              sx={{
                                color: 'error.main',
                                '&:hover': { bgcolor: 'error.light', color: 'white' },
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
      {isFormOpen && (
        <InstructorForm
          open={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setSelectedInstructor(undefined);
            // Refresh the instructors list after closing
            if (currentSchool?.id) {
              fetchInstructors();
            }
          }}
          instructor={selectedInstructor}
        />
      )}
      <ConfirmationDialog
        open={deleteConfirmOpen}
        title={t('deleteConfirmation.title', { ns: 'common' })}
        message={t('deleteConfirmation.message', {
          ns: 'common',
          item: instructorToDelete
            ? `${instructorToDelete.firstName} ${instructorToDelete.lastName}`
            : '',
        })}
        onConfirm={handleDeleteConfirm}
        onCancel={() => {
          setDeleteConfirmOpen(false);
          setInstructorToDelete(null);
        }}
        confirmText={t('ui.confirm', { ns: 'common' })}
        cancelText={t('ui.cancel', { ns: 'common' })}
      />
    </Container>
  );
};

export default Instructors;
