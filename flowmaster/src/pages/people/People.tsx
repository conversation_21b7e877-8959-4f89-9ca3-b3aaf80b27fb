import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Container, Typography, Grid, Card, CardContent, CardActionArea } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { School, SupervisorAccount, Person } from '@mui/icons-material';

/**
 * People component serves as a landing page for managing different user categories.
 * Features:
 * - Navigation to Students section
 * - Navigation to Instructors section
 * - Navigation to Clients section
 */
const People: React.FC = () => {
  const { t } = useTranslation(['people', 'common']);
  const navigate = useNavigate();

  const categories = [
    {
      title: t('categories.students'),
      description: t('descriptions.students'),
      icon: <School sx={{ fontSize: 40 }} />,
      path: '/people/students',
      color: '#1976d2', // Blue
    },
    {
      title: t('categories.instructors'),
      description: t('descriptions.instructors'),
      icon: <SupervisorAccount sx={{ fontSize: 40 }} />,
      path: '/people/instructors',
      color: '#2e7d32', // Green
    },
    {
      title: t('categories.clients'),
      description: t('descriptions.clients'),
      icon: <Person sx={{ fontSize: 40 }} />,
      path: '/people/clients',
      color: '#ed6c02', // Orange
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box display="flex" alignItems="center" mb={1}>
          <Person sx={{ fontSize: '2rem', mr: 1 }} />
          <Typography variant="h4" component="h1">
            {t('title')}
          </Typography>
        </Box>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {t('subtitle', 'Manage students, instructors, and clients')}
        </Typography>
        <Grid container spacing={3}>
          {categories.map((category) => (
            <Grid item xs={12} sm={6} md={4} key={category.path}>
              <Card>
                <CardActionArea onClick={() => navigate(category.path)}>
                  <CardContent>
                    <Box sx={{ color: category.color, mb: 2 }}>{category.icon}</Box>
                    <Typography variant="h6" component="div" gutterBottom>
                      {category.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {category.description}
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );
};

export default People;
