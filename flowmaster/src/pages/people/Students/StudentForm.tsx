import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { doc, setDoc, collection } from 'firebase/firestore';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  CircularProgress,
  TextField,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Paper,
  MenuItem,
  Typography,
} from '@mui/material';
import { useSchool } from '../../../hooks/useSchool';
import { db } from '../../../services/firebase';
import { fetchLevels } from '../../../services/levels';
import { fetchSports } from '../../../services/sports';
import type { Student, StudentFormData } from '../../../types/student';
import { useAuth } from '../../../hooks/useAuth';

interface StudentFormProps {
  open: boolean;
  onClose: () => void;
  student?: Student;
}

function StudentForm({ open, onClose, student }: StudentFormProps) {
  const { t } = useTranslation(['people', 'common']);
  const { currentSchool } = useSchool();
  const { user } = useAuth();
  const [loading, setLoading] = React.useState(false);
  const [levelOptions, setLevelOptions] = React.useState<Array<{ value: string; label: string }>>(
    []
  );
  const [specialtyOptions, setSpecialtyOptions] = React.useState<
    Array<{ value: string; label: string }>
  >([]);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadData = async () => {
      if (!currentSchool?.id) {
        console.log('No school ID available');
        return;
      }

      try {
        setLoading(true);
        const [levels, sports] = await Promise.all([
          fetchLevels(currentSchool.id),
          fetchSports(currentSchool.id),
        ]);

        // Map levels
        if (levels.length === 0) {
          setError('No levels configured for this school');
        } else {
          const levelOpts = levels.map((level) => ({
            value: level.name,
            label: level.displayName || level.name,
          }));
          setLevelOptions(levelOpts);
        }

        // Map sports/specialties
        if (sports.length === 0) {
          setError((prev) =>
            prev ? `${prev}. No sports configured` : 'No sports configured for this school'
          );
        } else {
          const sportOpts = sports.map((sport) => ({
            value: sport.name,
            label: sport.displayName || sport.name,
          }));
          setSpecialtyOptions(sportOpts);
        }

        setError(null);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load configuration data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentSchool?.id]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    watch,
  } = useForm<StudentFormData>({
    defaultValues: {
      firstName: student?.firstName || '',
      lastName: student?.lastName || '',
      email: student?.email || '',
      phone: student?.phone || '',
      enrollmentDate: student?.enrollmentDate || new Date().toISOString().split('T')[0],
      level: student?.level || '',
      specialties: student?.specialties || [],
      status: student?.status || 'active',
      type: student?.type || 'both',
    },
  });

  const specialties = watch('specialties') || []; // Changed from disciplines

  const handleSpecialtyChange = (specialty: string) => {
    // Changed from handleDisciplineChange
    const currentSpecialties = specialties;
    const newSpecialties = currentSpecialties.includes(specialty)
      ? currentSpecialties.filter((s) => s !== specialty)
      : [...currentSpecialties, specialty];
    setValue('specialties', newSpecialties);
  };

  const canManageStudents = user?.role === 'admin' || user?.role === 'manager';

  const onSubmit = async (data: StudentFormData) => {
    if (!currentSchool?.id || !canManageStudents) return;

    setLoading(true);
    try {
      const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
      const studentDoc = student?.id ? doc(studentsRef, student.id) : doc(studentsRef);

      await setDoc(studentDoc, data, { merge: true });
      onClose();
      reset();
    } catch (error) {
      console.error('Error saving student:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const statusOptions = [
    { value: 'active', label: t('common:status.active') as string },
    { value: 'inactive', label: t('common:status.inactive') as string },
  ];

  const typeOptions = [
    {
      value: 'program',
      label: t('students.types.program', 'Program Student') as string,
    },
    {
      value: 'lesson',
      label: t('students.types.lesson', 'Lesson Student') as string,
    },
    {
      value: 'both',
      label: t('students.types.both', 'Both Programs & Lessons') as string,
    },
  ];

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white' }}>
        {student ? t('students.editStudent') : t('students.addStudent')}
      </DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Paper elevation={0} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
              <TextField
                {...register('firstName', { required: t('common:validation.required') })}
                label={t('students.fields.firstName')}
                error={!!errors.firstName}
                helperText={errors.firstName?.message}
                fullWidth
                variant="outlined"
              />

              <TextField
                {...register('lastName', { required: t('common:validation.required') })}
                label={t('students.fields.lastName')}
                error={!!errors.lastName}
                helperText={errors.lastName?.message}
                fullWidth
                variant="outlined"
              />

              <TextField
                {...register('email', {
                  required: t('common:validation.required'),
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: t('common:validation.invalidEmail'),
                  },
                })}
                label={t('students.fields.email')}
                error={!!errors.email}
                helperText={errors.email?.message}
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              />

              <TextField
                {...register('phone', {
                  pattern: {
                    value: /^[+]?[0-9-().\s]+$/,
                    message: t('common:validation.invalidPhone'),
                  },
                })}
                label={t('students.fields.phone')}
                error={!!errors.phone}
                helperText={errors.phone?.message}
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
              />

              <TextField
                {...register('enrollmentDate', { required: t('common:validation.required') })}
                label={t('students.fields.enrollmentDate')}
                type="date"
                error={!!errors.enrollmentDate}
                helperText={errors.enrollmentDate?.message}
                fullWidth
                variant="outlined"
                InputLabelProps={{ shrink: true }}
              />

              <FormControl component="fieldset" variant="standard">
                <FormLabel component="legend">{t('students.fields.level')}</FormLabel>
                <FormGroup row sx={{ mt: 1 }}>
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : error ? (
                    <div style={{ color: 'red' }}>{error}</div>
                  ) : levelOptions.length === 0 ? (
                    <div>No levels available</div>
                  ) : (
                    levelOptions.map((option: { value: string; label: string }) => (
                      <FormControlLabel
                        key={option.value}
                        control={
                          <Checkbox
                            checked={watch('level') === option.value}
                            onChange={() => setValue('level', option.value)}
                            color="primary"
                          />
                        }
                        label={option.label}
                      />
                    ))
                  )}
                </FormGroup>
                {errors.level && (
                  <Typography color="error" variant="caption" sx={{ mt: 1 }}>
                    {errors.level.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl component="fieldset" variant="standard">
                <FormLabel component="legend">{t('students.fields.disciplines')}</FormLabel>
                <FormGroup row sx={{ mt: 1 }}>
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : error ? (
                    <div style={{ color: 'red' }}>{error}</div>
                  ) : specialtyOptions.length === 0 ? (
                    <div>No specialties available</div>
                  ) : (
                    specialtyOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        control={
                          <Checkbox
                            checked={specialties.includes(option.value)}
                            onChange={() => handleSpecialtyChange(option.value)}
                            color="primary"
                          />
                        }
                        label={option.label}
                      />
                    ))
                  )}
                </FormGroup>
              </FormControl>

              <TextField
                {...register('status')}
                select
                label={t('students.fields.status')}
                defaultValue="active"
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper', mb: 2 }}
              >
                {statusOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>

              <TextField
                {...register('type')}
                select
                label={t('students.fields.type', 'Student Type')}
                defaultValue="both"
                fullWidth
                variant="outlined"
                sx={{ bgcolor: 'background.paper' }}
                helperText={t(
                  'students.typeHelp',
                  'Determines what type of activities this student participates in'
                )}
              >
                {typeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Box>
          </Paper>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'background.default' }}>
          <Button onClick={handleClose} variant="outlined">
            {t('common:ui.cancel')}
          </Button>
          <Button type="submit" variant="contained" disabled={loading || !canManageStudents}>
            {loading ? (
              <CircularProgress size={24} />
            ) : student ? (
              t('common:ui.save')
            ) : (
              t('common:ui.add')
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}

export default StudentForm;
