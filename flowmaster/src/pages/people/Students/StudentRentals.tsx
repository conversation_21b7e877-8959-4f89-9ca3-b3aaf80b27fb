import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../../hooks/useSchool';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as VisibilityIcon,
  AssignmentReturn as ReturnIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { getRentalRecords, returnRental } from '../../../services/rentalService';
import { RentalRecord, RentalStatus } from '../../../types/equipment';

interface StudentRentalsProps {
  studentId: string;
}

const StudentRentals: React.FC<StudentRentalsProps> = ({ studentId }) => {
  const { t } = useTranslation(['equipment', 'common', 'people']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  // State
  const [rentals, setRentals] = useState<RentalRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch rental data
  useEffect(() => {
    const fetchRentals = async () => {
      if (!currentSchool?.id || !studentId) return;

      setLoading(true);
      try {
        const records = await getRentalRecords(currentSchool.id, { customerId: studentId });
        setRentals(records);
      } catch (err) {
        console.error('Error fetching rentals:', err);
        setError(t('messages.fetchError', 'Failed to fetch rental data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchRentals();
  }, [currentSchool?.id, studentId, t]);

  // Handle new rental
  const handleNewRental = () => {
    navigate('/equipment/rentals/new', { state: { studentId } });
  };

  // Handle view rental
  const handleViewRental = (id: string) => {
    navigate(`/equipment/rentals/${id}`);
  };

  // Handle return rental
  const handleReturnRental = (id: string) => {
    navigate(`/equipment/rentals/${id}`, { state: { returnMode: true } });
  };

  // Render status chip
  const renderStatusChip = (status: RentalStatus) => {
    switch (status) {
      case 'active':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('rental.status.active', 'Active')}
            color="primary"
            size="small"
          />
        );
      case 'returned':
        return (
          <Chip
            icon={<CheckCircleIcon />}
            label={t('rental.status.returned', 'Returned')}
            color="success"
            size="small"
          />
        );
      case 'overdue':
        return (
          <Chip
            icon={<WarningIcon />}
            label={t('rental.status.overdue', 'Overdue')}
            color="error"
            size="small"
          />
        );
      default:
        return null;
    }
  };

  // Format date - handles different date formats
  const formatDate = (date: any) => {
    try {
      if (
        date &&
        typeof date === 'object' &&
        'toDate' in date &&
        typeof date.toDate === 'function'
      ) {
        // Firebase Timestamp
        return format(date.toDate(), 'PPP');
      } else if (date instanceof Date) {
        // JavaScript Date object
        return format(date, 'PPP');
      } else if (typeof date === 'string') {
        // ISO string or other date string
        return format(new Date(date), 'PPP');
      } else if (date && typeof date === 'number') {
        // Unix timestamp (milliseconds)
        return format(new Date(date), 'PPP');
      }
      return 'Invalid date';
    } catch (error) {
      console.error('Error formatting date:', error, date);
      return 'Invalid date';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          {t('students.activeRentals', 'Active Rentals', { ns: 'people' })}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleNewRental}
        >
          {t('rental.addButton', 'New Rental')}
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : rentals.length === 0 ? (
        <Typography variant="body2" color="textSecondary">
          {t('students.noRentals', 'No equipment rentals found for this student', { ns: 'people' })}
        </Typography>
      ) : (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>{t('rental.dateColumn', 'Date')}</TableCell>
                <TableCell>{t('rental.itemsColumn', 'Items')}</TableCell>
                <TableCell>{t('rental.dueDateColumn', 'Due Date')}</TableCell>
                <TableCell>{t('rental.statusColumn', 'Status')}</TableCell>
                <TableCell align="right">{t('rental.actionsColumn', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rentals.map((rental) => (
                <TableRow key={rental.id}>
                  <TableCell>{formatDate(rental.date)}</TableCell>
                  <TableCell>
                    {rental.items.length} {t('rental.itemsCount', 'items')}
                  </TableCell>
                  <TableCell>{formatDate(rental.dueDate)}</TableCell>
                  <TableCell>{renderStatusChip(rental.status)}</TableCell>
                  <TableCell align="right">
                    <Tooltip title={t('actions.view', 'View', { ns: 'common' })}>
                      <IconButton size="small" onClick={() => handleViewRental(rental.id)}>
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    {!rental.returned && (
                      <Tooltip title={t('rental.returnAction', 'Return')}>
                        <IconButton size="small" onClick={() => handleReturnRental(rental.id)}>
                          <ReturnIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default StudentRentals;
