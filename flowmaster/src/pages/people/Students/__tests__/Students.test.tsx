import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import { collection, getDocs, deleteDoc, doc, DocumentReference } from 'firebase/firestore';
import Students from '../Students';
import { useAuth } from '../../../../hooks/useAuth';
import { useSchool } from '../../../../hooks/useSchool';
import type { Student } from '../../../../types/student';

// Mock Firebase services
jest.mock('../../../../services/firebase', () => ({
  db: {},
  auth: {},
  storage: {},
}));

// Mock the required hooks and Firebase functions
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('../../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../../hooks/useSchool', () => ({
  useSchool: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  getDocs: jest.fn(),
  deleteDoc: jest.fn(),
  doc: jest.fn(),
  getFirestore: jest.fn(),
}));

jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
}));

jest.mock('firebase/storage', () => ({
  getStorage: jest.fn(),
}));

// Mock translations
jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (key: string, options?: Record<string, unknown>) => {
        if (options?.ns === 'common') {
          const commonTranslations: { [key: string]: string } = {
            'deleteConfirmation.title': 'Delete Confirmation',
            'deleteConfirmation.message': options?.item
              ? `Are you sure you want to delete ${options.item}?`
              : 'Are you sure you want to delete this item?',
            'ui.confirm': 'Delete',
            'ui.cancel': 'Cancel',
            'ui.actions': 'Actions',
            'ui.edit': 'Edit',
            'ui.delete': 'Delete',
          };
          return commonTranslations[key] || key;
        }

        const translations: { [key: string]: string } = {
          'students.title': 'Students',
          'students.addStudent': 'Add Student',
          'students.fields.firstName': 'First Name',
          'students.fields.lastName': 'Last Name',
          'students.fields.email': 'Email',
          'students.fields.phone': 'Phone',
          'students.fields.status': 'Status',
          'students.fields.level': 'Level',
        };
        return translations[key] || key;
      },
    };
  },
}));

const mockStudents = [
  {
    id: '1',
    firstName: 'Alice',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '1234567890',
    level: 'Intermediate',
    status: 'active',
  },
  {
    id: '2',
    firstName: 'Bob',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '0987654321',
    level: 'Beginner',
    status: 'inactive',
  },
];

describe('Students Component', () => {
  const mockNavigate = jest.fn();
  const mockSchoolId = 'school-123';

  // Add console error mocking as recommended in the guide
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalError;
  });

  // Add proper cleanup between tests
  afterEach(() => {
    jest.clearAllMocks();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (collection as jest.Mock).mockReturnValue({});
    (getDocs as jest.Mock).mockResolvedValue({
      docs: mockStudents.map((student) => ({
        id: student.id,
        data: () => student,
      })),
    });
  });

  const setupComponent = (role: string | null = 'admin') => {
    (useAuth as jest.Mock).mockReturnValue({
      user: role ? { role } : null,
    });
    (useSchool as jest.Mock).mockReturnValue({
      currentSchool: { id: mockSchoolId },
    });
  };

  it('renders loading state initially', () => {
    setupComponent();
    render(<Students />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders students list after loading', async () => {
    setupComponent();
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('1234567890')).toBeInTheDocument();
  });

  it('shows add student button for admin users', async () => {
    setupComponent('admin');
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Add Student')).toBeInTheDocument();
  });

  it('hides add student button for non-admin users', async () => {
    setupComponent('user');
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.queryByText('Add Student')).not.toBeInTheDocument();
  });

  it('navigates to student details when clicking on student name', async () => {
    setupComponent();
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Alice Johnson'));
    expect(mockNavigate).toHaveBeenCalledWith('/people/students/1');
  });

  it('shows edit and delete buttons for admin users', async () => {
    setupComponent('admin');
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Use proper aria-labels as shown in the guide
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });

    expect(editButtons).toHaveLength(mockStudents.length);
    expect(deleteButtons).toHaveLength(mockStudents.length);
  });

  it('handles student deletion', async () => {
    setupComponent('admin');
    const mockDocRef = {} as DocumentReference;
    (doc as jest.Mock).mockReturnValue(mockDocRef);
    (deleteDoc as jest.Mock).mockResolvedValueOnce(undefined);

    render(<Students />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Find and click delete button using proper aria-label
    const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
    fireEvent.click(deleteButton);

    // Find and interact with confirmation dialog
    const dialog = await screen.findByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Use the exact text from translations
    const confirmButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(confirmButton);

    // Verify deletion with proper error handling
    await waitFor(() => {
      // Verify Firebase doc was called with correct path
      expect(doc).toHaveBeenCalledWith(
        expect.any(Object),
        'schools',
        mockSchoolId,
        'students',
        mockStudents[0].id
      );
      // Verify deleteDoc was called
      expect(deleteDoc).toHaveBeenCalledWith(mockDocRef);
      // Verify item is removed from UI
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });
  });

  it('handles student deletion failure', async () => {
    setupComponent('admin');
    const mockError = new Error('Delete failed');
    (doc as jest.Mock).mockReturnValue({});
    (deleteDoc as jest.Mock).mockRejectedValueOnce(mockError);

    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
    fireEvent.click(deleteButton);

    const confirmButton = await screen.findByRole('button', { name: /delete/i });
    fireEvent.click(confirmButton);

    // Verify error handling
    await waitFor(() => {
      // Verify item still exists in UI
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    });
  });

  it('displays level chips', async () => {
    setupComponent();
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Intermediate')).toBeInTheDocument();
    expect(screen.getByText('Beginner')).toBeInTheDocument();
  });

  it('displays status with correct color', async () => {
    setupComponent();
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    const activeStatus = screen.getByText('active');
    const inactiveStatus = screen.getByText('inactive');

    expect(activeStatus).toHaveStyle({ backgroundColor: expect.stringContaining('success') });
    expect(inactiveStatus).toHaveStyle({ backgroundColor: expect.stringContaining('error') });
  });

  it('opens student form when add button is clicked', async () => {
    setupComponent('admin');
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Add Student'));
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('opens student form with data when edit is clicked', async () => {
    setupComponent('admin');
    render(<Students />);

    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Find and click edit button using proper role and name
    const editButton = screen.getAllByRole('button', { name: /edit/i })[0];
    fireEvent.click(editButton);

    // Wait for the dialog and form to appear
    const dialog = await screen.findByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Check form fields using proper roles and labels
    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: /first name/i })).toHaveValue('Alice');
      expect(screen.getByRole('textbox', { name: /last name/i })).toHaveValue('Johnson');
      expect(screen.getByRole('textbox', { name: /email/i })).toHaveValue('<EMAIL>');
    });
  });
});

// Add mock for the StudentForm component
jest.mock('../StudentForm', () => {
  return function MockStudentForm({
    open,
    student,
    onClose,
  }: {
    open: boolean;
    student?: Student;
    onClose: () => void;
  }) {
    return open ? (
      <div role="dialog">
        <input type="text" aria-label="First Name" value={student?.firstName || ''} readOnly />
        <input type="text" aria-label="Last Name" value={student?.lastName || ''} readOnly />
        <input type="text" aria-label="Email" value={student?.email || ''} readOnly />
        <button onClick={onClose}>Close</button>
      </div>
    ) : null;
  };
});
