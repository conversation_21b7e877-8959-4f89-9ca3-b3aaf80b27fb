import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { doc, setDoc } from 'firebase/firestore';
import StudentForm from '../StudentForm';
import { useSchool } from '../../../../hooks/useSchool';
import { fetchLevels } from '../../../../services/levels';
import { fetchSports } from '../../../../services/sports';
import type { Student } from '../../../../types/student';

// Mock Firebase
jest.mock('../../../../services/firebase', () => ({
  db: {},
}));

// Mock hooks and services
jest.mock('../../../../hooks/useSchool', () => ({
  useSchool: jest.fn(),
}));

jest.mock('../../../../services/levels', () => ({
  fetchLevels: jest.fn(),
}));

jest.mock('../../../../services/sports', () => ({
  fetchSports: jest.fn(),
}));

// Simplify Firebase mocks to match InstructorForm.test.tsx
jest.mock('firebase/firestore', () => ({
  doc: jest.fn(),
  setDoc: jest.fn(),
  collection: jest.fn(),
}));

// Mock translations (keep existing translation mock)

// Simplify useAuth mock to match the pattern
jest.mock('../../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { uid: 'test-uid', role: 'admin' },
  }),
}));

const mockLevels = [
  { name: 'Beginner', displayName: 'Beginner', id: '1' },
  { name: 'Intermediate', displayName: 'Intermediate', id: '2' },
  { name: 'Advanced', displayName: 'Advanced', id: '3' },
];

const mockSports = [
  { name: 'Yoga', displayName: 'Yoga', id: '1' },
  { name: 'Pilates', displayName: 'Pilates', id: '2' },
  { name: 'CrossFit', displayName: 'CrossFit', id: '3' },
];

describe('StudentForm', () => {
  const mockOnClose = jest.fn();
  const mockSchoolId = 'school-123';

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useSchool as jest.Mock).mockReturnValue({ currentSchool: { id: mockSchoolId } });
    (fetchLevels as jest.Mock).mockResolvedValue(mockLevels);
    (fetchSports as jest.Mock).mockResolvedValue(mockSports);
  });

  it('renders form in add mode', async () => {
    render(<StudentForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Basic fields
    expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Phone')).toBeInTheDocument();
    expect(screen.getByLabelText('Enrollment Date')).toBeInTheDocument();

    // Wait for levels to load and check them
    await waitFor(() => {
      const levelGroup = screen.getByRole('group', { name: /level/i });
      mockLevels.forEach((level) => {
        expect(within(levelGroup).getByLabelText(level.displayName)).toBeInTheDocument();
      });
    });

    // Wait for specialties to load and check them
    await waitFor(() => {
      mockSports.forEach((sport) => {
        expect(screen.getByLabelText(sport.displayName)).toBeInTheDocument();
      });
    });
  });

  it('renders form in edit mode with student data', async () => {
    const mockStudent: Student = {
      id: 'student-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '************',
      level: 'Intermediate',
      specialties: ['Yoga', 'Pilates'],
      status: 'active',
      enrollmentDate: '2023-01-01',
    };

    render(<StudentForm {...defaultProps} student={mockStudent} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    expect(screen.getByText('Edit Student')).toBeInTheDocument();
    expect(screen.getByLabelText('First Name')).toHaveValue('John');
    expect(screen.getByLabelText('Last Name')).toHaveValue('Doe');
    expect(screen.getByLabelText('Email')).toHaveValue('<EMAIL>');
    expect(screen.getByLabelText('Phone')).toHaveValue('************');
    expect(screen.getByLabelText('Level')).toHaveValue('Intermediate');
    expect(screen.getByLabelText('Enrollment Date')).toHaveValue('2023-01-01');
  });

  it('handles form validation', async () => {
    render(<StudentForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    const submitButton = screen.getByRole('button', { name: /add|save/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      const requiredMessages = screen.getAllByText('This field is required');
      expect(requiredMessages.length).toBeGreaterThan(0);
    });

    // Test email validation
    const emailInput = screen.getByLabelText('Email');
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    });
  });

  it('handles form submission successfully', async () => {
    const mockDocRef = {};
    (doc as jest.Mock).mockReturnValue(mockDocRef);
    (setDoc as jest.Mock).mockResolvedValueOnce(undefined);

    render(<StudentForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Fill in required fields
    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Enrollment Date'), { target: { value: '2023-01-01' } });

    // Select a level using checkbox
    const levelGroup = screen.getByRole('group', { name: /level/i });
    const levelCheckbox = within(levelGroup).getByLabelText('Beginner');
    fireEvent.click(levelCheckbox);

    const submitButton = screen.getByRole('button', { name: /add|save/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(setDoc).toHaveBeenCalledWith(
        mockDocRef,
        expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          level: 'Beginner',
          enrollmentDate: '2023-01-01',
        }),
        expect.any(Object)
      );
    });

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles form submission error', async () => {
    const mockError = new Error('Submission failed');
    (setDoc as jest.Mock).mockRejectedValueOnce(mockError);

    render(<StudentForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } });
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } });
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Enrollment Date'), { target: { value: '2023-01-01' } });

    const submitButton = screen.getByRole('button', { name: /add|save/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith('Error saving student:', mockError);
    });

    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('handles form cancellation', async () => {
    render(<StudentForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });
});
