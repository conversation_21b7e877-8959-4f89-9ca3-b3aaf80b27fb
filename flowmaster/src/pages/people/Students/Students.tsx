import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import StudentForm from './StudentForm';
import ConfirmationDialog from '../../../components/common/ConfirmationDialog';
import type { Student } from '../../../types/student';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Tooltip,
  CircularProgress,
  Chip,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from '@mui/material';
// Development imports - these will be cleaned up before production
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  // Remove unused imports - add them back only when you need them
} from '@mui/icons-material';
import { useAuth } from '../../../hooks/useAuth';
import { useSchool } from '../../../hooks/useSchool';
import { useNavigate } from 'react-router-dom';

const Students: React.FC = () => {
  const { t } = useTranslation(['people', 'common']);
  const { user } = useAuth();
  const { currentSchool, loading: schoolLoading } = useSchool();
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState<Student | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState<Student | null>(null);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<string>('firstName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const navigate = useNavigate();

  // Check if user has admin or manager role
  const canManageStudents = user?.role === 'admin' || user?.role === 'manager';

  const fetchStudents = useCallback(async () => {
    if (!currentSchool?.id) return;

    setLoading(true);
    try {
      const studentsCollection = collection(db, 'schools', currentSchool.id, 'students');
      const studentsSnapshot = await getDocs(studentsCollection);
      const studentsData = studentsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Student[];

      setStudents(studentsData);
      setFilteredStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
      // Don't show the error to users as it might be a permissions issue
    } finally {
      setLoading(false);
    }
  }, [currentSchool?.id]);

  useEffect(() => {
    if (!schoolLoading) {
      fetchStudents();
    }
  }, [fetchStudents, schoolLoading]);

  // Filter and sort students when typeFilter, sortField, or sortDirection changes
  useEffect(() => {
    // First, filter the students
    let filtered = [...students];

    if (typeFilter !== 'all') {
      filtered = filtered.filter(
        (student) =>
          student.type === typeFilter ||
          // If student has no type but filter is 'both', include them
          (typeFilter === 'both' && !student.type)
      );
    }

    // Then, sort the filtered students
    filtered.sort((a, b) => {
      let aValue: any = a[sortField as keyof Student] || '';
      let bValue: any = b[sortField as keyof Student] || '';

      // Handle special case for type field (default to 'both' if not set)
      if (sortField === 'type') {
        aValue = a.type || 'both';
        bValue = b.type || 'both';
      }

      // Compare the values
      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setFilteredStudents(filtered);
  }, [typeFilter, sortField, sortDirection, students]);

  const handleAddStudent = () => {
    if (!canManageStudents) {
      console.warn('Unauthorized access attempt to add student');
      return;
    }
    setSelectedStudent(undefined);
    setIsFormOpen(true);
  };

  const handleEditStudent = (student: Student) => {
    if (!canManageStudents) {
      console.warn('Unauthorized access attempt to edit student:', student.id);
      return;
    }
    setSelectedStudent(student);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (student: Student) => {
    if (!canManageStudents) {
      console.warn('Unauthorized access attempt to delete student:', student.id);
      return;
    }
    setStudentToDelete(student);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!canManageStudents || !currentSchool?.id || !studentToDelete) {
      return;
    }
    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'students', studentToDelete.id));
      setStudents(students.filter((s) => s.id !== studentToDelete.id));
      setDeleteConfirmOpen(false);
      setStudentToDelete(null);
    } catch (error) {
      console.error('Error deleting student:', error);
    }
  };

  // Handle sorting when a column header is clicked
  const handleSort = (field: string) => {
    if (sortField === field) {
      // If already sorting by this field, toggle direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If sorting by a new field, set it and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Helper to render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
            }}
          >
            {t('students.title')}
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel id="type-filter-label">
                {t('students.fields.type', 'Student Type')}
              </InputLabel>
              <Select
                labelId="type-filter-label"
                id="type-filter"
                value={typeFilter}
                label={t('students.fields.type', 'Student Type')}
                onChange={(e: SelectChangeEvent) => setTypeFilter(e.target.value)}
              >
                <MenuItem value="all">{t('common:filters.all', 'All')}</MenuItem>
                <MenuItem value="program">
                  {t('students.types.program', 'Program Student')}
                </MenuItem>
                <MenuItem value="lesson">{t('students.types.lesson', 'Lesson Student')}</MenuItem>
                <MenuItem value="both">
                  {t('students.types.both', 'Both Programs & Lessons')}
                </MenuItem>
              </Select>
            </FormControl>
            {canManageStudents && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleAddStudent}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  px: 3,
                }}
              >
                {t('students.addStudent')}
              </Button>
            )}
          </Box>
        </Box>

        {loading || schoolLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer
            component={Paper}
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              overflow: 'hidden',
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('firstName')}
                  >
                    {t('students.fields.firstName')} / {t('students.fields.lastName')}
                    {renderSortIndicator('firstName')}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('email')}
                  >
                    {t('students.fields.email')}
                    {renderSortIndicator('email')}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('phone')}
                  >
                    {t('students.fields.phone')}
                    {renderSortIndicator('phone')}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('level')}
                  >
                    {t('students.fields.level')}
                    {renderSortIndicator('level')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('students.fields.disciplines')}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('enrollmentDate')}
                  >
                    {t('students.fields.enrollmentDate')}
                    {renderSortIndicator('enrollmentDate')}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('status')}
                  >
                    {t('students.fields.status')}
                    {renderSortIndicator('status')}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                    }}
                    onClick={() => handleSort('type')}
                  >
                    {t('students.fields.type', 'Type')}
                    {renderSortIndicator('type')}
                  </TableCell>
                  {canManageStudents && (
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>
                      {t('ui.actions', { ns: 'common' })}
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredStudents.map((student) => (
                  <TableRow
                    key={student.id}
                    sx={{
                      '&:nth-of-type(odd)': { bgcolor: 'action.hover' },
                      '&:hover': { bgcolor: 'action.selected' },
                      transition: 'background-color 0.2s ease',
                    }}
                  >
                    <TableCell>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          cursor: 'pointer',
                          '&:hover': {
                            textDecoration: 'underline',
                            color: 'primary.main',
                          },
                        }}
                        onClick={() => navigate(`/people/students/${student.id}`)}
                      >
                        {`${student.firstName} ${student.lastName}`}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ color: 'primary.main' }}>
                        {student.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{student.phone || '-'}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {student.level || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {student.specialties?.length ? (
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                          {student.specialties.map((specialty) => (
                            <Chip
                              key={specialty}
                              label={specialty}
                              size="small"
                              sx={{
                                bgcolor: 'primary.light',
                                color: 'white',
                                fontSize: '0.75rem',
                              }}
                            />
                          ))}
                        </Box>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(student.enrollmentDate).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={student.status}
                        size="small"
                        sx={{
                          bgcolor: student.status === 'active' ? 'success.light' : 'error.light',
                          color: 'white',
                          textTransform: 'capitalize',
                          fontWeight: 500,
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={student.type || 'both'}
                        size="small"
                        sx={{
                          bgcolor:
                            student.type === 'program'
                              ? 'primary.light'
                              : student.type === 'lesson'
                                ? 'secondary.light'
                                : 'info.light',
                          color: 'white',
                          textTransform: 'capitalize',
                          fontWeight: 500,
                        }}
                      />
                    </TableCell>
                    {canManageStudents && (
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                          <Tooltip title={t('ui.edit', 'Edit', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleEditStudent(student)}
                              size="small"
                              sx={{
                                color: 'primary.main',
                                '&:hover': { bgcolor: 'primary.light', color: 'white' },
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={t('ui.delete', 'Delete', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleDeleteClick(student)}
                              size="small"
                              sx={{
                                color: 'error.main',
                                '&:hover': { bgcolor: 'error.light', color: 'white' },
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
      {isFormOpen && (
        <StudentForm
          open={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setSelectedStudent(undefined);
            // Refresh the students list after closing
            if (currentSchool?.id) {
              fetchStudents();
            }
          }}
          student={selectedStudent}
        />
      )}
      <ConfirmationDialog
        open={deleteConfirmOpen}
        title={t('deleteConfirmation.title', { ns: 'common' })}
        message={t('deleteConfirmation.message', {
          ns: 'common',
          item: studentToDelete ? `${studentToDelete.firstName} ${studentToDelete.lastName}` : '',
        })}
        onConfirm={handleDeleteConfirm}
        onCancel={() => {
          setDeleteConfirmOpen(false);
          setStudentToDelete(null);
        }}
      />
    </Container>
  );
};

export default Students;
