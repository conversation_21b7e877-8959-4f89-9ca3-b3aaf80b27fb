import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { doc, getDoc } from 'firebase/firestore';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Divider,
  Avatar,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { db } from '../../../services/firebase';
import { useSchool } from '../../../hooks/useSchool';
import type { Student } from '../../../types/student';
import StudentPrograms from '../../../components/students/StudentPrograms';
import StudentAttendanceSummary from '../../../components/students/StudentAttendanceSummary';
import StudentAttendanceHistory from '../../../components/students/StudentAttendanceHistory';
import StudentRentals from './StudentRentals';

const StudentProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['people', 'common']);
  const { currentSchool } = useSchool();
  const [student, setStudent] = React.useState<Student | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchStudent = async () => {
      if (!currentSchool?.id || !id) return;

      try {
        setLoading(true);
        const studentRef = doc(db, 'schools', currentSchool.id, 'students', id);
        const studentDoc = await getDoc(studentRef);

        if (studentDoc.exists()) {
          setStudent({ id: studentDoc.id, ...studentDoc.data() } as Student);
        } else {
          setError(t('students.notFound', 'Student not found'));
        }
      } catch (err) {
        console.error('Error fetching student:', err);
        setError(t('common:error.fetch', 'Failed to fetch student data'));
      } finally {
        setLoading(false);
      }
    };

    fetchStudent();
  }, [currentSchool?.id, id, t]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !student) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          {t('common:ui.back')}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mb: 4 }}>
        {t('common:ui.back')}
      </Button>

      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '3rem',
                }}
              >
                {student.firstName?.[0]}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {`${student.firstName} ${student.lastName}`}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 2 }}>
                <Chip
                  label={student.status}
                  size="small"
                  sx={{
                    bgcolor: student.status === 'active' ? 'success.light' : 'error.light',
                    color: 'white',
                    textTransform: 'capitalize',
                  }}
                />
                <Chip
                  label={student.type || 'both'}
                  size="small"
                  sx={{
                    bgcolor:
                      student.type === 'program'
                        ? 'primary.light'
                        : student.type === 'lesson'
                          ? 'secondary.light'
                          : 'info.light',
                    color: 'white',
                    textTransform: 'capitalize',
                  }}
                />
              </Box>
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                {t('students.fields.email')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {student.email}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('students.fields.phone')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {student.phone || '-'}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('students.fields.level')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {student.level || '-'}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('students.fields.enrollmentDate')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {new Date(student.enrollmentDate).toLocaleDateString()}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('students.fields.disciplines')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {student.specialties?.length ? (
                  student.specialties.map((specialty) => (
                    <Chip
                      key={specialty}
                      label={specialty}
                      size="small"
                      sx={{
                        bgcolor: 'primary.light',
                        color: 'white',
                        fontSize: '0.75rem',
                      }}
                    />
                  ))
                ) : (
                  <Typography variant="body1">-</Typography>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <StudentAttendanceSummary student={student} />
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <StudentPrograms student={student} />
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('students.equipmentRentals', 'Equipment Rentals')}
            </Typography>
            <StudentRentals studentId={id || ''} />
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('students.progressTracking', 'Progress Tracking')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              {/* TODO: Implement progress tracking */}
              <Typography variant="body2" color="text.secondary">
                {t('common:ui.comingSoon', 'Coming soon')}
              </Typography>
            </Box>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <StudentAttendanceHistory student={student} />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default StudentProfile;
