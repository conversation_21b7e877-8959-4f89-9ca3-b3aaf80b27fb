import React, { useState, useEffect } from 'react';
import { 
  Typo<PERSON>, 
  Box, 
  Card, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemAvatar, 
  Avatar, 
  Divider, 
  IconButton, 
  Skeleton,
  Badge,
  Tabs,
  Tab,
  Snackbar,
  Alert,
  Menu,
  MenuItem,
  Chip
} from '@mui/material';
import { 
  Notifications as NotificationsIcon, 
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Event as EventIcon,
  School as SchoolIcon,
  Person as PersonIcon,
  MoreVert as MoreVertIcon,
  DeleteSweep as DeleteSweepIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { format, formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit, 
  Timestamp, 
  doc, 
  updateDoc, 
  deleteDoc, 
  writeBatch 
} from 'firebase/firestore';
import { Notification } from '../../types/notification';

enum NotificationTab {
  ALL = 'all',
  UNREAD = 'unread',
  READ = 'read'
}

const MobileNotifications: React.FC = () => {
  const { t } = useTranslation(['common', 'notifications']);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentTab, setCurrentTab] = useState<NotificationTab>(NotificationTab.ALL);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotification, setSelectedNotification] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');

  useEffect(() => {
    const fetchNotifications = async () => {
      if (!user?.uid || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        const notificationsQuery = query(
          collection(db, 'schools', currentSchool.id, 'notifications'),
          where('recipientId', '==', user.uid),
          orderBy('createdAt', 'desc'),
          limit(50)
        );
        
        const notificationsSnapshot = await getDocs(notificationsQuery);
        
        if (notificationsSnapshot.empty) {
          setNotifications([]);
          setFilteredNotifications([]);
        } else {
          const notificationsData = notificationsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Notification[];
          
          setNotifications(notificationsData);
          applyFilter(notificationsData, currentTab);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
        showSnackbar(t('notifications:fetchError', 'Failed to load notifications'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchNotifications();
  }, [user?.uid, currentSchool?.id, t]);

  const applyFilter = (notificationsList: Notification[], tab: NotificationTab) => {
    let filtered = notificationsList;
    
    if (tab === NotificationTab.UNREAD) {
      filtered = filtered.filter(notification => !notification.read);
    } else if (tab === NotificationTab.READ) {
      filtered = filtered.filter(notification => notification.read);
    }
    
    setFilteredNotifications(filtered);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: NotificationTab) => {
    setCurrentTab(newValue);
    applyFilter(notifications, newValue);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, notificationId: string) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedNotification(notificationId);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedNotification(null);
  };

  const handleMarkAsRead = async () => {
    if (!selectedNotification || !currentSchool?.id) {
      handleMenuClose();
      return;
    }
    
    try {
      await updateDoc(doc(db, 'schools', currentSchool.id, 'notifications', selectedNotification), {
        read: true,
        updatedAt: Timestamp.now()
      });
      
      // Update local state
      setNotifications(prevNotifications => 
        prevNotifications.map(notification => 
          notification.id === selectedNotification 
            ? { ...notification, read: true, updatedAt: Timestamp.now() } 
            : notification
        )
      );
      
      applyFilter(
        notifications.map(notification => 
          notification.id === selectedNotification 
            ? { ...notification, read: true, updatedAt: Timestamp.now() } 
            : notification
        ),
        currentTab
      );
      
      showSnackbar(t('notifications:markedAsRead', 'Notification marked as read'), 'success');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      showSnackbar(t('notifications:markAsReadError', 'Failed to mark notification as read'), 'error');
    }
    
    handleMenuClose();
  };

  const handleMarkAsUnread = async () => {
    if (!selectedNotification || !currentSchool?.id) {
      handleMenuClose();
      return;
    }
    
    try {
      await updateDoc(doc(db, 'schools', currentSchool.id, 'notifications', selectedNotification), {
        read: false,
        updatedAt: Timestamp.now()
      });
      
      // Update local state
      setNotifications(prevNotifications => 
        prevNotifications.map(notification => 
          notification.id === selectedNotification 
            ? { ...notification, read: false, updatedAt: Timestamp.now() } 
            : notification
        )
      );
      
      applyFilter(
        notifications.map(notification => 
          notification.id === selectedNotification 
            ? { ...notification, read: false, updatedAt: Timestamp.now() } 
            : notification
        ),
        currentTab
      );
      
      showSnackbar(t('notifications:markedAsUnread', 'Notification marked as unread'), 'success');
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      showSnackbar(t('notifications:markAsUnreadError', 'Failed to mark notification as unread'), 'error');
    }
    
    handleMenuClose();
  };

  const handleDelete = async () => {
    if (!selectedNotification || !currentSchool?.id) {
      handleMenuClose();
      return;
    }
    
    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'notifications', selectedNotification));
      
      // Update local state
      setNotifications(prevNotifications => 
        prevNotifications.filter(notification => notification.id !== selectedNotification)
      );
      
      applyFilter(
        notifications.filter(notification => notification.id !== selectedNotification),
        currentTab
      );
      
      showSnackbar(t('notifications:deleted', 'Notification deleted'), 'success');
    } catch (error) {
      console.error('Error deleting notification:', error);
      showSnackbar(t('notifications:deleteError', 'Failed to delete notification'), 'error');
    }
    
    handleMenuClose();
  };

  const handleMarkAllAsRead = async () => {
    if (!currentSchool?.id || !user?.uid) return;
    
    try {
      const unreadNotifications = notifications.filter(notification => !notification.read);
      
      if (unreadNotifications.length === 0) {
        showSnackbar(t('notifications:noUnreadNotifications', 'No unread notifications'), 'info');
        return;
      }
      
      const batch = writeBatch(db);
      
      unreadNotifications.forEach(notification => {
        const notificationRef = doc(db, 'schools', currentSchool.id, 'notifications', notification.id);
        batch.update(notificationRef, {
          read: true,
          updatedAt: Timestamp.now()
        });
      });
      
      await batch.commit();
      
      // Update local state
      setNotifications(prevNotifications => 
        prevNotifications.map(notification => 
          !notification.read 
            ? { ...notification, read: true, updatedAt: Timestamp.now() } 
            : notification
        )
      );
      
      applyFilter(
        notifications.map(notification => 
          !notification.read 
            ? { ...notification, read: true, updatedAt: Timestamp.now() } 
            : notification
        ),
        currentTab
      );
      
      showSnackbar(t('notifications:allMarkedAsRead', 'All notifications marked as read'), 'success');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      showSnackbar(t('notifications:markAllAsReadError', 'Failed to mark all notifications as read'), 'error');
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read if not already read
    if (!notification.read && currentSchool?.id) {
      try {
        await updateDoc(doc(db, 'schools', currentSchool.id, 'notifications', notification.id), {
          read: true,
          updatedAt: Timestamp.now()
        });
        
        // Update local state
        setNotifications(prevNotifications => 
          prevNotifications.map(n => 
            n.id === notification.id 
              ? { ...n, read: true, updatedAt: Timestamp.now() } 
              : n
          )
        );
        
        applyFilter(
          notifications.map(n => 
            n.id === notification.id 
              ? { ...n, read: true, updatedAt: Timestamp.now() } 
              : n
          ),
          currentTab
        );
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }
    
    // Navigate based on notification type
    if (notification.type === 'lesson' && notification.entityId) {
      navigate(`/mobile/lessons/${notification.entityId}`);
    } else if (notification.type === 'program' && notification.entityId) {
      navigate(`/mobile/programs/${notification.entityId}`);
    } else if (notification.type === 'student' && notification.entityId) {
      navigate(`/mobile/students/${notification.entityId}`);
    }
  };

  const formatNotificationTime = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const getNotificationIcon = (notification: Notification) => {
    switch (notification.type) {
      case 'lesson':
        return <EventIcon />;
      case 'program':
        return <SchoolIcon />;
      case 'student':
        return <PersonIcon />;
      default:
        return <NotificationsIcon />;
    }
  };

  const getNotificationColor = (notification: Notification) => {
    switch (notification.type) {
      case 'lesson':
        return 'primary.main';
      case 'program':
        return 'secondary.main';
      case 'student':
        return 'success.main';
      default:
        return 'info.main';
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const getUnreadCount = () => {
    return notifications.filter(notification => !notification.read).length;
  };

  return (
    <MobileLayout title={t('notifications:title', 'Notifications')}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ flexGrow: 1 }}
        >
          <Tab label={t('notifications:allTab', 'All')} value={NotificationTab.ALL} />
          <Tab 
            label={
              <Badge color="error" badgeContent={getUnreadCount()} max={99}>
                {t('notifications:unreadTab', 'Unread')}
              </Badge>
            } 
            value={NotificationTab.UNREAD} 
          />
          <Tab label={t('notifications:readTab', 'Read')} value={NotificationTab.READ} />
        </Tabs>
        
        <IconButton onClick={handleMarkAllAsRead} disabled={getUnreadCount() === 0}>
          <CheckCircleIcon />
        </IconButton>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        {loading ? (
          <Card>
            {[1, 2, 3, 4, 5].map((item) => (
              <Box key={item} sx={{ p: 2 }}>
                <Skeleton variant="rectangular" width="100%" height={60} />
                {item < 5 && <Divider />}
              </Box>
            ))}
          </Card>
        ) : filteredNotifications.length > 0 ? (
          <Card>
            <List sx={{ p: 0 }}>
              {filteredNotifications.map((notification, index) => (
                <React.Fragment key={notification.id}>
                  <ListItem 
                    button
                    onClick={() => handleNotificationClick(notification)}
                    sx={{ 
                      bgcolor: notification.read ? 'inherit' : 'action.hover',
                      position: 'relative'
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: getNotificationColor(notification) }}>
                        {getNotificationIcon(notification)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText 
                      primary={notification.title}
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" component="div" color="text.secondary">
                            {formatNotificationTime(notification.createdAt)}
                          </Typography>
                        </>
                      }
                    />
                    <IconButton 
                      edge="end" 
                      aria-label="more"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuOpen(e, notification.id);
                      }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                    
                    {!notification.read && (
                      <Box 
                        sx={{ 
                          position: 'absolute', 
                          top: 0, 
                          left: 0, 
                          width: 8, 
                          height: '100%', 
                          bgcolor: 'primary.main' 
                        }} 
                      />
                    )}
                  </ListItem>
                  {index < filteredNotifications.length - 1 && <Divider component="li" />}
                </React.Fragment>
              ))}
            </List>
          </Card>
        ) : (
          <Card>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {t('notifications:noNotifications', 'No notifications')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {currentTab === NotificationTab.UNREAD 
                  ? t('notifications:noUnreadNotifications', 'You have no unread notifications')
                  : currentTab === NotificationTab.READ
                    ? t('notifications:noReadNotifications', 'You have no read notifications')
                    : t('notifications:emptyNotifications', 'Your notifications will appear here')}
              </Typography>
            </Box>
          </Card>
        )}
      </Box>
      
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        {selectedNotification && notifications.find(n => n.id === selectedNotification)?.read ? (
          <MenuItem onClick={handleMarkAsUnread}>
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                <NotificationsIcon />
              </Avatar>
            </ListItemAvatar>
            <ListItemText primary={t('notifications:markAsUnread', 'Mark as unread')} />
          </MenuItem>
        ) : (
          <MenuItem onClick={handleMarkAsRead}>
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: 'success.main' }}>
                <CheckCircleIcon />
              </Avatar>
            </ListItemAvatar>
            <ListItemText primary={t('notifications:markAsRead', 'Mark as read')} />
          </MenuItem>
        )}
        <MenuItem onClick={handleDelete}>
          <ListItemAvatar>
            <Avatar sx={{ bgcolor: 'error.main' }}>
              <DeleteIcon />
            </Avatar>
          </ListItemAvatar>
          <ListItemText primary={t('notifications:delete', 'Delete')} />
        </MenuItem>
      </Menu>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileNotifications;
