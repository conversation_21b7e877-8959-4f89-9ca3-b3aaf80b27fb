import React, { useState, useEffect } from 'react';
import { 
  <PERSON>po<PERSON>, 
  Box, 
  TextField, 
  Button, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Select, 
  FormHelperText,
  Snackbar,
  Alert,
  CircularProgress,
  Autocomplete,
  Chip,
  Grid,
  Paper
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  getDocs, 
  Timestamp, 
  serverTimestamp 
} from 'firebase/firestore';
import { Lesson } from '../../types/lesson';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';
import { v4 as uuidv4 } from 'uuid';

const lessonTypes = ['individual', 'group', 'children'];
const disciplines = ['Skiing', 'Snowboarding', 'Cross-country'];
const levels = ['Beginner', 'Intermediate', 'Advanced', 'Expert'];

const MobileLessonForm: React.FC = () => {
  const { t } = useTranslation(['common', 'lessons']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const isEditMode = !!id;
  
  const [formData, setFormData] = useState<Partial<Lesson>>({
    title: '',
    type: 'individual',
    discipline: 'Skiing',
    level: 'Beginner',
    startTime: Timestamp.fromDate(new Date()),
    duration: 60,
    status: 'scheduled',
    instructorId: user?.role === 'instructor' ? user.uid : '',
    studentIds: [],
  });
  
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Fetch instructors
        const instructorsQuery = query(
          collection(db, 'schools', currentSchool.id, 'instructors')
        );
        
        const instructorsSnapshot = await getDocs(instructorsQuery);
        const instructorsData = instructorsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Instructor[];
        
        setInstructors(instructorsData);
        
        // Fetch students
        const studentsQuery = query(
          collection(db, 'schools', currentSchool.id, 'students')
        );
        
        const studentsSnapshot = await getDocs(studentsQuery);
        const studentsData = studentsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Student[];
        
        setStudents(studentsData);
        
        // If in edit mode, fetch lesson data
        if (isEditMode && id) {
          const lessonDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'lessons', id));
          
          if (lessonDoc.exists()) {
            const lessonData = { id: lessonDoc.id, ...lessonDoc.data() } as Lesson;
            setFormData(lessonData);
            
            // Set start date
            if (lessonData.startTime) {
              const startTime = lessonData.startTime instanceof Timestamp 
                ? lessonData.startTime.toDate() 
                : new Date(lessonData.startTime);
              
              setStartDate(startTime);
            }
            
            // Set selected students
            if (lessonData.studentIds && lessonData.studentIds.length > 0) {
              const selectedStudentsData = studentsData.filter(
                student => lessonData.studentIds?.includes(student.id)
              );
              
              setSelectedStudents(selectedStudentsData);
            }
          } else {
            showSnackbar(t('lessons:lessonNotFound', 'Lesson not found'), 'error');
            navigate('/mobile/schedule');
          }
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        showSnackbar(t('lessons:fetchError', 'Failed to load data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, currentSchool?.id, isEditMode, navigate, t, user?.role, user?.uid]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      setStartDate(date);
      setFormData(prev => ({ ...prev, startTime: Timestamp.fromDate(date) }));
      
      // Clear error for this field
      if (errors.startTime) {
        setErrors(prev => ({ ...prev, startTime: '' }));
      }
    }
  };

  const handleStudentsChange = (_: any, newValue: Student[]) => {
    setSelectedStudents(newValue);
    setFormData(prev => ({ 
      ...prev, 
      studentIds: newValue.map(student => student.id) 
    }));
    
    // Clear error for this field
    if (errors.studentIds) {
      setErrors(prev => ({ ...prev, studentIds: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.title) {
      newErrors.title = t('lessons:errors.titleRequired', 'Title is required');
    }
    
    if (!formData.instructorId) {
      newErrors.instructorId = t('lessons:errors.instructorRequired', 'Instructor is required');
    }
    
    if (!formData.startTime) {
      newErrors.startTime = t('lessons:errors.startTimeRequired', 'Start time is required');
    }
    
    if (!formData.duration) {
      newErrors.duration = t('lessons:errors.durationRequired', 'Duration is required');
    } else if (formData.duration <= 0) {
      newErrors.duration = t('lessons:errors.durationPositive', 'Duration must be positive');
    }
    
    if (!formData.studentIds || formData.studentIds.length === 0) {
      newErrors.studentIds = t('lessons:errors.studentsRequired', 'At least one student is required');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !currentSchool?.id) {
      return;
    }
    
    try {
      setSaving(true);
      
      const lessonData = {
        ...formData,
        updatedAt: serverTimestamp(),
      };
      
      if (isEditMode && id) {
        // Update existing lesson
        await updateDoc(doc(db, 'schools', currentSchool.id, 'lessons', id), lessonData);
        showSnackbar(t('lessons:updateSuccess', 'Lesson updated successfully'), 'success');
      } else {
        // Create new lesson
        const newLessonId = uuidv4();
        await setDoc(doc(db, 'schools', currentSchool.id, 'lessons', newLessonId), {
          ...lessonData,
          createdAt: serverTimestamp(),
          createdBy: user?.uid,
        });
        showSnackbar(t('lessons:createSuccess', 'Lesson created successfully'), 'success');
      }
      
      // Navigate back to schedule
      navigate('/mobile/schedule');
      
    } catch (error) {
      console.error('Error saving lesson:', error);
      showSnackbar(
        isEditMode 
          ? t('lessons:updateError', 'Failed to update lesson') 
          : t('lessons:createError', 'Failed to create lesson'), 
        'error'
      );
    } finally {
      setSaving(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  if (loading) {
    return (
      <MobileLayout 
        title={isEditMode ? t('lessons:editLesson', 'Edit Lesson') : t('lessons:createLesson', 'Create Lesson')} 
        showBackButton
      >
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout 
      title={isEditMode ? t('lessons:editLesson', 'Edit Lesson') : t('lessons:createLesson', 'Create Lesson')} 
      showBackButton
    >
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          {isEditMode 
            ? t('lessons:editLessonDetails', 'Edit Lesson Details') 
            : t('lessons:newLessonDetails', 'New Lesson Details')}
        </Typography>
        
        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                name="title"
                label={t('lessons:title', 'Title')}
                value={formData.title || ''}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.title}
                helperText={errors.title}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal" error={!!errors.type}>
                <InputLabel id="type-label">{t('lessons:type', 'Type')}</InputLabel>
                <Select
                  labelId="type-label"
                  name="type"
                  value={formData.type || 'individual'}
                  onChange={handleSelectChange}
                  label={t('lessons:type', 'Type')}
                >
                  {lessonTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {t(`lessons:types.${type}`, type)}
                    </MenuItem>
                  ))}
                </Select>
                {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal" error={!!errors.discipline}>
                <InputLabel id="discipline-label">{t('lessons:discipline', 'Discipline')}</InputLabel>
                <Select
                  labelId="discipline-label"
                  name="discipline"
                  value={formData.discipline || 'Skiing'}
                  onChange={handleSelectChange}
                  label={t('lessons:discipline', 'Discipline')}
                >
                  {disciplines.map(discipline => (
                    <MenuItem key={discipline} value={discipline}>
                      {discipline}
                    </MenuItem>
                  ))}
                </Select>
                {errors.discipline && <FormHelperText>{errors.discipline}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal" error={!!errors.level}>
                <InputLabel id="level-label">{t('lessons:level', 'Level')}</InputLabel>
                <Select
                  labelId="level-label"
                  name="level"
                  value={formData.level || 'Beginner'}
                  onChange={handleSelectChange}
                  label={t('lessons:level', 'Level')}
                >
                  {levels.map(level => (
                    <MenuItem key={level} value={level}>
                      {level}
                    </MenuItem>
                  ))}
                </Select>
                {errors.level && <FormHelperText>{errors.level}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="duration"
                label={t('lessons:duration', 'Duration (minutes)')}
                type="number"
                value={formData.duration || 60}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.duration}
                helperText={errors.duration}
                margin="normal"
                InputProps={{ inputProps: { min: 15, step: 15 } }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label={t('lessons:startTime', 'Start Time')}
                  value={startDate}
                  onChange={handleDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      margin: 'normal',
                      error: !!errors.startTime,
                      helperText: errors.startTime
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal" error={!!errors.instructorId}>
                <InputLabel id="instructor-label">{t('lessons:instructor', 'Instructor')}</InputLabel>
                <Select
                  labelId="instructor-label"
                  name="instructorId"
                  value={formData.instructorId || ''}
                  onChange={handleSelectChange}
                  label={t('lessons:instructor', 'Instructor')}
                  disabled={user?.role === 'instructor'}
                >
                  {instructors.map(instructor => (
                    <MenuItem key={instructor.id} value={instructor.id}>
                      {instructor.firstName} {instructor.lastName}
                    </MenuItem>
                  ))}
                </Select>
                {errors.instructorId && <FormHelperText>{errors.instructorId}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Autocomplete
                multiple
                id="students"
                options={students}
                value={selectedStudents}
                onChange={handleStudentsChange}
                getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      label={`${option.firstName} ${option.lastName}`}
                      {...getTagProps({ index })}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('lessons:students', 'Students')}
                    placeholder={t('lessons:selectStudents', 'Select students')}
                    error={!!errors.studentIds}
                    helperText={errors.studentIds}
                    margin="normal"
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  fullWidth
                  disabled={saving}
                >
                  {saving 
                    ? t('common:saving', 'Saving...') 
                    : isEditMode 
                      ? t('common:update', 'Update') 
                      : t('common:create', 'Create')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileLessonForm;
