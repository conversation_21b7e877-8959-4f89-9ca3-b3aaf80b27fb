import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Card, 
  CardContent, 
  Divider, 
  Button, 
  Chip, 
  IconButton, 
  Skeleton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Snackbar,
  Alert
} from '@mui/material';
import { 
  AccessTime as TimeIcon, 
  Event as EventIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Schedule as ScheduleIcon,
  Assignment as AssignmentIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { doc, getDoc, updateDoc, collection, getDocs, query, where, Timestamp, deleteDoc } from 'firebase/firestore';
import { Lesson } from '../../types/lesson';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';
import LessonRescheduleDialog from '../../components/lessons/LessonRescheduleDialog';

const MobileLessonDetail: React.FC = () => {
  const { t } = useTranslation(['common', 'lessons']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [instructor, setInstructor] = useState<Instructor | null>(null);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [speedDialOpen, setSpeedDialOpen] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [confirmCancelOpen, setConfirmCancelOpen] = useState(false);
  const [confirmCompleteOpen, setConfirmCompleteOpen] = useState(false);
  const [rescheduleDialogOpen, setRescheduleDialogOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');

  useEffect(() => {
    const fetchLessonData = async () => {
      if (!id || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Fetch lesson data
        const lessonDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'lessons', id));
        
        if (!lessonDoc.exists()) {
          console.error('Lesson not found');
          navigate('/mobile/schedule');
          return;
        }
        
        const lessonData = { id: lessonDoc.id, ...lessonDoc.data() } as Lesson;
        setLesson(lessonData);
        
        // Fetch instructor data
        if (lessonData.instructorId) {
          const instructorDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'instructors', lessonData.instructorId));
          if (instructorDoc.exists()) {
            setInstructor({ id: instructorDoc.id, ...instructorDoc.data() } as Instructor);
          }
        }
        
        // Fetch students data
        if (lessonData.studentIds && lessonData.studentIds.length > 0) {
          const studentsData: Student[] = [];
          
          for (const studentId of lessonData.studentIds) {
            const studentDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'students', studentId));
            if (studentDoc.exists()) {
              studentsData.push({ id: studentDoc.id, ...studentDoc.data() } as Student);
            }
          }
          
          setStudents(studentsData);
        }
        
      } catch (error) {
        console.error('Error fetching lesson data:', error);
        showSnackbar(t('lessons:messages.fetchError', 'Failed to load lesson data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchLessonData();
  }, [id, currentSchool?.id, navigate, t]);

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'HH:mm');
  };
  
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  const getLessonStatusChip = () => {
    if (!lesson) return null;
    
    switch (lesson.status) {
      case 'completed':
        return <Chip icon={<CheckCircleIcon />} label={t('lessons:status.completed')} color="success" />;
      case 'cancelled':
        return <Chip icon={<CancelIcon />} label={t('lessons:status.cancelled')} color="error" />;
      case 'scheduled':
      default:
        return <Chip icon={<TimeIcon />} label={t('lessons:status.scheduled')} color="primary" />;
    }
  };

  const handleDelete = async () => {
    if (!id || !currentSchool?.id) return;
    
    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'lessons', id));
      showSnackbar(t('lessons:messages.deleteSuccess', 'Lesson deleted successfully'), 'success');
      navigate('/mobile/schedule');
    } catch (error) {
      console.error('Error deleting lesson:', error);
      showSnackbar(t('lessons:messages.deleteError', 'Failed to delete lesson'), 'error');
    }
  };

  const handleCancel = async () => {
    if (!id || !currentSchool?.id || !lesson) return;
    
    try {
      await updateDoc(doc(db, 'schools', currentSchool.id, 'lessons', id), {
        status: 'cancelled',
        updatedAt: Timestamp.now()
      });
      
      setLesson({
        ...lesson,
        status: 'cancelled',
        updatedAt: Timestamp.now()
      });
      
      showSnackbar(t('lessons:messages.cancelSuccess', 'Lesson cancelled successfully'), 'success');
      setConfirmCancelOpen(false);
    } catch (error) {
      console.error('Error cancelling lesson:', error);
      showSnackbar(t('lessons:messages.cancelError', 'Failed to cancel lesson'), 'error');
    }
  };

  const handleComplete = async () => {
    if (!id || !currentSchool?.id || !lesson) return;
    
    try {
      await updateDoc(doc(db, 'schools', currentSchool.id, 'lessons', id), {
        status: 'completed',
        updatedAt: Timestamp.now()
      });
      
      setLesson({
        ...lesson,
        status: 'completed',
        updatedAt: Timestamp.now()
      });
      
      showSnackbar(t('lessons:messages.completeSuccess', 'Lesson marked as completed'), 'success');
      setConfirmCompleteOpen(false);
    } catch (error) {
      console.error('Error completing lesson:', error);
      showSnackbar(t('lessons:messages.completeError', 'Failed to mark lesson as completed'), 'error');
    }
  };

  const handleReschedule = () => {
    setRescheduleDialogOpen(true);
  };

  const handleRescheduleSuccess = (updatedLesson: Lesson) => {
    setLesson(updatedLesson);
    showSnackbar(t('lessons:messages.rescheduleSuccess', 'Lesson rescheduled successfully'), 'success');
    setRescheduleDialogOpen(false);
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const canEdit = () => {
    if (!user || !lesson) return false;
    
    // Admin can edit any lesson
    if (user.role === 'admin') return true;
    
    // Instructor can edit their own lessons
    if (user.role === 'instructor' && user.uid === lesson.instructorId) return true;
    
    return false;
  };

  const actions = [
    { icon: <EditIcon />, name: t('common:edit', 'Edit'), action: () => navigate(`/mobile/lessons/edit/${id}`) },
    { icon: <ScheduleIcon />, name: t('lessons:reschedule', 'Reschedule'), action: handleReschedule },
    { icon: <CheckCircleIcon />, name: t('lessons:markComplete', 'Mark Complete'), action: () => setConfirmCompleteOpen(true) },
    { icon: <CancelIcon />, name: t('lessons:cancel', 'Cancel Lesson'), action: () => setConfirmCancelOpen(true) },
    { icon: <DeleteIcon />, name: t('common:delete', 'Delete'), action: () => setConfirmDeleteOpen(true) },
  ];

  return (
    <MobileLayout 
      title={lesson?.title || t('lessons:lessonDetails', 'Lesson Details')} 
      showBackButton
    >
      {loading ? (
        <Box>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={150} />
        </Box>
      ) : lesson ? (
        <>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h5" component="h1">
              {lesson.title}
            </Typography>
            {getLessonStatusChip()}
          </Box>
          
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EventIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {formatDate(lesson.startTime)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <TimeIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {formatTime(lesson.startTime)} - {lesson.duration} min
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('lessons:type', 'Type')}
                  </Typography>
                  <Typography variant="body1">
                    {t(`lessons:types.${lesson.type}`, lesson.type)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('lessons:discipline', 'Discipline')}
                  </Typography>
                  <Typography variant="body1">
                    {lesson.discipline}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('lessons:level', 'Level')}
                  </Typography>
                  <Typography variant="body1">
                    {lesson.level}
                  </Typography>
                </Grid>
                {lesson.programId && (
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      {t('lessons:program', 'Program')}
                    </Typography>
                    <Button 
                      size="small" 
                      startIcon={<SchoolIcon />}
                      onClick={() => navigate(`/mobile/programs/${lesson.programId}`)}
                    >
                      {t('lessons:viewProgram', 'View Program')}
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
          
          <Typography variant="h6" gutterBottom>
            {t('lessons:instructor', 'Instructor')}
          </Typography>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              {instructor ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                    {instructor.firstName?.charAt(0) || 'I'}
                  </Avatar>
                  <Box>
                    <Typography variant="body1">
                      {instructor.firstName} {instructor.lastName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {instructor.email}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  {t('lessons:noInstructor', 'No instructor assigned')}
                </Typography>
              )}
            </CardContent>
          </Card>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {t('lessons:students', 'Students')} ({students.length})
            </Typography>
            <Button 
              size="small" 
              startIcon={<AssignmentIcon />}
              onClick={() => navigate(`/mobile/lessons/${id}/attendance`)}
            >
              {t('lessons:attendance', 'Attendance')}
            </Button>
          </Box>
          <Card>
            {students.length > 0 ? (
              <List sx={{ p: 0 }}>
                {students.map((student, index) => (
                  <React.Fragment key={student.id}>
                    <ListItem 
                      button
                      onClick={() => navigate(`/mobile/students/${student.id}`)}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {student.firstName?.charAt(0) || 'S'}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText 
                        primary={`${student.firstName} ${student.lastName}`}
                        secondary={student.email}
                      />
                    </ListItem>
                    {index < students.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <CardContent>
                <Typography variant="body2" color="text.secondary" align="center">
                  {t('lessons:noStudents', 'No students assigned to this lesson')}
                </Typography>
              </CardContent>
            )}
          </Card>
          
          {canEdit() && (
            <SpeedDial
              ariaLabel="lesson actions"
              sx={{ position: 'fixed', bottom: 70, right: 16 }}
              icon={<SpeedDialIcon />}
              onClose={() => setSpeedDialOpen(false)}
              onOpen={() => setSpeedDialOpen(true)}
              open={speedDialOpen}
              direction="up"
            >
              {actions.map((action) => (
                <SpeedDialAction
                  key={action.name}
                  icon={action.icon}
                  tooltipTitle={action.name}
                  onClick={() => {
                    setSpeedDialOpen(false);
                    action.action();
                  }}
                />
              ))}
            </SpeedDial>
          )}
          
          {/* Reschedule Dialog */}
          {lesson && (
            <LessonRescheduleDialog
              open={rescheduleDialogOpen}
              onClose={() => setRescheduleDialogOpen(false)}
              lesson={lesson}
              onReschedule={handleRescheduleSuccess}
            />
          )}
          
          {/* Confirm Delete Dialog */}
          <Dialog
            open={confirmDeleteOpen}
            onClose={() => setConfirmDeleteOpen(false)}
          >
            <DialogTitle>{t('lessons:confirmDelete', 'Delete Lesson?')}</DialogTitle>
            <DialogContent>
              <Typography>
                {t('lessons:confirmDeleteMessage', 'Are you sure you want to delete this lesson? This action cannot be undone.')}
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setConfirmDeleteOpen(false)}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button onClick={handleDelete} color="error">
                {t('common:delete', 'Delete')}
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Confirm Cancel Dialog */}
          <Dialog
            open={confirmCancelOpen}
            onClose={() => setConfirmCancelOpen(false)}
          >
            <DialogTitle>{t('lessons:confirmCancel', 'Cancel Lesson?')}</DialogTitle>
            <DialogContent>
              <Typography>
                {t('lessons:confirmCancelMessage', 'Are you sure you want to cancel this lesson?')}
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setConfirmCancelOpen(false)}>
                {t('common:no', 'No')}
              </Button>
              <Button onClick={handleCancel} color="primary">
                {t('common:yes', 'Yes')}
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Confirm Complete Dialog */}
          <Dialog
            open={confirmCompleteOpen}
            onClose={() => setConfirmCompleteOpen(false)}
          >
            <DialogTitle>{t('lessons:confirmComplete', 'Mark as Completed?')}</DialogTitle>
            <DialogContent>
              <Typography>
                {t('lessons:confirmCompleteMessage', 'Are you sure you want to mark this lesson as completed?')}
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setConfirmCompleteOpen(false)}>
                {t('common:no', 'No')}
              </Button>
              <Button onClick={handleComplete} color="primary">
                {t('common:yes', 'Yes')}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Typography variant="body1" color="error">
          {t('lessons:lessonNotFound', 'Lesson not found')}
        </Typography>
      )}
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileLessonDetail;
