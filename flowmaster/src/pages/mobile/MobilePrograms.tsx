import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Card,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  IconButton,
  Chip,
  Skeleton,
  Tabs,
  Tab,
  Snackbar,
  Alert,
  Menu,
  MenuItem,
  LinearProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  School as SchoolIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  ArrowForward as ArrowForwardIcon,
  MoreVert as MoreVertIcon,
  Event as EventIcon,
  Person as PersonIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, orderBy, limit, startAfter, Timestamp, doc, getDoc } from 'firebase/firestore';
import { Program } from '../../types/program';
import { format } from 'date-fns';

enum ProgramTab {
  ALL = 'all',
  ACTIVE = 'active',
  COMPLETED = 'completed'
}

enum SortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  DATE_ASC = 'date_asc',
  DATE_DESC = 'date_desc'
}

const MobilePrograms: React.FC = () => {
  const { t } = useTranslation(['common', 'programs']);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();

  const [programs, setPrograms] = useState<Program[]>([]);
  const [filteredPrograms, setFilteredPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTab, setCurrentTab] = useState<ProgramTab>(ProgramTab.ALL);
  const [sortOption, setSortOption] = useState<SortOption>(SortOption.DATE_DESC);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [programStats, setProgramStats] = useState<Record<string, { students: number, sessions: number, completedSessions: number }>>({});
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    const fetchPrograms = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        let programsQuery;

        if (user?.role === 'instructor') {
          // Instructors only see programs they're assigned to
          programsQuery = query(
            collection(db, 'schools', currentSchool.id, 'programs'),
            where('instructorIds', 'array-contains', user.uid),
            orderBy('startDate', 'desc'),
            limit(20)
          );
        } else {
          // Admins see all programs
          programsQuery = query(
            collection(db, 'schools', currentSchool.id, 'programs'),
            orderBy('startDate', 'desc'),
            limit(20)
          );
        }

        const programsSnapshot = await getDocs(programsQuery);

        if (programsSnapshot.empty) {
          setPrograms([]);
          setFilteredPrograms([]);
          setHasMore(false);
        } else {
          const programsData = programsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Program[];

          setPrograms(programsData);
          setFilteredPrograms(programsData);
          setLastVisible(programsSnapshot.docs[programsSnapshot.docs.length - 1]);
          setHasMore(programsSnapshot.docs.length === 20);

          // Fetch stats for each program
          await fetchProgramStats(programsData);
        }
      } catch (error) {
        console.error('Error fetching programs:', error);
        showSnackbar(t('programs:fetchError', 'Failed to load programs'), 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchPrograms();
  }, [currentSchool?.id, user?.role, user?.uid, t]);

  const fetchProgramStats = async (programsData: Program[]) => {
    if (!currentSchool?.id) return;

    const stats: Record<string, { students: number, sessions: number, completedSessions: number }> = {};

    for (const program of programsData) {
      try {
        // Get number of students
        const studentsCount = program.studentIds?.length || 0;

        // Get number of sessions
        const sessionsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs', program.id, 'sessions')
        );

        const sessionsSnapshot = await getDocs(sessionsQuery);
        const sessionsCount = sessionsSnapshot.size;

        // Get number of completed sessions
        const completedSessionsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs', program.id, 'sessions'),
          where('status', '==', 'completed')
        );

        const completedSessionsSnapshot = await getDocs(completedSessionsQuery);
        const completedSessionsCount = completedSessionsSnapshot.size;

        stats[program.id] = {
          students: studentsCount,
          sessions: sessionsCount,
          completedSessions: completedSessionsCount
        };
      } catch (error) {
        console.error(`Error fetching stats for program ${program.id}:`, error);
        stats[program.id] = {
          students: 0,
          sessions: 0,
          completedSessions: 0
        };
      }
    }

    setProgramStats(stats);
  };

  const loadMorePrograms = async () => {
    if (!currentSchool?.id || !lastVisible || loadingMore) return;

    try {
      setLoadingMore(true);

      let programsQuery;

      if (user?.role === 'instructor') {
        // Instructors only see programs they're assigned to
        programsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs'),
          where('instructorIds', 'array-contains', user.uid),
          orderBy('startDate', 'desc'),
          startAfter(lastVisible),
          limit(20)
        );
      } else {
        // Admins see all programs
        programsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs'),
          orderBy('startDate', 'desc'),
          startAfter(lastVisible),
          limit(20)
        );
      }

      const programsSnapshot = await getDocs(programsQuery);

      if (programsSnapshot.empty) {
        setHasMore(false);
      } else {
        const newProgramsData = programsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Program[];

        setPrograms(prev => [...prev, ...newProgramsData]);
        setLastVisible(programsSnapshot.docs[programsSnapshot.docs.length - 1]);
        setHasMore(programsSnapshot.docs.length === 20);

        // Fetch stats for new programs
        await fetchProgramStats(newProgramsData);

        // Apply current filters to the updated list
        applyFilters([...programs, ...newProgramsData]);
      }
    } catch (error) {
      console.error('Error loading more programs:', error);
      showSnackbar(t('programs:loadMoreError', 'Failed to load more programs'), 'error');
    } finally {
      setLoadingMore(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    applyFilters(programs, e.target.value, currentTab, sortOption);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: ProgramTab) => {
    setCurrentTab(newValue);
    applyFilters(programs, searchQuery, newValue, sortOption);
  };

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleSortOptionSelect = (option: SortOption) => {
    setSortOption(option);
    applyFilters(programs, searchQuery, currentTab, option);
    handleSortMenuClose();
  };

  const applyFilters = (
    programsList: Program[],
    search: string = searchQuery,
    tab: ProgramTab = currentTab,
    sort: SortOption = sortOption
  ) => {
    // Filter by search query
    let filtered = programsList;

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(program =>
        program.title.toLowerCase().includes(searchLower) ||
        program.description?.toLowerCase().includes(searchLower) ||
        program.discipline?.toLowerCase().includes(searchLower)
      );
    }

    // Filter by tab
    if (tab === ProgramTab.ACTIVE) {
      const now = new Date();
      filtered = filtered.filter(program => {
        const endDate = program.endDate instanceof Timestamp
          ? program.endDate.toDate()
          : new Date(program.endDate);
        return endDate >= now;
      });
    } else if (tab === ProgramTab.COMPLETED) {
      const now = new Date();
      filtered = filtered.filter(program => {
        const endDate = program.endDate instanceof Timestamp
          ? program.endDate.toDate()
          : new Date(program.endDate);
        return endDate < now;
      });
    }

    // Apply sorting
    if (sort === SortOption.NAME_ASC) {
      filtered.sort((a, b) => a.title.localeCompare(b.title));
    } else if (sort === SortOption.NAME_DESC) {
      filtered.sort((a, b) => b.title.localeCompare(a.title));
    } else if (sort === SortOption.DATE_ASC) {
      filtered.sort((a, b) => {
        const dateA = a.startDate instanceof Timestamp
          ? a.startDate.toDate()
          : new Date(a.startDate);
        const dateB = b.startDate instanceof Timestamp
          ? b.startDate.toDate()
          : new Date(b.startDate);
        return dateA.getTime() - dateB.getTime();
      });
    } else if (sort === SortOption.DATE_DESC) {
      filtered.sort((a, b) => {
        const dateA = a.startDate instanceof Timestamp
          ? a.startDate.toDate()
          : new Date(a.startDate);
        const dateB = b.startDate instanceof Timestamp
          ? b.startDate.toDate()
          : new Date(b.startDate);
        return dateB.getTime() - dateA.getTime();
      });
    }

    setFilteredPrograms(filtered);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';

    const date = timestamp instanceof Timestamp
      ? timestamp.toDate()
      : new Date(timestamp);

    return format(date, 'MMM d, yyyy');
  };

  const getCompletionPercentage = (programId: string) => {
    const stats = programStats[programId];
    if (!stats || stats.sessions === 0) return 0;
    return Math.round((stats.completedSessions / stats.sessions) * 100);
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const bottom = e.currentTarget.scrollHeight - e.currentTarget.scrollTop <= e.currentTarget.clientHeight + 200;
    if (bottom && hasMore && !loadingMore) {
      loadMorePrograms();
    }
  };

  return (
    <MobileLayout title={t('common:programs', 'Programs')}>
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          placeholder={t('programs:searchPlaceholder', 'Search programs...')}
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size="small"
        />
      </Box>

      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ flexGrow: 1 }}
        >
          <Tab label={t('programs:allPrograms', 'All')} value={ProgramTab.ALL} />
          <Tab label={t('programs:activePrograms', 'Active')} value={ProgramTab.ACTIVE} />
          <Tab label={t('programs:completedPrograms', 'Completed')} value={ProgramTab.COMPLETED} />
        </Tabs>

        <IconButton onClick={handleSortMenuOpen}>
          <SortIcon />
        </IconButton>

        <Menu
          anchorEl={menuAnchorEl}
          open={Boolean(menuAnchorEl)}
          onClose={handleSortMenuClose}
        >
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.NAME_ASC)}
            selected={sortOption === SortOption.NAME_ASC}
          >
            {t('programs:sortNameAsc', 'Name (A-Z)')}
          </MenuItem>
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.NAME_DESC)}
            selected={sortOption === SortOption.NAME_DESC}
          >
            {t('programs:sortNameDesc', 'Name (Z-A)')}
          </MenuItem>
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.DATE_ASC)}
            selected={sortOption === SortOption.DATE_ASC}
          >
            {t('programs:sortDateAsc', 'Date (Oldest first)')}
          </MenuItem>
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.DATE_DESC)}
            selected={sortOption === SortOption.DATE_DESC}
          >
            {t('programs:sortDateDesc', 'Date (Newest first)')}
          </MenuItem>
        </Menu>
      </Box>

      <Box sx={{ mb: 2 }} onScroll={handleScroll}>
        {loading ? (
          <Card>
            {[1, 2, 3, 4, 5].map((item) => (
              <Box key={item} sx={{ p: 2 }}>
                <Skeleton variant="rectangular" width="100%" height={100} />
                {item < 5 && <Divider />}
              </Box>
            ))}
          </Card>
        ) : filteredPrograms.length > 0 ? (
          <Card>
            <List sx={{ p: 0 }}>
              {filteredPrograms.map((program, index) => {
                const completionPercentage = getCompletionPercentage(program.id);
                const isCompleted = currentTab === ProgramTab.COMPLETED;

                return (
                  <React.Fragment key={program.id}>
                    <ListItem
                      button
                      onClick={() => navigate(`/mobile/programs/${program.id}`)}
                      sx={{ flexDirection: 'column', alignItems: 'flex-start' }}
                    >
                      <Box sx={{ display: 'flex', width: '100%', mb: 1 }}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: isCompleted ? 'success.main' : 'primary.main' }}>
                            <SchoolIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1" component="span">
                                {program.title}
                              </Typography>
                              {program.discipline && (
                                <Chip size="small" label={program.discipline} color="primary" />
                              )}
                            </Box>
                          }
                          secondary={
                            <>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <EventIcon fontSize="small" />
                                <Typography variant="body2" component="span">
                                  {formatDate(program.startDate)} - {formatDate(program.endDate)}
                                </Typography>
                              </Box>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <PersonIcon fontSize="small" />
                                <Typography variant="body2" component="span">
                                  {programStats[program.id]?.students || 0} {t('common:students', 'Students')}
                                </Typography>
                              </Box>
                            </>
                          }
                        />
                        <IconButton edge="end" aria-label="details">
                          <ArrowForwardIcon />
                        </IconButton>
                      </Box>

                      <Box sx={{ width: '100%', px: 2, pb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2" color="text.secondary">
                            {t('programs:completion', 'Completion')}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {completionPercentage}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={completionPercentage}
                          color={completionPercentage === 100 ? "success" : "primary"}
                        />
                      </Box>
                    </ListItem>
                    {index < filteredPrograms.length - 1 && <Divider component="li" />}
                  </React.Fragment>
                );
              })}
              {loadingMore && (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Skeleton variant="rectangular" width="100%" height={100} />
                </Box>
              )}
            </List>
          </Card>
        ) : (
          <Card>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <SchoolIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {t('programs:noProgramsFound', 'No programs found')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchQuery
                  ? t('programs:tryDifferentSearch', 'Try a different search term or filter')
                  : t('programs:noProgramsYet', 'No programs have been added yet')}
              </Typography>
            </Box>
          </Card>
        )}
      </Box>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobilePrograms;
