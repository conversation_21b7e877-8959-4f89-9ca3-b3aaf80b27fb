import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Card, 
  CardContent, 
  Divider, 
  Button, 
  Chip, 
  IconButton, 
  Skeleton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Grid,
  Tabs,
  Tab,
  Snackbar,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import { 
  AccessTime as TimeIcon, 
  Event as EventIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  ArrowForward as ArrowForwardIcon,
  Add as AddIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { doc, getDoc, collection, getDocs, query, where, orderBy, Timestamp } from 'firebase/firestore';
import { Program } from '../../types/program';
import { ProgramSession } from '../../types/programSession';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';

enum TabValue {
  SESSIONS = 'sessions',
  STUDENTS = 'students',
  INSTRUCTORS = 'instructors'
}

const MobileProgramDetail: React.FC = () => {
  const { t } = useTranslation(['common', 'programs']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [program, setProgram] = useState<Program | null>(null);
  const [sessions, setSessions] = useState<ProgramSession[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentTab, setCurrentTab] = useState<TabValue>(TabValue.SESSIONS);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);

  useEffect(() => {
    const fetchProgramData = async () => {
      if (!id || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Fetch program data
        const programDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'programs', id));
        
        if (!programDoc.exists()) {
          console.error('Program not found');
          navigate('/mobile/programs');
          return;
        }
        
        const programData = { id: programDoc.id, ...programDoc.data() } as Program;
        setProgram(programData);
        
        // Fetch program sessions
        const sessionsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs', id, 'sessions'),
          orderBy('date', 'asc')
        );
        
        const sessionsSnapshot = await getDocs(sessionsQuery);
        const sessionsData = sessionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as ProgramSession[];
        
        setSessions(sessionsData);
        
        // Fetch students
        if (programData.studentIds && programData.studentIds.length > 0) {
          const studentsData: Student[] = [];
          
          for (const studentId of programData.studentIds) {
            const studentDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'students', studentId));
            if (studentDoc.exists()) {
              studentsData.push({ id: studentDoc.id, ...studentDoc.data() } as Student);
            }
          }
          
          setStudents(studentsData);
        }
        
        // Fetch instructors
        if (programData.instructorIds && programData.instructorIds.length > 0) {
          const instructorsData: Instructor[] = [];
          
          for (const instructorId of programData.instructorIds) {
            const instructorDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'instructors', instructorId));
            if (instructorDoc.exists()) {
              instructorsData.push({ id: instructorDoc.id, ...instructorDoc.data() } as Instructor);
            }
          }
          
          setInstructors(instructorsData);
        }
        
      } catch (error) {
        console.error('Error fetching program data:', error);
        showSnackbar(t('programs:fetchError', 'Failed to load program data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProgramData();
  }, [id, currentSchool?.id, navigate, t]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: TabValue) => {
    setCurrentTab(newValue);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  const getSessionStatusChip = (session: ProgramSession) => {
    switch (session.status) {
      case 'completed':
        return <Chip size="small" icon={<CheckCircleIcon />} label={t('programs:status.completed')} color="success" />;
      case 'cancelled':
        return <Chip size="small" icon={<CancelIcon />} label={t('programs:status.cancelled')} color="error" />;
      case 'scheduled':
      default:
        return <Chip size="small" icon={<TimeIcon />} label={t('programs:status.scheduled')} color="primary" />;
    }
  };

  const getCompletionPercentage = () => {
    if (!sessions.length) return 0;
    const completedSessions = sessions.filter(session => session.status === 'completed').length;
    return Math.round((completedSessions / sessions.length) * 100);
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const isProgramActive = () => {
    if (!program) return false;
    
    const now = new Date();
    const endDate = program.endDate instanceof Timestamp 
      ? program.endDate.toDate() 
      : new Date(program.endDate);
      
    return endDate >= now;
  };

  return (
    <MobileLayout 
      title={program?.title || t('programs:programDetails', 'Program Details')} 
      showBackButton
    >
      {loading ? (
        <Box>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={150} />
        </Box>
      ) : program ? (
        <>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="h5" gutterBottom>
                  {program.title}
                </Typography>
                {program.discipline && (
                  <Chip label={program.discipline} color="primary" sx={{ mr: 1 }} />
                )}
                {program.level && (
                  <Chip label={program.level} color="secondary" />
                )}
              </Box>
              
              {program.description && (
                <Typography variant="body2" color="text.secondary" paragraph>
                  {program.description}
                </Typography>
              )}
              
              <Divider sx={{ my: 2 }} />
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EventIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {formatDate(program.startDate)} - {formatDate(program.endDate)}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('programs:students', 'Students')}
                  </Typography>
                  <Typography variant="body1">
                    {students.length}
                  </Typography>
                </Grid>
                
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('programs:sessions', 'Sessions')}
                  </Typography>
                  <Typography variant="body1">
                    {sessions.length}
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {t('programs:completion', 'Completion')}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress 
                        variant="determinate" 
                        value={getCompletionPercentage()} 
                        color={getCompletionPercentage() === 100 ? "success" : "primary"}
                      />
                    </Box>
                    <Box sx={{ minWidth: 35 }}>
                      <Typography variant="body2" color="text.secondary">
                        {getCompletionPercentage()}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          
          <Box sx={{ mb: 2 }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="fullWidth"
              indicatorColor="primary"
              textColor="primary"
            >
              <Tab 
                label={t('programs:sessions', 'Sessions')} 
                value={TabValue.SESSIONS} 
              />
              <Tab 
                label={t('programs:students', 'Students')} 
                value={TabValue.STUDENTS} 
              />
              <Tab 
                label={t('programs:instructors', 'Instructors')} 
                value={TabValue.INSTRUCTORS} 
              />
            </Tabs>
          </Box>
          
          {currentTab === TabValue.SESSIONS && (
            <Card>
              {sessions.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {sessions.map((session, index) => (
                    <React.Fragment key={session.id}>
                      <ListItem 
                        button
                        onClick={() => navigate(`/mobile/programs/${id}/sessions/${session.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: session.status === 'completed' ? 'success.main' : 'primary.main' }}>
                            <EventIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1" component="span">
                                {t('programs:sessionNumber', 'Session {{number}}', { number: index + 1 })}
                              </Typography>
                              {getSessionStatusChip(session)}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography variant="body2" component="span">
                                {formatDate(session.date)}
                              </Typography>
                              {session.lessonId && (
                                <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                                  {t('programs:hasLesson', 'Has scheduled lesson')}
                                </Typography>
                              )}
                            </>
                          }
                        />
                        <IconButton edge="end" aria-label="details">
                          <ArrowForwardIcon />
                        </IconButton>
                      </ListItem>
                      {index < sessions.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <CardContent>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('programs:noSessions', 'No sessions found for this program')}
                  </Typography>
                </CardContent>
              )}
            </Card>
          )}
          
          {currentTab === TabValue.STUDENTS && (
            <Card>
              {students.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {students.map((student, index) => (
                    <React.Fragment key={student.id}>
                      <ListItem 
                        button
                        onClick={() => navigate(`/mobile/students/${student.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {student.firstName?.charAt(0) || 'S'}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={`${student.firstName} ${student.lastName}`}
                          secondary={student.email}
                        />
                        <IconButton edge="end" aria-label="details">
                          <ArrowForwardIcon />
                        </IconButton>
                      </ListItem>
                      {index < students.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <CardContent>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('programs:noStudents', 'No students enrolled in this program')}
                  </Typography>
                </CardContent>
              )}
            </Card>
          )}
          
          {currentTab === TabValue.INSTRUCTORS && (
            <Card>
              {instructors.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {instructors.map((instructor, index) => (
                    <React.Fragment key={instructor.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {instructor.firstName?.charAt(0) || 'I'}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={`${instructor.firstName} ${instructor.lastName}`}
                          secondary={instructor.email}
                        />
                      </ListItem>
                      {index < instructors.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <CardContent>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('programs:noInstructors', 'No instructors assigned to this program')}
                  </Typography>
                </CardContent>
              )}
            </Card>
          )}
          
          {isProgramActive() && currentTab === TabValue.SESSIONS && (
            <Box sx={{ position: 'fixed', bottom: 70, right: 16 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AssignmentIcon />}
                onClick={() => navigate(`/mobile/programs/${id}/attendance`)}
              >
                {t('programs:takeAttendance', 'Take Attendance')}
              </Button>
            </Box>
          )}
        </>
      ) : (
        <Typography variant="body1" color="error">
          {t('programs:programNotFound', 'Program not found')}
        </Typography>
      )}
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileProgramDetail;
