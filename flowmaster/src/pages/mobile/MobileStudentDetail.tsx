import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Card, 
  CardContent, 
  Divider, 
  Button, 
  Chip, 
  IconButton, 
  Skeleton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Grid,
  Tabs,
  Tab,
  Snackbar,
  Alert,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField
} from '@mui/material';
import { 
  AccessTime as TimeIcon, 
  Event as EventIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Notes as NotesIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { doc, getDoc, updateDoc, collection, getDocs, query, where, Timestamp, deleteDoc, orderBy, limit } from 'firebase/firestore';
import { Student } from '../../types/student';
import { Lesson } from '../../types/lesson';

enum TabValue {
  UPCOMING = 'upcoming',
  PAST = 'past',
  NOTES = 'notes'
}

const MobileStudentDetail: React.FC = () => {
  const { t } = useTranslation(['common', 'students', 'lessons']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [student, setStudent] = useState<Student | null>(null);
  const [upcomingLessons, setUpcomingLessons] = useState<Lesson[]>([]);
  const [pastLessons, setPastLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentTab, setCurrentTab] = useState<TabValue>(TabValue.UPCOMING);
  const [speedDialOpen, setSpeedDialOpen] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [confirmStatusChangeOpen, setConfirmStatusChangeOpen] = useState(false);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');

  useEffect(() => {
    const fetchStudentData = async () => {
      if (!id || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Fetch student data
        const studentDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'students', id));
        
        if (!studentDoc.exists()) {
          console.error('Student not found');
          navigate('/mobile/students');
          return;
        }
        
        const studentData = { id: studentDoc.id, ...studentDoc.data() } as Student;
        setStudent(studentData);
        
        // Get current time
        const now = new Date();
        
        // Fetch upcoming lessons
        const upcomingLessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('studentIds', 'array-contains', id),
          where('startTime', '>=', Timestamp.fromDate(now)),
          where('status', '==', 'scheduled'),
          orderBy('startTime', 'asc'),
          limit(10)
        );
        
        const upcomingLessonsSnapshot = await getDocs(upcomingLessonsQuery);
        const upcomingLessonsData = upcomingLessonsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Lesson[];
        
        setUpcomingLessons(upcomingLessonsData);
        
        // Fetch past lessons
        const pastLessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('studentIds', 'array-contains', id),
          where('startTime', '<', Timestamp.fromDate(now)),
          orderBy('startTime', 'desc'),
          limit(10)
        );
        
        const pastLessonsSnapshot = await getDocs(pastLessonsQuery);
        const pastLessonsData = pastLessonsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Lesson[];
        
        setPastLessons(pastLessonsData);
        
      } catch (error) {
        console.error('Error fetching student data:', error);
        showSnackbar(t('students:fetchError', 'Failed to load student data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchStudentData();
  }, [id, currentSchool?.id, navigate, t]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: TabValue) => {
    setCurrentTab(newValue);
  };

  const handleDelete = async () => {
    if (!id || !currentSchool?.id) return;
    
    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'students', id));
      showSnackbar(t('students:deleteSuccess', 'Student deleted successfully'), 'success');
      navigate('/mobile/students');
    } catch (error) {
      console.error('Error deleting student:', error);
      showSnackbar(t('students:deleteError', 'Failed to delete student'), 'error');
    }
  };

  const handleStatusChange = async () => {
    if (!id || !currentSchool?.id || !student) return;
    
    const newStatus = student.status === 'active' ? 'inactive' : 'active';
    
    try {
      await updateDoc(doc(db, 'schools', currentSchool.id, 'students', id), {
        status: newStatus,
        updatedAt: Timestamp.now()
      });
      
      setStudent({
        ...student,
        status: newStatus,
        updatedAt: Timestamp.now()
      });
      
      showSnackbar(
        newStatus === 'active' 
          ? t('students:activateSuccess', 'Student activated successfully') 
          : t('students:deactivateSuccess', 'Student deactivated successfully'), 
        'success'
      );
      setConfirmStatusChangeOpen(false);
    } catch (error) {
      console.error('Error updating student status:', error);
      showSnackbar(t('students:updateError', 'Failed to update student status'), 'error');
    }
  };

  const handleAddNote = async () => {
    if (!id || !currentSchool?.id || !student || !currentNote.trim()) return;
    
    try {
      const notes = student.notes || [];
      const newNote = {
        text: currentNote.trim(),
        createdAt: Timestamp.now(),
        createdBy: user?.uid
      };
      
      await updateDoc(doc(db, 'schools', currentSchool.id, 'students', id), {
        notes: [newNote, ...notes],
        updatedAt: Timestamp.now()
      });
      
      setStudent({
        ...student,
        notes: [newNote, ...notes],
        updatedAt: Timestamp.now()
      });
      
      showSnackbar(t('students:noteAddSuccess', 'Note added successfully'), 'success');
      setNoteDialogOpen(false);
      setCurrentNote('');
    } catch (error) {
      console.error('Error adding note:', error);
      showSnackbar(t('students:noteAddError', 'Failed to add note'), 'error');
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'HH:mm');
  };
  
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  const getLessonStatusChip = (lesson: Lesson) => {
    switch (lesson.status) {
      case 'completed':
        return <Chip size="small" icon={<CheckCircleIcon />} label={t('lessons:status.completed')} color="success" />;
      case 'cancelled':
        return <Chip size="small" icon={<CancelIcon />} label={t('lessons:status.cancelled')} color="error" />;
      case 'scheduled':
      default:
        return <Chip size="small" icon={<TimeIcon />} label={t('lessons:status.scheduled')} color="primary" />;
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const actions = [
    { icon: <EditIcon />, name: t('common:edit', 'Edit'), action: () => navigate(`/mobile/students/edit/${id}`) },
    { 
      icon: student?.status === 'active' ? <BlockIcon /> : <CheckCircleIcon />, 
      name: student?.status === 'active' ? t('students:deactivate', 'Deactivate') : t('students:activate', 'Activate'), 
      action: () => setConfirmStatusChangeOpen(true) 
    },
    { icon: <NotesIcon />, name: t('students:addNote', 'Add Note'), action: () => setNoteDialogOpen(true) },
    { icon: <DeleteIcon />, name: t('common:delete', 'Delete'), action: () => setConfirmDeleteOpen(true) },
  ];

  return (
    <MobileLayout 
      title={student ? `${student.firstName} ${student.lastName}` : t('students:studentDetails', 'Student Details')} 
      showBackButton
    >
      {loading ? (
        <Box>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={150} />
        </Box>
      ) : student ? (
        <>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ width: 60, height: 60, mr: 2, bgcolor: student.status === 'active' ? 'primary.main' : 'grey.500' }}>
                  {student.firstName?.charAt(0) || 'S'}
                </Avatar>
                <Box>
                  <Typography variant="h5">
                    {student.firstName} {student.lastName}
                  </Typography>
                  <Chip 
                    size="small" 
                    label={student.status === 'active' ? t('students:active', 'Active') : t('students:inactive', 'Inactive')} 
                    color={student.status === 'active' ? 'success' : 'default'} 
                    sx={{ mt: 0.5 }}
                  />
                </Box>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {student.email || t('students:noEmail', 'No email provided')}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PhoneIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {student.phone || t('students:noPhone', 'No phone provided')}
                    </Typography>
                  </Box>
                </Grid>
                
                {student.type && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      {t('students:type', 'Type')}
                    </Typography>
                    <Typography variant="body1">
                      {t(`students:types.${student.type}`, student.type)}
                    </Typography>
                  </Grid>
                )}
                
                {student.level && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      {t('students:level', 'Level')}
                    </Typography>
                    <Typography variant="body1">
                      {student.level}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
          
          <Box sx={{ mb: 2 }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              variant="fullWidth"
              indicatorColor="primary"
              textColor="primary"
            >
              <Tab 
                label={t('students:upcomingLessons', 'Upcoming')} 
                value={TabValue.UPCOMING} 
              />
              <Tab 
                label={t('students:pastLessons', 'Past')} 
                value={TabValue.PAST} 
              />
              <Tab 
                label={t('students:notes', 'Notes')} 
                value={TabValue.NOTES} 
              />
            </Tabs>
          </Box>
          
          {currentTab === TabValue.UPCOMING && (
            <Card>
              {upcomingLessons.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {upcomingLessons.map((lesson, index) => (
                    <React.Fragment key={lesson.id}>
                      <ListItem 
                        button
                        onClick={() => navigate(`/mobile/lessons/${lesson.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <TimeIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1" component="span">
                                {lesson.title}
                              </Typography>
                              {getLessonStatusChip(lesson)}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography variant="body2" component="span">
                                {formatDate(lesson.startTime)}, {formatTime(lesson.startTime)}
                              </Typography>
                              <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                                {lesson.duration} {t('lessons:minutes', 'minutes')}
                              </Typography>
                            </>
                          }
                        />
                      </ListItem>
                      {index < upcomingLessons.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <CardContent>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('students:noUpcomingLessons', 'No upcoming lessons scheduled')}
                  </Typography>
                  <Box sx={{ textAlign: 'center', mt: 2 }}>
                    <Button 
                      variant="outlined" 
                      startIcon={<AddIcon />}
                      onClick={() => navigate('/mobile/lessons/create', { state: { studentId: id } })}
                    >
                      {t('students:scheduleLesson', 'Schedule Lesson')}
                    </Button>
                  </Box>
                </CardContent>
              )}
            </Card>
          )}
          
          {currentTab === TabValue.PAST && (
            <Card>
              {pastLessons.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {pastLessons.map((lesson, index) => (
                    <React.Fragment key={lesson.id}>
                      <ListItem 
                        button
                        onClick={() => navigate(`/mobile/lessons/${lesson.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <TimeIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1" component="span">
                                {lesson.title}
                              </Typography>
                              {getLessonStatusChip(lesson)}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography variant="body2" component="span">
                                {formatDate(lesson.startTime)}, {formatTime(lesson.startTime)}
                              </Typography>
                              <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                                {lesson.duration} {t('lessons:minutes', 'minutes')}
                              </Typography>
                            </>
                          }
                        />
                      </ListItem>
                      {index < pastLessons.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <CardContent>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('students:noPastLessons', 'No past lessons found')}
                  </Typography>
                </CardContent>
              )}
            </Card>
          )}
          
          {currentTab === TabValue.NOTES && (
            <Card>
              {student.notes && student.notes.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {student.notes.map((note, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <NotesIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={note.text}
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              {note.createdAt && formatDate(note.createdAt)}
                            </Typography>
                          }
                        />
                      </ListItem>
                      {index < student.notes.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <CardContent>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('students:noNotes', 'No notes found')}
                  </Typography>
                  <Box sx={{ textAlign: 'center', mt: 2 }}>
                    <Button 
                      variant="outlined" 
                      startIcon={<AddIcon />}
                      onClick={() => setNoteDialogOpen(true)}
                    >
                      {t('students:addNote', 'Add Note')}
                    </Button>
                  </Box>
                </CardContent>
              )}
            </Card>
          )}
          
          <SpeedDial
            ariaLabel="student actions"
            sx={{ position: 'fixed', bottom: 70, right: 16 }}
            icon={<SpeedDialIcon />}
            onClose={() => setSpeedDialOpen(false)}
            onOpen={() => setSpeedDialOpen(true)}
            open={speedDialOpen}
            direction="up"
          >
            {actions.map((action) => (
              <SpeedDialAction
                key={action.name}
                icon={action.icon}
                tooltipTitle={action.name}
                onClick={() => {
                  setSpeedDialOpen(false);
                  action.action();
                }}
              />
            ))}
          </SpeedDial>
          
          {/* Confirm Delete Dialog */}
          <Dialog
            open={confirmDeleteOpen}
            onClose={() => setConfirmDeleteOpen(false)}
          >
            <DialogTitle>{t('students:confirmDelete', 'Delete Student?')}</DialogTitle>
            <DialogContent>
              <DialogContentText>
                {t('students:confirmDeleteMessage', 'Are you sure you want to delete this student? This action cannot be undone.')}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setConfirmDeleteOpen(false)}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button onClick={handleDelete} color="error">
                {t('common:delete', 'Delete')}
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Confirm Status Change Dialog */}
          <Dialog
            open={confirmStatusChangeOpen}
            onClose={() => setConfirmStatusChangeOpen(false)}
          >
            <DialogTitle>
              {student.status === 'active' 
                ? t('students:confirmDeactivate', 'Deactivate Student?') 
                : t('students:confirmActivate', 'Activate Student?')}
            </DialogTitle>
            <DialogContent>
              <DialogContentText>
                {student.status === 'active' 
                  ? t('students:confirmDeactivateMessage', 'Are you sure you want to deactivate this student?')
                  : t('students:confirmActivateMessage', 'Are you sure you want to activate this student?')}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setConfirmStatusChangeOpen(false)}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button onClick={handleStatusChange} color="primary">
                {t('common:confirm', 'Confirm')}
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Add Note Dialog */}
          <Dialog
            open={noteDialogOpen}
            onClose={() => setNoteDialogOpen(false)}
            fullWidth
            maxWidth="sm"
          >
            <DialogTitle>{t('students:addNote', 'Add Note')}</DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                label={t('students:noteText', 'Note')}
                fullWidth
                multiline
                rows={4}
                value={currentNote}
                onChange={(e) => setCurrentNote(e.target.value)}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setNoteDialogOpen(false)}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button 
                onClick={handleAddNote} 
                color="primary"
                disabled={!currentNote.trim()}
              >
                {t('common:save', 'Save')}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Typography variant="body1" color="error">
          {t('students:studentNotFound', 'Student not found')}
        </Typography>
      )}
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileStudentDetail;
