import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Card,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  IconButton,
  Chip,
  Skeleton,
  Tabs,
  Tab,
  Snackbar,
  Alert,
  Menu,
  MenuItem,
  Fab
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  ArrowForward as ArrowForwardIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, orderBy, limit, startAfter, Timestamp, doc, getDoc } from 'firebase/firestore';
import { Student } from '../../types/student';
import { Lesson } from '../../types/lesson';

enum StudentTab {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

enum SortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  RECENT = 'recent'
}

const MobileStudents: React.FC = () => {
  const { t } = useTranslation(['common', 'students']);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();

  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTab, setCurrentTab] = useState<StudentTab>(StudentTab.ALL);
  const [sortOption, setSortOption] = useState<SortOption>(SortOption.NAME_ASC);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [studentLessons, setStudentLessons] = useState<Record<string, number>>({});
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        let studentsQuery = query(
          collection(db, 'schools', currentSchool.id, 'students'),
          where('instructorId', '==', user.uid),
          orderBy('lastName'),
          limit(20)
        );

        const studentsSnapshot = await getDocs(studentsQuery);

        if (studentsSnapshot.empty) {
          setStudents([]);
          setFilteredStudents([]);
          setHasMore(false);
        } else {
          const studentsData = studentsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Student[];

          setStudents(studentsData);
          setFilteredStudents(studentsData);
          setLastVisible(studentsSnapshot.docs[studentsSnapshot.docs.length - 1]);
          setHasMore(studentsSnapshot.docs.length === 20);

          // Fetch lesson counts for each student
          await fetchStudentLessonCounts(studentsData);
        }
      } catch (error) {
        console.error('Error fetching students:', error);
        showSnackbar(t('students:fetchError', 'Failed to load students'), 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, t]);

  const fetchStudentLessonCounts = async (studentsData: Student[]) => {
    if (!currentSchool?.id) return;

    const counts: Record<string, number> = {};

    for (const student of studentsData) {
      try {
        const lessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('studentIds', 'array-contains', student.id),
          where('instructorId', '==', user.uid),
          where('status', '==', 'scheduled')
        );

        const lessonsSnapshot = await getDocs(lessonsQuery);
        counts[student.id] = lessonsSnapshot.size;
      } catch (error) {
        console.error(`Error fetching lessons for student ${student.id}:`, error);
        counts[student.id] = 0;
      }
    }

    setStudentLessons(counts);
  };

  const loadMoreStudents = async () => {
    if (!currentSchool?.id || !lastVisible || loadingMore) return;

    try {
      setLoadingMore(true);

      let studentsQuery = query(
        collection(db, 'schools', currentSchool.id, 'students'),
        where('instructorId', '==', user.uid),
        orderBy('lastName'),
        startAfter(lastVisible),
        limit(20)
      );

      const studentsSnapshot = await getDocs(studentsQuery);

      if (studentsSnapshot.empty) {
        setHasMore(false);
      } else {
        const newStudentsData = studentsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Student[];

        setStudents(prev => [...prev, ...newStudentsData]);
        setLastVisible(studentsSnapshot.docs[studentsSnapshot.docs.length - 1]);
        setHasMore(studentsSnapshot.docs.length === 20);

        // Fetch lesson counts for new students
        await fetchStudentLessonCounts(newStudentsData);

        // Apply current filters to the updated list
        applyFilters([...students, ...newStudentsData]);
      }
    } catch (error) {
      console.error('Error loading more students:', error);
      showSnackbar(t('students:loadMoreError', 'Failed to load more students'), 'error');
    } finally {
      setLoadingMore(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    applyFilters(students, e.target.value, currentTab, sortOption);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: StudentTab) => {
    setCurrentTab(newValue);
    applyFilters(students, searchQuery, newValue, sortOption);
  };

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleSortOptionSelect = (option: SortOption) => {
    setSortOption(option);
    applyFilters(students, searchQuery, currentTab, option);
    handleSortMenuClose();
  };

  const applyFilters = (
    studentsList: Student[],
    search: string = searchQuery,
    tab: StudentTab = currentTab,
    sort: SortOption = sortOption
  ) => {
    // Filter by search query
    let filtered = studentsList;

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(student =>
        `${student.firstName} ${student.lastName}`.toLowerCase().includes(searchLower) ||
        student.email?.toLowerCase().includes(searchLower) ||
        student.phone?.toLowerCase().includes(searchLower)
      );
    }

    // Filter by tab
    if (tab === StudentTab.ACTIVE) {
      filtered = filtered.filter(student => student.status === 'active');
    } else if (tab === StudentTab.INACTIVE) {
      filtered = filtered.filter(student => student.status === 'inactive');
    }

    // Apply sorting
    if (sort === SortOption.NAME_ASC) {
      filtered.sort((a, b) => `${a.lastName} ${a.firstName}`.localeCompare(`${b.lastName} ${b.firstName}`));
    } else if (sort === SortOption.NAME_DESC) {
      filtered.sort((a, b) => `${b.lastName} ${b.firstName}`.localeCompare(`${a.lastName} ${a.firstName}`));
    } else if (sort === SortOption.RECENT) {
      // Sort by most recent lessons (this would require additional data)
      // For now, we'll just use the lesson count as a proxy
      filtered.sort((a, b) => (studentLessons[b.id] || 0) - (studentLessons[a.id] || 0));
    }

    setFilteredStudents(filtered);
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const bottom = e.currentTarget.scrollHeight - e.currentTarget.scrollTop <= e.currentTarget.clientHeight + 200;
    if (bottom && hasMore && !loadingMore) {
      loadMoreStudents();
    }
  };

  return (
    <MobileLayout title={t('common:students', 'Students')}>
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          placeholder={t('students:searchPlaceholder', 'Search students...')}
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size="small"
        />
      </Box>

      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ flexGrow: 1 }}
        >
          <Tab label={t('students:allStudents', 'All')} value={StudentTab.ALL} />
          <Tab label={t('students:activeStudents', 'Active')} value={StudentTab.ACTIVE} />
          <Tab label={t('students:inactiveStudents', 'Inactive')} value={StudentTab.INACTIVE} />
        </Tabs>

        <IconButton onClick={handleSortMenuOpen}>
          <SortIcon />
        </IconButton>

        <Menu
          anchorEl={menuAnchorEl}
          open={Boolean(menuAnchorEl)}
          onClose={handleSortMenuClose}
        >
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.NAME_ASC)}
            selected={sortOption === SortOption.NAME_ASC}
          >
            {t('students:sortNameAsc', 'Name (A-Z)')}
          </MenuItem>
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.NAME_DESC)}
            selected={sortOption === SortOption.NAME_DESC}
          >
            {t('students:sortNameDesc', 'Name (Z-A)')}
          </MenuItem>
          <MenuItem
            onClick={() => handleSortOptionSelect(SortOption.RECENT)}
            selected={sortOption === SortOption.RECENT}
          >
            {t('students:sortRecent', 'Most Lessons')}
          </MenuItem>
        </Menu>
      </Box>

      <Box sx={{ mb: 2 }} onScroll={handleScroll}>
        {loading ? (
          <Card>
            {[1, 2, 3, 4, 5].map((item) => (
              <Box key={item} sx={{ p: 2 }}>
                <Skeleton variant="rectangular" width="100%" height={60} />
                {item < 5 && <Divider />}
              </Box>
            ))}
          </Card>
        ) : filteredStudents.length > 0 ? (
          <Card>
            <List sx={{ p: 0 }}>
              {filteredStudents.map((student, index) => (
                <React.Fragment key={student.id}>
                  <ListItem
                    button
                    onClick={() => navigate(`/mobile/students/${student.id}`)}
                    secondaryAction={
                      <IconButton edge="end" aria-label="details">
                        <ArrowForwardIcon />
                      </IconButton>
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: student.status === 'active' ? 'primary.main' : 'grey.500' }}>
                        {student.firstName?.charAt(0) || 'S'}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1" component="span">
                            {student.firstName} {student.lastName}
                          </Typography>
                          {student.status === 'inactive' && (
                            <Chip size="small" label={t('students:inactive', 'Inactive')} color="default" />
                          )}
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            {student.email}
                          </Typography>
                          <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                            {t('students:upcomingLessons', 'Upcoming Lessons')}: {studentLessons[student.id] || 0}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                  {index < filteredStudents.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
              {loadingMore && (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Skeleton variant="rectangular" width="100%" height={60} />
                </Box>
              )}
            </List>
          </Card>
        ) : (
          <Card>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <PersonIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {t('students:noStudentsFound', 'No students found')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchQuery
                  ? t('students:tryDifferentSearch', 'Try a different search term or filter')
                  : t('students:noStudentsYet', 'No students have been added yet')}
              </Typography>
            </Box>
          </Card>
        )}
      </Box>

      <Fab
        color="primary"
        aria-label="add student"
        sx={{ position: 'fixed', bottom: 70, right: 16 }}
        onClick={() => navigate('/mobile/students/create')}
      >
        <AddIcon />
      </Fab>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileStudents;
