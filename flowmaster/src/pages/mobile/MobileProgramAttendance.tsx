import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Card, 
  CardContent, 
  Divider, 
  Button, 
  Chip, 
  IconButton, 
  Skeleton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Avatar,
  Grid,
  Tabs,
  Tab,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Checkbox,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { 
  AccessTime as TimeIcon, 
  Event as EventIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Save as SaveIcon,
  Notes as NotesIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { 
  doc, 
  getDoc, 
  collection, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  Timestamp, 
  updateDoc, 
  setDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { Program } from '../../types/program';
import { ProgramSession } from '../../types/programSession';
import { Student } from '../../types/student';
import { Attendance } from '../../types/attendance';

interface AttendanceRecord {
  studentId: string;
  studentName: string;
  present: boolean;
  notes: string;
}

const MobileProgramAttendance: React.FC = () => {
  const { t } = useTranslation(['common', 'programs', 'attendance']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [program, setProgram] = useState<Program | null>(null);
  const [sessions, setSessions] = useState<ProgramSession[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedSession, setSelectedSession] = useState<string>('');
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState('');
  const [currentStudentId, setCurrentStudentId] = useState('');

  useEffect(() => {
    const fetchProgramData = async () => {
      if (!id || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Fetch program data
        const programDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'programs', id));
        
        if (!programDoc.exists()) {
          console.error('Program not found');
          navigate('/mobile/programs');
          return;
        }
        
        const programData = { id: programDoc.id, ...programDoc.data() } as Program;
        setProgram(programData);
        
        // Fetch program sessions
        const sessionsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs', id, 'sessions'),
          orderBy('date', 'asc')
        );
        
        const sessionsSnapshot = await getDocs(sessionsQuery);
        const sessionsData = sessionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as ProgramSession[];
        
        setSessions(sessionsData);
        
        // Set default selected session to the next upcoming session or the last session
        const now = new Date();
        const upcomingSessions = sessionsData.filter(session => {
          const sessionDate = session.date instanceof Timestamp 
            ? session.date.toDate() 
            : new Date(session.date);
          return sessionDate >= now;
        });
        
        if (upcomingSessions.length > 0) {
          setSelectedSession(upcomingSessions[0].id);
        } else if (sessionsData.length > 0) {
          setSelectedSession(sessionsData[sessionsData.length - 1].id);
        }
        
        // Fetch students
        if (programData.studentIds && programData.studentIds.length > 0) {
          const studentsData: Student[] = [];
          
          for (const studentId of programData.studentIds) {
            const studentDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'students', studentId));
            if (studentDoc.exists()) {
              studentsData.push({ id: studentDoc.id, ...studentDoc.data() } as Student);
            }
          }
          
          setStudents(studentsData);
          
          // Initialize attendance records
          const initialAttendanceRecords: AttendanceRecord[] = studentsData.map(student => ({
            studentId: student.id,
            studentName: `${student.firstName} ${student.lastName}`,
            present: false,
            notes: ''
          }));
          
          setAttendanceRecords(initialAttendanceRecords);
        }
        
      } catch (error) {
        console.error('Error fetching program data:', error);
        showSnackbar(t('programs:fetchError', 'Failed to load program data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProgramData();
  }, [id, currentSchool?.id, navigate, t]);

  useEffect(() => {
    const fetchAttendanceData = async () => {
      if (!id || !currentSchool?.id || !selectedSession || !students.length) return;
      
      try {
        // Fetch existing attendance records for the selected session
        const attendanceQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs', id, 'sessions', selectedSession, 'attendance')
        );
        
        const attendanceSnapshot = await getDocs(attendanceQuery);
        
        // Initialize attendance records with all students
        const initialAttendanceRecords: AttendanceRecord[] = students.map(student => ({
          studentId: student.id,
          studentName: `${student.firstName} ${student.lastName}`,
          present: false,
          notes: ''
        }));
        
        // Update with existing attendance data
        if (!attendanceSnapshot.empty) {
          attendanceSnapshot.forEach(doc => {
            const attendanceData = doc.data() as Attendance;
            const studentIndex = initialAttendanceRecords.findIndex(
              record => record.studentId === doc.id
            );
            
            if (studentIndex !== -1) {
              initialAttendanceRecords[studentIndex].present = attendanceData.present;
              initialAttendanceRecords[studentIndex].notes = attendanceData.notes || '';
            }
          });
        }
        
        setAttendanceRecords(initialAttendanceRecords);
        
      } catch (error) {
        console.error('Error fetching attendance data:', error);
        showSnackbar(t('attendance:fetchError', 'Failed to load attendance data'), 'error');
      }
    };
    
    fetchAttendanceData();
  }, [id, currentSchool?.id, selectedSession, students, t]);

  const handleSessionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedSession(event.target.value as string);
  };

  const handleToggleAttendance = (studentId: string) => {
    setAttendanceRecords(prevRecords => 
      prevRecords.map(record => 
        record.studentId === studentId 
          ? { ...record, present: !record.present } 
          : record
      )
    );
  };

  const handleOpenNoteDialog = (studentId: string) => {
    const record = attendanceRecords.find(r => r.studentId === studentId);
    if (record) {
      setCurrentNote(record.notes);
      setCurrentStudentId(studentId);
      setNoteDialogOpen(true);
    }
  };

  const handleSaveNote = () => {
    setAttendanceRecords(prevRecords => 
      prevRecords.map(record => 
        record.studentId === currentStudentId 
          ? { ...record, notes: currentNote } 
          : record
      )
    );
    setNoteDialogOpen(false);
  };

  const handleMarkAllPresent = () => {
    setAttendanceRecords(prevRecords => 
      prevRecords.map(record => ({ ...record, present: true }))
    );
  };

  const handleSaveAttendance = async () => {
    if (!id || !currentSchool?.id || !selectedSession) return;
    
    try {
      setSaving(true);
      
      // Get the selected session
      const sessionDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'programs', id, 'sessions', selectedSession));
      
      if (!sessionDoc.exists()) {
        showSnackbar(t('programs:sessionNotFound', 'Session not found'), 'error');
        return;
      }
      
      const sessionData = sessionDoc.data() as ProgramSession;
      
      // Update attendance records in Firestore
      for (const record of attendanceRecords) {
        await setDoc(
          doc(db, 'schools', currentSchool.id, 'programs', id, 'sessions', selectedSession, 'attendance', record.studentId),
          {
            present: record.present,
            notes: record.notes,
            updatedAt: serverTimestamp(),
            updatedBy: user?.uid
          }
        );
      }
      
      // Update session status to completed if not already
      if (sessionData.status !== 'completed') {
        await updateDoc(doc(db, 'schools', currentSchool.id, 'programs', id, 'sessions', selectedSession), {
          status: 'completed',
          updatedAt: serverTimestamp()
        });
        
        // Update sessions list
        setSessions(prevSessions => 
          prevSessions.map(session => 
            session.id === selectedSession 
              ? { ...session, status: 'completed' } 
              : session
          )
        );
      }
      
      showSnackbar(t('attendance:saveSuccess', 'Attendance saved successfully'), 'success');
    } catch (error) {
      console.error('Error saving attendance:', error);
      showSnackbar(t('attendance:saveError', 'Failed to save attendance'), 'error');
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  const getSessionStatusChip = (session: ProgramSession) => {
    switch (session.status) {
      case 'completed':
        return <Chip size="small" icon={<CheckCircleIcon />} label={t('programs:status.completed')} color="success" />;
      case 'cancelled':
        return <Chip size="small" icon={<CancelIcon />} label={t('programs:status.cancelled')} color="error" />;
      case 'scheduled':
      default:
        return <Chip size="small" icon={<TimeIcon />} label={t('programs:status.scheduled')} color="primary" />;
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const getSelectedSessionIndex = () => {
    return sessions.findIndex(session => session.id === selectedSession);
  };

  return (
    <MobileLayout 
      title={t('programs:attendance', 'Program Attendance')} 
      showBackButton
    >
      {loading ? (
        <Box>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={300} />
        </Box>
      ) : program && sessions.length > 0 ? (
        <>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" component="h1" gutterBottom>
              {program.title}
            </Typography>
            
            <FormControl fullWidth margin="normal">
              <InputLabel id="session-select-label">{t('programs:selectSession', 'Select Session')}</InputLabel>
              <Select
                labelId="session-select-label"
                value={selectedSession}
                onChange={handleSessionChange}
                label={t('programs:selectSession', 'Select Session')}
              >
                {sessions.map((session, index) => (
                  <MenuItem key={session.id} value={session.id}>
                    {t('programs:sessionNumber', 'Session {{number}}', { number: index + 1 })} - {formatDate(session.date)} {getSessionStatusChip(session)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          
          {selectedSession && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  {t('attendance:students', 'Students')} ({attendanceRecords.length})
                </Typography>
                <Button 
                  size="small" 
                  startIcon={<CheckCircleIcon />}
                  onClick={handleMarkAllPresent}
                >
                  {t('attendance:markAllPresent', 'Mark All Present')}
                </Button>
              </Box>
              
              <Card sx={{ mb: 3 }}>
                {attendanceRecords.length > 0 ? (
                  <List sx={{ p: 0 }}>
                    {attendanceRecords.map((record, index) => (
                      <React.Fragment key={record.studentId}>
                        <ListItem>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: record.present ? 'success.main' : 'grey.400' }}>
                              {record.present ? <CheckCircleIcon /> : <CancelIcon />}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText 
                            primary={record.studentName}
                            secondary={record.notes ? record.notes : t('attendance:noNotes', 'No notes')}
                          />
                          <ListItemSecondaryAction>
                            <IconButton 
                              edge="end" 
                              aria-label="edit notes"
                              onClick={() => handleOpenNoteDialog(record.studentId)}
                              sx={{ mr: 1 }}
                            >
                              <NotesIcon />
                            </IconButton>
                            <Checkbox
                              edge="end"
                              checked={record.present}
                              onChange={() => handleToggleAttendance(record.studentId)}
                              inputProps={{ 'aria-labelledby': `attendance-${record.studentId}` }}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < attendanceRecords.length - 1 && <Divider variant="inset" component="li" />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <CardContent>
                    <Typography variant="body2" color="text.secondary" align="center">
                      {t('programs:noStudents', 'No students enrolled in this program')}
                    </Typography>
                  </CardContent>
                )}
              </Card>
              
              <Button
                variant="contained"
                color="primary"
                fullWidth
                startIcon={<SaveIcon />}
                onClick={handleSaveAttendance}
                disabled={saving}
              >
                {saving 
                  ? t('common:saving', 'Saving...') 
                  : t('common:save', 'Save')}
              </Button>
              
              {/* Note Dialog */}
              <Dialog
                open={noteDialogOpen}
                onClose={() => setNoteDialogOpen(false)}
                fullWidth
                maxWidth="sm"
              >
                <DialogTitle>{t('attendance:studentNotes', 'Student Notes')}</DialogTitle>
                <DialogContent>
                  <TextField
                    autoFocus
                    margin="dense"
                    label={t('attendance:notes', 'Notes')}
                    fullWidth
                    multiline
                    rows={4}
                    value={currentNote}
                    onChange={(e) => setCurrentNote(e.target.value)}
                  />
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setNoteDialogOpen(false)}>
                    {t('common:cancel', 'Cancel')}
                  </Button>
                  <Button onClick={handleSaveNote} color="primary">
                    {t('common:save', 'Save')}
                  </Button>
                </DialogActions>
              </Dialog>
            </>
          )}
        </>
      ) : (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <SchoolIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            {t('programs:noSessions', 'No sessions found')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('programs:noSessionsMessage', 'This program does not have any sessions yet')}
          </Typography>
        </Box>
      )}
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileProgramAttendance;
