import React, { useState, useEffect } from 'react';
import { 
  <PERSON>po<PERSON>, 
  <PERSON>, 
  Card, 
  CardContent, 
  Button, 
  Skeleton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Avatar,
  Divider,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  Checkbox,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { 
  Save as SaveIcon,
  Person as PersonIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Edit as EditIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { doc, getDoc, updateDoc, collection, getDocs, query, where, Timestamp, setDoc } from 'firebase/firestore';
import { Lesson } from '../../types/lesson';
import { Student } from '../../types/student';
import { Attendance } from '../../types/attendance';

interface AttendanceRecord {
  studentId: string;
  studentName: string;
  present: boolean;
  notes: string;
}

const MobileAttendance: React.FC = () => {
  const { t } = useTranslation(['common', 'lessons', 'attendance']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [students, setStudents] = useState<Student[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [editingStudentId, setEditingStudentId] = useState<string | null>(null);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState('');
  const [currentStudentId, setCurrentStudentId] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      if (!id || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Fetch lesson data
        const lessonDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'lessons', id));
        
        if (!lessonDoc.exists()) {
          console.error('Lesson not found');
          navigate('/mobile/schedule');
          return;
        }
        
        const lessonData = { id: lessonDoc.id, ...lessonDoc.data() } as Lesson;
        setLesson(lessonData);
        
        // Fetch students data
        if (lessonData.studentIds && lessonData.studentIds.length > 0) {
          const studentsData: Student[] = [];
          
          for (const studentId of lessonData.studentIds) {
            const studentDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'students', studentId));
            if (studentDoc.exists()) {
              studentsData.push({ id: studentDoc.id, ...studentDoc.data() } as Student);
            }
          }
          
          setStudents(studentsData);
          
          // Initialize attendance records
          const initialAttendanceRecords: AttendanceRecord[] = studentsData.map(student => ({
            studentId: student.id,
            studentName: `${student.firstName} ${student.lastName}`,
            present: false,
            notes: ''
          }));
          
          // Fetch existing attendance records
          const attendanceQuery = query(
            collection(db, 'schools', currentSchool.id, 'lessons', id, 'attendance')
          );
          
          const attendanceSnapshot = await getDocs(attendanceQuery);
          
          if (!attendanceSnapshot.empty) {
            attendanceSnapshot.forEach(doc => {
              const attendanceData = doc.data() as Attendance;
              const studentIndex = initialAttendanceRecords.findIndex(
                record => record.studentId === doc.id
              );
              
              if (studentIndex !== -1) {
                initialAttendanceRecords[studentIndex].present = attendanceData.present;
                initialAttendanceRecords[studentIndex].notes = attendanceData.notes || '';
              }
            });
          }
          
          setAttendanceRecords(initialAttendanceRecords);
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        showSnackbar(t('attendance:fetchError', 'Failed to load attendance data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, currentSchool?.id, navigate, t]);

  const handleToggleAttendance = (studentId: string) => {
    setAttendanceRecords(prevRecords => 
      prevRecords.map(record => 
        record.studentId === studentId 
          ? { ...record, present: !record.present } 
          : record
      )
    );
  };

  const handleOpenNoteDialog = (studentId: string) => {
    const record = attendanceRecords.find(r => r.studentId === studentId);
    if (record) {
      setCurrentNote(record.notes);
      setCurrentStudentId(studentId);
      setNoteDialogOpen(true);
    }
  };

  const handleSaveNote = () => {
    setAttendanceRecords(prevRecords => 
      prevRecords.map(record => 
        record.studentId === currentStudentId 
          ? { ...record, notes: currentNote } 
          : record
      )
    );
    setNoteDialogOpen(false);
  };

  const handleSaveAttendance = async () => {
    if (!id || !currentSchool?.id || !lesson) return;
    
    try {
      setSaving(true);
      
      // Update attendance records in Firestore
      for (const record of attendanceRecords) {
        await setDoc(
          doc(db, 'schools', currentSchool.id, 'lessons', id, 'attendance', record.studentId),
          {
            present: record.present,
            notes: record.notes,
            updatedAt: Timestamp.now(),
            updatedBy: user?.uid
          }
        );
      }
      
      // Update lesson status to completed if not already
      if (lesson.status !== 'completed') {
        await updateDoc(doc(db, 'schools', currentSchool.id, 'lessons', id), {
          status: 'completed',
          updatedAt: Timestamp.now()
        });
        
        setLesson({
          ...lesson,
          status: 'completed',
          updatedAt: Timestamp.now()
        });
      }
      
      showSnackbar(t('attendance:saveSuccess', 'Attendance saved successfully'), 'success');
    } catch (error) {
      console.error('Error saving attendance:', error);
      showSnackbar(t('attendance:saveError', 'Failed to save attendance'), 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleMarkAllPresent = () => {
    setAttendanceRecords(prevRecords => 
      prevRecords.map(record => ({ ...record, present: true }))
    );
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  return (
    <MobileLayout 
      title={t('attendance:title', 'Attendance')} 
      showBackButton
    >
      {loading ? (
        <Box>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={300} />
        </Box>
      ) : lesson ? (
        <>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" component="h1" gutterBottom>
              {lesson.title}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {formatDate(lesson.startTime)}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {t('attendance:students', 'Students')} ({attendanceRecords.length})
            </Typography>
            <Button 
              size="small" 
              startIcon={<CheckIcon />}
              onClick={handleMarkAllPresent}
            >
              {t('attendance:markAllPresent', 'Mark All Present')}
            </Button>
          </Box>
          
          <Card sx={{ mb: 3 }}>
            {attendanceRecords.length > 0 ? (
              <List sx={{ p: 0 }}>
                {attendanceRecords.map((record, index) => (
                  <React.Fragment key={record.studentId}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: record.present ? 'success.main' : 'grey.400' }}>
                          {record.present ? <CheckIcon /> : <CloseIcon />}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText 
                        primary={record.studentName}
                        secondary={record.notes ? record.notes : t('attendance:noNotes', 'No notes')}
                      />
                      <ListItemSecondaryAction>
                        <IconButton 
                          edge="end" 
                          aria-label="edit notes"
                          onClick={() => handleOpenNoteDialog(record.studentId)}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon />
                        </IconButton>
                        <Checkbox
                          edge="end"
                          checked={record.present}
                          onChange={() => handleToggleAttendance(record.studentId)}
                          inputProps={{ 'aria-labelledby': `attendance-${record.studentId}` }}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < attendanceRecords.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <CardContent>
                <Typography variant="body2" color="text.secondary" align="center">
                  {t('attendance:noStudents', 'No students assigned to this lesson')}
                </Typography>
              </CardContent>
            )}
          </Card>
          
          <Button
            variant="contained"
            color="primary"
            fullWidth
            startIcon={<SaveIcon />}
            onClick={handleSaveAttendance}
            disabled={saving}
          >
            {saving 
              ? t('common:saving', 'Saving...') 
              : t('common:save', 'Save')}
          </Button>
          
          {/* Note Dialog */}
          <Dialog
            open={noteDialogOpen}
            onClose={() => setNoteDialogOpen(false)}
            fullWidth
            maxWidth="sm"
          >
            <DialogTitle>{t('attendance:studentNotes', 'Student Notes')}</DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                label={t('attendance:notes', 'Notes')}
                fullWidth
                multiline
                rows={4}
                value={currentNote}
                onChange={(e) => setCurrentNote(e.target.value)}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setNoteDialogOpen(false)}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button onClick={handleSaveNote} color="primary">
                {t('common:save', 'Save')}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Typography variant="body1" color="error">
          {t('lessons:lessonNotFound', 'Lesson not found')}
        </Typography>
      )}
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileAttendance;
