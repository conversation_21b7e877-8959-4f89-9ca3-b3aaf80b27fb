import React, { useState, useEffect } from 'react';
import { 
  Typo<PERSON>, 
  <PERSON>, 
  Card, 
  CardContent, 
  Divider, 
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Switch,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  Alert,
  CircularProgress,
  LinearProgress,
  FormControlLabel,
  Checkbox,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import { 
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  CloudOff as CloudOffIcon,
  Storage as StorageIcon,
  Delete as DeleteIcon,
  Sync as SyncIcon,
  Language as LanguageIcon,
  Brightness4 as Brightness4Icon,
  ExitToApp as ExitToAppIcon,
  Info as InfoIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { useOfflineMode } from '../../hooks/useOfflineMode';
import { useTheme } from '../../hooks/useTheme';

const MobileSettings: React.FC = () => {
  const { t, i18n } = useTranslation(['common', 'settings']);
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { currentSchool } = useSchool();
  const { 
    isOfflineModeEnabled, 
    toggleOfflineMode, 
    syncData, 
    clearOfflineData, 
    lastSyncTime,
    syncProgress,
    isSyncing
  } = useOfflineMode();
  const { darkMode, toggleDarkMode } = useTheme();
  
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [confirmLogoutOpen, setConfirmLogoutOpen] = useState(false);
  const [confirmClearDataOpen, setConfirmClearDataOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [language, setLanguage] = useState(i18n.language);

  const handleNotificationsToggle = () => {
    setNotificationsEnabled(!notificationsEnabled);
    showSnackbar(
      !notificationsEnabled 
        ? t('settings:notificationsEnabled', 'Notifications enabled') 
        : t('settings:notificationsDisabled', 'Notifications disabled'),
      'success'
    );
  };

  const handleOfflineModeToggle = () => {
    toggleOfflineMode();
    showSnackbar(
      !isOfflineModeEnabled 
        ? t('settings:offlineModeEnabled', 'Offline mode enabled') 
        : t('settings:offlineModeDisabled', 'Offline mode disabled'),
      'success'
    );
  };

  const handleDarkModeToggle = () => {
    toggleDarkMode();
    showSnackbar(
      !darkMode 
        ? t('settings:darkModeEnabled', 'Dark mode enabled') 
        : t('settings:darkModeDisabled', 'Dark mode disabled'),
      'success'
    );
  };

  const handleLanguageChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const newLanguage = event.target.value as string;
    setLanguage(newLanguage);
    i18n.changeLanguage(newLanguage);
    showSnackbar(t('settings:languageChanged', 'Language changed'), 'success');
  };

  const handleSyncData = async () => {
    try {
      await syncData();
      showSnackbar(t('settings:dataSynced', 'Data synchronized successfully'), 'success');
    } catch (error) {
      console.error('Error syncing data:', error);
      showSnackbar(t('settings:syncError', 'Failed to synchronize data'), 'error');
    }
  };

  const handleClearOfflineData = async () => {
    try {
      await clearOfflineData();
      setConfirmClearDataOpen(false);
      showSnackbar(t('settings:dataCleared', 'Offline data cleared'), 'success');
    } catch (error) {
      console.error('Error clearing offline data:', error);
      showSnackbar(t('settings:clearError', 'Failed to clear offline data'), 'error');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
    } catch (error) {
      console.error('Error logging out:', error);
      showSnackbar(t('settings:logoutError', 'Failed to log out'), 'error');
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const formatLastSyncTime = () => {
    if (!lastSyncTime) return t('settings:neverSynced', 'Never');
    
    const date = new Date(lastSyncTime);
    return date.toLocaleString();
  };

  return (
    <MobileLayout title={t('common:settings', 'Settings')}>
      <Card sx={{ mb: 3 }}>
        <List>
          <ListItem>
            <ListItemIcon>
              <NotificationsIcon />
            </ListItemIcon>
            <ListItemText 
              primary={t('settings:notifications', 'Notifications')}
              secondary={t('settings:notificationsDescription', 'Enable or disable notifications')}
            />
            <ListItemSecondaryAction>
              <Switch
                edge="end"
                checked={notificationsEnabled}
                onChange={handleNotificationsToggle}
              />
            </ListItemSecondaryAction>
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem>
            <ListItemIcon>
              <CloudOffIcon />
            </ListItemIcon>
            <ListItemText 
              primary={t('settings:offlineMode', 'Offline Mode')}
              secondary={t('settings:offlineModeDescription', 'Access your data without internet connection')}
            />
            <ListItemSecondaryAction>
              <Switch
                edge="end"
                checked={isOfflineModeEnabled}
                onChange={handleOfflineModeToggle}
              />
            </ListItemSecondaryAction>
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem>
            <ListItemIcon>
              <Brightness4Icon />
            </ListItemIcon>
            <ListItemText 
              primary={t('settings:darkMode', 'Dark Mode')}
              secondary={t('settings:darkModeDescription', 'Toggle dark theme')}
            />
            <ListItemSecondaryAction>
              <Switch
                edge="end"
                checked={darkMode}
                onChange={handleDarkModeToggle}
              />
            </ListItemSecondaryAction>
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem>
            <ListItemIcon>
              <LanguageIcon />
            </ListItemIcon>
            <ListItemText 
              primary={t('settings:language', 'Language')}
              secondary={t('settings:languageDescription', 'Change application language')}
            />
            <ListItemSecondaryAction>
              <FormControl sx={{ minWidth: 120 }}>
                <Select
                  value={language}
                  onChange={handleLanguageChange}
                  size="small"
                >
                  <MenuItem value="en">English</MenuItem>
                  <MenuItem value="sl">Slovenščina</MenuItem>
                </Select>
              </FormControl>
            </ListItemSecondaryAction>
          </ListItem>
        </List>
      </Card>
      
      <Typography variant="h6" gutterBottom>
        {t('settings:dataSync', 'Data Synchronization')}
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {t('settings:lastSync', 'Last synchronized')}
            </Typography>
            <Typography variant="body1">
              {formatLastSyncTime()}
            </Typography>
          </Box>
          
          {isSyncing && (
            <Box sx={{ width: '100%', mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('settings:syncProgress', 'Sync progress')}
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={syncProgress} 
                sx={{ height: 10, borderRadius: 5 }}
              />
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {syncProgress}%
                </Typography>
              </Box>
            </Box>
          )}
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SyncIcon />}
              onClick={handleSyncData}
              disabled={isSyncing}
            >
              {isSyncing 
                ? t('settings:syncing', 'Syncing...') 
                : t('settings:syncNow', 'Sync Now')}
            </Button>
            
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={() => setConfirmClearDataOpen(true)}
              disabled={isSyncing}
            >
              {t('settings:clearData', 'Clear Data')}
            </Button>
          </Box>
        </CardContent>
      </Card>
      
      <Typography variant="h6" gutterBottom>
        {t('settings:account', 'Account')}
      </Typography>
      
      <Card>
        <List>
          <ListItem>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText 
              primary={t('settings:accountInfo', 'Account Information')}
              secondary={user?.email}
            />
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem>
            <ListItemIcon>
              <SecurityIcon />
            </ListItemIcon>
            <ListItemText 
              primary={t('settings:changePassword', 'Change Password')}
              secondary={t('settings:changePasswordDescription', 'Update your account password')}
            />
            <ListItemSecondaryAction>
              <Button 
                variant="text" 
                color="primary"
                onClick={() => navigate('/auth/reset-password')}
              >
                {t('settings:change', 'Change')}
              </Button>
            </ListItemSecondaryAction>
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem button onClick={() => setConfirmLogoutOpen(true)}>
            <ListItemIcon>
              <ExitToAppIcon />
            </ListItemIcon>
            <ListItemText 
              primary={t('common:logout', 'Logout')}
              secondary={t('settings:logoutDescription', 'Sign out of your account')}
            />
          </ListItem>
        </List>
      </Card>
      
      {/* Confirm Logout Dialog */}
      <Dialog
        open={confirmLogoutOpen}
        onClose={() => setConfirmLogoutOpen(false)}
      >
        <DialogTitle>{t('settings:confirmLogout', 'Confirm Logout')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('settings:confirmLogoutMessage', 'Are you sure you want to log out? Any unsynchronized data may be lost.')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmLogoutOpen(false)}>
            {t('common:cancel', 'Cancel')}
          </Button>
          <Button onClick={handleLogout} color="primary">
            {t('common:logout', 'Logout')}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Confirm Clear Data Dialog */}
      <Dialog
        open={confirmClearDataOpen}
        onClose={() => setConfirmClearDataOpen(false)}
      >
        <DialogTitle>{t('settings:confirmClearData', 'Confirm Clear Data')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('settings:confirmClearDataMessage', 'Are you sure you want to clear all offline data? This action cannot be undone.')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmClearDataOpen(false)}>
            {t('common:cancel', 'Cancel')}
          </Button>
          <Button onClick={handleClearOfflineData} color="error">
            {t('settings:clearData', 'Clear Data')}
          </Button>
        </DialogActions>
      </Dialog>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileSettings;
