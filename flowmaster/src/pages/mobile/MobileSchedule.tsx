import React, { useState, useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Divider,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  IconButton,
  Fab,
  Skeleton,
  Badge,
  Paper,
  Button,
  ButtonGroup,
} from '@mui/material';
import {
  Add as AddIcon,
  Event as EventIcon,
  AccessTime as TimeIcon,
  ArrowForward as ArrowForwardIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Today as TodayIcon,
  DateRange as DateRangeIcon,
  CalendarViewMonth as CalendarViewMonthIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  format,
  addDays,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isToday,
  isSameDay,
  addWeeks,
  subWeeks,
} from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../config/firebase';
import { collection, query, where, getDocs, orderBy, Timestamp } from 'firebase/firestore';
import { Lesson } from '../../types/lesson';

enum ViewType {
  Day = 'day',
  Week = 'week',
  Month = 'month',
}

const MobileSchedule: React.FC = () => {
  const { t } = useTranslation(['common', 'lessons', 'scheduling']);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();

  const [viewType, setViewType] = useState<ViewType>(ViewType.Day);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [weekStart, setWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [weekDays, setWeekDays] = useState<Date[]>([]);

  useEffect(() => {
    // Calculate week days when week start changes
    const days = eachDayOfInterval({
      start: weekStart,
      end: endOfWeek(weekStart, { weekStartsOn: 1 }),
    });
    setWeekDays(days);
  }, [weekStart]);

  useEffect(() => {
    const fetchLessons = async () => {
      if (!user?.uid || !currentSchool?.id) return;

      try {
        setLoading(true);

        let startDate: Date;
        let endDate: Date;

        switch (viewType) {
          case ViewType.Day:
            // For day view, get lessons for the selected day
            startDate = new Date(selectedDate);
            startDate.setHours(0, 0, 0, 0);

            endDate = new Date(selectedDate);
            endDate.setHours(23, 59, 59, 999);
            break;

          case ViewType.Week:
            // For week view, get lessons for the entire week
            startDate = weekStart;
            endDate = endOfWeek(weekStart, { weekStartsOn: 1 });
            endDate.setHours(23, 59, 59, 999);
            break;

          case ViewType.Month:
            // For month view, we'll implement this later
            // For now, just show the current week
            startDate = weekStart;
            endDate = endOfWeek(weekStart, { weekStartsOn: 1 });
            endDate.setHours(23, 59, 59, 999);
            break;

          default:
            startDate = new Date(selectedDate);
            startDate.setHours(0, 0, 0, 0);

            endDate = new Date(selectedDate);
            endDate.setHours(23, 59, 59, 999);
        }

        const lessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('instructorId', '==', user.uid),
          where('startTime', '>=', Timestamp.fromDate(startDate)),
          where('startTime', '<=', Timestamp.fromDate(endDate)),
          orderBy('startTime', 'asc')
        );

        const lessonsSnapshot = await getDocs(lessonsQuery);
        const lessonsData = lessonsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Lesson[];

        setLessons(lessonsData);
      } catch (error) {
        console.error('Error fetching lessons:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLessons();
  }, [user?.uid, currentSchool?.id, viewType, selectedDate, weekStart]);

  const handleViewChange = (_: React.SyntheticEvent, newValue: ViewType) => {
    setViewType(newValue);
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    if (viewType === ViewType.Week) {
      setWeekStart(startOfWeek(date, { weekStartsOn: 1 }));
    }
  };

  const handlePrevious = () => {
    if (viewType === ViewType.Day) {
      setSelectedDate((prevDate) => addDays(prevDate, -1));
    } else if (viewType === ViewType.Week) {
      setWeekStart((prevWeekStart) => subWeeks(prevWeekStart, 1));
    }
  };

  const handleNext = () => {
    if (viewType === ViewType.Day) {
      setSelectedDate((prevDate) => addDays(prevDate, 1));
    } else if (viewType === ViewType.Week) {
      setWeekStart((prevWeekStart) => addWeeks(prevWeekStart, 1));
    }
  };

  const handleToday = () => {
    const today = new Date();
    setSelectedDate(today);
    if (viewType === ViewType.Week) {
      setWeekStart(startOfWeek(today, { weekStartsOn: 1 }));
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';

    const date = timestamp instanceof Timestamp ? timestamp.toDate() : new Date(timestamp);

    return format(date, 'HH:mm');
  };

  const getLessonStatusChip = (lesson: Lesson) => {
    switch (lesson.status) {
      case 'completed':
        return (
          <Chip
            size="small"
            icon={<CheckCircleIcon />}
            label={t('lessons:status.completed')}
            color="success"
          />
        );
      case 'cancelled':
        return (
          <Chip
            size="small"
            icon={<CancelIcon />}
            label={t('lessons:status.cancelled')}
            color="error"
          />
        );
      case 'scheduled':
      default:
        return (
          <Chip
            size="small"
            icon={<TimeIcon />}
            label={t('lessons:status.scheduled')}
            color="primary"
          />
        );
    }
  };

  const getLessonsForDay = (day: Date) => {
    return lessons.filter((lesson) => {
      const lessonDate =
        lesson.startTime instanceof Timestamp
          ? lesson.startTime.toDate()
          : new Date(lesson.startTime);

      return isSameDay(lessonDate, day);
    });
  };

  const renderDayView = () => (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handlePrevious}>
          <NavigateBeforeIcon />
        </IconButton>
        <Typography variant="h6">{format(selectedDate, 'EEEE, MMMM d')}</Typography>
        <IconButton onClick={handleNext}>
          <NavigateNextIcon />
        </IconButton>
      </Box>

      <Card>
        {loading ? (
          <CardContent>
            {[1, 2, 3].map((item) => (
              <Box key={item} sx={{ mb: 2 }}>
                <Skeleton variant="rectangular" width="100%" height={60} />
              </Box>
            ))}
          </CardContent>
        ) : lessons.length > 0 ? (
          <List sx={{ p: 0 }}>
            {lessons.map((lesson, index) => (
              <React.Fragment key={lesson.id}>
                <ListItem
                  button
                  onClick={() => navigate(`/mobile/lessons/${lesson.id}`)}
                  secondaryAction={
                    <IconButton edge="end" aria-label="details">
                      <ArrowForwardIcon />
                    </IconButton>
                  }
                >
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <TimeIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" component="span">
                          {lesson.title}
                        </Typography>
                        {getLessonStatusChip(lesson)}
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography variant="body2" component="span">
                          {formatTime(lesson.startTime)} - {lesson.duration} min
                        </Typography>
                        <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                          {lesson.studentIds?.length || 0} {t('common:students', 'Students')}
                        </Typography>
                      </>
                    }
                  />
                </ListItem>
                {index < lessons.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        ) : (
          <CardContent>
            <Typography variant="body2" color="text.secondary" align="center">
              {t('scheduling:noLessonsForDay', 'No lessons scheduled for this day')}
            </Typography>
          </CardContent>
        )}
      </Card>
    </>
  );

  const renderWeekView = () => (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handlePrevious}>
          <NavigateBeforeIcon />
        </IconButton>
        <Typography variant="h6">
          {format(weekStart, 'MMM d')} -{' '}
          {format(endOfWeek(weekStart, { weekStartsOn: 1 }), 'MMM d, yyyy')}
        </Typography>
        <IconButton onClick={handleNext}>
          <NavigateNextIcon />
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', overflowX: 'auto', pb: 1, mb: 2 }}>
        {weekDays.map((day) => (
          <Box
            key={day.toString()}
            sx={{
              minWidth: 60,
              textAlign: 'center',
              mx: 0.5,
              cursor: 'pointer',
            }}
            onClick={() => handleDateChange(day)}
          >
            <Paper
              elevation={isSameDay(day, selectedDate) ? 3 : 1}
              sx={{
                py: 1,
                bgcolor: isToday(day)
                  ? 'primary.light'
                  : isSameDay(day, selectedDate)
                    ? 'primary.main'
                    : 'background.paper',
                color: isSameDay(day, selectedDate)
                  ? 'primary.contrastText'
                  : isToday(day)
                    ? 'primary.contrastText'
                    : 'text.primary',
              }}
            >
              <Typography variant="body2">{format(day, 'EEE')}</Typography>
              <Badge
                color="secondary"
                badgeContent={getLessonsForDay(day).length}
                max={99}
                sx={{ '& .MuiBadge-badge': { top: 5, right: -15 } }}
              >
                <Typography variant="h6">{format(day, 'd')}</Typography>
              </Badge>
            </Paper>
          </Box>
        ))}
      </Box>

      <Typography variant="h6" sx={{ mb: 2 }}>
        {format(selectedDate, 'EEEE, MMMM d')}
      </Typography>

      <Card>
        {loading ? (
          <CardContent>
            {[1, 2, 3].map((item) => (
              <Box key={item} sx={{ mb: 2 }}>
                <Skeleton variant="rectangular" width="100%" height={60} />
              </Box>
            ))}
          </CardContent>
        ) : getLessonsForDay(selectedDate).length > 0 ? (
          <List sx={{ p: 0 }}>
            {getLessonsForDay(selectedDate).map((lesson, index) => (
              <React.Fragment key={lesson.id}>
                <ListItem
                  button
                  onClick={() => navigate(`/mobile/lessons/${lesson.id}`)}
                  secondaryAction={
                    <IconButton edge="end" aria-label="details">
                      <ArrowForwardIcon />
                    </IconButton>
                  }
                >
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <TimeIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" component="span">
                          {lesson.title}
                        </Typography>
                        {getLessonStatusChip(lesson)}
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography variant="body2" component="span">
                          {formatTime(lesson.startTime)} - {lesson.duration} min
                        </Typography>
                        <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                          {lesson.studentIds?.length || 0} {t('common:students', 'Students')}
                        </Typography>
                      </>
                    }
                  />
                </ListItem>
                {index < getLessonsForDay(selectedDate).length - 1 && (
                  <Divider variant="inset" component="li" />
                )}
              </React.Fragment>
            ))}
          </List>
        ) : (
          <CardContent>
            <Typography variant="body2" color="text.secondary" align="center">
              {t('scheduling:noLessonsForDay', 'No lessons scheduled for this day')}
            </Typography>
          </CardContent>
        )}
      </Card>
    </>
  );

  return (
    <MobileLayout title={t('common:schedule', 'Schedule')}>
      <Box sx={{ mb: 2 }}>
        <ButtonGroup variant="outlined" fullWidth>
          <Button startIcon={<TodayIcon />} onClick={handleToday}>
            {t('common:today', 'Today')}
          </Button>
        </ButtonGroup>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Tabs
          value={viewType}
          onChange={handleViewChange}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab icon={<TodayIcon />} label={t('scheduling:dayView', 'Day')} value={ViewType.Day} />
          <Tab
            icon={<DateRangeIcon />}
            label={t('scheduling:weekView', 'Week')}
            value={ViewType.Week}
          />
          <Tab
            icon={<CalendarViewMonthIcon />}
            label={t('scheduling:monthView', 'Month')}
            value={ViewType.Month}
            disabled
          />
        </Tabs>
      </Box>

      {viewType === ViewType.Day && renderDayView()}
      {viewType === ViewType.Week && renderWeekView()}
      {viewType === ViewType.Month && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1">
            {t('scheduling:monthViewComingSoon', 'Month view coming soon')}
          </Typography>
        </Box>
      )}

      <Fab
        color="primary"
        aria-label="add lesson"
        sx={{ position: 'fixed', bottom: 70, right: 16 }}
        onClick={() => navigate('/mobile/lessons/create')}
      >
        <AddIcon />
      </Fab>
    </MobileLayout>
  );
};

export default MobileSchedule;
