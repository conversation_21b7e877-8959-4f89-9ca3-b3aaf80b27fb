import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Box, 
  TextField, 
  Button, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Select, 
  FormHelperText,
  Snackbar,
  Alert,
  CircularProgress,
  Grid,
  Paper,
  Switch,
  FormControlLabel
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  Timestamp, 
  serverTimestamp 
} from 'firebase/firestore';
import { Student } from '../../types/student';
import { v4 as uuidv4 } from 'uuid';

const studentTypes = ['individual', 'program', 'both'];
const levels = ['Beginner', 'Intermediate', 'Advanced', 'Expert'];

const MobileStudentForm: React.FC = () => {
  const { t } = useTranslation(['common', 'students']);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const isEditMode = !!id;
  
  const [formData, setFormData] = useState<Partial<Student>>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    type: 'individual',
    level: 'Beginner',
    status: 'active',
    notes: []
  });
  
  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('success');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchStudentData = async () => {
      if (!isEditMode || !id || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        const studentDoc = await getDoc(doc(db, 'schools', currentSchool.id, 'students', id));
        
        if (studentDoc.exists()) {
          const studentData = { id: studentDoc.id, ...studentDoc.data() } as Student;
          setFormData(studentData);
        } else {
          showSnackbar(t('students:studentNotFound', 'Student not found'), 'error');
          navigate('/mobile/students');
        }
      } catch (error) {
        console.error('Error fetching student data:', error);
        showSnackbar(t('students:fetchError', 'Failed to load student data'), 'error');
      } finally {
        setLoading(false);
      }
    };
    
    fetchStudentData();
  }, [id, currentSchool?.id, isEditMode, navigate, t]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ 
      ...prev, 
      status: e.target.checked ? 'active' : 'inactive' 
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.firstName) {
      newErrors.firstName = t('students:errors.firstNameRequired', 'First name is required');
    }
    
    if (!formData.lastName) {
      newErrors.lastName = t('students:errors.lastNameRequired', 'Last name is required');
    }
    
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('students:errors.invalidEmail', 'Invalid email format');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !currentSchool?.id) {
      return;
    }
    
    try {
      setSaving(true);
      
      const studentData = {
        ...formData,
        updatedAt: serverTimestamp(),
      };
      
      if (isEditMode && id) {
        // Update existing student
        await updateDoc(doc(db, 'schools', currentSchool.id, 'students', id), studentData);
        showSnackbar(t('students:updateSuccess', 'Student updated successfully'), 'success');
      } else {
        // Create new student
        const newStudentId = uuidv4();
        await setDoc(doc(db, 'schools', currentSchool.id, 'students', newStudentId), {
          ...studentData,
          createdAt: serverTimestamp(),
          createdBy: user?.uid,
        });
        showSnackbar(t('students:createSuccess', 'Student created successfully'), 'success');
      }
      
      // Navigate back to students list
      navigate('/mobile/students');
      
    } catch (error) {
      console.error('Error saving student:', error);
      showSnackbar(
        isEditMode 
          ? t('students:updateError', 'Failed to update student') 
          : t('students:createError', 'Failed to create student'), 
        'error'
      );
    } finally {
      setSaving(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  if (loading) {
    return (
      <MobileLayout 
        title={isEditMode ? t('students:editStudent', 'Edit Student') : t('students:createStudent', 'Create Student')} 
        showBackButton
      >
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout 
      title={isEditMode ? t('students:editStudent', 'Edit Student') : t('students:createStudent', 'Create Student')} 
      showBackButton
    >
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          {isEditMode 
            ? t('students:editStudentDetails', 'Edit Student Details') 
            : t('students:newStudentDetails', 'New Student Details')}
        </Typography>
        
        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="firstName"
                label={t('students:firstName', 'First Name')}
                value={formData.firstName || ''}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.firstName}
                helperText={errors.firstName}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="lastName"
                label={t('students:lastName', 'Last Name')}
                value={formData.lastName || ''}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.lastName}
                helperText={errors.lastName}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="email"
                label={t('students:email', 'Email')}
                type="email"
                value={formData.email || ''}
                onChange={handleInputChange}
                fullWidth
                error={!!errors.email}
                helperText={errors.email}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="phone"
                label={t('students:phone', 'Phone')}
                value={formData.phone || ''}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal" error={!!errors.type}>
                <InputLabel id="type-label">{t('students:type', 'Type')}</InputLabel>
                <Select
                  labelId="type-label"
                  name="type"
                  value={formData.type || 'individual'}
                  onChange={handleSelectChange}
                  label={t('students:type', 'Type')}
                >
                  {studentTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {t(`students:types.${type}`, type)}
                    </MenuItem>
                  ))}
                </Select>
                {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal" error={!!errors.level}>
                <InputLabel id="level-label">{t('students:level', 'Level')}</InputLabel>
                <Select
                  labelId="level-label"
                  name="level"
                  value={formData.level || 'Beginner'}
                  onChange={handleSelectChange}
                  label={t('students:level', 'Level')}
                >
                  {levels.map(level => (
                    <MenuItem key={level} value={level}>
                      {level}
                    </MenuItem>
                  ))}
                </Select>
                {errors.level && <FormHelperText>{errors.level}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.status === 'active'}
                    onChange={handleStatusChange}
                    color="primary"
                  />
                }
                label={t('students:active', 'Active')}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  fullWidth
                  disabled={saving}
                >
                  {saving 
                    ? t('common:saving', 'Saving...') 
                    : isEditMode 
                      ? t('common:update', 'Update') 
                      : t('common:create', 'Create')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </MobileLayout>
  );
};

export default MobileStudentForm;
