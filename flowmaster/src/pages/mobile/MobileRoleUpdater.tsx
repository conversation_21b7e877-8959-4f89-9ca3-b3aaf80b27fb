import React, { useState } from 'react';
import { Box, Button, Typography, CircularProgress, Alert } from '@mui/material';
import { useAuth } from '../../hooks/useAuth';
import { updateUserRole } from '../../utils/updateUserRole';
import MobileLayout from '../../components/mobile/layout/MobileLayout';

const MobileRoleUpdater: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleUpdateRole = async () => {
    if (!user) {
      setError('You must be logged in to update your role');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      await updateUserRole(user.uid);
      
      setSuccess(true);
      // Force reload to update the user context
      window.location.reload();
    } catch (err) {
      console.error('Error updating role:', err);
      setError('Failed to update role. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MobileLayout title="Update Role">
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Add Instructor Role
        </Typography>
        
        <Typography variant="body1" paragraph>
          This utility will add the instructor role to your account while maintaining your admin privileges.
          This will allow you to see instructor-specific content in the mobile app.
        </Typography>
        
        <Typography variant="body2" paragraph color="text.secondary">
          Current user: {user?.displayName || user?.email}
          <br />
          Current role: {user?.role || 'Unknown'}
        </Typography>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Role updated successfully! The page will reload to apply changes.
          </Alert>
        )}
        
        <Button
          variant="contained"
          color="primary"
          onClick={handleUpdateRole}
          disabled={loading || !user}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Updating...' : 'Update Role to Instructor'}
        </Button>
      </Box>
    </MobileLayout>
  );
};

export default MobileRoleUpdater;
