import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Timestamp, collection, getDocs } from 'firebase/firestore';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  FormHelperText,
  Grid,
  Autocomplete,
  Chip,
  CircularProgress,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { useAuth } from '../../../hooks/useAuth';
import { CourseFormData, TimeSlot } from '../../../types/course';
import { fetchLevels } from '../../../services/levels';
import { fetchSports } from '../../../services/sports';
import { useSchool } from '../../../hooks/useSchool';
import { db } from '../../../services/firebase';
import type { Student } from '../../../types/student';
import type { Instructor } from '../../../types/instructor';
import StudentForm from '../../people/Students/StudentForm';
import AddIcon from '@mui/icons-material/Add';

// Helper function to convert Timestamp to Date
const convertToDate = (value: Date | Timestamp | null | undefined): Date => {
  if (!value) return new Date();
  if (value instanceof Date) return value;
  if (value instanceof Timestamp) return value.toDate();
  return new Date(value);
};

interface CourseFormProps {
  open: boolean;
  onClose: () => void;
  onSave?: (data: CourseFormData) => void;
  course?: CourseFormData & { id: string };
}

const DAYS_OF_WEEK = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

const CourseForm: React.FC<CourseFormProps> = ({ open, onClose, onSave, course }) => {
  const { t } = useTranslation('courses');
  const { user } = useAuth();
  const { currentSchool } = useSchool();

  // Add these states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [disciplineOptions, setDisciplineOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const [levelOptions, setLevelOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [isStudentFormOpen, setIsStudentFormOpen] = useState(false);

  const initialFormData: CourseFormData = {
    title: course?.title || '',
    type: course?.type || 'weekly',
    discipline: course?.discipline || '',
    instructorId: course?.instructorId || '',
    studentIds: course?.studentIds || [],
    ageGroup: course?.ageGroup || 'adults',
    level: course?.level || '',
    date: course?.date || new Date(),
    dateRange: course?.dateRange || [new Date(), new Date()],
    startDate: course?.type === 'weekly' ? convertToDate(course?.startDate) : new Date(),
    endDate: course?.type === 'weekly' ? convertToDate(course?.endDate) : new Date(),
    timeSlots: course?.timeSlots?.map((slot) => ({
      startTime: convertToDate(slot.startTime),
      endTime: convertToDate(slot.endTime),
    })) || [
      {
        startTime: new Date(),
        endTime: new Date(),
      },
    ],
    selectedDays: course?.type === 'weekly' ? course?.selectedDays || [] : [],
    maxParticipants: course?.maxParticipants || 10,
    status: course?.status || 'active',
    notes: course?.notes || '',
    createdBy: course?.createdBy || user?.uid || '',
    updatedAt: new Date(),
  };

  const [formData, setFormData] = useState<CourseFormData>(initialFormData);

  const [errors, setErrors] = useState<Partial<Record<keyof CourseFormData, string>>>({});

  const handleChange = (field: keyof CourseFormData, value: unknown) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
      updatedAt: new Date(),
    }));
  };

  const handleDaysChange = (_: React.MouseEvent<HTMLElement>, newDays: number[]) => {
    handleChange('selectedDays', newDays);
  };

  const handleAddTimeSlot = () => {
    setFormData((prev) => ({
      ...prev,
      timeSlots: [...prev.timeSlots, { startTime: new Date(), endTime: new Date() }],
    }));
  };

  const handleRemoveTimeSlot = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      timeSlots: prev.timeSlots.filter((_, i) => i !== index),
    }));
  };

  const handleTimeSlotChange = (index: number, field: keyof TimeSlot, value: Date) => {
    setFormData((prev) => ({
      ...prev,
      timeSlots: prev.timeSlots.map((slot, i) =>
        i === index ? { ...slot, [field]: value } : slot
      ),
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CourseFormData, string>> = {};

    if (!formData.title) {
      newErrors.title = t('errors.titleRequired', { ns: 'courses' });
    }

    if (!formData.discipline) {
      newErrors.discipline = t('errors.disciplineRequired', { ns: 'courses' });
    }

    if (!formData.instructorId) {
      newErrors.instructorId = t('errors.instructorRequired', { ns: 'courses' });
    }

    // Validate dates based on course type
    if (formData.type === 'weekly') {
      if (!formData.startDate || !formData.endDate) {
        newErrors.startDate = t('errors.titleRequired', { ns: 'courses' });
        newErrors.endDate = t('errors.titleRequired', { ns: 'courses' });
      } else if (formData.endDate < formData.startDate) {
        newErrors.endDate = t('errors.endDateBeforeStartDate', { ns: 'courses' });
      }

      if (!formData.selectedDays || formData.selectedDays.length === 0) {
        newErrors.selectedDays = t('errors.daysRequired', { ns: 'courses' });
      }
    } else {
      // Daily course validation
      if (!formData.startDate) {
        newErrors.startDate = t('errors.titleRequired', { ns: 'courses' });
      }
    }

    // Validate time slots
    if (!formData.timeSlots || formData.timeSlots.length === 0) {
      newErrors.timeSlots = t('errors.timeSlotsRequired', { ns: 'courses' });
    } else {
      formData.timeSlots.forEach((slot) => {
        if (!slot.startTime || !slot.endTime) {
          newErrors.timeSlots = t('errors.timeSlotsRequired', { ns: 'courses' });
        } else if (slot.endTime <= slot.startTime) {
          newErrors.timeSlots = t('errors.endTimeBeforeStartTime', { ns: 'courses' });
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      console.log('Saving course with data:', formData); // Add this
      if (onSave) {
        await onSave(formData);
      }
      onClose();
    } catch (err) {
      console.error('Error saving course:', err);
    }
  };

  // Add this effect for fetching instructors and students
  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        const [instructorsSnapshot, studentsSnapshot] = await Promise.all([
          getDocs(collection(db, 'schools', currentSchool.id, 'instructors')),
          getDocs(collection(db, 'schools', currentSchool.id, 'students')),
        ]);

        const instructorsData = instructorsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Instructor[];

        const studentsData = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Student[];

        setInstructors(instructorsData);
        setStudents(studentsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to fetch instructors and students');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id]);

  // Add this effect for fetching disciplines and levels
  useEffect(() => {
    const loadData = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);
        const [levels, sports] = await Promise.all([
          fetchLevels(currentSchool.id),
          fetchSports(currentSchool.id),
        ]);

        // Map levels
        if (levels.length === 0) {
          setError('No levels configured for this school');
        } else {
          const levelOpts = levels.map((level: { name: string; displayName?: string }) => ({
            value: level.name,
            label: level.displayName || level.name,
          }));
          setLevelOptions(levelOpts);

          // Set default level if not already set
          if (!formData.level && levelOpts.length > 0) {
            handleChange('level', levelOpts[0].value);
          }
        }

        // Map sports/disciplines
        if (sports.length === 0) {
          setError((prev) =>
            prev
              ? `${prev}. No disciplines configured`
              : 'No disciplines configured for this school'
          );
        } else {
          const sportOpts = sports.map((sport: { name: string; displayName?: string }) => ({
            value: sport.name,
            label: sport.displayName || sport.name,
          }));
          setDisciplineOptions(sportOpts);

          // Set default discipline if not already set
          if (!formData.discipline && sportOpts.length > 0) {
            handleChange('discipline', sportOpts[0].value);
          }
        }
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load configuration data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentSchool?.id, formData.discipline, formData.level]);

  // Add this handler for student selection
  const handleStudentChange = (selectedStudents: Student[]) => {
    const studentIds = selectedStudents.map((student) => student.id);
    handleChange('studentIds', studentIds);
  };

  // Get selected students
  const selectedStudents = students.filter((student) => formData.studentIds?.includes(student.id));

  // Add a check for daily course
  const isDailyCourse = formData.type === 'daily';

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{course ? t('editCourse') : t('createCourse')}</DialogTitle>
      <DialogContent>
        {error && (
          <Box sx={{ mb: 2 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('courseTitle')}
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              error={!!errors.title}
              helperText={errors.title}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>{t('courseType')}</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => handleChange('type', e.target.value)}
                label={t('courseType')}
              >
                <MenuItem value="weekly">{t('weekly')}</MenuItem>
                <MenuItem value="daily">{t('daily')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Age Group Selection */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>{t('ageGroup')}</InputLabel>
              <Select
                value={formData.ageGroup}
                onChange={(e) => handleChange('ageGroup', e.target.value)}
                label={t('ageGroup')}
              >
                <MenuItem value="children">{t('children')}</MenuItem>
                <MenuItem value="adults">{t('adults')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Date Selection - Different for daily and weekly courses */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              {isDailyCourse ? t('date', { ns: 'courses' }) : t('dateRange', { ns: 'courses' })}
            </Typography>
            <Box display="flex" gap={2}>
              <DatePicker
                label={isDailyCourse ? t('date') : t('startDate')}
                value={convertToDate(formData.startDate)}
                onChange={(newDate) => handleChange('startDate', newDate)}
              />
              {!isDailyCourse && (
                <DatePicker
                  label={t('endDate')}
                  value={convertToDate(formData.endDate)}
                  onChange={(date) => handleChange('endDate', date)}
                />
              )}
            </Box>
          </Grid>

          {/* Days Selection - Only show for weekly courses */}
          {!isDailyCourse && (
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                {t('selectDays')}
              </Typography>
              <ToggleButtonGroup
                value={formData.selectedDays}
                onChange={handleDaysChange}
                aria-label="days of week"
                sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: { xs: 0.5, sm: 1 },
                  '& .MuiToggleButton-root': {
                    flex: '1 0 auto',
                    minWidth: '100px',
                    borderRadius: 1,
                    m: 0.5,
                    typography: 'button',
                    '&.Mui-selected': {
                      backgroundColor: 'primary.main',
                      color: 'primary.contrastText',
                      '&:hover': {
                        backgroundColor: 'primary.dark',
                      },
                    },
                  },
                }}
              >
                {DAYS_OF_WEEK.map((day, index) => {
                  const translationKey = `form.days.${day.toLowerCase()}` as
                    | 'form.days.sunday'
                    | 'form.days.monday'
                    | 'form.days.tuesday'
                    | 'form.days.wednesday'
                    | 'form.days.thursday'
                    | 'form.days.friday'
                    | 'form.days.saturday';
                  return (
                    <ToggleButton key={day} value={index} aria-label={day}>
                      {t(translationKey)}
                    </ToggleButton>
                  );
                })}
              </ToggleButtonGroup>
              {errors.selectedDays && <FormHelperText error>{errors.selectedDays}</FormHelperText>}
            </Grid>
          )}

          {/* Time Slots */}
          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="subtitle1">{t('timeSlots')}</Typography>
              <Button onClick={handleAddTimeSlot} variant="outlined" size="small">
                {t('addTimeSlot')}
              </Button>
            </Box>
            {formData.timeSlots.map((slot, index) => (
              <Box key={index} display="flex" gap={2} mb={2}>
                <TimePicker
                  label={t('startTime')}
                  value={convertToDate(slot.startTime)}
                  onChange={(time) => handleTimeSlotChange(index, 'startTime', time as Date)}
                />
                <TimePicker
                  label={t('endTime')}
                  value={convertToDate(slot.endTime)}
                  onChange={(time) => handleTimeSlotChange(index, 'endTime', time as Date)}
                />
                {formData.timeSlots.length > 1 && (
                  <Button
                    onClick={() => handleRemoveTimeSlot(index)}
                    color="error"
                    variant="outlined"
                  >
                    {t('remove')}
                  </Button>
                )}
              </Box>
            ))}
          </Grid>

          {/* Add Discipline Selection */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.discipline}>
              <InputLabel>{t('form.discipline')}</InputLabel>
              <Select
                value={formData.discipline}
                onChange={(e) => handleChange('discipline', e.target.value)}
                label={t('form.discipline')}
              >
                {loading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} />
                  </MenuItem>
                ) : disciplineOptions.length === 0 ? (
                  <MenuItem disabled>No disciplines available</MenuItem>
                ) : (
                  disciplineOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))
                )}
              </Select>
              {errors.discipline && <FormHelperText>{errors.discipline}</FormHelperText>}
            </FormControl>
          </Grid>

          {/* Add Instructor Selection */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.instructorId}>
              <InputLabel>{t('form.instructor')}</InputLabel>
              <Select
                value={formData.instructorId}
                onChange={(e) => handleChange('instructorId', e.target.value)}
                label={t('form.instructor')}
              >
                {loading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} />
                  </MenuItem>
                ) : instructors.length === 0 ? (
                  <MenuItem disabled>No instructors available</MenuItem>
                ) : (
                  instructors.map((instructor) => (
                    <MenuItem key={instructor.id} value={instructor.id}>
                      {`${instructor.firstName} ${instructor.lastName}`}
                    </MenuItem>
                  ))
                )}
              </Select>
              {errors.instructorId && <FormHelperText>{errors.instructorId}</FormHelperText>}
            </FormControl>
          </Grid>

          {/* Add Level Selection */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>{t('form.level')}</InputLabel>
              <Select
                value={formData.level}
                onChange={(e) => handleChange('level', e.target.value)}
                label={t('form.level')}
              >
                {loading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} />
                  </MenuItem>
                ) : levelOptions.length === 0 ? (
                  <MenuItem disabled>No levels available</MenuItem>
                ) : (
                  levelOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Add Student Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Autocomplete
                multiple
                options={students}
                getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
                value={selectedStudents}
                onChange={(_, newValue) => handleStudentChange(newValue)}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      label={`${option.firstName} ${option.lastName}`}
                      {...getTagProps({ index })}
                      key={option.id}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('form.students')}
                    placeholder={t('form.selectStudents')}
                  />
                )}
              />
              <Box display="flex" justifyContent="flex-end" mt={1}>
                <Button
                  startIcon={<AddIcon />}
                  onClick={() => setIsStudentFormOpen(true)}
                  size="small"
                >
                  {t('form.addNewStudent')}
                </Button>
              </Box>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <StudentForm
        open={isStudentFormOpen}
        onClose={() => {
          setIsStudentFormOpen(false);
          // Refresh students list after adding a new student
          if (currentSchool?.id) {
            getDocs(collection(db, 'schools', currentSchool.id, 'students'))
              .then((snapshot) => {
                const studentsData = snapshot.docs.map((doc) => ({
                  id: doc.id,
                  ...doc.data(),
                })) as Student[];
                setStudents(studentsData);
              })
              .catch((error) => console.error('Error refreshing students:', error));
          }
        }}
      />
      <DialogActions>
        <Button onClick={onClose}>{t('ui.cancel', { ns: 'common' })}</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {course ? t('ui.save', { ns: 'common' }) : t('ui.create', { ns: 'common' })}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CourseForm;
