import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  collection,
  getDocs,
  deleteDoc,
  doc,
  query,
  where,
  Timestamp,
  setDoc,
} from 'firebase/firestore';
import { db } from '../../../services/firebase';
import CourseForm from './CourseForm';
import ConfirmationDialog from '../../../components/common/ConfirmationDialog';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Tooltip,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CalendarMonth as CalendarIcon,
} from '@mui/icons-material';
import { useAuth } from '../../../hooks/useAuth';
import { useSchool } from '../../../hooks/useSchool';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Course, CourseFormData } from '../../../types/course';

const WeeklyCourses: React.FC = () => {
  const { t } = useTranslation(['courses', 'common']);
  const { user } = useAuth();
  const { currentSchool, loading: schoolLoading } = useSchool();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCourse, setSelectedCourse] = useState<Course | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);
  const navigate = useNavigate();

  // Check if user has admin or instructor role
  const canManageCourses = user?.role === 'admin' || user?.role === 'instructor';

  const fetchCourses = useCallback(async () => {
    if (!currentSchool?.id) {
      console.log('No school ID available');
      return;
    }

    setLoading(true);
    try {
      const coursesCollection = collection(db, 'schools', currentSchool.id, 'courses');
      console.log('Attempting to query courses for school:', currentSchool.id);
      console.log('Query path:', `schools/${currentSchool.id}/courses`);

      // Log the query conditions
      const coursesQuery = query(coursesCollection, where('type', '==', 'weekly'));
      console.log('Query conditions:', { type: 'weekly' });

      const coursesSnapshot = await getDocs(coursesQuery);
      console.log('Query snapshot empty?', coursesSnapshot.empty);
      console.log('Number of documents:', coursesSnapshot.size);

      // Log each document for debugging
      coursesSnapshot.forEach((doc) => {
        console.log('Document data:', {
          id: doc.id,
          data: doc.data(),
          exists: doc.exists(),
        });
      });

      const coursesData = coursesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Course[];

      console.log('Processed courses data:', coursesData);
      setCourses(coursesData);
    } catch (error) {
      console.error('Error fetching courses:', error);
      if (error instanceof Error) {
        console.error('Detailed error:', {
          message: error.message,
          stack: error.stack,
          name: error.name,
        });
      }
    } finally {
      setLoading(false);
    }
  }, [currentSchool?.id]);

  useEffect(() => {
    if (!schoolLoading) {
      fetchCourses();
    }
  }, [fetchCourses, schoolLoading]);

  const handleAddCourse = () => {
    if (!canManageCourses) {
      console.warn('Unauthorized access attempt to add course');
      return;
    }
    setSelectedCourse(undefined);
    setIsFormOpen(true);
  };

  const handleEditCourse = (course: Course) => {
    if (!canManageCourses) {
      console.warn('Unauthorized access attempt to edit course');
      return;
    }
    setSelectedCourse(course);
    setIsFormOpen(true);
  };

  const handleDeleteCourse = (course: Course) => {
    if (!canManageCourses) {
      console.warn('Unauthorized access attempt to delete course');
      return;
    }
    setCourseToDelete(course);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!courseToDelete || !currentSchool?.id) return;

    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'courses', courseToDelete.id));
      setCourses((prevCourses) => prevCourses.filter((course) => course.id !== courseToDelete.id));
      setDeleteConfirmOpen(false);
      setCourseToDelete(null);
    } catch (error) {
      console.error('Error deleting course:', error);
    }
  };

  const handleViewDetails = (courseId: string) => {
    navigate(`/courses/${courseId}`);
  };

  const formatFirebaseDate = (date: Date | Timestamp) => {
    if (date instanceof Timestamp) {
      return format(date.toDate(), 'PP');
    }
    return format(date, 'PP');
  };

  const handleSaveCourse = async (courseData: CourseFormData) => {
    if (!currentSchool?.id) return;

    try {
      const courseRef = selectedCourse
        ? doc(db, 'schools', currentSchool.id, 'courses', selectedCourse.id)
        : doc(collection(db, 'schools', currentSchool.id, 'courses'));

      // Ensure type is set to 'weekly'
      const courseToSave = {
        ...courseData,
        type: 'weekly', // Force the type to be weekly
        updatedAt: Timestamp.now(),
        createdBy: user?.uid || '',
      };

      console.log('Saving course with data:', courseToSave); // Debug log

      await setDoc(courseRef, courseToSave, { merge: true });

      // Refresh the courses list
      await fetchCourses();

      setIsFormOpen(false);
    } catch (error) {
      console.error('Error saving course:', error);
    }
  };

  if (loading || schoolLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box display="flex" alignItems="center">
            <CalendarIcon sx={{ fontSize: '2rem', mr: 1 }} />
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            >
              {t('weekly')}
            </Typography>
          </Box>
          {canManageCourses && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddCourse}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
              }}
            >
              {t('create.title')}
            </Button>
          )}
        </Box>

        <TableContainer
          component={Paper}
          sx={{
            borderRadius: 2,
            boxShadow: 3,
            overflow: 'hidden',
          }}
        >
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'primary.main' }}>
                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>{t('form.title')}</TableCell>
                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                  {t('form.discipline')}
                </TableCell>
                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>{t('ageGroup')}</TableCell>
                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                  {t('form.startDate')}
                </TableCell>
                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                  {t('form.endDate')}
                </TableCell>
                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>{t('form.level')}</TableCell>
                {canManageCourses && (
                  <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('ui.actions', { ns: 'common' })}
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {courses.map((course) => (
                <TableRow
                  key={course.id}
                  sx={{
                    '&:nth-of-type(odd)': { bgcolor: 'action.hover' },
                    '&:hover': { bgcolor: 'action.selected' },
                    transition: 'background-color 0.2s ease',
                    cursor: 'pointer',
                  }}
                  onClick={() => handleViewDetails(course.id)}
                >
                  <TableCell>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {course.title}
                    </Typography>
                  </TableCell>
                  <TableCell>{course.discipline}</TableCell>
                  <TableCell>
                    <Chip
                      label={t(`form.ageGroup.${course.ageGroup}`)}
                      color={course.ageGroup === 'children' ? 'primary' : 'secondary'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{formatFirebaseDate(course.startDate)}</TableCell>
                  <TableCell>{formatFirebaseDate(course.endDate)}</TableCell>
                  <TableCell>{course.level}</TableCell>
                  {canManageCourses && (
                    <TableCell align="right">
                      <Box
                        sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Tooltip title={t('ui.edit', { ns: 'common' })}>
                          <IconButton
                            onClick={() => handleEditCourse(course)}
                            size="small"
                            sx={{
                              color: 'primary.main',
                              '&:hover': { bgcolor: 'primary.light', color: 'white' },
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('ui.delete', { ns: 'common' })}>
                          <IconButton
                            onClick={() => handleDeleteCourse(course)}
                            size="small"
                            sx={{
                              color: 'error.main',
                              '&:hover': { bgcolor: 'error.light', color: 'white' },
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {isFormOpen && (
        <CourseForm
          open={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSave={handleSaveCourse}
          course={selectedCourse}
        />
      )}

      <ConfirmationDialog
        open={deleteConfirmOpen}
        title={t('deleteConfirmation.title', { ns: 'common' })}
        message={t('deleteConfirmation.message', {
          ns: 'common',
          item: courseToDelete?.title || '',
        })}
        onConfirm={confirmDelete}
        onCancel={() => {
          setDeleteConfirmOpen(false);
          setCourseToDelete(null);
        }}
      />
    </Container>
  );
};

export default WeeklyCourses;
