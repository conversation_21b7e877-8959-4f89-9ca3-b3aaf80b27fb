import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { doc, getDoc, Timestamp } from 'firebase/firestore';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CalendarMonth as CalendarIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Group as GroupIcon,
} from '@mui/icons-material';
import { db } from '../../../services/firebase';
import { useSchool } from '../../../hooks/useSchool';
import { format } from 'date-fns';
import { Course } from '../../../types/course';

interface CourseProfileProps {
  courseId?: string;
}

const CourseProfile: React.FC<CourseProfileProps> = ({ courseId }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['courses', 'common']);
  const { currentSchool } = useSchool();
  const [course, setCourse] = React.useState<Course | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchCourse = async () => {
      const targetId = courseId || id;
      if (!currentSchool?.id || !targetId) return;

      try {
        setLoading(true);
        const courseRef = doc(db, 'schools', currentSchool.id, 'courses', targetId);
        const courseDoc = await getDoc(courseRef);

        if (courseDoc.exists()) {
          setCourse({ id: courseDoc.id, ...courseDoc.data() } as Course);
        } else {
          setError(t('notFound', 'Course not found'));
        }
      } catch (err) {
        console.error('Error fetching course:', err);
        setError(t('common:error.fetch', 'Failed to fetch course data'));
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [currentSchool?.id, id, courseId, t]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !course) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          {t('common:ui.back')}
        </Button>
      </Container>
    );
  }

  const formatFirebaseDate = (date: Date | Timestamp) => {
    if (date instanceof Timestamp) {
      return format(date.toDate(), 'PP');
    }
    return format(date, 'PP');
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mb: 4 }}>
        {t('common:ui.back')}
      </Button>

      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '3rem',
                }}
              >
                <CalendarIcon sx={{ fontSize: 60 }} />
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {course.title}
              </Typography>
              <Chip
                label={t(`form.ageGroup.${course.ageGroup}`)}
                color={course.ageGroup === 'children' ? 'primary' : 'secondary'}
                size="small"
                sx={{
                  textTransform: 'capitalize',
                  fontWeight: 500,
                  mb: 2,
                }}
              />
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                {t('form.discipline')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {course.discipline}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.level')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {course.level}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.startDate')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {formatFirebaseDate(course.startDate)}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.endDate')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {formatFirebaseDate(course.endDate)}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.type')}
              </Typography>
              <Typography variant="body1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                {course.type}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.title')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {course.description || '-'}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <ScheduleIcon sx={{ mr: 1 }} />
              {t('schedule', 'Schedule')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              {course.schedule ? (
                <List>
                  {Object.entries(course.schedule).map(([day, times]) => (
                    <ListItem key={day}>
                      <ListItemIcon>
                        <CalendarIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={t(['days', day].join('.'), day)}
                        secondary={times.join(', ')}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  {t('noSchedule', 'No schedule information available')}
                </Typography>
              )}
            </Box>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <PersonIcon sx={{ mr: 1 }} />
              {t('instructor', 'Instructor')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {t('common:ui.comingSoon', 'Coming soon')}
              </Typography>
            </Box>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <GroupIcon sx={{ mr: 1 }} />
              {t('students', 'Students')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {t('common:ui.comingSoon', 'Coming soon')}
              </Typography>
            </Box>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('progress', 'Course Progress')}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {t('common:ui.comingSoon', 'Coming soon')}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CourseProfile;
