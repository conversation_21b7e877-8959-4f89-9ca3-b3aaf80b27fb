import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Button,
} from '@mui/material';
import {
  Group,
  Person,
  ChildCare,
  Add as AddIcon,
  CalendarMonth,
  CalendarToday,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import LessonForm from './LessonForm';
import CourseForm from './courses/CourseForm';

/**
 * Lessons component serves as a landing page for managing different lesson categories.
 * Features:
 * - Navigation to Individual Lessons section
 * - Navigation to Group Lessons section
 * - Navigation to Children's Lessons section
 */
const Lessons: React.FC = () => {
  const { t } = useTranslation(['lessons', 'programs', 'courses']);
  const navigate = useNavigate();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isCourseFormOpen, setIsCourseFormOpen] = useState(false);

  const handleCreateLesson = () => {
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
  };

  const handleCreateCourse = () => {
    setIsCourseFormOpen(true);
  };

  const handleCloseCourseForm = () => {
    setIsCourseFormOpen(false);
  };

  const categories = [
    {
      title: t('categories.individual'),
      description: t('descriptions.individual'),
      icon: <Person sx={{ fontSize: 40 }} />,
      path: '/lessons/individual',
      color: '#1976d2', // Blue
    },
    {
      title: t('categories.group'),
      description: t('descriptions.group'),
      icon: <Group sx={{ fontSize: 40 }} />,
      path: '/lessons/group',
      color: '#2e7d32', // Green
    },
    {
      title: t('categories.children'),
      description: t('descriptions.children'),
      icon: <ChildCare sx={{ fontSize: 40 }} />,
      path: '/lessons/children',
      color: '#ed6c02', // Orange
    },
  ];

  const courses = [
    {
      title: t('weekly', { ns: 'courses' }),
      description: t('weeklyCoursesDesc', { ns: 'courses' }),
      icon: <CalendarMonth sx={{ fontSize: 40 }} />,
      path: '/lessons/courses/weekly',
      color: '#ed6c02', // Orange
    },
    {
      title: t('daily', { ns: 'courses' }),
      description: t('dailyCoursesDesc', { ns: 'courses' }),
      icon: <CalendarToday sx={{ fontSize: 40 }} />,
      path: '/lessons/courses/daily',
      color: '#1976d2', // Blue
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box display="flex" alignItems="center">
            <Group sx={{ fontSize: '2rem', mr: 1 }} />
            <Typography variant="h4" component="h1">
              {t('title')}
            </Typography>
          </Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateLesson}
          >
            {t('create.title')}
          </Button>
        </Box>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {t('subtitle')}
        </Typography>
        <Grid container spacing={3}>
          {categories.map((category) => (
            <Grid item xs={12} sm={6} md={4} key={category.path}>
              <Card>
                <CardActionArea onClick={() => navigate(category.path)}>
                  <CardContent>
                    <Box sx={{ color: category.color, mb: 2 }}>{category.icon}</Box>
                    <Typography variant="h6" component="div" gutterBottom>
                      {category.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {category.description}
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Courses Section */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Box display="flex" alignItems="center">
            <CalendarMonth sx={{ fontSize: '2rem', mr: 1 }} />
            <Typography variant="h4" component="h2">
              {t('courses', { ns: 'courses' })}
            </Typography>
          </Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateCourse}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3,
            }}
          >
            {t('createCourse', { ns: 'courses' })}
          </Button>
        </Box>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {t('coursesDesc', { ns: 'courses' })}
        </Typography>
        <Grid container spacing={3}>
          {courses.map((course) => (
            <Grid item xs={12} sm={6} md={4} key={course.path}>
              <Card>
                <CardActionArea onClick={() => navigate(course.path)}>
                  <CardContent>
                    <Box sx={{ color: course.color, mb: 2 }}>{course.icon}</Box>
                    <Typography variant="h6" component="div" gutterBottom>
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {course.description}
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <LessonForm open={isFormOpen} onClose={handleCloseForm} />
      <CourseForm
        open={isCourseFormOpen}
        onClose={handleCloseCourseForm}
        onSave={() => {
          handleCloseCourseForm();
          // You might want to refresh the courses list here
        }}
      />
    </Container>
  );
};

export default Lessons;
