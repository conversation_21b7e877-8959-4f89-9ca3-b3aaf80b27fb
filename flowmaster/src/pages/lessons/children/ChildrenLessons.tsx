import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { collection, getDocs, deleteDoc, doc, query, where } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import LessonForm from '../LessonForm';
import ConfirmationDialog from '../../../components/common/ConfirmationDialog';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Tooltip,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Event as EventIcon,
} from '@mui/icons-material';
import { useAuth } from '../../../hooks/useAuth';
import { useSchool } from '../../../hooks/useSchool';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';

interface Lesson {
  id: string;
  title: string;
  type: 'individual' | 'group' | 'children';
  discipline: string;
  instructorId: string;
  studentIds: string[];
  startTime: { toDate: () => Date }; // Firebase Timestamp
  duration: number;
  level: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
  createdBy?: string;
  updatedAt?: Date;
}

const ChildrenLessons: React.FC = () => {
  console.log('ChildrenLessons component mounted');

  const { t } = useTranslation(['lessons', 'common']);
  const { user } = useAuth();
  const { currentSchool, loading: schoolLoading } = useSchool();
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLesson, setSelectedLesson] = useState<Lesson | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [lessonToDelete, setLessonToDelete] = useState<Lesson | null>(null);
  const navigate = useNavigate();

  // Check if user has admin or instructor role
  const canManageLessons = user?.role === 'admin' || user?.role === 'instructor';

  const fetchLessons = useCallback(async () => {
    if (!currentSchool?.id) return;

    setLoading(true);
    try {
      const lessonsCollection = collection(db, 'schools', currentSchool.id, 'lessons');
      const lessonsQuery = query(lessonsCollection, where('type', '==', 'children'));
      const lessonsSnapshot = await getDocs(lessonsQuery);
      const lessonsData = lessonsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Lesson[];

      setLessons(lessonsData);
    } catch (error) {
      console.error('Error fetching lessons:', error);
    } finally {
      setLoading(false);
    }
  }, [currentSchool?.id]);

  useEffect(() => {
    if (!schoolLoading) {
      fetchLessons();
    }
  }, [fetchLessons, schoolLoading]);

  const handleAddLesson = () => {
    if (!canManageLessons) {
      console.warn('Unauthorized access attempt to add lesson');
      return;
    }
    setSelectedLesson(undefined);
    setIsFormOpen(true);
  };

  const handleEditLesson = (lesson: Lesson) => {
    if (!canManageLessons) {
      console.warn('Unauthorized access attempt to edit lesson');
      return;
    }
    setSelectedLesson(lesson);
    setIsFormOpen(true);
  };

  const handleDeleteLesson = (lesson: Lesson) => {
    if (!canManageLessons) {
      console.warn('Unauthorized access attempt to delete lesson');
      return;
    }
    setLessonToDelete(lesson);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!lessonToDelete || !currentSchool?.id) return;

    try {
      await deleteDoc(doc(db, 'schools', currentSchool.id, 'lessons', lessonToDelete.id));
      setLessons((prevLessons) => prevLessons.filter((lesson) => lesson.id !== lessonToDelete.id));
      setDeleteConfirmOpen(false);
      setLessonToDelete(null);
    } catch (error) {
      console.error('Error deleting lesson:', error);
    }
  };

  const handleViewDetails = (lessonId: string) => {
    navigate(`/lessons/${lessonId}`);
  };

  if (loading || schoolLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box display="flex" alignItems="center">
            <EventIcon sx={{ fontSize: '2rem', mr: 1 }} />
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            >
              {t('children.title')}
            </Typography>
          </Box>
          {canManageLessons && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddLesson}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
              }}
            >
              {t('create.title')}
            </Button>
          )}
        </Box>

        {loading || schoolLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer
            component={Paper}
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              overflow: 'hidden',
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('form.title')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('form.discipline')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('form.startTime')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('form.duration')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('form.level')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('form.status')}
                  </TableCell>
                  {canManageLessons && (
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>
                      {t('ui.actions', { ns: 'common' })}
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {lessons.map((lesson) => (
                  <TableRow
                    key={lesson.id}
                    sx={{
                      '&:nth-of-type(odd)': { bgcolor: 'action.hover' },
                      '&:hover': { bgcolor: 'action.selected' },
                      transition: 'background-color 0.2s ease',
                      cursor: 'pointer',
                    }}
                    onClick={() => handleViewDetails(lesson.id)}
                  >
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {lesson.title}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{lesson.discipline}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {format(lesson.startTime.toDate(), 'PPp')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{lesson.duration} min</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {lesson.level}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={t(`status.${lesson.status}`)}
                        color={
                          lesson.status === 'completed'
                            ? 'success'
                            : lesson.status === 'cancelled'
                              ? 'error'
                              : 'primary'
                        }
                        size="small"
                        sx={{
                          textTransform: 'capitalize',
                          fontWeight: 500,
                        }}
                      />
                    </TableCell>
                    {canManageLessons && (
                      <TableCell align="right">
                        <Box
                          sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Tooltip title={t('ui.edit', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleEditLesson(lesson)}
                              size="small"
                              sx={{
                                color: 'primary.main',
                                '&:hover': { bgcolor: 'primary.light', color: 'white' },
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={t('ui.delete', { ns: 'common' })}>
                            <IconButton
                              onClick={() => handleDeleteLesson(lesson)}
                              size="small"
                              sx={{
                                color: 'error.main',
                                '&:hover': { bgcolor: 'error.light', color: 'white' },
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>

      {isFormOpen && (
        <LessonForm
          open={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setSelectedLesson(undefined);
          }}
          onSave={() => {
            setIsFormOpen(false);
            setSelectedLesson(undefined);
            fetchLessons();
          }}
          lesson={
            selectedLesson
              ? {
                  ...selectedLesson,
                  startTime: selectedLesson.startTime.toDate(),
                  createdBy: selectedLesson.createdBy || user?.uid || '',
                  updatedAt: selectedLesson.updatedAt || new Date(),
                }
              : undefined
          }
        />
      )}

      <ConfirmationDialog
        open={deleteConfirmOpen}
        title={t('deleteConfirmation.title', { ns: 'common' })}
        message={t('deleteConfirmation.message', {
          ns: 'common',
          item: lessonToDelete?.title || '',
        })}
        onConfirm={confirmDelete}
        onCancel={() => {
          setDeleteConfirmOpen(false);
          setLessonToDelete(null);
        }}
      />
    </Container>
  );
};

export default ChildrenLessons;
