import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { doc, getDoc, Timestamp } from 'firebase/firestore';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Divider,
  Avatar,
  Tabs,
  Tab,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Event as EventIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Notes as NotesIcon,
  EventNote as EventNoteIcon,
} from '@mui/icons-material';
import LessonRescheduleDialog from '../../../components/lessons/LessonRescheduleDialog';
import { db } from '../../../services/firebase';
import { useSchool } from '../../../hooks/useSchool';
import LessonEquipment from '../../../components/lessons/LessonEquipment';
import { format } from 'date-fns';

interface Lesson {
  id: string;
  title: string;
  type: 'individual' | 'group' | 'children';
  discipline: string;
  instructorId: string;
  studentIds: string[];
  startTime: Timestamp;
  duration: number;
  level: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
  createdBy?: string;
  updatedAt?: Date;
}

interface LessonProfileProps {
  lessonId?: string;
}

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`lesson-tabpanel-${index}`}
      aria-labelledby={`lesson-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
};

// Tab a11y props
const a11yProps = (index: number) => {
  return {
    id: `lesson-tab-${index}`,
    'aria-controls': `lesson-tabpanel-${index}`,
  };
};

const LessonProfile: React.FC<LessonProfileProps> = ({ lessonId }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['lessons', 'common', 'equipment']);
  const { currentSchool } = useSchool();
  const [lesson, setLesson] = React.useState<Lesson | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);

  React.useEffect(() => {
    const fetchLesson = async () => {
      const targetId = lessonId || id;
      if (!currentSchool?.id || !targetId) return;

      try {
        setLoading(true);
        const lessonRef = doc(db, 'schools', currentSchool.id, 'lessons', targetId);
        const lessonDoc = await getDoc(lessonRef);

        if (lessonDoc.exists()) {
          setLesson({ id: lessonDoc.id, ...lessonDoc.data() } as Lesson);
        } else {
          setError(t('lessons:notFound', 'Lesson not found'));
        }
      } catch (err) {
        console.error('Error fetching lesson:', err);
        setError(t('common:error.fetch', 'Failed to fetch lesson data'));
      } finally {
        setLoading(false);
      }
    };

    fetchLesson();
  }, [currentSchool?.id, id, lessonId, t]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !lesson) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          {t('common:ui.back')}
        </Button>
      </Container>
    );
  }

  const getStatusColor = (status: Lesson['status']) => {
    switch (status) {
      case 'scheduled':
        return 'primary';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Button startIcon={<ArrowBackIcon />} onClick={() => navigate(-1)} sx={{ mb: 4 }}>
        {t('common:ui.back')}
      </Button>

      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '3rem',
                }}
              >
                <EventIcon sx={{ fontSize: 60 }} />
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {lesson.title}
              </Typography>
              <Chip
                label={t(`status.${lesson.status}`)}
                color={getStatusColor(lesson.status)}
                size="small"
                sx={{
                  textTransform: 'capitalize',
                  fontWeight: 500,
                  mb: 2,
                }}
              />
              <Button
                startIcon={<EventNoteIcon />}
                variant="outlined"
                color="primary"
                onClick={() => setIsRescheduleDialogOpen(true)}
                sx={{ mt: 2 }}
              >
                {t('reschedule.button', 'Reschedule')}
              </Button>
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                {t('form.discipline')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {lesson.discipline}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.level')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {lesson.level}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.startTime')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {format(lesson.startTime.toDate(), 'PPp')}
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.duration')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {lesson.duration} min
              </Typography>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                {t('form.notes')}
              </Typography>
              <Typography variant="body1" gutterBottom>
                {lesson.notes || '-'}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="lesson tabs"
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab
                  icon={<PeopleIcon />}
                  iconPosition="start"
                  label={t('lessons:attendance', 'Attendance')}
                  {...a11yProps(0)}
                />
                <Tab
                  icon={<InventoryIcon />}
                  iconPosition="start"
                  label={t('equipment:tabs.equipment', 'Equipment')}
                  {...a11yProps(1)}
                />
                <Tab
                  icon={<AssessmentIcon />}
                  iconPosition="start"
                  label={t('lessons:feedback', 'Feedback')}
                  {...a11yProps(2)}
                />
                <Tab
                  icon={<NotesIcon />}
                  iconPosition="start"
                  label={t('lessons:history', 'History')}
                  {...a11yProps(3)}
                />
              </Tabs>
            </Box>

            {/* Attendance Tab */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                {t('lessons:attendance', 'Attendance')}
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="primary.main">
                      {lesson.studentIds?.length || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('lessons:totalStudents', 'Total Students')}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="success.main">
                      0
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('lessons:present', 'Present')}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="error.main">
                      0
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('lessons:absent', 'Absent')}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {t('lessons:materials', 'Lesson Materials')}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {t('common:ui.comingSoon', 'Coming soon')}
                  </Typography>
                </Box>
              </Box>
            </TabPanel>

            {/* Equipment Tab */}
            <TabPanel value={tabValue} index={1}>
              <LessonEquipment lessonId={lessonId || id || ''} />
            </TabPanel>

            {/* Feedback Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                {t('lessons:feedback', 'Feedback')}
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('common:ui.comingSoon', 'Coming soon')}
                </Typography>
              </Box>
            </TabPanel>

            {/* History Tab */}
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>
                {t('lessons:history', 'Lesson History')}
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('common:ui.comingSoon', 'Coming soon')}
                </Typography>
              </Box>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      {/* Reschedule Dialog */}
      {lesson && (
        <LessonRescheduleDialog
          open={isRescheduleDialogOpen}
          onClose={() => setIsRescheduleDialogOpen(false)}
          lesson={lesson}
          onReschedule={() => {
            // Refresh lesson data
            const fetchLesson = async () => {
              const targetId = lessonId || id;
              if (!currentSchool?.id || !targetId) return;

              try {
                setLoading(true);
                const lessonRef = doc(db, 'schools', currentSchool.id, 'lessons', targetId);
                const lessonDoc = await getDoc(lessonRef);

                if (lessonDoc.exists()) {
                  setLesson({ id: lessonDoc.id, ...lessonDoc.data() } as Lesson);
                }
              } catch (err) {
                console.error('Error fetching lesson:', err);
              } finally {
                setLoading(false);
              }
            };

            fetchLesson();
            setIsRescheduleDialogOpen(false);
          }}
        />
      )}
    </Container>
  );
};

export default LessonProfile;
