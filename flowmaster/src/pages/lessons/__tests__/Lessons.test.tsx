import { render, screen, fireEvent } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import Lessons from '../Lessons';

// Mock the required hooks
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => {
      const translations: { [key: string]: string } = {
        'categories.individual': 'Individual Lessons',
        'categories.group': 'Group Lessons',
        'categories.children': "Children's Lessons",
        'descriptions.individual': 'Individual lesson description',
        'descriptions.group': 'Group lesson description',
        'descriptions.children': 'Children lesson description',
        title: 'Lessons',
        subtitle: defaultValue || 'Manage lessons',
      };
      return translations[key] || key;
    },
  }),
}));

describe('Lessons Component', () => {
  let mockNavigate: jest.Mock;

  beforeEach(() => {
    mockNavigate = jest.fn();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
  });

  it('renders the component with correct title and subtitle', () => {
    render(<Lessons />);

    expect(screen.getByText('Lessons')).toBeInTheDocument();
    expect(
      screen.getByText("Manage individual, group, and children's lessons")
    ).toBeInTheDocument();
  });

  it('renders all lesson categories with correct titles', () => {
    render(<Lessons />);

    expect(screen.getByText('Individual Lessons')).toBeInTheDocument();
    expect(screen.getByText('Group Lessons')).toBeInTheDocument();
    expect(screen.getByText("Children's Lessons")).toBeInTheDocument();
  });

  it('renders all lesson descriptions', () => {
    render(<Lessons />);

    expect(screen.getByText('Individual lesson description')).toBeInTheDocument();
    expect(screen.getByText('Group lesson description')).toBeInTheDocument();
    expect(screen.getByText('Children lesson description')).toBeInTheDocument();
  });

  it('navigates to correct paths when clicking on category cards', () => {
    render(<Lessons />);

    // Click on Individual Lessons card
    fireEvent.click(screen.getByText('Individual Lessons'));
    expect(mockNavigate).toHaveBeenCalledWith('/lessons/individual');

    // Click on Group Lessons card
    fireEvent.click(screen.getByText('Group Lessons'));
    expect(mockNavigate).toHaveBeenCalledWith('/lessons/group');

    // Click on Children's Lessons card
    fireEvent.click(screen.getByText("Children's Lessons"));
    expect(mockNavigate).toHaveBeenCalledWith('/lessons/children');
  });

  it('renders all category icons', () => {
    render(<Lessons />);

    // Since icons are MUI components, we can verify their presence by their data-testid
    const icons = screen.getAllByTestId(/Icon$/);
    expect(icons).toHaveLength(4); // 3 category icons + 1 header icon
  });
});
