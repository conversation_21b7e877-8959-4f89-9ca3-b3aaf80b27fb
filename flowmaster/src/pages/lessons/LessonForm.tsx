import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Grid,
  Autocomplete,
  Chip,
  Typography,
  FormHelperText,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon, SportsMartialArts, AccessTime } from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { StaticDateTimePicker } from '@mui/x-date-pickers/StaticDateTimePicker';
import { fetchLevels } from '../../services/levels';
import { fetchSports } from '../../services/sports';
import { useSchool } from '../../hooks/useSchool';
import { useAuth } from '../../hooks/useAuth';
import { collection, getDocs, doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import type { Student } from '../../types/student';
import StudentForm from '../people/Students/StudentForm';
import type { Instructor } from '../../types/instructor';

interface LessonFormData {
  title: string;
  type: 'individual' | 'group' | 'children';
  discipline: string;
  instructorId: string;
  studentIds: string[];
  startTime: Date;
  duration: number; // in minutes
  level: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

interface LessonFormProps {
  open: boolean;
  onClose: () => void;
  onSave?: (data: LessonFormData) => void;
  lesson?: LessonFormData & { id: string };
}

const DURATIONS = [30, 45, 60, 90, 120];

const LessonForm: React.FC<LessonFormProps> = ({ open, onClose, onSave, lesson }) => {
  const { t } = useTranslation(['lessons', 'common']);
  const { currentSchool } = useSchool();
  const { user } = useAuth();
  const [levelOptions, setLevelOptions] = React.useState<Array<{ value: string; label: string }>>(
    []
  );
  const [disciplineOptions, setDisciplineOptions] = React.useState<
    Array<{ value: string; label: string }>
  >([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [isStudentFormOpen, setIsStudentFormOpen] = useState(false);

  // Use React Hook Form
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    watch,
  } = useForm<LessonFormData>({
    defaultValues: {
      title: lesson?.title || '',
      type: lesson?.type || 'individual',
      discipline: lesson?.discipline || '',
      instructorId: lesson?.instructorId || '',
      studentIds: lesson?.studentIds || [],
      startTime: lesson?.startTime || new Date(),
      duration: lesson?.duration || 60,
      level: lesson?.level || '', // Change from 'beginner' to empty string initially
      status: lesson?.status || 'scheduled',
      notes: lesson?.notes || '',
      createdBy: lesson?.createdBy || user?.uid || '',
      updatedAt: lesson?.updatedAt || new Date(),
    },
  });

  // Watch form values
  const formValues = watch();

  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        const [instructorsSnapshot, studentsSnapshot] = await Promise.all([
          getDocs(collection(db, 'schools', currentSchool.id, 'instructors')),
          getDocs(collection(db, 'schools', currentSchool.id, 'students')),
        ]);

        const instructorsData = instructorsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Instructor[];

        const studentsData = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Student[];

        setInstructors(instructorsData);
        setStudents(studentsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id]);

  React.useEffect(() => {
    const loadData = async () => {
      if (!currentSchool?.id) {
        console.log('No school ID available');
        return;
      }

      try {
        setLoading(true);
        const [levels, sports] = await Promise.all([
          fetchLevels(currentSchool.id),
          fetchSports(currentSchool.id),
        ]);

        // Map levels
        if (levels.length === 0) {
          setError('No levels configured for this school');
        } else {
          const levelOpts = levels.map((level: { name: string; displayName?: string }) => ({
            value: level.name,
            label: level.displayName || level.name,
          }));
          setLevelOptions(levelOpts);

          // Set default level if not already set and options are available
          const currentLevel = watch('level');
          if ((!currentLevel || currentLevel === '') && levelOpts.length > 0) {
            setValue('level', levelOpts[0].value);
          }
        }

        // Map sports/disciplines
        if (sports.length === 0) {
          setError((prev) =>
            prev
              ? `${prev}. No disciplines configured`
              : 'No disciplines configured for this school'
          );
        } else {
          const sportOpts = sports.map((sport: { name: string; displayName?: string }) => ({
            value: sport.name,
            label: sport.displayName || sport.name,
          }));
          setDisciplineOptions(sportOpts);

          // Set default discipline if not already set and options are available
          const currentDiscipline = watch('discipline');
          if ((!currentDiscipline || currentDiscipline === '') && sportOpts.length > 0) {
            setValue('discipline', sportOpts[0].value);
          }
        }

        setError(null);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load configuration data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentSchool?.id, setValue, watch]);

  // Handle student selection
  const handleStudentChange = (selectedStudents: Student[]) => {
    const studentIds = selectedStudents.map((student) => student.id);
    setValue('studentIds', studentIds);
  };

  // Handle form submission
  const onSubmit = async (data: LessonFormData) => {
    if (!currentSchool?.id) {
      console.error('No school ID available');
      return;
    }

    if (!user) {
      console.error('No authenticated user');
      setError('You must be logged in to save a lesson');
      return;
    }

    // Debug information
    console.log('Current user:', user);
    console.log('User role:', user.role);
    console.log('School ID:', currentSchool.id);

    setLoading(true);
    try {
      // Create a reference to the lessons collection for the current school
      const lessonsRef = collection(db, 'schools', currentSchool.id, 'lessons');

      // If we're editing an existing lesson, use its ID, otherwise create a new doc
      const lessonDoc = lesson?.id ? doc(lessonsRef, lesson.id) : doc(lessonsRef);

      // Prepare the data with updated timestamps
      const lessonData = {
        ...data,
        startTime: new Date(data.startTime), // Ensure it's a Date object
        createdBy: user.uid,
        updatedAt: new Date(),
      };

      console.log('Saving lesson data:', lessonData);

      // Save the lesson document
      await setDoc(lessonDoc, lessonData, { merge: true });

      onClose();
      onSave?.(data);
      reset();
    } catch (error) {
      console.error('Error saving lesson:', error);
      setError('Failed to save lesson');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  // Get selected students
  const selectedStudentIds = watch('studentIds') || [];
  const selectedStudents = students.filter((student) => selectedStudentIds.includes(student.id));

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <SportsMartialArts />
          <Typography>{lesson ? t('lessons:edit.title') : t('lessons:create.title')}</Typography>
        </Box>
      </DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          {error && (
            <Typography color="error" sx={{ mb: 2 }}>
              {error}
            </Typography>
          )}
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Left Column */}
            <Grid item xs={12} md={6}>
              <Box display="flex" flexDirection="column" gap={3}>
                <TextField
                  fullWidth
                  label={t('lessons:form.title')}
                  {...register('title', { required: true })}
                  error={!!errors.title}
                  helperText={errors.title ? t('common:validation.required') : ''}
                />

                <FormControl fullWidth error={!!errors.type}>
                  <InputLabel>{t('lessons:form.type')}</InputLabel>
                  <Select
                    label={t('lessons:form.type')}
                    {...register('type', { required: true })}
                    defaultValue={formValues.type}
                  >
                    <MenuItem value="individual">{t('lessons:types.individual')}</MenuItem>
                    <MenuItem value="group">{t('lessons:types.group')}</MenuItem>
                    <MenuItem value="children">{t('lessons:types.children')}</MenuItem>
                  </Select>
                  {errors.type && (
                    <FormHelperText>{t('common:validation.required')}</FormHelperText>
                  )}
                </FormControl>

                <FormControl fullWidth error={!!errors.discipline}>
                  <InputLabel>{t('lessons:form.discipline')}</InputLabel>
                  <Select
                    label={t('lessons:form.discipline')}
                    {...register('discipline', { required: true })}
                    defaultValue={formValues.discipline}
                  >
                    {loading ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} />
                      </MenuItem>
                    ) : disciplineOptions.length === 0 ? (
                      <MenuItem disabled>No disciplines available</MenuItem>
                    ) : (
                      disciplineOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  {errors.discipline && (
                    <FormHelperText>{t('common:validation.required')}</FormHelperText>
                  )}
                </FormControl>

                <FormControl fullWidth error={!!errors.instructorId}>
                  <InputLabel>{t('lessons:form.instructor')}</InputLabel>
                  <Select
                    label={t('lessons:form.instructor')}
                    {...register('instructorId', { required: true })}
                    defaultValue={formValues.instructorId}
                  >
                    {loading ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} />
                      </MenuItem>
                    ) : instructors.length === 0 ? (
                      <MenuItem disabled>No instructors available</MenuItem>
                    ) : (
                      instructors.map((instructor) => (
                        <MenuItem key={instructor.id} value={instructor.id}>
                          {`${instructor.firstName} ${instructor.lastName}`}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  {errors.instructorId && (
                    <FormHelperText>{t('common:validation.required')}</FormHelperText>
                  )}
                </FormControl>

                <FormControl fullWidth>
                  <Autocomplete
                    multiple
                    options={students}
                    getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
                    value={selectedStudents}
                    onChange={(_, newValue) => handleStudentChange(newValue)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          label={`${option.firstName} ${option.lastName}`}
                          {...getTagProps({ index })}
                          key={option.id}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={t('lessons:form.students')}
                        placeholder={t('lessons:form.selectStudents')}
                      />
                    )}
                  />
                  <Box display="flex" justifyContent="flex-end" mt={1}>
                    <Button
                      startIcon={<AddIcon />}
                      onClick={() => setIsStudentFormOpen(true)}
                      size="small"
                    >
                      {t('lessons:form.addNewStudent')}
                    </Button>
                  </Box>
                </FormControl>

                <FormControl fullWidth error={!!errors.level}>
                  <InputLabel>{t('lessons:form.level')}</InputLabel>
                  <Select
                    label={t('lessons:form.level')}
                    {...register('level', { required: true })}
                    defaultValue={formValues.level}
                  >
                    {loading ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} />
                      </MenuItem>
                    ) : levelOptions.length === 0 ? (
                      <MenuItem disabled>No levels available</MenuItem>
                    ) : (
                      levelOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  {errors.level && (
                    <FormHelperText>{t('common:validation.required')}</FormHelperText>
                  )}
                </FormControl>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label={t('lessons:form.notes')}
                  {...register('notes')}
                />
              </Box>
            </Grid>

            {/* Right Column */}
            <Grid item xs={12} md={6}>
              <Box display="flex" flexDirection="column" gap={3}>
                <Box>
                  <Typography variant="subtitle1" gutterBottom display="flex" alignItems="center">
                    <AccessTime sx={{ mr: 1 }} />
                    {t('lessons:form.dateTime')}
                  </Typography>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <StaticDateTimePicker
                      value={formValues.startTime}
                      onChange={(newValue) => {
                        if (newValue) {
                          setValue('startTime', newValue);
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Box>

                <FormControl fullWidth error={!!errors.duration}>
                  <InputLabel>{t('lessons:form.duration')}</InputLabel>
                  <Select
                    label={t('lessons:form.duration')}
                    {...register('duration', { required: true })}
                    defaultValue={formValues.duration}
                  >
                    {DURATIONS.map((duration) => (
                      <MenuItem key={duration} value={duration}>
                        {`${duration} ${t('lessons:form.minutes')}`}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.duration && (
                    <FormHelperText>{t('common:validation.required')}</FormHelperText>
                  )}
                </FormControl>

                <FormControl fullWidth error={!!errors.status}>
                  <InputLabel>{t('lessons:form.status')}</InputLabel>
                  <Select
                    label={t('lessons:form.status')}
                    {...register('status', { required: true })}
                    defaultValue={formValues.status}
                  >
                    <MenuItem value="scheduled">{t('lessons:status.scheduled')}</MenuItem>
                    <MenuItem value="completed">{t('lessons:status.completed')}</MenuItem>
                    <MenuItem value="cancelled">{t('lessons:status.cancelled')}</MenuItem>
                  </Select>
                  {errors.status && (
                    <FormHelperText>{t('common:validation.required')}</FormHelperText>
                  )}
                </FormControl>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <StudentForm
          open={isStudentFormOpen}
          onClose={() => {
            setIsStudentFormOpen(false);
            // Refresh students list after adding a new student
            if (currentSchool?.id) {
              getDocs(collection(db, 'schools', currentSchool.id, 'students'))
                .then((snapshot) => {
                  const studentsData = snapshot.docs.map((doc) => ({
                    id: doc.id,
                    ...doc.data(),
                  })) as Student[];
                  setStudents(studentsData);
                })
                .catch((error) => console.error('Error refreshing students:', error));
            }
          }}
        />
        <DialogActions>
          <Button onClick={handleClose}>{t('common:ui.cancel')}</Button>
          <Button variant="contained" color="primary" type="submit" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : t('common:ui.save')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default LessonForm;
