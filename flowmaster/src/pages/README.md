# FlowMaster Pages Documentation

This directory contains all the page components for the FlowMaster application. Each page is organized into its respective subdirectory based on its functionality.

## Directory Structure

```
pages/
├── auth/           # Authentication related pages
│   ├── Login.tsx
│   ├── Register.tsx
│   └── ResetPassword.tsx
├── dashboard/      # Main dashboard
│   └── Dashboard.tsx
├── lessons/        # Lesson management
│   └── Lessons.tsx
├── programs/       # Program management
│   └── Programs.tsx
├── profile/        # User profile management
│   └── Profile.tsx
├── settings/       # Application settings
│   └── Settings.tsx
└── index.ts        # Barrel exports for all pages
```

## Pages Overview

### Authentication Pages

#### `Login.tsx`
- User authentication
- Email/password login
- OAuth provider integration
- Remember me functionality
- Error handling and validation

#### `Register.tsx`
- New user registration
- Form validation
- Terms of service acceptance
- Email verification initiation

#### `ResetPassword.tsx`
- Password reset functionality
- Email verification
- Success/error handling
- Navigation back to login

### Main Pages

#### `Dashboard.tsx`
- User's main landing page
- Overview of programs and lessons
- Quick access to important features
- Activity summary

#### `Programs.tsx`
- Program listing and management
- Program creation and editing
- Program enrollment status
- Progress tracking

#### `Lessons.tsx`
- Lesson listing and management
- Lesson creation and editing
- Lesson completion tracking
- Resource management

### User Pages

#### `Profile.tsx`
Features:
- Display and edit user information
- Profile picture management
- Contact information updates
- Role and permissions display
- Bio and personal information

Props:
- None (uses context for user data)

State:
- `profile`: User profile data
- `loading`: Loading state
- `error`: Error state
- `success`: Success message state

#### `Settings.tsx`
Features:
- Theme customization (light/dark mode)
- Language preferences
- Notification settings
- Security settings
- Account management

Props:
- None (uses context for settings)

State:
- `settings`: User settings
- `success`: Success message state
- `error`: Error state

## Common Patterns

### Error Handling
All pages implement consistent error handling:
```typescript
try {
  // Operation
} catch (err) {
  setError(t('error.message'));
} finally {
  setLoading(false);
}
```

### Loading States
Pages use Material-UI's CircularProgress for loading states:
```typescript
if (loading) {
  return (
    <Box display="flex" justifyContent="center" mt={4}>
      <CircularProgress />
    </Box>
  );
}
```

### Form Validation
Forms use consistent validation patterns:
- Required field validation
- Email format validation
- Password strength requirements
- Custom field validation

### Internationalization
All text content uses react-i18next:
```typescript
const { t } = useTranslation();
// Usage
t('page.section.key')
```

### Authentication
Pages use the useAuth hook for authentication:
```typescript
const { user, signIn, signOut } = useAuth();
```

### Permissions
Role-based access control using usePermission hook:
```typescript
const { role, hasPermission } = usePermission();
```

## Best Practices

1. **Component Organization**
   - Keep components focused and single-responsibility
   - Use TypeScript for type safety
   - Implement proper error boundaries

2. **State Management**
   - Use React hooks for state management
   - Implement proper loading states
   - Handle errors gracefully

3. **Performance**
   - Implement proper memoization
   - Use lazy loading for heavy components
   - Optimize re-renders

4. **Accessibility**
   - Use semantic HTML
   - Implement proper ARIA labels
   - Ensure keyboard navigation
   - Maintain proper contrast ratios

5. **Testing**
   - Write unit tests for components
   - Test error scenarios
   - Test loading states
   - Test user interactions

## Contributing

When adding new pages:
1. Create a new directory under pages/
2. Implement the page component
3. Add proper documentation
4. Add exports to index.ts
5. Add tests
6. Update this README if necessary
