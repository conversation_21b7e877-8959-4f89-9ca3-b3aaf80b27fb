import React from 'react';

const VerifyEmail: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Verify Your Email
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Please check your email for a verification link.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <p className="text-gray-700 mb-4">
            We&apos;ve sent a verification email to your registered email address. Click the link in
            the email to verify your account.
          </p>
          <p className="text-sm text-gray-500">
            If you haven&apos;t received the email, please check your spam folder or request a new
            verification email.
          </p>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmail;
