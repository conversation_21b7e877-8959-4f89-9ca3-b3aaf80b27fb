import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Tooltip,
  SelectChangeEvent,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { collection, query, where, getDocs, doc, getDoc, updateDoc, setDoc, limit, orderBy, startAfter, DocumentSnapshot } from 'firebase/firestore';
import { db, auth } from '../../../services/firebase';
import { setCustomClaims } from '../../../services/authService';
import { RoleType } from '../../../types/role';
import { useSchool } from '../../../hooks/useSchool';

// Define UserData interface
interface UserData {
  id: string;
  email: string;
  displayName?: string;
  roleId?: RoleType;
  roles?: RoleType[];
  schoolId?: string;
  createdAt?: any;
  updatedAt?: any;
}

const UserManagement: React.FC = () => {
  const { t } = useTranslation(['settings', 'common']);
  const { currentSchool } = useSchool();

  // State for users
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastVisible, setLastVisible] = useState<DocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for search
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<UserData[]>([]);

  // State for role editing
  const [roles, setRoles] = useState<{ id: RoleType; name: string }[]>([
    { id: RoleType.ADMIN, name: 'Administrator' },
    { id: RoleType.MANAGER, name: 'Manager' },
    { id: RoleType.INSTRUCTOR, name: 'Instructor' },
    { id: RoleType.STUDENT, name: 'Student' },
    { id: RoleType.CLIENT, name: 'Client' },
  ]);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [selectedRole, setSelectedRole] = useState<RoleType | ''>('');
  const [additionalRoles, setAdditionalRoles] = useState<RoleType[]>([]);
  const [processingRole, setProcessingRole] = useState(false);

  // State for add user dialog
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserName, setNewUserName] = useState('');
  const [newUserRole, setNewUserRole] = useState<RoleType>(RoleType.INSTRUCTOR);
  const [processingNewUser, setProcessingNewUser] = useState(false);

  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning',
  });

  // Define showSnackbar function
  const showSnackbar = useCallback(
    (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
      setSnackbar({
        open: true,
        message,
        severity,
      });
    },
    []
  );

  // Define fetchUsers function
  const fetchUsers = useCallback(async () => {
    if (!currentSchool?.id) return;

    try {
      setLoading(true);

      // Get current user first
      const currentUser = auth.currentUser;
      let currentUserData: UserData | null = null;

      if (currentUser) {
        const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
        if (userDoc.exists()) {
          currentUserData = {
            id: userDoc.id,
            ...userDoc.data(),
          } as UserData;
        }
      }

      // Then get other users
      const usersQuery = query(
        collection(db, 'users'),
        where('schoolId', '==', currentSchool.id),
        orderBy('createdAt', 'desc'),
        limit(20)
      );

      const usersSnapshot = await getDocs(usersQuery);
      const usersList: UserData[] = [];

      usersSnapshot.forEach((doc) => {
        usersList.push({
          id: doc.id,
          ...doc.data(),
        } as UserData);
      });

      let allUsers = usersList;

      // Add current user to the list if not already included
      if (currentUserData && !allUsers.some((user) => user.id === currentUserData?.id)) {
        allUsers = [currentUserData, ...allUsers];
      }

      setUsers(allUsers);
      setFilteredUsers(allUsers);
      setLastVisible(usersSnapshot.docs[usersSnapshot.docs.length - 1] || null);
      setHasMore(usersSnapshot.docs.length === 20);
      setPage(0);
    } catch (error) {
      console.error('Error fetching users:', error);
      showSnackbar(t('users.fetchError', 'Failed to load users'), 'error');
    } finally {
      setLoading(false);
    }
  }, [currentSchool?.id, t, showSnackbar]);

  // Fetch users on component mount
  useEffect(() => {
    if (currentSchool?.id) {
      fetchUsers();
    }
  }, [currentSchool?.id, fetchUsers]);

  // Filter users when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredUsers(users);
    } else {
      const term = searchTerm.toLowerCase();
      const filtered = users.filter(
        (user) =>
          user.displayName?.toLowerCase().includes(term) || user.email?.toLowerCase().includes(term)
      );
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  const loadMoreUsers = async () => {
    if (!currentSchool?.id || !lastVisible || !hasMore) return;

    try {
      setLoading(true);
      const usersQuery = query(
        collection(db, 'users'),
        where('schoolId', '==', currentSchool.id),
        orderBy('createdAt', 'desc'),
        startAfter(lastVisible),
        limit(20)
      );

      const usersSnapshot = await getDocs(usersQuery);
      const newUsers: UserData[] = [];

      usersSnapshot.forEach((doc) => {
        newUsers.push({
          id: doc.id,
          ...doc.data(),
        } as UserData);
      });

      setUsers((prevUsers) => [...prevUsers, ...newUsers]);
      setFilteredUsers((prevUsers) => [...prevUsers, ...newUsers]);
      setLastVisible(usersSnapshot.docs[usersSnapshot.docs.length - 1] || null);
      setHasMore(usersSnapshot.docs.length === 20);
    } catch (error) {
      console.error('Error loading more users:', error);
      showSnackbar(t('users.loadMoreError', 'Failed to load more users'), 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);

    // Load more users if we're on the last page and there are more to load
    if (newPage === Math.ceil(filteredUsers.length / rowsPerPage) - 1 && hasMore) {
      loadMoreUsers();
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleRefresh = () => {
    fetchUsers();
  };

  const handleAddUserClick = () => {
    setAddUserDialogOpen(true);
  };

  const handleCloseAddUserDialog = () => {
    setAddUserDialogOpen(false);
    setNewUserEmail('');
    setNewUserName('');
    setNewUserRole(RoleType.INSTRUCTOR);
    setProcessingNewUser(false);
  };

  const handleCreateUser = async (uid: string) => {
    if (!currentSchool?.id) return;

    try {
      setProcessingNewUser(true);

      // Create user document in Firestore
      const userRef = doc(db, 'users', uid);
      await setDoc(userRef, {
        email: newUserEmail,
        displayName: newUserName,
        schoolId: currentSchool.id,
        roleId: newUserRole,
        roles: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }, { merge: true });

      // Set custom claims
      await setCustomClaims(uid, {
        role: newUserRole,
        schoolId: currentSchool.id,
      });

      // Close dialog and refresh users
      handleCloseAddUserDialog();
      fetchUsers();

      showSnackbar(t('users.createSuccess', 'User created successfully'), 'success');
    } catch (error) {
      console.error('Error creating user:', error);
      showSnackbar(t('users.createError', 'Failed to create user'), 'error');
    } finally {
      setProcessingNewUser(false);
    }
  };

  const handleEditRole = (user: UserData) => {
    setSelectedUser(user);
    setSelectedRole(user.roleId || '');
    setAdditionalRoles(user.roles || []);
    setEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setSelectedUser(null);
    setSelectedRole('');
    setAdditionalRoles([]);
  };

  const handleSaveRole = async () => {
    if (!selectedUser || !selectedRole || !currentSchool?.id) return;

    try {
      setProcessingRole(true);

      // Update role in Firestore
      const userRef = doc(db, 'users', selectedUser.id);
      await updateDoc(userRef, {
        roleId: selectedRole,
        roles: additionalRoles,
        updatedAt: new Date(),
      });

      // Update custom claims
      await setCustomClaims(selectedUser.id, {
        role: selectedRole as RoleType,
        schoolId: currentSchool.id,
      });

      // Update the user in the local state
      setUsers((prevUsers) =>
        prevUsers.map((user) =>
          user.id === selectedUser.id
            ? { ...user, roleId: selectedRole as RoleType, roles: additionalRoles }
            : user
        )
      );

      showSnackbar(t('users.roleUpdateSuccess', 'User role updated successfully'), 'success');
      handleCloseEditDialog();
    } catch (error) {
      console.error('Error updating user role:', error);
      showSnackbar(t('users.roleUpdateError', 'Failed to update user role'), 'error');
    } finally {
      setProcessingRole(false);
    }
  };

  const handleAdditionalRoleChange = (event: SelectChangeEvent<RoleType[]>) => {
    const value = event.target.value as RoleType[];
    setAdditionalRoles(value);
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  // Get role name from role ID
  const getRoleName = (roleId: RoleType) => {
    const role = roles.find((r) => r.id === roleId);
    return role ? role.name : roleId;
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">{t('users.manageUsers', 'Manage Users')}</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            {t('common:refresh', 'Refresh')}
          </Button>
          <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddUserClick}>
            {t('users.addUser', 'Add User')}
          </Button>
        </Box>
      </Box>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder={t('users.searchPlaceholder', 'Search users by name or email...')}
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size="small"
        />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('users.name', 'Name')}</TableCell>
              <TableCell>{t('users.email', 'Email')}</TableCell>
              <TableCell>{t('users.role', 'Role')}</TableCell>
              <TableCell>{t('users.additionalRoles', 'Additional Roles')}</TableCell>
              <TableCell align="right">{t('common:actions', 'Actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading && users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                  <CircularProgress size={24} sx={{ mr: 1 }} />
                  {t('common:loading', 'Loading...')}
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                  {searchTerm
                    ? t('users.noSearchResults', 'No users match your search')
                    : t('users.noUsers', 'No users found')}
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.displayName || '-'}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={getRoleName(user.roleId as RoleType)}
                        color={
                          user.roleId === RoleType.ADMIN
                            ? 'primary'
                            : user.roleId === RoleType.INSTRUCTOR
                              ? 'success'
                              : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {user.roles && user.roles.length > 0
                        ? user.roles.map((role) => (
                            <Chip
                              key={role}
                              label={getRoleName(role)}
                              size="small"
                              variant="outlined"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))
                        : '-'}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title={t('users.editRole', 'Edit Role')}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditRole(user)}
                          aria-label={t('users.editRole', 'Edit Role')}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
            )}
            {loading && users.length > 0 && (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 2 }}>
                  <CircularProgress size={24} />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredUsers.length + (hasMore ? 1 : 0)}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Edit Role Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseEditDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {t('users.editUserRole', 'Edit User Role')}
          {selectedUser && (
            <Typography variant="subtitle1" color="text.secondary">
              {selectedUser.displayName || selectedUser.email}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>{t('users.primaryRole', 'Primary Role')}</InputLabel>
              <Select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value as RoleType)}
                label={t('users.primaryRole', 'Primary Role')}
              >
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>{t('users.additionalRoles', 'Additional Roles')}</InputLabel>
              <Select
                multiple
                value={additionalRoles}
                onChange={handleAdditionalRoleChange}
                label={t('users.additionalRoles', 'Additional Roles')}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={getRoleName(value)} size="small" />
                    ))}
                  </Box>
                )}
              >
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {t(
                'users.roleExplanation',
                'The primary role determines the main permissions, while additional roles can grant extra capabilities.'
              )}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>{t('common:cancel', 'Cancel')}</Button>
          <Button
            onClick={handleSaveRole}
            variant="contained"
            disabled={!selectedRole || processingRole}
            startIcon={processingRole ? <CircularProgress size={20} /> : null}
          >
            {processingRole ? t('common:saving', 'Saving...') : t('common:save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add User Dialog */}
      <Dialog open={addUserDialogOpen} onClose={handleCloseAddUserDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{t('users.addUser', 'Add User')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body1" paragraph>
              {t(
                'users.addUserDescription',
                'To add a new user, please use the Firebase Authentication console to create the account first, then assign roles here.'
              )}
            </Typography>

            <Typography variant="body2" color="text.secondary" paragraph>
              {t(
                'users.addUserNote',
                'Note: New users need to be created in Firebase Authentication before they can be managed here.'
              )}
            </Typography>

            <Box sx={{ mt: 2, mb: 3 }}>
              <Button
                variant="outlined"
                component="a"
                href="https://console.firebase.google.com/u/0/project/flowmaster-e3947/authentication/users"
                target="_blank"
                rel="noopener noreferrer"
                fullWidth
              >
                {t('users.openFirebaseConsole', 'Open Firebase Console')}
              </Button>
            </Box>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              {t('users.createUserInFirestore', 'Create User in Firestore')}
            </Typography>

            <Typography variant="body2" color="text.secondary" paragraph>
              {t('users.createUserInFirestoreDescription', 'After creating a user in Firebase Authentication, enter their details below to create a corresponding document in Firestore.')}
            </Typography>

            <TextField
              label={t('users.firebaseUid', 'Firebase UID')}
              fullWidth
              margin="normal"
              helperText={t('users.firebaseUidHelp', 'The UID from Firebase Authentication')}
              id="firebase-uid"
              name="firebase-uid"
              autoComplete="off"
              required
            />

            <TextField
              label={t('users.email', 'Email')}
              fullWidth
              margin="normal"
              value={newUserEmail}
              onChange={(e) => setNewUserEmail(e.target.value)}
              type="email"
              id="email"
              name="email"
              autoComplete="email"
              required
            />

            <TextField
              label={t('users.name', 'Name')}
              fullWidth
              margin="normal"
              value={newUserName}
              onChange={(e) => setNewUserName(e.target.value)}
              id="name"
              name="name"
              autoComplete="name"
            />

            <FormControl fullWidth margin="normal">
              <InputLabel>{t('users.role', 'Role')}</InputLabel>
              <Select
                value={newUserRole}
                onChange={(e) => setNewUserRole(e.target.value as RoleType)}
                label={t('users.role', 'Role')}
              >
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.id}>
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddUserDialog}>{t('common:cancel', 'Cancel')}</Button>
          <Button
            variant="contained"
            onClick={() => {
              const uidInput = document.getElementById('firebase-uid') as HTMLInputElement;
              if (uidInput && uidInput.value) {
                handleCreateUser(uidInput.value);
              } else {
                showSnackbar(t('users.uidRequired', 'Firebase UID is required'), 'error');
              }
            }}
            disabled={!newUserEmail || processingNewUser}
            startIcon={processingNewUser ? <CircularProgress size={20} /> : null}
          >
            {processingNewUser
              ? t('common:processing', 'Processing...')
              : t('users.createUser', 'Create User')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserManagement;
