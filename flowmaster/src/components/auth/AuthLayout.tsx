import { Box, Container, Paper, Typography, useTheme, useMediaQuery } from '@mui/material';
import LanguageSwitcher from '../shared/LanguageSwitcher';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

const AuthLayout = ({ children, title, subtitle }: AuthLayoutProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const content = (
    <>
      <Typography component="h1" variant="h5" sx={{ mb: subtitle ? 1 : 3 }}>
        {title}
      </Typography>
      {subtitle && (
        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3 }}>
          {subtitle}
        </Typography>
      )}
      {children}
    </>
  );

  // Desktop-optimized layout
  const desktopLayout = (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default',
      }}
    >
      <Container component="main" maxWidth="sm" sx={{ mb: 4 }}>
        <Paper
          elevation={3}
          sx={{
            my: { xs: 3, md: 6 },
            p: { xs: 2, md: 4 },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {content}
        </Paper>
      </Container>
    </Box>
  );

  // Mobile-optimized layout
  const mobileLayout = (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default',
      }}
    >
      <Box sx={{ position: 'absolute', top: isMobile ? 8 : 16, right: isMobile ? 8 : 16 }}>
        <LanguageSwitcher />
      </Box>
      <Container component="main" maxWidth="sm" sx={{ flex: 1, py: 2 }}>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            pt: 2,
          }}
        >
          {content}
        </Box>
      </Container>
    </Box>
  );

  return isMobile ? mobileLayout : desktopLayout;
};

export default AuthLayout;
