import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import Permission from './Permission';
import { Permission as PermissionType } from '../../types/role';
import LoadingSpinner from '../common/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: PermissionType | PermissionType[];
  requiredPermissionType?: 'all' | 'any';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredPermissionType = 'any',
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!user.emailVerified) {
    return <Navigate to="/verify-email" state={{ from: location }} replace />;
  }

  if (requiredPermission) {
    return (
      <Permission permissions={requiredPermission} type={requiredPermissionType}>
        {children}
      </Permission>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
