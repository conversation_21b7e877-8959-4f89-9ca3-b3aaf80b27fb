import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18next from 'i18next';
import ProtectedRoute from '../ProtectedRoute';
import { AuthContext } from '../../../context/AuthContext';
import { usePermission } from '../../../hooks/usePermission';
import { Role, RoleType } from '../../../types/role';

jest.mock('../../../hooks/usePermission');
const mockUsePermission = usePermission as jest.MockedFunction<typeof usePermission>;

const mockRole: Role = {
  id: RoleType.STUDENT,
  name: 'User',
  description: 'Basic user role',
  permissions: ['manage_users', 'view_lessons', 'manage_lessons'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const defaultAuthContext = {
  user: null,
  userProfile: null,
  isLoading: false,
  isAuthenticated: false,
  signIn: jest.fn(),
  signOut: jest.fn(),
  signUp: jest.fn(),
  verifyEmail: jest.fn(),
  resetPassword: jest.fn(),
  error: null,
};

const renderWithRouter = (
  ui: React.ReactElement,
  { authContext = {}, initialRoute = '/' } = {}
) => {
  return render(
    <I18nextProvider i18n={i18next}>
      <MemoryRouter initialEntries={[initialRoute]}>
        <AuthContext.Provider value={{ ...defaultAuthContext, ...authContext }}>
          <Routes>
            <Route path="/" element={ui} />
            <Route path="/login" element={<div>Login Page</div>} />
            <Route path="/verify-email" element={<div>Email Verification Page</div>} />
          </Routes>
        </AuthContext.Provider>
      </MemoryRouter>
    </I18nextProvider>
  );
};

describe('ProtectedRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUsePermission.mockReturnValue({
      role: mockRole,
      hasPermission: () => true,
      hasAllPermissions: () => true,
      hasAnyPermission: () => true,
    });
  });

  it('redirects to login when user is not authenticated', () => {
    renderWithRouter(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByText('Login Page')).toBeInTheDocument();
  });

  it('redirects to email verification when email is not verified', () => {
    const user = {
      email: '<EMAIL>',
      emailVerified: false,
    };

    renderWithRouter(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>,
      {
        authContext: {
          user,
          isAuthenticated: true,
        },
      }
    );

    expect(screen.getByText('Email Verification Page')).toBeInTheDocument();
  });

  it('renders children when user is authenticated and email is verified', () => {
    const user = {
      email: '<EMAIL>',
      emailVerified: true,
    };

    renderWithRouter(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>,
      {
        authContext: {
          user,
          isAuthenticated: true,
        },
      }
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('renders children when required permission is granted', () => {
    const user = {
      email: '<EMAIL>',
      emailVerified: true,
    };

    const hasPermission = jest.fn().mockReturnValue(true);
    mockUsePermission.mockReturnValue({
      role: mockRole,
      hasPermission,
      hasAllPermissions: () => true,
      hasAnyPermission: () => true,
    });

    renderWithRouter(
      <ProtectedRoute requiredPermission="manage_users">
        <div>Protected Content</div>
      </ProtectedRoute>,
      {
        authContext: {
          user,
          isAuthenticated: true,
        },
      }
    );

    expect(hasPermission).toHaveBeenCalledWith('manage_users');
    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('does not render children when permission is denied', () => {
    const user = {
      email: '<EMAIL>',
      emailVerified: true,
    };

    const hasPermission = jest.fn().mockReturnValue(false);
    mockUsePermission.mockReturnValue({
      role: mockRole,
      hasPermission,
      hasAllPermissions: () => true,
      hasAnyPermission: () => true,
    });

    renderWithRouter(
      <ProtectedRoute requiredPermission="manage_users">
        <div>Protected Content</div>
      </ProtectedRoute>,
      {
        authContext: {
          user,
          isAuthenticated: true,
        },
      }
    );

    expect(hasPermission).toHaveBeenCalledWith('manage_users');
    expect(screen.queryByText('Protected Content')).toBeNull();
  });

  it('shows loading state when authentication is in progress', () => {
    renderWithRouter(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>,
      {
        authContext: {
          isLoading: true,
        },
      }
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
});
