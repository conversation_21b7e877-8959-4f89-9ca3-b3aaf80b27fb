import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';
import { I18nextProvider } from 'react-i18next';
import i18next from 'i18next';
import AuthLayout from '../AuthLayout';

// Mock useMediaQuery hook
jest.mock('@mui/material/useMediaQuery');
const mockUseMediaQuery = useMediaQuery as jest.MockedFunction<typeof useMediaQuery>;

// Mock LanguageSwitcher component
jest.mock('../../shared/LanguageSwitcher', () => {
  return {
    __esModule: true,
    default: () => <div data-testid="language-switcher">Language Switcher</div>,
  };
});

const renderWithTheme = (ui: React.ReactElement, isMobile = false) => {
  mockUseMediaQuery.mockReturnValue(isMobile);
  const theme = createTheme();

  return render(
    <I18nextProvider i18n={i18next}>
      <ThemeProvider theme={theme}>{ui}</ThemeProvider>
    </I18nextProvider>
  );
};

describe('AuthLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders title correctly', () => {
    renderWithTheme(
      <AuthLayout title="Test Title">
        <div>Test Content</div>
      </AuthLayout>
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('renders subtitle when provided', () => {
    renderWithTheme(
      <AuthLayout title="Test Title" subtitle="Test Subtitle">
        <div>Test Content</div>
      </AuthLayout>
    );

    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
  });

  it('renders children content', () => {
    renderWithTheme(
      <AuthLayout title="Test Title">
        <div>Test Content</div>
      </AuthLayout>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders language switcher', () => {
    renderWithTheme(
      <AuthLayout title="Test Title">
        <div>Test Content</div>
      </AuthLayout>
    );

    expect(screen.getByTestId('language-switcher')).toBeInTheDocument();
  });

  it('uses desktop layout when not mobile', () => {
    renderWithTheme(
      <AuthLayout title="Test Title">
        <div>Test Content</div>
      </AuthLayout>
    );

    const container = screen.getByRole('main');
    expect(container).toHaveAttribute('class');
    expect(container.getAttribute('class')).toContain('MuiContainer-maxWidthSm'); // Verify Container maxWidth="sm" prop
  });

  it('uses mobile layout when on mobile device', () => {
    renderWithTheme(
      <AuthLayout title="Test Title">
        <div>Test Content</div>
      </AuthLayout>,
      true
    );

    const container = screen.getByRole('main');
    expect(container).toHaveStyle({ flex: 1, paddingTop: '16px', paddingBottom: '16px' });
  });
});
