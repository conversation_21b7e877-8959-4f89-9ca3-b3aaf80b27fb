import { render, screen } from '@testing-library/react';
import Permission from '../Permission';
import { usePermission } from '../../../hooks/usePermission';
import { Permission as PermissionType, Role, RoleType } from '../../../types/role';

// Mock usePermission hook
jest.mock('../../../hooks/usePermission');
const mockUsePermission = usePermission as jest.MockedFunction<typeof usePermission>;

describe('Permission Component', () => {
  const mockRole: Role = {
    id: RoleType.STUDENT, // Changed from USER to STUDENT
    name: 'Student',
    description: 'Student access',
    permissions: ['view_lessons', 'view_programs'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const defaultMockPermissions = {
    role: mockRole,
    hasPermission: jest.fn(),
    hasAllPermissions: jest.fn(),
    hasAnyPermission: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUsePermission.mockReturnValue(defaultMockPermissions);
  });

  describe('Single Permission', () => {
    it('renders children when user has the required permission', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(true);

      render(
        <Permission permissions="view_lessons">
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('does not render children when user lacks the required permission', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(false);

      render(
        <Permission permissions="manage_users">
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('renders fallback content when user lacks permission', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(false);

      render(
        <Permission permissions="manage_users" fallback={<div>Access Denied</div>}>
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('Multiple Permissions - ANY', () => {
    const permissions: PermissionType[] = ['view_lessons', 'manage_users'];

    it('renders children when user has any required permission', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(true);

      render(
        <Permission permissions={permissions} type="any">
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
      expect(defaultMockPermissions.hasAnyPermission).toHaveBeenCalledWith(permissions);
    });

    it('does not render children when user has none of the required permissions', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(false);

      render(
        <Permission permissions={permissions} type="any">
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      expect(defaultMockPermissions.hasAnyPermission).toHaveBeenCalledWith(permissions);
    });
  });

  describe('Multiple Permissions - ALL', () => {
    const permissions: PermissionType[] = ['view_lessons', 'view_programs'];

    it('renders children when user has all required permissions', () => {
      defaultMockPermissions.hasAllPermissions.mockReturnValue(true);

      render(
        <Permission permissions={permissions} type="all">
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
      expect(defaultMockPermissions.hasAllPermissions).toHaveBeenCalledWith(permissions);
    });

    it('does not render children when user lacks any required permission', () => {
      defaultMockPermissions.hasAllPermissions.mockReturnValue(false);

      render(
        <Permission permissions={permissions} type="all">
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      expect(defaultMockPermissions.hasAllPermissions).toHaveBeenCalledWith(permissions);
    });
  });

  describe('Edge Cases', () => {
    it('handles empty permissions array', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(false);

      render(
        <Permission permissions={[]}>
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('handles null fallback', () => {
      defaultMockPermissions.hasAnyPermission.mockReturnValue(false);

      render(
        <Permission permissions="manage_users" fallback={null}>
          <div>Protected Content</div>
        </Permission>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      expect(screen.queryByText('Access Denied')).not.toBeInTheDocument();
    });
  });
});
