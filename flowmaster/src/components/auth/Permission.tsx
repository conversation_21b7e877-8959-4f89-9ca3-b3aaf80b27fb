import React from 'react';
import { usePermission } from '../../hooks/usePermission';
import { Permission as PermissionType } from '../../types/role';

interface PermissionProps {
  children: React.ReactNode;
  permissions: PermissionType | PermissionType[];
  type?: 'all' | 'any';
  fallback?: React.ReactNode;
}

const Permission: React.FC<PermissionProps> = ({
  children,
  permissions,
  type = 'any',
  fallback = null,
}) => {
  const { hasAllPermissions, hasAnyPermission } = usePermission();
  const permissionArray = Array.isArray(permissions) ? permissions : [permissions];

  const hasAccess =
    type === 'all' ? hasAllPermissions(permissionArray) : hasAnyPermission(permissionArray);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default Permission;
