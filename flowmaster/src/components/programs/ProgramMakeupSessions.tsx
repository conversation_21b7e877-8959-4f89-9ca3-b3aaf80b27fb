import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program, ProgramSession, AttendanceStatus } from '../../types/program';
import { Student } from '../../types/student';
import { doc, getDoc } from 'firebase/firestore';
import {
  createMakeupSession,
  getMakeupSessions,
  getStudentsNeedingMakeup,
  markStudentsForMakeup,
} from '../../services/makeupSessionService';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  Divider,
  Tabs,
  Tab,
  <PERSON>ltip,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  Avatar,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Add as AddIcon,
  Event as EventIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

interface ProgramMakeupSessionsProps {
  program: Program;
  sessions: ProgramSession[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`makeup-tabpanel-${index}`}
      aria-labelledby={`makeup-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `makeup-tab-${index}`,
    'aria-controls': `makeup-tabpanel-${index}`,
  };
}

const ProgramMakeupSessions: React.FC<ProgramMakeupSessionsProps> = ({ program, sessions }) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  const [makeupSessions, setMakeupSessions] = useState<ProgramSession[]>([]);
  const [studentsNeedingMakeup, setStudentsNeedingMakeup] = useState<
    { student: Student; missedSessions: ProgramSession[] }[]
  >([]);

  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [markDialogOpen, setMarkDialogOpen] = useState(false);

  const [selectedSession, setSelectedSession] = useState<ProgramSession | null>(null);
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [makeupDate, setMakeupDate] = useState<Date | null>(new Date());
  const [makeupReason, setMakeupReason] = useState<string>('');

  const [students, setStudents] = useState<Record<string, Student>>({});

  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch makeup sessions
        const makeupSessionsData = await getMakeupSessions(currentSchool.id, program.id);
        setMakeupSessions(makeupSessionsData);

        // Fetch students needing makeup
        const studentsNeedingMakeupData = await getStudentsNeedingMakeup(
          currentSchool.id,
          program.id
        );
        setStudentsNeedingMakeup(studentsNeedingMakeupData);

        // Fetch student details for all participants
        const studentsData: Record<string, Student> = {};

        for (const studentId of program.participants) {
          const studentDoc = await getDoc(
            doc(db, 'schools', currentSchool.id, 'students', studentId)
          );

          if (studentDoc.exists()) {
            studentsData[studentId] = {
              id: studentDoc.id,
              ...studentDoc.data(),
            } as Student;
          }
        }

        setStudents(studentsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching makeup data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id, program.id, program.participants, t]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCreateMakeupSession = async () => {
    if (!currentSchool?.id || !selectedSession || !makeupDate || selectedStudents.length === 0) {
      return;
    }

    try {
      await createMakeupSession(
        currentSchool.id,
        program.id,
        selectedSession.id,
        selectedStudents,
        makeupDate,
        makeupReason
      );

      // Refresh data
      const makeupSessionsData = await getMakeupSessions(currentSchool.id, program.id);
      setMakeupSessions(makeupSessionsData);

      const studentsNeedingMakeupData = await getStudentsNeedingMakeup(
        currentSchool.id,
        program.id
      );
      setStudentsNeedingMakeup(studentsNeedingMakeupData);

      // Reset form
      setCreateDialogOpen(false);
      setSelectedSession(null);
      setSelectedStudents([]);
      setMakeupDate(new Date());
      setMakeupReason('');
    } catch (err) {
      console.error('Error creating makeup session:', err);
      setError(t('messages.createError', 'Failed to create makeup session'));
    }
  };

  const handleMarkForMakeup = async () => {
    if (!currentSchool?.id || !selectedSession || selectedStudents.length === 0) {
      return;
    }

    try {
      await markStudentsForMakeup(
        currentSchool.id,
        program.id,
        selectedSession.id,
        selectedStudents
      );

      // Refresh data
      const studentsNeedingMakeupData = await getStudentsNeedingMakeup(
        currentSchool.id,
        program.id
      );
      setStudentsNeedingMakeup(studentsNeedingMakeupData);

      // Reset form
      setMarkDialogOpen(false);
      setSelectedSession(null);
      setSelectedStudents([]);
    } catch (err) {
      console.error('Error marking students for makeup:', err);
      setError(t('messages.updateError', 'Failed to mark students for makeup'));
    }
  };

  const openCreateDialog = (session: ProgramSession) => {
    setSelectedSession(session);
    setSelectedStudents([]);
    setMakeupDate(new Date());
    setMakeupReason('');
    setCreateDialogOpen(true);
  };

  const openMarkDialog = (session: ProgramSession) => {
    setSelectedSession(session);
    setSelectedStudents([]);
    setMarkDialogOpen(true);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('makeup.title', 'Makeup Sessions')}
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="makeup session tabs">
          <Tab label={t('makeup.scheduled', 'Scheduled Makeups')} {...a11yProps(0)} />
          <Tab label={t('makeup.needed', 'Students Needing Makeup')} {...a11yProps(1)} />
          <Tab label={t('makeup.create', 'Create Makeup Session')} {...a11yProps(2)} />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {makeupSessions.length === 0 ? (
          <Alert severity="info">{t('makeup.noScheduled', 'No makeup sessions scheduled')}</Alert>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('fields.date', 'Date')}</TableCell>
                  <TableCell>{t('fields.time', 'Time')}</TableCell>
                  <TableCell>{t('makeup.forStudents', 'For Students')}</TableCell>
                  <TableCell>{t('makeup.originalSession', 'Original Session')}</TableCell>
                  <TableCell>{t('makeup.reason', 'Reason')}</TableCell>
                  <TableCell align="right">{t('common:ui.actions', 'Actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {makeupSessions.map((session) => {
                  const sessionDate = session.date.toDate();
                  const forStudents = session.makeupDetails?.forStudents || [];
                  const originalSessionId = session.makeupDetails?.originalSessionId;
                  const originalSession = sessions.find((s) => s.id === originalSessionId);

                  return (
                    <TableRow key={session.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          {format(sessionDate, 'EEEE, MMMM d, yyyy')}
                          <Chip
                            label={t('sessionType.makeup', 'Makeup')}
                            size="small"
                            color="info"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        {program.schedule.startTime} - {program.schedule.endTime}
                      </TableCell>
                      <TableCell>
                        <Box display="flex" flexDirection="column" gap={0.5}>
                          {forStudents.map((studentId) => (
                            <Chip
                              key={studentId}
                              label={
                                students[studentId]
                                  ? `${students[studentId].firstName} ${students[studentId].lastName}`
                                  : studentId
                              }
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {originalSession
                          ? format(originalSession.date.toDate(), 'MMM d, yyyy')
                          : t('makeup.unknownSession', 'Unknown session')}
                      </TableCell>
                      <TableCell>
                        {session.makeupDetails?.reason ||
                          t('makeup.noReason', 'No reason provided')}
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title={t('actions.takeAttendance', 'Take Attendance')}>
                          <IconButton size="small" color="primary">
                            <CheckCircleIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {studentsNeedingMakeup.length === 0 ? (
          <Alert severity="success">
            {t('makeup.noStudentsNeedMakeup', 'No students currently need makeup sessions')}
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {studentsNeedingMakeup.map(({ student, missedSessions }) => (
              <Grid item xs={12} md={6} key={student.id}>
                <Paper sx={{ p: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ mr: 2 }}>
                        {student.firstName.charAt(0)}
                        {student.lastName.charAt(0)}
                      </Avatar>
                      <Typography variant="h6">
                        {student.firstName} {student.lastName}
                      </Typography>
                    </Box>
                    <Chip
                      label={t('makeup.missedCount', '{{count}} missed', {
                        count: missedSessions.length,
                      })}
                      color="warning"
                    />
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>
                    {t('makeup.missedSessions', 'Missed Sessions')}
                  </Typography>

                  <List dense>
                    {missedSessions.map((session) => (
                      <ListItem
                        key={session.id}
                        secondaryAction={
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<ScheduleIcon />}
                            onClick={() => {
                              setSelectedSession(session);
                              setSelectedStudents([student.id]);
                              setMakeupDate(new Date());
                              setMakeupReason('');
                              setCreateDialogOpen(true);
                            }}
                          >
                            {t('makeup.schedule', 'Schedule Makeup')}
                          </Button>
                        }
                      >
                        <ListItemIcon>
                          <CalendarIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary={format(session.date.toDate(), 'EEEE, MMMM d, yyyy')}
                          secondary={`${program.schedule.startTime} - ${program.schedule.endTime}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            {t('makeup.selectSession', 'Select a session to create makeup for')}
          </Typography>

          <TableContainer sx={{ mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('fields.date', 'Date')}</TableCell>
                  <TableCell>{t('fields.time', 'Time')}</TableCell>
                  <TableCell>{t('fields.actions', 'Actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sessions
                  .filter((session) => !session.isMakeup)
                  .sort((a, b) => b.date.toDate().getTime() - a.date.toDate().getTime())
                  .slice(0, 10)
                  .map((session) => (
                    <TableRow key={session.id}>
                      <TableCell>{format(session.date.toDate(), 'EEEE, MMMM d, yyyy')}</TableCell>
                      <TableCell>
                        {program.schedule.startTime} - {program.schedule.endTime}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          startIcon={<AddIcon />}
                          onClick={() => openCreateDialog(session)}
                          size="small"
                        >
                          {t('makeup.createMakeup', 'Create Makeup')}
                        </Button>
                        <Button
                          variant="text"
                          startIcon={<PersonIcon />}
                          onClick={() => openMarkDialog(session)}
                          size="small"
                          sx={{ ml: 1 }}
                        >
                          {t('makeup.markStudents', 'Mark Students')}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      {/* Create Makeup Session Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t('makeup.createMakeupSession', 'Create Makeup Session')}</DialogTitle>
        <DialogContent>
          {selectedSession && (
            <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="subtitle2">
                {t('makeup.originalSession', 'Original Session')}:{' '}
                {format(selectedSession.date.toDate(), 'EEEE, MMMM d, yyyy')}
              </Typography>

              <FormControl fullWidth>
                <InputLabel>{t('makeup.selectStudents', 'Select Students')}</InputLabel>
                <Select
                  multiple
                  value={selectedStudents}
                  onChange={(e) => setSelectedStudents(e.target.value as string[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={
                            students[value]
                              ? `${students[value].firstName} ${students[value].lastName}`
                              : value
                          }
                          size="small"
                        />
                      ))}
                    </Box>
                  )}
                  label={t('makeup.selectStudents', 'Select Students')}
                >
                  {program.participants.map((studentId) => (
                    <MenuItem key={studentId} value={studentId}>
                      <Checkbox checked={selectedStudents.indexOf(studentId) > -1} />
                      <ListItemText
                        primary={
                          students[studentId]
                            ? `${students[studentId].firstName} ${students[studentId].lastName}`
                            : studentId
                        }
                      />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={t('makeup.date', 'Makeup Date')}
                  value={makeupDate}
                  onChange={(newDate) => setMakeupDate(newDate)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </LocalizationProvider>

              <TextField
                label={t('makeup.reason', 'Reason for Makeup')}
                value={makeupReason}
                onChange={(e) => setMakeupReason(e.target.value)}
                fullWidth
                multiline
                rows={3}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleCreateMakeupSession}
            variant="contained"
            color="primary"
            disabled={!selectedSession || !makeupDate || selectedStudents.length === 0}
          >
            {t('common:ui.create', 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Mark Students for Makeup Dialog */}
      <Dialog
        open={markDialogOpen}
        onClose={() => setMarkDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t('makeup.markStudentsForMakeup', 'Mark Students for Makeup')}</DialogTitle>
        <DialogContent>
          {selectedSession && (
            <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="subtitle2">
                {t('makeup.session', 'Session')}:{' '}
                {format(selectedSession.date.toDate(), 'EEEE, MMMM d, yyyy')}
              </Typography>

              <FormControl fullWidth>
                <InputLabel>{t('makeup.selectStudents', 'Select Students')}</InputLabel>
                <Select
                  multiple
                  value={selectedStudents}
                  onChange={(e) => setSelectedStudents(e.target.value as string[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={
                            students[value]
                              ? `${students[value].firstName} ${students[value].lastName}`
                              : value
                          }
                          size="small"
                        />
                      ))}
                    </Box>
                  )}
                  label={t('makeup.selectStudents', 'Select Students')}
                >
                  {program.participants.map((studentId) => (
                    <MenuItem key={studentId} value={studentId}>
                      <Checkbox checked={selectedStudents.indexOf(studentId) > -1} />
                      <ListItemText
                        primary={
                          students[studentId]
                            ? `${students[studentId].firstName} ${students[studentId].lastName}`
                            : studentId
                        }
                      />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMarkDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleMarkForMakeup}
            variant="contained"
            color="primary"
            disabled={!selectedSession || selectedStudents.length === 0}
          >
            {t('common:ui.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProgramMakeupSessions;
