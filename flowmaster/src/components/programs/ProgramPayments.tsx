import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program, Payment, PaymentMethod } from '../../types/program';
import { Student } from '../../types/student';
import { doc, getDoc, Timestamp } from 'firebase/firestore';
import {
  getProgramPayments,
  createPayment,
  updatePayment,
  deletePayment,
  calculateRemainingBalance,
} from '../../services/paymentService';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Tooltip,
  Divider,
  Card,
  CardContent,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Receipt as ReceiptIcon,
  AttachMoney as MoneyIcon,
  AccountBalance as AccountIcon,
  CreditCard as CardIcon,
  LocalAtm as CashIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface ProgramPaymentsProps {
  program: Program;
}

interface StudentWithPayments {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  totalPaid: number;
  remainingBalance: number;
  paymentStatus: 'paid' | 'partial' | 'pending';
  payments: Payment[];
}

const ProgramPayments: React.FC<ProgramPaymentsProps> = ({ program }) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [students, setStudents] = useState<Record<string, Student>>({});
  const [studentsWithPayments, setStudentsWithPayments] = useState<StudentWithPayments[]>([]);

  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [paymentDate, setPaymentDate] = useState<Date>(new Date());
  const [paymentNotes, setPaymentNotes] = useState<string>('');
  const [receiptNumber, setReceiptNumber] = useState<string>('');

  useEffect(() => {
    const fetchPaymentsData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch all payments for this program
        const paymentsData = await getProgramPayments(currentSchool.id, program.id);
        setPayments(paymentsData);

        // Fetch student details for all participants
        const studentsData: Record<string, Student> = {};

        for (const studentId of program.participants) {
          const studentDoc = await getDoc(
            doc(db, 'schools', currentSchool.id, 'students', studentId)
          );

          if (studentDoc.exists()) {
            studentsData[studentId] = {
              id: studentDoc.id,
              ...studentDoc.data(),
            } as Student;
          }
        }

        setStudents(studentsData);

        // Process students with their payments
        const studentsWithPaymentsData: StudentWithPayments[] = [];

        for (const studentId of program.participants) {
          const student = studentsData[studentId];

          if (student) {
            const studentPayments = paymentsData.filter((p) => p.studentId === studentId);
            const totalPaid = studentPayments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingBalance = await calculateRemainingBalance(
              currentSchool.id,
              program.id,
              studentId
            );

            studentsWithPaymentsData.push({
              id: studentId,
              firstName: student.firstName,
              lastName: student.lastName,
              email: student.email,
              totalPaid,
              remainingBalance,
              paymentStatus: program.pricing.paymentStatus[studentId] || 'pending',
              payments: studentPayments,
            });
          }
        }

        setStudentsWithPayments(studentsWithPaymentsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching payments data:', err);
        setError(t('messages.fetchError', 'Failed to fetch payments data'));
        setLoading(false);
      }
    };

    fetchPaymentsData();
  }, [currentSchool?.id, program.id, program.participants, program.pricing.paymentStatus, t]);

  const handleAddPayment = (studentId: string) => {
    setSelectedStudent(studentId);
    setPaymentAmount(0);
    setPaymentMethod('cash');
    setPaymentDate(new Date());
    setPaymentNotes('');
    setReceiptNumber('');
    setPaymentDialogOpen(true);
  };

  const handleEditPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setSelectedStudent(payment.studentId);
    setPaymentAmount(payment.amount);
    setPaymentMethod(payment.method);
    setPaymentDate(payment.date.toDate());
    setPaymentNotes(payment.notes || '');
    setReceiptNumber(payment.receiptNumber || '');
    setPaymentDialogOpen(true);
  };

  const handleDeletePayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setDeleteDialogOpen(true);
  };

  const handleSavePayment = async () => {
    if (!currentSchool?.id) return;

    try {
      if (selectedPayment) {
        // Update existing payment
        await updatePayment(currentSchool.id, program.id, selectedPayment.id, {
          amount: paymentAmount,
          method: paymentMethod,
          date: Timestamp.fromDate(new Date(paymentDate)),
          notes: paymentNotes,
          receiptNumber,
        });
      } else {
        // Create new payment
        await createPayment(currentSchool.id, program.id, {
          studentId: selectedStudent,
          amount: paymentAmount,
          currency: program.pricing.currency,
          method: paymentMethod,
          date: Timestamp.fromDate(new Date(paymentDate)),
          notes: paymentNotes,
          receiptNumber,
        });
      }

      // Refresh data
      const paymentsData = await getProgramPayments(currentSchool.id, program.id);
      setPayments(paymentsData);

      // Update students with payments
      const updatedStudentsWithPayments = [...studentsWithPayments];

      for (const student of updatedStudentsWithPayments) {
        const studentPayments = paymentsData.filter((p) => p.studentId === student.id);
        const totalPaid = studentPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingBalance = await calculateRemainingBalance(
          currentSchool.id,
          program.id,
          student.id
        );

        student.totalPaid = totalPaid;
        student.remainingBalance = remainingBalance;
        student.paymentStatus = program.pricing.paymentStatus[student.id] || 'pending';
        student.payments = studentPayments;
      }

      setStudentsWithPayments(updatedStudentsWithPayments);
      setPaymentDialogOpen(false);
      setSelectedPayment(null);
    } catch (err) {
      console.error('Error saving payment:', err);
      setError(t('messages.saveError', 'Failed to save payment'));
    }
  };

  const handleConfirmDelete = async () => {
    if (!currentSchool?.id || !selectedPayment) return;

    try {
      await deletePayment(currentSchool.id, program.id, selectedPayment.id);

      // Refresh data
      const paymentsData = await getProgramPayments(currentSchool.id, program.id);
      setPayments(paymentsData);

      // Update students with payments
      const updatedStudentsWithPayments = [...studentsWithPayments];

      for (const student of updatedStudentsWithPayments) {
        const studentPayments = paymentsData.filter((p) => p.studentId === student.id);
        const totalPaid = studentPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingBalance = await calculateRemainingBalance(
          currentSchool.id,
          program.id,
          student.id
        );

        student.totalPaid = totalPaid;
        student.remainingBalance = remainingBalance;
        student.paymentStatus = program.pricing.paymentStatus[student.id] || 'pending';
        student.payments = studentPayments;
      }

      setStudentsWithPayments(updatedStudentsWithPayments);
      setDeleteDialogOpen(false);
      setSelectedPayment(null);
    } catch (err) {
      console.error('Error deleting payment:', err);
      setError(t('messages.deleteError', 'Failed to delete payment'));
    }
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'cash':
        return <CashIcon />;
      case 'card':
        return <CardIcon />;
      case 'transfer':
        return <AccountIcon />;
      default:
        return <MoneyIcon />;
    }
  };

  const getPaymentStatusColor = (status: string): 'success' | 'warning' | 'error' => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'partial':
        return 'warning';
      case 'pending':
        return 'error';
      default:
        return 'error';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('payments.title', 'Program Payments')}
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('payments.totalFee', 'Total Program Fee')}
              </Typography>
              <Typography variant="h4" color="primary">
                {program.pricing.totalFee} {program.pricing.currency}
              </Typography>
              {program.pricing.installments && (
                <Typography variant="body2" color="text.secondary">
                  {t('payments.installments', 'Installments: {{count}}', {
                    count: program.pricing.installments,
                  })}
                </Typography>
              )}
              {program.pricing.dueDate && (
                <Typography variant="body2" color="text.secondary">
                  {t('payments.dueDate', 'Due Date: {{date}}', {
                    date: format(program.pricing.dueDate.toDate(), 'PPP'),
                  })}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('payments.totalCollected', 'Total Collected')}
              </Typography>
              <Typography variant="h4" color="success.main">
                {payments.reduce((sum, payment) => sum + payment.amount, 0)}{' '}
                {program.pricing.currency}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('payments.totalPayments', 'Total Payments: {{count}}', {
                  count: payments.length,
                })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('payments.outstanding', 'Outstanding Balance')}
              </Typography>
              <Typography variant="h4" color="error.main">
                {program.pricing.totalFee -
                  payments.reduce((sum, payment) => sum + payment.amount, 0)}{' '}
                {program.pricing.currency}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('payments.pendingStudents', 'Students with Pending Payments: {{count}}', {
                  count: Object.values(program.pricing.paymentStatus).filter(
                    (status) => status !== 'paid'
                  ).length,
                })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h6" gutterBottom>
        {t('payments.studentPayments', 'Student Payments')}
      </Typography>

      {studentsWithPayments.map((student) => (
        <Paper key={student.id} sx={{ mb: 3, p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Box>
              <Typography variant="h6">
                {student.firstName} {student.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {student.email}
              </Typography>
            </Box>

            <Box display="flex" alignItems="center">
              <Chip
                label={t(`payments.status.${student.paymentStatus}`, student.paymentStatus)}
                color={getPaymentStatusColor(student.paymentStatus)}
                sx={{ mr: 2 }}
              />

              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleAddPayment(student.id)}
                size="small"
              >
                {t('payments.addPayment', 'Add Payment')}
              </Button>
            </Box>
          </Box>

          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('payments.totalPaid', 'Total Paid')}
                </Typography>
                <Typography variant="h6" color="success.main">
                  {student.totalPaid} {program.pricing.currency}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('payments.remainingBalance', 'Remaining Balance')}
                </Typography>
                <Typography variant="h6" color="error.main">
                  {student.remainingBalance} {program.pricing.currency}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('payments.progress', 'Payment Progress')}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={(student.totalPaid / program.pricing.totalFee) * 100}
              sx={{ height: 10, borderRadius: 5 }}
            />
          </Box>

          <Typography variant="subtitle2" gutterBottom>
            {t('payments.history', 'Payment History')}
          </Typography>

          {student.payments.length > 0 ? (
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>{t('payments.date', 'Date')}</TableCell>
                    <TableCell>{t('payments.amount', 'Amount')}</TableCell>
                    <TableCell>{t('payments.method', 'Method')}</TableCell>
                    <TableCell>{t('payments.receipt', 'Receipt')}</TableCell>
                    <TableCell>{t('payments.notes', 'Notes')}</TableCell>
                    <TableCell align="right">{t('common:ui.actions', 'Actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {student.payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>{format(payment.date.toDate(), 'PP')}</TableCell>
                      <TableCell>
                        <Typography fontWeight="medium">
                          {payment.amount} {payment.currency}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getPaymentMethodIcon(payment.method)}
                          label={t(`payments.methods.${payment.method}`, payment.method)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{payment.receiptNumber || '-'}</TableCell>
                      <TableCell>{payment.notes || '-'}</TableCell>
                      <TableCell align="right">
                        <Tooltip title={t('common:ui.edit', 'Edit')}>
                          <IconButton size="small" onClick={() => handleEditPayment(payment)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common:ui.delete', 'Delete')}>
                          <IconButton
                            size="small"
                            onClick={() => handleDeletePayment(payment)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography variant="body2" color="text.secondary">
              {t('payments.noPayments', 'No payments recorded')}
            </Typography>
          )}
        </Paper>
      ))}

      {/* Payment Dialog */}
      <Dialog
        open={paymentDialogOpen}
        onClose={() => setPaymentDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedPayment
            ? t('payments.editPayment', 'Edit Payment')
            : t('payments.addPayment', 'Add Payment')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label={t('payments.amount', 'Amount')}
              type="number"
              value={paymentAmount}
              onChange={(e) => setPaymentAmount(Number(e.target.value))}
              fullWidth
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">{program.pricing.currency}</InputAdornment>
                ),
              }}
            />

            <FormControl fullWidth>
              <InputLabel>{t('payments.method', 'Payment Method')}</InputLabel>
              <Select
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                label={t('payments.method', 'Payment Method')}
              >
                <MenuItem value="cash">{t('payments.methods.cash', 'Cash')}</MenuItem>
                <MenuItem value="card">{t('payments.methods.card', 'Card')}</MenuItem>
                <MenuItem value="transfer">
                  {t('payments.methods.transfer', 'Bank Transfer')}
                </MenuItem>
                <MenuItem value="check">{t('payments.methods.check', 'Check')}</MenuItem>
                <MenuItem value="other">{t('payments.methods.other', 'Other')}</MenuItem>
              </Select>
            </FormControl>

            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={t('payments.date', 'Payment Date')}
                value={paymentDate}
                onChange={(newDate) => newDate && setPaymentDate(newDate)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>

            <TextField
              label={t('payments.receipt', 'Receipt Number')}
              value={receiptNumber}
              onChange={(e) => setReceiptNumber(e.target.value)}
              fullWidth
            />

            <TextField
              label={t('payments.notes', 'Notes')}
              value={paymentNotes}
              onChange={(e) => setPaymentNotes(e.target.value)}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleSavePayment}
            variant="contained"
            color="primary"
            disabled={paymentAmount <= 0}
          >
            {t('common:ui.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>{t('payments.deletePayment', 'Delete Payment')}</DialogTitle>
        <DialogContent>
          <Typography>
            {t(
              'payments.deleteConfirmation',
              'Are you sure you want to delete this payment? This action cannot be undone.'
            )}
          </Typography>
          {selectedPayment && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2">
                <strong>{t('payments.amount', 'Amount')}:</strong> {selectedPayment.amount}{' '}
                {selectedPayment.currency}
              </Typography>
              <Typography variant="body2">
                <strong>{t('payments.date', 'Date')}:</strong>{' '}
                {format(selectedPayment.date.toDate(), 'PPP')}
              </Typography>
              <Typography variant="body2">
                <strong>{t('payments.method', 'Method')}:</strong>{' '}
                {t(`payments.methods.${selectedPayment.method}`, selectedPayment.method)}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button onClick={handleConfirmDelete} variant="contained" color="error">
            {t('common:ui.delete', 'Delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProgramPayments;
