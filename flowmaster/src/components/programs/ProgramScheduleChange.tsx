import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program, ProgramSession, WeekDay } from '../../types/program';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  writeBatch,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
} from 'firebase/firestore';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tooltip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Checkbox,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Notifications as NotificationsIcon,
  ExpandMore as ExpandMoreIcon,
  CalendarMonth as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import { format, addDays, isSameDay, isAfter, isBefore, parseISO } from 'date-fns';

interface ProgramScheduleChangeProps {
  program: Program;
  sessions: ProgramSession[];
  onUpdate?: () => void;
}

interface ScheduleChangeData {
  days: WeekDay[];
  startTime: string;
  endTime: string;
  startDate: Date | null;
  endDate: Date | null;
  notifyStudents: boolean;
  notifyInstructors: boolean;
  reason: string;
  affectExistingSessions: boolean;
}

const ProgramScheduleChange: React.FC<ProgramScheduleChangeProps> = ({
  program,
  sessions,
  onUpdate,
}) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [scheduleChangeData, setScheduleChangeData] = useState<ScheduleChangeData>({
    days: program.schedule.days,
    startTime: program.schedule.startTime,
    endTime: program.schedule.endTime,
    startDate: program.schedule.startDate
      ? new Date(program.schedule.startDate.toDate())
      : new Date(),
    endDate: program.schedule.endDate ? new Date(program.schedule.endDate.toDate()) : null,
    notifyStudents: true,
    notifyInstructors: true,
    reason: '',
    affectExistingSessions: false,
  });

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [affectedSessions, setAffectedSessions] = useState<ProgramSession[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [instructors, setInstructors] = useState<Instructor[]>([]);

  useEffect(() => {
    const fetchParticipants = async () => {
      if (!currentSchool?.id) return;

      try {
        // Fetch students
        const studentData: Student[] = [];
        for (const studentId of program.participants) {
          const studentDoc = await getDoc(
            doc(db, 'schools', currentSchool.id, 'students', studentId)
          );
          if (studentDoc.exists()) {
            studentData.push({
              id: studentDoc.id,
              ...studentDoc.data(),
            } as Student);
          }
        }
        setStudents(studentData);

        // Fetch instructors
        const instructorData: Instructor[] = [];
        for (const instructorId of program.instructors) {
          const instructorDoc = await getDoc(
            doc(db, 'schools', currentSchool.id, 'instructors', instructorId)
          );
          if (instructorDoc.exists()) {
            instructorData.push({
              id: instructorDoc.id,
              ...instructorDoc.data(),
            } as Instructor);
          }
        }
        setInstructors(instructorData);
      } catch (err) {
        console.error('Error fetching participants:', err);
      }
    };

    fetchParticipants();
  }, [currentSchool?.id, program.participants, program.instructors]);

  const handleInputChange = (field: keyof ScheduleChangeData, value: any) => {
    setScheduleChangeData({
      ...scheduleChangeData,
      [field]: value,
    });
  };

  const handleDayToggle = (day: WeekDay) => {
    const updatedDays = scheduleChangeData.days.includes(day)
      ? scheduleChangeData.days.filter((d) => d !== day)
      : [...scheduleChangeData.days, day];

    handleInputChange('days', updatedDays);
  };

  const calculateAffectedSessions = () => {
    if (!scheduleChangeData.affectExistingSessions) {
      return [];
    }

    // Find sessions that will be affected by the schedule change
    return sessions.filter((session) => {
      const sessionDate = session.date.toDate();

      // Check if the session is after the start date
      if (scheduleChangeData.startDate && isAfter(sessionDate, scheduleChangeData.startDate)) {
        // Check if the session is before the end date (if provided)
        if (!scheduleChangeData.endDate || isBefore(sessionDate, scheduleChangeData.endDate)) {
          return true;
        }
      }

      return false;
    });
  };

  const handlePreviewChanges = () => {
    const affected = calculateAffectedSessions();
    setAffectedSessions(affected);
    setPreviewDialogOpen(true);
  };

  const handleScheduleChange = async () => {
    if (!currentSchool?.id) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const batch = writeBatch(db);

      // Update program schedule
      const programRef = doc(db, 'schools', currentSchool.id, 'programs', program.id);
      batch.update(programRef, {
        'schedule.days': scheduleChangeData.days,
        'schedule.startTime': scheduleChangeData.startTime,
        'schedule.endTime': scheduleChangeData.endTime,
        'schedule.startDate': scheduleChangeData.startDate
          ? Timestamp.fromDate(scheduleChangeData.startDate)
          : null,
        'schedule.endDate': scheduleChangeData.endDate
          ? Timestamp.fromDate(scheduleChangeData.endDate)
          : null,
        updatedAt: serverTimestamp(),
      });

      // Update affected sessions if needed
      if (scheduleChangeData.affectExistingSessions) {
        const affected = calculateAffectedSessions();

        affected.forEach((session) => {
          const sessionRef = doc(
            db,
            'schools',
            currentSchool.id,
            'programs',
            program.id,
            'sessions',
            session.id
          );

          // Update session time
          batch.update(sessionRef, {
            startTime: scheduleChangeData.startTime,
            endTime: scheduleChangeData.endTime,
            updatedAt: serverTimestamp(),
          });
        });
      }

      // Create a notification record if notifications are enabled
      if (scheduleChangeData.notifyStudents || scheduleChangeData.notifyInstructors) {
        const notificationsRef = collection(db, 'schools', currentSchool.id, 'notifications');

        const recipients = [
          ...(scheduleChangeData.notifyStudents ? program.participants : []),
          ...(scheduleChangeData.notifyInstructors ? program.instructors : []),
        ];

        batch.set(doc(notificationsRef), {
          type: 'schedule_change',
          programId: program.id,
          programName: program.name,
          message:
            scheduleChangeData.reason ||
            t('scheduleChange.defaultMessage', 'The schedule for this program has been updated.'),
          recipients,
          read: {},
          createdAt: serverTimestamp(),
        });
      }

      await batch.commit();

      setSuccess(t('scheduleChange.success', 'Schedule updated successfully'));
      setConfirmDialogOpen(false);

      // Notify parent component about the update
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error updating schedule:', err);
      setError(t('messages.updateError', 'Failed to update schedule'));
    } finally {
      setLoading(false);
    }
  };

  const hasScheduleChanged = () => {
    return (
      !arraysEqual(scheduleChangeData.days, program.schedule.days) ||
      scheduleChangeData.startTime !== program.schedule.startTime ||
      scheduleChangeData.endTime !== program.schedule.endTime ||
      (scheduleChangeData.startDate &&
        program.schedule.startDate &&
        !isSameDay(scheduleChangeData.startDate, program.schedule.startDate.toDate())) ||
      (scheduleChangeData.endDate &&
        program.schedule.endDate &&
        !isSameDay(scheduleChangeData.endDate, program.schedule.endDate.toDate()))
    );
  };

  const arraysEqual = (a: any[], b: any[]) => {
    if (a.length !== b.length) return false;
    return a.every((val, index) => val === b[index]);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">{t('scheduleChange.title', 'Schedule Change')}</Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          {t('scheduleChange.currentSchedule', 'Current Schedule')}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center">
              <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body1">
                {t('scheduleChange.days', 'Days')}:{' '}
                {program.schedule.days.map((day) => t(`days.${day}`, day)).join(', ')}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center">
              <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body1">
                {t('scheduleChange.time', 'Time')}: {program.schedule.startTime} -{' '}
                {program.schedule.endTime}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center">
              <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body1">
                {t('scheduleChange.dateRange', 'Date Range')}:{' '}
                {program.schedule.startDate
                  ? format(program.schedule.startDate.toDate(), 'MMM d, yyyy')
                  : '-'}
                {program.schedule.endDate
                  ? ` - ${format(program.schedule.endDate.toDate(), 'MMM d, yyyy')}`
                  : ''}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          {t('scheduleChange.newSchedule', 'New Schedule')}
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              {t('fields.days', 'Days of the Week')}
            </Typography>

            <Box display="flex" flexWrap="wrap" gap={1}>
              {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(
                (day) => (
                  <Chip
                    key={day}
                    label={t(`days.${day}`, day)}
                    color={scheduleChangeData.days.includes(day as WeekDay) ? 'primary' : 'default'}
                    onClick={() => handleDayToggle(day as WeekDay)}
                    sx={{ textTransform: 'capitalize' }}
                  />
                )
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label={t('fields.startTime', 'Start Time')}
              type="time"
              value={scheduleChangeData.startTime}
              onChange={(e) => handleInputChange('startTime', e.target.value)}
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label={t('fields.endTime', 'End Time')}
              type="time"
              value={scheduleChangeData.endTime}
              onChange={(e) => handleInputChange('endTime', e.target.value)}
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={t('fields.startDate', 'Start Date')}
                value={scheduleChangeData.startDate}
                onChange={(date) => handleInputChange('startDate', date)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} md={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={t('fields.endDate', 'End Date')}
                value={scheduleChangeData.endDate}
                onChange={(date) => handleInputChange('endDate', date)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              {t('scheduleChange.options', 'Change Options')}
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={scheduleChangeData.affectExistingSessions}
                  onChange={(e) => handleInputChange('affectExistingSessions', e.target.checked)}
                />
              }
              label={t('scheduleChange.affectExisting', 'Apply changes to existing sessions')}
            />

            <Box mt={2}>
              <Typography variant="subtitle2" gutterBottom>
                {t('scheduleChange.notifications', 'Notifications')}
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={scheduleChangeData.notifyStudents}
                    onChange={(e) => handleInputChange('notifyStudents', e.target.checked)}
                  />
                }
                label={t('scheduleChange.notifyStudents', 'Notify students')}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={scheduleChangeData.notifyInstructors}
                    onChange={(e) => handleInputChange('notifyInstructors', e.target.checked)}
                  />
                }
                label={t('scheduleChange.notifyInstructors', 'Notify instructors')}
              />
            </Box>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label={t('scheduleChange.reason', 'Reason for Schedule Change')}
              value={scheduleChangeData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              fullWidth
              multiline
              rows={3}
              placeholder={t(
                'scheduleChange.reasonPlaceholder',
                'Explain why the schedule is changing...'
              )}
            />
          </Grid>
        </Grid>

        <Box mt={3} display="flex" gap={2} justifyContent="flex-end">
          <Button
            variant="outlined"
            onClick={handlePreviewChanges}
            disabled={!hasScheduleChanged() || loading}
          >
            {t('scheduleChange.preview', 'Preview Changes')}
          </Button>

          <Button
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={() => setConfirmDialogOpen(true)}
            disabled={!hasScheduleChanged() || loading}
          >
            {loading
              ? t('common:ui.saving', 'Saving...')
              : t('scheduleChange.save', 'Save Changes')}
          </Button>
        </Box>
      </Paper>

      {/* Preview Dialog */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{t('scheduleChange.previewTitle', 'Preview Schedule Changes')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('scheduleChange.summary', 'Summary of Changes')}
            </Typography>

            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {t('scheduleChange.currentSchedule', 'Current Schedule')}
                  </Typography>
                  <Typography variant="body2">
                    {t('scheduleChange.days', 'Days')}:{' '}
                    {program.schedule.days.map((day) => t(`days.${day}`, day)).join(', ')}
                  </Typography>
                  <Typography variant="body2">
                    {t('scheduleChange.time', 'Time')}: {program.schedule.startTime} -{' '}
                    {program.schedule.endTime}
                  </Typography>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    {t('scheduleChange.newSchedule', 'New Schedule')}
                  </Typography>
                  <Typography variant="body2">
                    {t('scheduleChange.days', 'Days')}:{' '}
                    {scheduleChangeData.days.map((day) => t(`days.${day}`, day)).join(', ')}
                  </Typography>
                  <Typography variant="body2">
                    {t('scheduleChange.time', 'Time')}: {scheduleChangeData.startTime} -{' '}
                    {scheduleChangeData.endTime}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>

            {scheduleChangeData.affectExistingSessions && (
              <>
                <Typography variant="subtitle2" gutterBottom>
                  {t('scheduleChange.affectedSessions', 'Affected Sessions')}
                </Typography>

                {affectedSessions.length === 0 ? (
                  <Alert severity="info">
                    {t(
                      'scheduleChange.noAffectedSessions',
                      'No existing sessions will be affected by this change'
                    )}
                  </Alert>
                ) : (
                  <List>
                    {affectedSessions.slice(0, 5).map((session) => (
                      <ListItem key={session.id}>
                        <ListItemAvatar>
                          <Avatar>
                            <CalendarIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={format(session.date.toDate(), 'EEEE, MMMM d, yyyy')}
                          secondary={`${program.schedule.startTime} → ${scheduleChangeData.startTime} - ${scheduleChangeData.endTime}`}
                        />
                      </ListItem>
                    ))}
                    {affectedSessions.length > 5 && (
                      <ListItem>
                        <ListItemText
                          primary={t(
                            'scheduleChange.moreAffected',
                            '... and {{count}} more sessions',
                            {
                              count: affectedSessions.length - 5,
                            }
                          )}
                        />
                      </ListItem>
                    )}
                  </List>
                )}
              </>
            )}

            {(scheduleChangeData.notifyStudents || scheduleChangeData.notifyInstructors) && (
              <>
                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  {t('scheduleChange.notifications', 'Notifications')}
                </Typography>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box display="flex" alignItems="center">
                      <NotificationsIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography>
                        {t(
                          'scheduleChange.notificationsSummary',
                          'Notifications will be sent to {{count}} people',
                          {
                            count:
                              (scheduleChangeData.notifyStudents ? students.length : 0) +
                              (scheduleChangeData.notifyInstructors ? instructors.length : 0),
                          }
                        )}
                      </Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    {scheduleChangeData.notifyStudents && students.length > 0 && (
                      <Box mb={2}>
                        <Typography variant="subtitle2" gutterBottom>
                          <PersonIcon sx={{ mr: 1, fontSize: 'small', verticalAlign: 'middle' }} />
                          {t('scheduleChange.studentsToNotify', 'Students ({{count}})', {
                            count: students.length,
                          })}
                        </Typography>
                        <Box display="flex" flexWrap="wrap" gap={1}>
                          {students.map((student) => (
                            <Chip
                              key={student.id}
                              label={`${student.firstName} ${student.lastName}`}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    )}

                    {scheduleChangeData.notifyInstructors && instructors.length > 0 && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          <SchoolIcon sx={{ mr: 1, fontSize: 'small', verticalAlign: 'middle' }} />
                          {t('scheduleChange.instructorsToNotify', 'Instructors ({{count}})', {
                            count: instructors.length,
                          })}
                        </Typography>
                        <Box display="flex" flexWrap="wrap" gap={1}>
                          {instructors.map((instructor) => (
                            <Chip
                              key={instructor.id}
                              label={`${instructor.firstName} ${instructor.lastName}`}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    )}
                  </AccordionDetails>
                </Accordion>
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>
            {t('common:ui.close', 'Close')}
          </Button>
          <Button
            onClick={() => {
              setPreviewDialogOpen(false);
              setConfirmDialogOpen(true);
            }}
            variant="contained"
            color="primary"
          >
            {t('scheduleChange.proceed', 'Proceed with Changes')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>
        <DialogTitle>{t('scheduleChange.confirmTitle', 'Confirm Schedule Change')}</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            {t(
              'scheduleChange.confirmText',
              'Are you sure you want to change the schedule for this program?'
            )}
          </Typography>
          {scheduleChangeData.affectExistingSessions && affectedSessions.length > 0 && (
            <Typography color="warning.main">
              {t(
                'scheduleChange.confirmAffectedSessions',
                'This will update {{count}} existing sessions.',
                {
                  count: affectedSessions.length,
                }
              )}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleScheduleChange}
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {loading ? t('common:ui.saving', 'Saving...') : t('common:ui.confirm', 'Confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProgramScheduleChange;
