import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Program, ProgramSession, AttendanceStatus } from '../../types/program';
import { Student } from '../../types/student';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { markStudentsForMakeup } from '../../services/makeupSessionService';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  CheckCircle as PresentIcon,
  Cancel as AbsentIcon,
  HourglassEmpty as ExcusedIcon,
  FileDownload as DownloadIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

interface ProgramAttendanceProps {
  program: Program;
  sessions: ProgramSession[];
}

const ProgramAttendance: React.FC<ProgramAttendanceProps> = ({ program, sessions }) => {
  const { t } = useTranslation('programs');
  const { currentSchool } = useSchool();

  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSession, setSelectedSession] = useState<string>('');
  const [excuseDialogOpen, setExcuseDialogOpen] = useState(false);
  const [excuseNote, setExcuseNote] = useState('');
  const [selectedStudentId, setSelectedStudentId] = useState<string>('');

  // Sort sessions by date
  const sortedSessions = [...sessions].sort(
    (a, b) => a.date.toDate().getTime() - b.date.toDate().getTime()
  );

  // Set the first session as selected by default when sessions change
  useEffect(() => {
    if (sortedSessions.length > 0 && !selectedSession) {
      setSelectedSession(sortedSessions[0].id);
    }
  }, [sortedSessions, selectedSession]);

  // Fetch students only when school or participants change
  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch students
        const studentPromises = program.participants.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'students', id))
        );

        const studentDocs = await Promise.all(studentPromises);
        const studentData = studentDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Student[];

        setStudents(studentData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, program.participants, t]);

  const handleSessionChange = (event: SelectChangeEvent) => {
    setSelectedSession(event.target.value);
  };

  const getAttendanceStatus = (sessionId: string, studentId: string): AttendanceStatus => {
    const session = sessions.find((s) => s.id === sessionId);
    if (!session) return 'absent';

    return session.attendance[studentId] || 'absent';
  };

  const handleAttendanceChange = async (studentId: string, status: AttendanceStatus) => {
    if (!currentSchool?.id || !selectedSession) return;

    // If marking as excused, open the dialog to enter a note
    if (status === 'excused') {
      setSelectedStudentId(studentId);
      setExcuseNote('');
      setExcuseDialogOpen(true);
      return;
    }

    // Otherwise, update attendance directly
    await updateAttendance(studentId, status);
  };

  const updateAttendance = async (studentId: string, status: AttendanceStatus, note?: string) => {
    if (!currentSchool?.id || !selectedSession) return;

    try {
      const session = sessions.find((s) => s.id === selectedSession);
      if (!session) return;

      // Update the session in Firestore
      const sessionRef = doc(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'sessions',
        selectedSession
      );

      // Create a new attendance object with the updated status
      const updatedAttendance = {
        ...session.attendance,
        [studentId]: status,
      };

      // Prepare the update data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: Record<string, any> = {
        attendance: updatedAttendance,
        updatedAt: new Date(),
      };

      // If the status is 'absent', mark the student for makeup
      if (status === 'absent') {
        // Add to missedStudents if not already there
        const missedStudents = session.missedStudents || [];
        if (!missedStudents.includes(studentId)) {
          await markStudentsForMakeup(currentSchool.id, program.id, selectedSession, [studentId]);
        }
      }

      // If a note was provided, add it to the notes field
      if (note && status === 'excused') {
        const studentName = students.find((s) => s.id === studentId)?.firstName || studentId;
        const noteWithTimestamp = `${new Date().toLocaleString()} - ${studentName} excused: ${note}`;

        updateData.notes = session.notes
          ? `${session.notes}\n${noteWithTimestamp}`
          : noteWithTimestamp;
      }

      await updateDoc(sessionRef, updateData);

      // This would be handled by a context or state management in a real app
      // For now, we'll just reload the page to see the changes
      window.location.reload();
    } catch (err) {
      console.error('Error updating attendance:', err);
      setError(t('messages.updateError', 'Failed to update attendance'));
    }
  };

  const handleBulkAttendanceChange = async (status: AttendanceStatus) => {
    if (!currentSchool?.id || !selectedSession || students.length === 0) return;

    try {
      const session = sessions.find((s) => s.id === selectedSession);
      if (!session) return;

      // Update the session in Firestore
      const sessionRef = doc(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'sessions',
        selectedSession
      );

      // Create a new attendance object with all students marked with the same status
      const updatedAttendance: Record<string, AttendanceStatus> = {};
      students.forEach((student) => {
        updatedAttendance[student.id] = status;
      });

      await updateDoc(sessionRef, {
        attendance: updatedAttendance,
        updatedAt: new Date(),
      });

      // If marking all as absent, mark them for makeup
      if (status === 'absent') {
        await markStudentsForMakeup(
          currentSchool.id,
          program.id,
          selectedSession,
          students.map((student) => student.id)
        );
      }

      // No need to update local state since we're reloading the page

      // This would be handled by a context or state management in a real app
      // For now, we'll just reload the page to see the changes
      window.location.reload();
    } catch (err) {
      console.error('Error updating attendance:', err);
      setError(t('messages.updateError', 'Failed to update attendance'));
    }
  };

  const getAttendanceIcon = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return <PresentIcon color="success" />;
      case 'absent':
        return <AbsentIcon color="error" />;
      case 'excused':
        return <ExcusedIcon color="warning" />;
      default:
        return <AbsentIcon color="error" />;
    }
  };

  const getCurrentSession = () => {
    return sessions.find((session) => session.id === selectedSession);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  if (sessions.length === 0) {
    return (
      <Alert severity="info">
        {t('messages.noSessions', 'No sessions found for this program')}
      </Alert>
    );
  }

  const currentSession = getCurrentSession();

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">{t('tabs.attendance', 'Attendance')}</Typography>

        <Box display="flex" gap={1}>
          <Button
            variant="contained"
            color="success"
            onClick={() => handleBulkAttendanceChange('present')}
          >
            {t('attendance.markAllAs', 'Mark All as {{status}}', {
              status: t('attendance.present', 'Present'),
            })}
          </Button>

          <Button variant="outlined" startIcon={<DownloadIcon />}>
            {t('actions.exportAttendance', 'Export Attendance')}
          </Button>
        </Box>
      </Box>

      <Box mb={3}>
        <FormControl fullWidth>
          <InputLabel>{t('fields.session', 'Session')}</InputLabel>
          <Select
            value={selectedSession}
            label={t('fields.session', 'Session')}
            onChange={handleSessionChange}
          >
            {sortedSessions.map((session) => (
              <MenuItem key={session.id} value={session.id}>
                {format(session.date.toDate(), 'EEEE, MMMM d, yyyy')} ({program.schedule.startTime}{' '}
                - {program.schedule.endTime})
                {session.isMakeup && ` - ${t('sessionType.makeup', 'Makeup')}`}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {currentSession && (
        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom>
            {t('attendance.attendanceFor', 'Attendance for {{date}}', {
              date: format(currentSession.date.toDate(), 'EEEE, MMMM d, yyyy'),
            })}
          </Typography>

          <Typography variant="body2" color="text.secondary" paragraph>
            {currentSession.notes || t('noNotes', 'No notes for this session')}
          </Typography>
        </Box>
      )}

      {students.length === 0 ? (
        <Alert severity="info">
          {t('messages.noParticipants', 'No participants enrolled in this program')}
        </Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('fields.name', 'Name')}</TableCell>
                <TableCell>{t('fields.status', 'Status')}</TableCell>
                <TableCell>{t('fields.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {students.map((student) => {
                const attendanceStatus = getAttendanceStatus(selectedSession, student.id);

                return (
                  <TableRow key={student.id}>
                    <TableCell>
                      <Typography variant="body1">
                        {student.firstName} {student.lastName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        {getAttendanceIcon(attendanceStatus)}
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {t(`attendance.${attendanceStatus}`, attendanceStatus)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={t('attendance.present', 'Present')}>
                        <IconButton
                          size="small"
                          color={attendanceStatus === 'present' ? 'success' : 'default'}
                          onClick={() => handleAttendanceChange(student.id, 'present')}
                        >
                          <PresentIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('attendance.absent', 'Absent')}>
                        <IconButton
                          size="small"
                          color={attendanceStatus === 'absent' ? 'error' : 'default'}
                          onClick={() => handleAttendanceChange(student.id, 'absent')}
                        >
                          <AbsentIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('attendance.excused', 'Excused')}>
                        <IconButton
                          size="small"
                          color={attendanceStatus === 'excused' ? 'warning' : 'default'}
                          onClick={() => handleAttendanceChange(student.id, 'excused')}
                        >
                          <ExcusedIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Excuse Dialog */}
      <Dialog open={excuseDialogOpen} onClose={() => setExcuseDialogOpen(false)}>
        <DialogTitle>{t('attendance.excuseDialogTitle', 'Excuse Student')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('attendance.excuseDialogText', 'Please provide a reason for excusing this student:')}
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label={t('attendance.excuseReason', 'Reason')}
            fullWidth
            multiline
            rows={3}
            value={excuseNote}
            onChange={(e) => setExcuseNote(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExcuseDialogOpen(false)}>
            {t('actions.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={() => {
              setExcuseDialogOpen(false);
              updateAttendance(selectedStudentId, 'excused', excuseNote);
            }}
            variant="contained"
            color="primary"
          >
            {t('actions.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProgramAttendance;
