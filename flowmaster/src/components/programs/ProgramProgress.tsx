import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Program } from '../../types/program';
import { Student } from '../../types/student';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Button,
  TextField,
  Alert,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  IconButton,
  Tooltip,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Flag as FlagIcon,
  Edit as EditIcon,
  EmojiFlags as GoalIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

interface ProgramProgressProps {
  program: Program;
}

const ProgramProgress: React.FC<ProgramProgressProps> = ({ program: initialProgram }) => {
  const { t } = useTranslation('programs');
  const { currentSchool } = useSchool();

  const [program, setProgram] = useState<Program>(initialProgram);

  const [students, setStudents] = useState<Student[]>([]);
  const [studentProgress, setStudentProgress] = useState<Record<string, Record<string, boolean>>>(
    {}
  );
  const [studentGoals, setStudentGoals] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newSkill, setNewSkill] = useState('');
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null);
  const [newGoal, setNewGoal] = useState('');
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [skillToDelete, setSkillToDelete] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const calculateProgress = (studentId: string, skills: string[]): number => {
    if (!skills.length) return 0;

    const studentSkills = studentProgress[studentId] || {};
    const completedSkills = skills.filter((skill) => studentSkills[skill]);

    return Math.round((completedSkills.length / skills.length) * 100);
  };

  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch students
        const studentPromises = program.participants.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'students', id))
        );

        const studentDocs = await Promise.all(studentPromises);
        const studentData = studentDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Student[];

        // Fetch progress data for each student
        const progressData: Record<string, Record<string, boolean>> = {};

        for (const student of studentData) {
          const progressRef = doc(
            db,
            'schools',
            currentSchool.id,
            'programs',
            program.id,
            'progress',
            student.id
          );

          const progressDoc = await getDoc(progressRef);

          if (progressDoc.exists()) {
            const data = progressDoc.data();
            progressData[student.id] = data.skills || {};
            setStudentGoals((prevGoals) => ({
              ...prevGoals,
              [student.id]: data.personalGoal || '',
            }));
          } else {
            progressData[student.id] = {};
            setStudentGoals((prevGoals) => ({
              ...prevGoals,
              [student.id]: '',
            }));
          }
        }

        setStudents(studentData);
        setStudentProgress(progressData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, program.participants, program.id, t]);

  const handleAddSkill = async () => {
    if (!newSkill.trim() || !currentSchool?.id) return;

    try {
      // Update the program in Firestore
      const programRef = doc(db, 'schools', currentSchool.id, 'programs', program.id);

      // Create a new skills array with the new skill
      const updatedSkills = [...program.progress.skills, newSkill.trim()];

      await updateDoc(programRef, {
        'progress.skills': updatedSkills,
        updatedAt: new Date(),
      });

      // This would be handled by a context or state management in a real app
      // For now, we'll just reload the page to see the changes
      window.location.reload();
    } catch (err) {
      console.error('Error adding skill:', err);
      setError(t('messages.updateError', 'Failed to update program'));
    }

    setNewSkill('');
  };

  const handleDeleteSkill = async (skill: string) => {
    if (!currentSchool?.id) return;

    try {
      setLoading(true);

      // Create a new array without the skill to delete
      const updatedSkills = program.progress.skills.filter((s) => s !== skill);

      // Update the program document
      await updateDoc(doc(db, 'schools', currentSchool.id, 'programs', program.id), {
        'progress.skills': updatedSkills,
      });

      // Update local state
      setProgram({
        ...program,
        progress: {
          ...program.progress,
          skills: updatedSkills,
        },
      });

      setSnackbarMessage(t('messages.skillDeleted', 'Skill deleted successfully'));
      setSnackbarOpen(true);
      setLoading(false);
    } catch (err) {
      console.error('Error deleting skill:', err);
      setError(t('messages.deleteSkillError', 'Failed to delete skill'));
      setLoading(false);
    }
  };

  const openDeleteConfirmation = (skill: string) => {
    setSkillToDelete(skill);
    setConfirmDeleteOpen(true);
  };

  const handleConfirmDelete = () => {
    handleDeleteSkill(skillToDelete);
    setConfirmDeleteOpen(false);
  };

  const handleToggleSkill = async (skill: string, studentId: string, completed: boolean) => {
    if (!currentSchool?.id) return;

    try {
      // Get the student's progress document or create it if it doesn't exist
      const progressRef = doc(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'progress',
        studentId
      );

      const progressDoc = await getDoc(progressRef);

      if (progressDoc.exists()) {
        // Update existing progress document
        const progressData = progressDoc.data();
        const skills = progressData.skills || {};

        await updateDoc(progressRef, {
          skills: {
            ...skills,
            [skill]: completed,
          },
          updatedAt: new Date(),
        });
      } else {
        // Create new progress document
        await updateDoc(progressRef, {
          studentId,
          skills: {
            [skill]: completed,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // This would be handled by a context or state management in a real app
      // For now, we'll just reload the page to see the changes
      window.location.reload();
    } catch (err) {
      console.error('Error updating skill progress:', err);
      setError(t('messages.updateError', 'Failed to update progress'));
    }
  };

  const handleUpdateStudentGoal = async (studentId: string, goal: string) => {
    if (!currentSchool?.id) return;

    try {
      // Get the student's progress document or create it if it doesn't exist
      const progressRef = doc(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'progress',
        studentId
      );

      const progressDoc = await getDoc(progressRef);

      if (progressDoc.exists()) {
        // Update existing progress document
        await updateDoc(progressRef, {
          personalGoal: goal,
          updatedAt: new Date(),
        });
      } else {
        // Create new progress document
        await updateDoc(progressRef, {
          studentId,
          personalGoal: goal,
          skills: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // Update local state
      setStudentGoals((prevGoals) => ({
        ...prevGoals,
        [studentId]: goal,
      }));

      // Close the dialog
      setSelectedStudent(null);
      setNewGoal('');
    } catch (err) {
      console.error('Error updating student goal:', err);
      setError(t('messages.updateError', 'Failed to update goal'));
    }
  };

  const handleSkillChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewSkill(event.target.value);
  };

  const handleGoalChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewGoal(event.target.value);
  };

  const openGoalDialog = (studentId: string, currentGoal: string) => {
    setSelectedStudent(studentId);
    setNewGoal(currentGoal);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Skills */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('fields.skills', 'Skills')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Box display="flex" alignItems="center" mb={2}>
                <TextField
                  fullWidth
                  size="small"
                  label={t('actions.addSkill', 'Add Skill')}
                  value={newSkill}
                  onChange={handleSkillChange}
                  sx={{ mr: 1 }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleAddSkill}
                  startIcon={<AddIcon />}
                >
                  {t('actions.add', 'Add')}
                </Button>
              </Box>

              {program.progress.skills.length === 0 ? (
                <Alert severity="info">
                  {t('messages.noSkills', 'No skills defined for this program')}
                </Alert>
              ) : (
                <List>
                  {program.progress.skills.map((skill, index) => (
                    <ListItem
                      key={index}
                      secondaryAction={
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => openDeleteConfirmation(skill)}
                          aria-label={t('actions.delete', 'Delete')}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      }
                    >
                      <ListItemText primary={skill} />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Goals */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('fields.goals', 'Goals')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Box mb={3}>
                <Typography variant="subtitle1" gutterBottom display="flex" alignItems="center">
                  <FlagIcon color="primary" sx={{ mr: 1 }} />
                  {t('fields.programGoals', 'Program Goals')}
                </Typography>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  value={program.progress.goals}
                  // This would update the program in Firestore
                  onChange={() => {}}
                />
              </Box>

              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  {t('fields.progress', 'Progress')}
                </Typography>

                <Box sx={{ width: '100%', mb: 2 }}>
                  <LinearProgress
                    variant="determinate"
                    value={30} // This would be calculated based on completed skills
                    sx={{ height: 10, borderRadius: 5 }}
                  />
                  <Box display="flex" justifyContent="space-between" mt={1}>
                    <Typography variant="body2" color="text.secondary">
                      {t('progress.started', 'Started')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      30% {t('progress.complete', 'Complete')}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Student Progress */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('fields.studentProgress', 'Student Progress')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              {students.length === 0 ? (
                <Alert severity="info">
                  {t('messages.noParticipants', 'No participants enrolled in this program')}
                </Alert>
              ) : (
                <Grid container spacing={2}>
                  {students.map((student) => (
                    <Grid item xs={12} sm={6} md={4} key={student.id}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle1" gutterBottom>
                          {student.firstName} {student.lastName}
                        </Typography>

                        <Box
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                          mb={1}
                        >
                          <Typography variant="body2" color="text.secondary">
                            {t('progress.personalGoal', 'Personal Goal')}:
                          </Typography>
                          <Tooltip title={t('actions.editGoal', 'Edit Goal')}>
                            <IconButton
                              size="small"
                              onClick={() =>
                                openGoalDialog(student.id, studentGoals[student.id] || '')
                              }
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>

                        <Typography variant="body2" mb={2} sx={{ fontStyle: 'italic' }}>
                          {studentGoals[student.id]
                            ? studentGoals[student.id]
                            : t('progress.noPersonalGoal', 'No personal goal set')}
                        </Typography>

                        {program.progress.skills.length > 0 ? (
                          <>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              {t('progress.skillsProgress', 'Skills Progress')}:
                            </Typography>

                            <List dense>
                              {program.progress.skills.map((skill, index) => (
                                <ListItem key={index} disablePadding>
                                  <ListItemIcon>
                                    <Checkbox
                                      edge="start"
                                      checked={studentProgress[student.id]?.[skill] || false}
                                      onChange={(e) =>
                                        handleToggleSkill(skill, student.id, e.target.checked)
                                      }
                                      tabIndex={-1}
                                      disableRipple
                                    />
                                  </ListItemIcon>
                                  <ListItemText primary={skill} />
                                </ListItem>
                              ))}
                            </List>

                            <Box sx={{ width: '100%', mt: 2, mb: 1 }}>
                              <LinearProgress
                                variant="determinate"
                                value={calculateProgress(student.id, program.progress.skills)}
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                align="right"
                                mt={0.5}
                              >
                                {calculateProgress(student.id, program.progress.skills)}%{' '}
                                {t('progress.complete', 'Complete')}
                              </Typography>
                            </Box>
                          </>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            {t('progress.noSkills', 'No skills defined for this program')}
                          </Typography>
                        )}
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      {/* Student Goal Dialog */}
      <Dialog open={selectedStudent !== null} onClose={() => setSelectedStudent(null)}>
        <DialogTitle>{t('progress.setPersonalGoal', 'Set Personal Goal')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t(
              'progress.personalGoalDescription',
              'Set a personal goal for this student in this program:'
            )}
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label={t('progress.goal', 'Goal')}
            fullWidth
            multiline
            rows={3}
            value={newGoal}
            onChange={handleGoalChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedStudent(null)}>{t('actions.cancel', 'Cancel')}</Button>
          <Button
            onClick={() => selectedStudent && handleUpdateStudentGoal(selectedStudent, newGoal)}
            variant="contained"
            color="primary"
            startIcon={<GoalIcon />}
          >
            {t('actions.saveGoal', 'Save Goal')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Skill Confirmation Dialog */}
      <Dialog open={confirmDeleteOpen} onClose={() => setConfirmDeleteOpen(false)}>
        <DialogTitle>{t('progress.confirmDeleteSkill', 'Confirm Delete Skill')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t(
              'progress.deleteSkillWarning',
              'Are you sure you want to delete this skill? This action cannot be undone and will remove all progress tracking for this skill.'
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteOpen(false)}>
            {t('actions.cancel', 'Cancel')}
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            {t('actions.delete', 'Delete')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default ProgramProgress;
