import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import { createProgram, updateProgram } from '../../services/programService';
import { Program, ProgramFormData, WeekDay } from '../../types/program';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  FormHelperText,
  Paper,
  Divider,
  Alert,
  CircularProgress,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Autocomplete,
  SelectChangeEvent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, parse } from 'date-fns';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';

interface ProgramFormProps {
  program?: Program;
  students: Student[];
  instructors: Instructor[];
  onSuccess?: (programId: string) => void;
}

const ProgramForm: React.FC<ProgramFormProps> = ({ program, students, instructors, onSuccess }) => {
  const { t } = useTranslation('programs');
  const navigate = useNavigate();
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const defaultFormData: ProgramFormData = {
    name: '',
    type: 'seasonal',
    description: '',
    schedule: {
      days: [],
      startTime: '09:00',
      endTime: '10:00',
      startDate: new Date(),
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)),
    },
    location: '',
    participants: [],
    instructors: [],
    pricing: {
      totalFee: 0,
      currency: 'EUR',
    },
    progress: {
      skills: [],
      goals: '',
    },
  };

  const [formData, setFormData] = useState<ProgramFormData>(defaultFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (program) {
      // Convert Firestore data to form data
      const programFormData: ProgramFormData = {
        name: program.name,
        type: program.type,
        description: program.description,
        schedule: {
          days: program.schedule.days,
          startTime: program.schedule.startTime,
          endTime: program.schedule.endTime,
          startDate: program.schedule.startDate.toDate(),
          endDate: program.schedule.endDate.toDate(),
        },
        location: program.location || '',
        participants: program.participants,
        instructors: program.instructors,
        pricing: {
          totalFee: program.pricing.totalFee,
          currency: program.pricing.currency,
        },
        progress: {
          skills: program.progress.skills,
          goals: program.progress.goals,
        },
      };

      setFormData(programFormData);
    }
  }, [program]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    const { name, value } = e.target;

    // Handle nested properties
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData((prev) => {
        const parentObj = prev[parent as keyof ProgramFormData];
        if (typeof parentObj === 'object' && parentObj !== null) {
          return {
            ...prev,
            [parent]: {
              ...parentObj,
              [child]: value,
            },
          };
        }
        return prev;
      });
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    // Clear error for this field if it exists
    if (formErrors[name]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleTypeChange = (event: SelectChangeEvent) => {
    setFormData((prev) => ({
      ...prev,
      type: event.target.value as 'yearly' | 'seasonal' | 'camp',
    }));
  };

  const handleDayToggle = (day: WeekDay) => {
    setFormData((prev) => {
      const days = [...prev.schedule.days];
      const index = days.indexOf(day);

      if (index === -1) {
        days.push(day);
      } else {
        days.splice(index, 1);
      }

      return {
        ...prev,
        schedule: {
          ...prev.schedule,
          days,
        },
      };
    });
  };

  const handleDateChange = (date: Date | null, field: 'startDate' | 'endDate') => {
    if (date) {
      setFormData((prev) => ({
        ...prev,
        schedule: {
          ...prev.schedule,
          [field]: date,
        },
      }));
    }
  };

  const handleTimeChange = (time: Date | null, field: 'startTime' | 'endTime') => {
    if (time) {
      const timeString = format(time, 'HH:mm');
      setFormData((prev) => ({
        ...prev,
        schedule: {
          ...prev.schedule,
          [field]: timeString,
        },
      }));
    }
  };

  const handleParticipantsChange = (_event: React.SyntheticEvent, value: Student[]) => {
    setFormData((prev) => ({
      ...prev,
      participants: value.map((student) => student.id),
    }));
  };

  const handleInstructorsChange = (_event: React.SyntheticEvent, value: Instructor[]) => {
    setFormData((prev) => ({
      ...prev,
      instructors: value.map((instructor) => instructor.id),
    }));
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = t('validation.nameRequired', 'Program name is required');
    }

    if (!formData.description.trim()) {
      errors.description = t('validation.descriptionRequired', 'Description is required');
    }

    if (formData.schedule.days.length === 0) {
      errors['schedule.days'] = t('validation.daysRequired', 'At least one day must be selected');
    }

    if (formData.schedule.startDate >= formData.schedule.endDate) {
      errors['schedule.endDate'] = t(
        'validation.endDateAfterStart',
        'End date must be after start date'
      );
    }

    // Only validate instructors if they are required
    // Commenting out this validation for now as it's causing issues
    // if (formData.instructors.length === 0) {
    //   errors.instructors = t(
    //     'validation.instructorsRequired',
    //     'At least one instructor must be assigned'
    //   );
    // }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentSchool?.id) {
      setError(t('validation.noSchool', 'No school selected'));
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (program) {
        // Update existing program
        await updateProgram(currentSchool.id, program.id, formData);
        setSuccess(t('messages.updateSuccess', 'Program updated successfully'));

        if (onSuccess) {
          onSuccess(program.id);
        }
      } else {
        // Create new program
        const programId = await createProgram(currentSchool.id, formData);
        setSuccess(t('messages.createSuccess', 'Program created successfully'));

        if (onSuccess) {
          onSuccess(programId);
        } else {
          // Redirect to the program details page
          setTimeout(() => {
            navigate(`/programs/${programId}`);
          }, 1500);
        }
      }
    } catch (err) {
      console.error('Error saving program:', err);
      setError(t('messages.saveError', 'Failed to save program'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/programs');
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box component="form" onSubmit={handleSubmit}>
        <Typography variant="h5" gutterBottom>
          {program ? t('actions.edit', 'Edit Program') : t('actions.create', 'Create Program')}
        </Typography>

        <Divider sx={{ mb: 3 }} />

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              {t('sections.basicInfo', 'Basic Information')}
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="name"
              label={t('fields.name', 'Program Name')}
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!formErrors.name}
              helperText={formErrors.name}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>{t('fields.type', 'Program Type')}</InputLabel>
              <Select
                value={formData.type}
                onChange={handleTypeChange}
                label={t('fields.type', 'Program Type')}
              >
                <MenuItem value="yearly">{t('programTypes.yearly', 'Yearly Program')}</MenuItem>
                <MenuItem value="seasonal">
                  {t('programTypes.seasonal', 'Seasonal Program')}
                </MenuItem>
                <MenuItem value="camp">{t('programTypes.camp', 'Camp')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              name="description"
              label={t('fields.description', 'Description')}
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={3}
              required
              error={!!formErrors.description}
              helperText={formErrors.description}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="location"
              label={t('fields.location', 'Location')}
              value={formData.location}
              onChange={handleInputChange}
              fullWidth
            />
          </Grid>

          {/* Schedule */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Divider />
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              {t('sections.schedule', 'Schedule')}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <FormControl
              component="fieldset"
              error={!!formErrors['schedule.days']}
              sx={{ width: '100%' }}
            >
              <Typography variant="subtitle2" gutterBottom>
                {t('fields.days', 'Days')}
              </Typography>
              <FormGroup row>
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(
                  (day) => (
                    <FormControlLabel
                      key={day}
                      control={
                        <Checkbox
                          checked={formData.schedule.days.includes(day as WeekDay)}
                          onChange={() => handleDayToggle(day as WeekDay)}
                        />
                      }
                      label={t(`weekdays.${day}`, day)}
                    />
                  )
                )}
              </FormGroup>
              {formErrors['schedule.days'] && (
                <FormHelperText>{formErrors['schedule.days']}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={t('fields.startDate', 'Start Date')}
                value={formData.schedule.startDate}
                onChange={(date) => handleDateChange(date, 'startDate')}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={t('fields.endDate', 'End Date')}
                value={formData.schedule.endDate}
                onChange={(date) => handleDateChange(date, 'endDate')}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    error: !!formErrors['schedule.endDate'],
                    helperText: formErrors['schedule.endDate'],
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <TimePicker
                label={t('fields.startTime', 'Start Time')}
                value={parse(formData.schedule.startTime, 'HH:mm', new Date())}
                onChange={(time) => handleTimeChange(time, 'startTime')}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <TimePicker
                label={t('fields.endTime', 'End Time')}
                value={parse(formData.schedule.endTime, 'HH:mm', new Date())}
                onChange={(time) => handleTimeChange(time, 'endTime')}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          {/* Participants and Instructors */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Divider />
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              {t('sections.people', 'People')}
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Autocomplete
              multiple
              options={students}
              getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
              value={students.filter((student) => formData.participants.includes(student.id))}
              onChange={handleParticipantsChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('fields.participants', 'Participants')}
                  placeholder={t('placeholders.selectParticipants', 'Select participants')}
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={`${option.firstName} ${option.lastName}`}
                    {...getTagProps({ index })}
                    key={option.id}
                  />
                ))
              }
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Autocomplete
              multiple
              options={instructors}
              getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
              value={instructors.filter((instructor) =>
                formData.instructors.includes(instructor.id)
              )}
              onChange={handleInstructorsChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('fields.instructors', 'Instructors')}
                  placeholder={t('placeholders.selectInstructors', 'Select instructors')}
                  error={!!formErrors.instructors}
                  helperText={formErrors.instructors}
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={`${option.firstName} ${option.lastName}`}
                    {...getTagProps({ index })}
                    key={option.id}
                  />
                ))
              }
            />
          </Grid>

          {/* Pricing */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Divider />
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              {t('sections.pricing', 'Pricing')}
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="pricing.totalFee"
              label={t('fields.totalFee', 'Total Fee')}
              type="number"
              value={formData.pricing.totalFee}
              onChange={handleInputChange}
              fullWidth
              InputProps={{
                inputProps: { min: 0 },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel>{t('fields.currency', 'Currency')}</InputLabel>
              <Select
                name="pricing.currency"
                value={formData.pricing.currency}
                onChange={handleInputChange}
                label={t('fields.currency', 'Currency')}
              >
                <MenuItem value="EUR">EUR</MenuItem>
                <MenuItem value="USD">USD</MenuItem>
                <MenuItem value="GBP">GBP</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="pricing.installments"
              label={t('fields.installments', 'Installments')}
              type="number"
              value={formData.pricing.installments || ''}
              onChange={handleInputChange}
              fullWidth
              InputProps={{
                inputProps: { min: 0 },
              }}
              helperText={t('fields.installmentsHelp', 'Number of allowed installments')}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={t('fields.dueDate', 'Payment Due Date')}
                value={formData.pricing.dueDate || null}
                onChange={(date) => {
                  setFormData({
                    ...formData,
                    pricing: {
                      ...formData.pricing,
                      dueDate: date || null,
                    },
                  });
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    helperText: t('fields.dueDateHelp', 'When payment is due'),
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="pricing.earlyBirdDiscount"
              label={t('fields.earlyBirdDiscount', 'Early Bird Discount')}
              type="number"
              value={formData.pricing.earlyBirdDiscount || ''}
              onChange={handleInputChange}
              fullWidth
              InputProps={{
                inputProps: { min: 0 },
              }}
              helperText={t('fields.earlyBirdDiscountHelp', 'Discount for early payment')}
            />
          </Grid>

          {/* Progress */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Divider />
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              {t('sections.progress', 'Progress')}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              name="progress.goals"
              label={t('fields.goals', 'Goals')}
              value={formData.progress.goals}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={2}
            />
          </Grid>

          {/* Form Actions */}
          <Grid item xs={12} sx={{ mt: 3 }}>
            <Divider />
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button variant="outlined" onClick={handleCancel} disabled={loading}>
                {t('actions.cancel', 'Cancel')}
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                {program
                  ? t('actions.update', 'Update Program')
                  : t('actions.create', 'Create Program')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default ProgramForm;
