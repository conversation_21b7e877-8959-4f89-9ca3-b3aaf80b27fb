import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program, ProgramSession, WeekDay } from '../../types/program';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  orderBy,
  writeBatch,
  Timestamp,
  serverTimestamp,
} from 'firebase/firestore';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ContentCopy as DuplicateIcon,
  Event as EventIcon,
  CalendarMonth as CalendarIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { format, addDays, addWeeks, isSameDay, isAfter, isBefore, parseISO } from 'date-fns';

interface ProgramBulkSessionsProps {
  program: Program;
  sessions: ProgramSession[];
  onUpdate?: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bulk-sessions-tabpanel-${index}`}
      aria-labelledby={`bulk-sessions-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `bulk-sessions-tab-${index}`,
    'aria-controls': `bulk-sessions-tabpanel-${index}`,
  };
}

const ProgramBulkSessions: React.FC<ProgramBulkSessionsProps> = ({
  program,
  sessions,
  onUpdate,
}) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  // Selected sessions for bulk operations
  const [selectedSessions, setSelectedSessions] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Bulk edit state
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editNotes, setEditNotes] = useState('');
  const [applyToAll, setApplyToAll] = useState(false);

  // Bulk create state
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(
    new Date(new Date().setMonth(new Date().getMonth() + 1))
  );
  const [selectedDays, setSelectedDays] = useState<WeekDay[]>([]);
  const [sessionTime, setSessionTime] = useState({
    startTime: program.schedule.startTime,
    endTime: program.schedule.endTime,
  });

  // Bulk delete state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteRange, setDeleteRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null,
  });

  // Sort sessions by date
  const sortedSessions = [...sessions].sort(
    (a, b) => a.date.toDate().getTime() - b.date.toDate().getTime()
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectAll(event.target.checked);
    if (event.target.checked) {
      setSelectedSessions(sessions.map((session) => session.id));
    } else {
      setSelectedSessions([]);
    }
  };

  const handleSelectSession = (sessionId: string) => {
    if (selectedSessions.includes(sessionId)) {
      setSelectedSessions(selectedSessions.filter((id) => id !== sessionId));
    } else {
      setSelectedSessions([...selectedSessions, sessionId]);
    }
  };

  const handleBulkEdit = async () => {
    if (!currentSchool?.id || selectedSessions.length === 0) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const batch = writeBatch(db);

      for (const sessionId of selectedSessions) {
        const sessionRef = doc(
          db,
          'schools',
          currentSchool.id,
          'programs',
          program.id,
          'sessions',
          sessionId
        );

        batch.update(sessionRef, {
          notes: editNotes,
          updatedAt: serverTimestamp(),
        });
      }

      await batch.commit();

      setSuccess(
        t('bulkSessions.editSuccess', 'Successfully updated {{count}} sessions', {
          count: selectedSessions.length,
        })
      );

      // Reset state
      setEditDialogOpen(false);
      setEditNotes('');
      setSelectedSessions([]);
      setSelectAll(false);

      // Notify parent component
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error updating sessions:', err);
      setError(t('messages.updateError', 'Failed to update sessions'));
    } finally {
      setLoading(false);
    }
  };

  const handleBulkCreate = async () => {
    if (!currentSchool?.id || !startDate || !endDate || selectedDays.length === 0) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Generate sessions based on selected days and date range
      const newSessions: Omit<ProgramSession, 'id'>[] = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const dayName = format(currentDate, 'EEEE').toLowerCase() as WeekDay;

        if (selectedDays.includes(dayName)) {
          // Check if a session already exists on this date
          const existingSession = sessions.find((session) =>
            isSameDay(session.date.toDate(), currentDate)
          );

          if (!existingSession) {
            newSessions.push({
              programId: program.id,
              date: Timestamp.fromDate(new Date(currentDate)),
              attendance: {},
              notes: '',
              skills: {},
              isMakeup: false,
              createdAt: Timestamp.fromDate(new Date()),
              updatedAt: Timestamp.fromDate(new Date()),
            });
          }
        }

        // Move to next day
        currentDate = addDays(currentDate, 1);
      }

      if (newSessions.length === 0) {
        setError(t('bulkSessions.noNewSessions', 'No new sessions to create'));
        setLoading(false);
        return;
      }

      // Batch write sessions to Firestore
      const batch = writeBatch(db);
      const sessionsRef = collection(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'sessions'
      );

      newSessions.forEach((session) => {
        const newSessionRef = doc(sessionsRef);
        batch.set(newSessionRef, session);
      });

      await batch.commit();

      setSuccess(
        t('bulkSessions.createSuccess', 'Successfully created {{count}} new sessions', {
          count: newSessions.length,
        })
      );

      // Reset state
      setSelectedDays([]);

      // Notify parent component
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error creating sessions:', err);
      setError(t('messages.createError', 'Failed to create sessions'));
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (!currentSchool?.id || !deleteRange.startDate || !deleteRange.endDate) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Find sessions within the date range
      const sessionsToDelete = sessions.filter((session) => {
        const sessionDate = session.date.toDate();
        return (
          (isAfter(sessionDate, deleteRange.startDate!) ||
            isSameDay(sessionDate, deleteRange.startDate!)) &&
          (isBefore(sessionDate, deleteRange.endDate!) ||
            isSameDay(sessionDate, deleteRange.endDate!))
        );
      });

      if (sessionsToDelete.length === 0) {
        setError(
          t('bulkSessions.noSessionsToDelete', 'No sessions found in the selected date range')
        );
        setLoading(false);
        return;
      }

      // Batch delete sessions
      const batch = writeBatch(db);

      sessionsToDelete.forEach((session) => {
        const sessionRef = doc(
          db,
          'schools',
          currentSchool.id,
          'programs',
          program.id,
          'sessions',
          session.id
        );
        batch.delete(sessionRef);
      });

      await batch.commit();

      setSuccess(
        t('bulkSessions.deleteSuccess', 'Successfully deleted {{count}} sessions', {
          count: sessionsToDelete.length,
        })
      );

      // Reset state
      setDeleteDialogOpen(false);
      setDeleteRange({ startDate: null, endDate: null });

      // Notify parent component
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error deleting sessions:', err);
      setError(t('messages.deleteError', 'Failed to delete sessions'));
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicateWeek = async () => {
    if (!currentSchool?.id || selectedSessions.length === 0) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the selected sessions
      const selectedSessionsData = sessions.filter((session) =>
        selectedSessions.includes(session.id)
      );

      // Create new sessions one week later
      const newSessions: Omit<ProgramSession, 'id'>[] = [];

      for (const session of selectedSessionsData) {
        const newDate = addWeeks(session.date.toDate(), 1);

        // Check if a session already exists on this date
        const existingSession = sessions.find((s) => isSameDay(s.date.toDate(), newDate));

        if (!existingSession) {
          newSessions.push({
            programId: program.id,
            date: Timestamp.fromDate(newDate),
            attendance: {},
            notes: session.notes,
            skills: {},
            isMakeup: false,
            createdAt: Timestamp.fromDate(new Date()),
            updatedAt: Timestamp.fromDate(new Date()),
          });
        }
      }

      if (newSessions.length === 0) {
        setError(
          t(
            'bulkSessions.noDuplicateSessions',
            'No new sessions to create (sessions already exist)'
          )
        );
        setLoading(false);
        return;
      }

      // Batch write sessions to Firestore
      const batch = writeBatch(db);
      const sessionsRef = collection(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'sessions'
      );

      newSessions.forEach((session) => {
        const newSessionRef = doc(sessionsRef);
        batch.set(newSessionRef, session);
      });

      await batch.commit();

      setSuccess(
        t('bulkSessions.duplicateSuccess', 'Successfully duplicated {{count}} sessions', {
          count: newSessions.length,
        })
      );

      // Reset state
      setSelectedSessions([]);
      setSelectAll(false);

      // Notify parent component
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error duplicating sessions:', err);
      setError(t('messages.createError', 'Failed to duplicate sessions'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">{t('bulkSessions.title', 'Bulk Session Operations')}</Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="bulk sessions tabs">
          <Tab label={t('bulkSessions.edit', 'Edit Sessions')} {...a11yProps(0)} />
          <Tab label={t('bulkSessions.create', 'Create Sessions')} {...a11yProps(1)} />
          <Tab label={t('bulkSessions.delete', 'Delete Sessions')} {...a11yProps(2)} />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom>
            {t('bulkSessions.selectSessions', 'Select sessions to edit or duplicate')}
          </Typography>

          <Box display="flex" gap={2} mb={2}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              onClick={() => setEditDialogOpen(true)}
              disabled={selectedSessions.length === 0 || loading}
            >
              {t('bulkSessions.editSelected', 'Edit Selected')}
            </Button>

            <Button
              variant="outlined"
              color="secondary"
              startIcon={<DuplicateIcon />}
              onClick={handleDuplicateWeek}
              disabled={selectedSessions.length === 0 || loading}
            >
              {t('bulkSessions.duplicateWeek', 'Duplicate to Next Week')}
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectAll}
                      onChange={handleSelectAll}
                      indeterminate={
                        selectedSessions.length > 0 && selectedSessions.length < sessions.length
                      }
                    />
                  </TableCell>
                  <TableCell>{t('fields.date', 'Date')}</TableCell>
                  <TableCell>{t('fields.time', 'Time')}</TableCell>
                  <TableCell>{t('fields.notes', 'Notes')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedSessions.map((session) => (
                  <TableRow
                    key={session.id}
                    selected={selectedSessions.includes(session.id)}
                    onClick={() => handleSelectSession(session.id)}
                    hover
                    sx={{ cursor: 'pointer' }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox checked={selectedSessions.includes(session.id)} />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        {format(session.date.toDate(), 'EEEE, MMMM d, yyyy')}
                        {session.isMakeup && (
                          <Chip
                            label={t('sessionType.makeup', 'Makeup')}
                            size="small"
                            color="info"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {program.schedule.startTime} - {program.schedule.endTime}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" noWrap sx={{ maxWidth: 300 }}>
                        {session.notes || t('noNotes', 'No notes')}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom>
            {t('bulkSessions.createSessions', 'Create multiple sessions')}
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={t('fields.startDate', 'Start Date')}
                  value={startDate}
                  onChange={(date) => setStartDate(date)}
                  slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={t('fields.endDate', 'End Date')}
                  value={endDate}
                  onChange={(date) => setEndDate(date)}
                  slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                {t('fields.days', 'Days of the Week')}
              </Typography>

              <Box display="flex" flexWrap="wrap" gap={1}>
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(
                  (day) => (
                    <Chip
                      key={day}
                      label={t(`days.${day}`, day)}
                      color={selectedDays.includes(day as WeekDay) ? 'primary' : 'default'}
                      onClick={() => {
                        if (selectedDays.includes(day as WeekDay)) {
                          setSelectedDays(selectedDays.filter((d) => d !== day));
                        } else {
                          setSelectedDays([...selectedDays, day as WeekDay]);
                        }
                      }}
                      sx={{ textTransform: 'capitalize' }}
                    />
                  )
                )}
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label={t('fields.startTime', 'Start Time')}
                type="time"
                value={sessionTime.startTime}
                onChange={(e) => setSessionTime({ ...sessionTime, startTime: e.target.value })}
                fullWidth
                margin="normal"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label={t('fields.endTime', 'End Time')}
                type="time"
                value={sessionTime.endTime}
                onChange={(e) => setSessionTime({ ...sessionTime, endTime: e.target.value })}
                fullWidth
                margin="normal"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          <Box mt={3} display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleBulkCreate}
              disabled={!startDate || !endDate || selectedDays.length === 0 || loading}
            >
              {loading
                ? t('common:ui.creating', 'Creating...')
                : t('bulkSessions.createSessions', 'Create Sessions')}
            </Button>
          </Box>
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom>
            {t('bulkSessions.deleteSessions', 'Delete sessions in a date range')}
          </Typography>

          <Alert severity="warning" sx={{ mb: 3 }}>
            {t(
              'bulkSessions.deleteWarning',
              'Warning: This will permanently delete all sessions in the selected date range. This action cannot be undone.'
            )}
          </Alert>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={t('fields.startDate', 'Start Date')}
                  value={deleteRange.startDate}
                  onChange={(date) => setDeleteRange({ ...deleteRange, startDate: date })}
                  slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={t('fields.endDate', 'End Date')}
                  value={deleteRange.endDate}
                  onChange={(date) => setDeleteRange({ ...deleteRange, endDate: date })}
                  slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>

          <Box mt={3} display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={() => setDeleteDialogOpen(true)}
              disabled={!deleteRange.startDate || !deleteRange.endDate || loading}
            >
              {t('bulkSessions.deleteInRange', 'Delete Sessions in Range')}
            </Button>
          </Box>
        </Box>
      </TabPanel>

      {/* Edit Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t('bulkSessions.editSessions', 'Edit Sessions')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('bulkSessions.editingCount', 'Editing {{count}} sessions', {
                count: selectedSessions.length,
              })}
            </Typography>

            <TextField
              label={t('fields.notes', 'Notes')}
              value={editNotes}
              onChange={(e) => setEditNotes(e.target.value)}
              fullWidth
              multiline
              rows={4}
              margin="normal"
            />

            <FormControlLabel
              control={
                <Switch checked={applyToAll} onChange={(e) => setApplyToAll(e.target.checked)} />
              }
              label={t('bulkSessions.applyToAll', 'Apply to all future sessions')}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button onClick={handleBulkEdit} variant="contained" color="primary" disabled={loading}>
            {loading ? t('common:ui.saving', 'Saving...') : t('common:ui.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>{t('bulkSessions.confirmDelete', 'Confirm Deletion')}</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            {t(
              'bulkSessions.confirmDeleteText',
              'Are you sure you want to delete all sessions between {{startDate}} and {{endDate}}?',
              {
                startDate: deleteRange.startDate
                  ? format(deleteRange.startDate, 'MMMM d, yyyy')
                  : '',
                endDate: deleteRange.endDate ? format(deleteRange.endDate, 'MMMM d, yyyy') : '',
              }
            )}
          </Typography>
          <Typography color="error">
            {t('bulkSessions.confirmDeleteWarning', 'This action cannot be undone.')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button onClick={handleBulkDelete} variant="contained" color="error" disabled={loading}>
            {loading ? t('common:ui.deleting', 'Deleting...') : t('common:ui.delete', 'Delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProgramBulkSessions;
