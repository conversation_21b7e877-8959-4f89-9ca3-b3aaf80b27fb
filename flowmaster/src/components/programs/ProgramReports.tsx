import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program, ProgramSession } from '../../types/program';
import { Student } from '../../types/student';
import { doc, getDoc } from 'firebase/firestore';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Assessment as AssessmentIcon,
  CalendarMonth as CalendarIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { startOfMonth, endOfMonth, subMonths, startOfQuarter, endOfQuarter } from 'date-fns';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  TooltipItem,
} from 'chart.js';
import { Doughnut, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

interface ProgramReportsProps {
  program: Program;
  sessions: ProgramSession[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
    </div>
  );
};

const a11yProps = (index: number) => {
  return {
    id: `report-tab-${index}`,
    'aria-controls': `report-tabpanel-${index}`,
  };
};

const ProgramReports: React.FC<ProgramReportsProps> = ({ program, sessions }) => {
  const { t } = useTranslation('programs');
  const { currentSchool } = useSchool();

  const [students, setStudents] = useState<Student[]>([]);
  const [studentProgress, setStudentProgress] = useState<Record<string, Record<string, boolean>>>(
    {}
  );
  const [loading, setLoading] = useState(true);
  const [exportLoading, setExportLoading] = useState(false);
  const [filterLoading, setFilterLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [reportPeriod, setReportPeriod] = useState('all');

  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch students
        const studentPromises = program.participants.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'students', id))
        );

        const studentDocs = await Promise.all(studentPromises);
        const studentData = studentDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Student[];

        // Fetch progress data for each student
        const progressData: Record<string, Record<string, boolean>> = {};

        for (const student of studentData) {
          const progressRef = doc(
            db,
            'schools',
            currentSchool.id,
            'programs',
            program.id,
            'progress',
            student.id
          );

          const progressDoc = await getDoc(progressRef);

          if (progressDoc.exists()) {
            const data = progressDoc.data();
            progressData[student.id] = data.skills || {};
          } else {
            progressData[student.id] = {};
          }
        }

        setStudents(studentData);
        setStudentProgress(progressData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, program.participants, program.id, t]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePeriodChange = (event: SelectChangeEvent) => {
    try {
      setFilterLoading(true);
      setReportPeriod(event.target.value);
      // Small delay to show loading indicator
      setTimeout(() => {
        setFilterLoading(false);
      }, 300);
    } catch (err) {
      console.error('Error changing period:', err);
      setError(t('messages.filterError', 'Failed to filter data'));
      setFilterLoading(false);
    }
  };

  const exportToCSV = () => {
    try {
      setExportLoading(true);
      // Create CSV content
      let csvContent = '';

      // Export based on current tab
      if (tabValue === 0) {
        // Summary tab - export both attendance and progress summary
        csvContent = `${t('reports.summary', 'Summary')} - ${program.name}\n\n`;
        csvContent += `${t('reports.attendanceOverview', 'Attendance Overview')}\n`;
        csvContent += `${t('attendance.present', 'Present')},${stats.present}\n`;
        csvContent += `${t('attendance.absent', 'Absent')},${stats.absent}\n`;
        csvContent += `${t('attendance.excused', 'Excused')},${stats.excused}\n`;
        csvContent += `${t('reports.attendanceRate', 'Attendance Rate')},${stats.attendanceRate}%\n\n`;

        csvContent += `${t('reports.progressOverview', 'Progress Overview')}\n`;
        csvContent += `${t('reports.averageCompletion', 'Average Completion')},${progressStats.averageCompletion}%\n`;
        csvContent += `${t('reports.skill', 'Skill')},${t('reports.completionRate', 'Completion Rate')}\n`;

        progressStats.skillCompletionRates.forEach((item) => {
          csvContent += `${item.skill},${item.completionRate}%\n`;
        });
      } else if (tabValue === 1) {
        // Attendance tab
        csvContent = `${t('reports.attendanceDetails', 'Attendance Details')} - ${program.name}\n\n`;
        csvContent += `${t('fields.student', 'Student')},${t('attendance.present', 'Present')},${t('attendance.absent', 'Absent')},${t('attendance.excused', 'Excused')},${t('reports.attendanceRate', 'Attendance Rate')}\n`;

        students.forEach((student) => {
          const studentStats = stats.byStudent[student.id] || {
            present: 0,
            absent: 0,
            excused: 0,
            rate: 0,
          };

          csvContent += `${student.firstName} ${student.lastName},${studentStats.present},${studentStats.absent},${studentStats.excused},${studentStats.rate}%\n`;
        });
      } else if (tabValue === 2) {
        // Progress tab
        csvContent = `${t('reports.progressDetails', 'Progress Details')} - ${program.name}\n\n`;

        // Header row with student and skills
        csvContent += `${t('fields.student', 'Student')},`;
        program.progress.skills.forEach((skill) => {
          csvContent += `${skill},`;
        });
        csvContent += `${t('reports.completion', 'Completion')}\n`;

        // Data rows
        students.forEach((student) => {
          const skills = studentProgress[student.id] || {};
          const completedCount = program.progress.skills.filter((skill) => skills[skill]).length;
          const completionRate =
            program.progress.skills.length > 0
              ? Math.round((completedCount / program.progress.skills.length) * 100)
              : 0;

          csvContent += `${student.firstName} ${student.lastName},`;

          program.progress.skills.forEach((skill) => {
            csvContent += `${skills[skill] ? '✓' : '-'},`;
          });

          csvContent += `${completionRate}%\n`;
        });
      }

      // Create a blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `${program.name}_${t(`reports.${tabValue === 0 ? 'summary' : tabValue === 1 ? 'attendance' : 'progress'}`, tabValue === 0 ? 'Summary' : tabValue === 1 ? 'Attendance' : 'Progress')}_${new Date().toISOString().split('T')[0]}.csv`
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Small delay to show loading indicator
      setTimeout(() => {
        setExportLoading(false);
      }, 500);
    } catch (err) {
      console.error('Error exporting data:', err);
      setError(t('messages.exportError', 'Failed to export data'));
      setExportLoading(false);
    }
  };

  // Filter data based on selected period
  const filterDataByPeriod = (sessionsToFilter: ProgramSession[]): ProgramSession[] => {
    const now = new Date();

    if (reportPeriod === 'month') {
      const monthStart = startOfMonth(now);
      const monthEnd = endOfMonth(now);
      return sessionsToFilter.filter((session) => {
        const sessionDate = session.date.toDate();
        return sessionDate >= monthStart && sessionDate <= monthEnd;
      });
    } else if (reportPeriod === 'quarter') {
      const quarterStart = startOfQuarter(now);
      const quarterEnd = endOfQuarter(now);
      return sessionsToFilter.filter((session) => {
        const sessionDate = session.date.toDate();
        return sessionDate >= quarterStart && sessionDate <= quarterEnd;
      });
    } else if (reportPeriod === 'lastMonth') {
      const lastMonthStart = startOfMonth(subMonths(now, 1));
      const lastMonthEnd = endOfMonth(subMonths(now, 1));
      return sessionsToFilter.filter((session) => {
        const sessionDate = session.date.toDate();
        return sessionDate >= lastMonthStart && sessionDate <= lastMonthEnd;
      });
    } else {
      // 'all' or default
      return sessionsToFilter;
    }
  };

  const calculateAttendanceStats = (sessionsToUse: ProgramSession[]) => {
    if (sessionsToUse.length === 0 || students.length === 0) {
      return {
        present: 0,
        absent: 0,
        excused: 0,
        attendanceRate: 0,
        byStudent: {} as Record<
          string,
          { present: number; absent: number; excused: number; rate: number }
        >,
      };
    }

    let totalPresent = 0;
    let totalAbsent = 0;
    let totalExcused = 0;
    let totalPossible = 0;

    // Track attendance by student
    const byStudent: Record<
      string,
      { present: number; absent: number; excused: number; rate: number }
    > = {};

    students.forEach((student) => {
      byStudent[student.id] = { present: 0, absent: 0, excused: 0, rate: 0 };
    });

    sessionsToUse.forEach((session) => {
      students.forEach((student) => {
        totalPossible++;
        const status = session.attendance[student.id];

        // Update overall counts
        if (status === 'present') {
          totalPresent++;
          byStudent[student.id].present++;
        } else if (status === 'absent') {
          totalAbsent++;
          byStudent[student.id].absent++;
        } else if (status === 'excused') {
          totalExcused++;
          byStudent[student.id].excused++;
        } else {
          totalAbsent++; // Count as absent if no status
          byStudent[student.id].absent++;
        }
      });
    });

    // Calculate rates for each student
    Object.keys(byStudent).forEach((studentId) => {
      const student = byStudent[studentId];
      const total = student.present + student.absent + student.excused;
      student.rate = total > 0 ? Math.round((student.present / total) * 100) : 0;
    });

    return {
      present: totalPresent,
      absent: totalAbsent,
      excused: totalExcused,
      attendanceRate: Math.round((totalPresent / totalPossible) * 100),
      byStudent,
    };
  };

  const calculateProgressStats = () => {
    if (program.progress.skills.length === 0 || students.length === 0) {
      return {
        averageCompletion: 0,
        skillCompletionRates: [] as { skill: string; completionRate: number }[],
      };
    }

    const skillCompletionCounts: Record<string, number> = {};
    program.progress.skills.forEach((skill) => {
      skillCompletionCounts[skill] = 0;
    });

    // Count completions for each skill
    Object.entries(studentProgress).forEach(([, skills]) => {
      Object.entries(skills).forEach(([skill, completed]) => {
        if (completed && program.progress.skills.includes(skill)) {
          skillCompletionCounts[skill]++;
        }
      });
    });

    // Calculate completion rates
    const skillCompletionRates = program.progress.skills.map((skill) => ({
      skill,
      completionRate: Math.round((skillCompletionCounts[skill] / students.length) * 100),
    }));

    // Calculate average completion across all skills
    const totalCompletions = Object.values(skillCompletionCounts).reduce(
      (sum, count) => sum + count,
      0
    );
    const totalPossible = program.progress.skills.length * students.length;
    const averageCompletion =
      totalPossible > 0 ? Math.round((totalCompletions / totalPossible) * 100) : 0;

    return {
      averageCompletion,
      skillCompletionRates,
    };
  };

  const filteredSessions = filterDataByPeriod(sessions);
  const stats = calculateAttendanceStats(filteredSessions);
  const progressStats = calculateProgressStats();

  // Chart data
  const attendanceChartData = {
    labels: [
      t('attendance.present', 'Present'),
      t('attendance.absent', 'Absent'),
      t('attendance.excused', 'Excused'),
    ] as string[],
    datasets: [
      {
        label: t('reports.attendanceDistribution', 'Attendance Distribution') as string,
        data: [stats.present, stats.absent, stats.excused],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',
          'rgba(255, 99, 132, 0.6)',
          'rgba(255, 206, 86, 0.6)',
        ],
        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(255, 206, 86, 1)'],
        borderWidth: 1,
      },
    ],
  };

  const progressChartData = {
    labels: progressStats.skillCompletionRates.map((item) => item.skill) as string[],
    datasets: [
      {
        label: t('reports.completionRate', 'Completion Rate (%)') as string,
        data: progressStats.skillCompletionRates.map((item) => item.completionRate),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: t('reports.completionPercentage', 'Completion Percentage') as string,
        },
      },
      x: {
        title: {
          display: true,
          text: t('reports.skills', 'Skills') as string,
        },
      },
    },
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: t('reports.skillsCompletion', 'Skills Completion') as string,
      },
    },
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              {t('actions.dismiss', 'Dismiss')}
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">{t('tabs.reports', 'Reports')}</Typography>

        <Box display="flex" gap={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="report-period-label">{t('reports.period', 'Period')}</InputLabel>
            <Select
              labelId="report-period-label"
              value={reportPeriod}
              label={t('reports.period', 'Period')}
              onChange={handlePeriodChange}
              disabled={filterLoading}
              IconComponent={filterLoading ? () => <CircularProgress size={20} /> : undefined}
            >
              <MenuItem value="all">{t('reports.allTime', 'All Time')}</MenuItem>
              <MenuItem value="month">{t('reports.thisMonth', 'This Month')}</MenuItem>
              <MenuItem value="lastMonth">{t('reports.lastMonth', 'Last Month')}</MenuItem>
              <MenuItem value="quarter">{t('reports.thisQuarter', 'This Quarter')}</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={exportLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
            onClick={exportToCSV}
            disabled={exportLoading}
          >
            {exportLoading
              ? t('actions.exporting', 'Exporting...')
              : t('actions.exportReport', 'Export Report')}
          </Button>
        </Box>
      </Box>

      <Paper sx={{ width: '100%', mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab
            icon={<AssessmentIcon />}
            label={t('reports.summary', 'Summary')}
            {...a11yProps(0)}
          />
          <Tab
            icon={<CalendarIcon />}
            label={t('reports.attendance', 'Attendance')}
            {...a11yProps(1)}
          />
          <Tab icon={<PersonIcon />} label={t('reports.progress', 'Progress')} {...a11yProps(2)} />
        </Tabs>

        <Divider />

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {t('reports.attendanceOverview', 'Attendance Overview')}
                  </Typography>

                  <Box height={300} display="flex" justifyContent="center" alignItems="center">
                    <Doughnut
                      data={attendanceChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                          },
                          tooltip: {
                            callbacks: {
                              label: function (tooltipItem: TooltipItem<'doughnut'>) {
                                const label = tooltipItem.label || '';
                                const value = tooltipItem.raw as number;
                                const total = stats.present + stats.absent + stats.excused;
                                const percentage =
                                  total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                              },
                            },
                          },
                        },
                      }}
                    />
                  </Box>

                  <Box mt={2} display="flex" justifyContent="center">
                    <Typography variant="h5" color="primary">
                      {stats.attendanceRate}% {t('reports.attendanceRate', 'Attendance Rate')}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {t('reports.progressOverview', 'Progress Overview')}
                  </Typography>

                  <Box height={300} display="flex" justifyContent="center" alignItems="center">
                    <Bar data={progressChartData} options={barChartOptions} />
                  </Box>

                  <Box mt={2} display="flex" justifyContent="center">
                    <Typography variant="h5" color="primary">
                      {progressStats.averageCompletion}%{' '}
                      {t('reports.averageCompletion', 'Average Completion')}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            {t('reports.attendanceDetails', 'Attendance Details')}
          </Typography>

          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('fields.student', 'Student')}</TableCell>
                  <TableCell align="center">{t('attendance.present', 'Present')}</TableCell>
                  <TableCell align="center">{t('attendance.absent', 'Absent')}</TableCell>
                  <TableCell align="center">{t('attendance.excused', 'Excused')}</TableCell>
                  <TableCell align="center">
                    {t('reports.attendanceRate', 'Attendance Rate')}
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {students.map((student) => {
                  // Get attendance stats for this student from our calculated stats
                  const studentStats = stats.byStudent[student.id] || {
                    present: 0,
                    absent: 0,
                    excused: 0,
                    rate: 0,
                  };

                  return (
                    <TableRow key={student.id}>
                      <TableCell>
                        {student.firstName} {student.lastName}
                      </TableCell>
                      <TableCell align="center">{studentStats.present}</TableCell>
                      <TableCell align="center">{studentStats.absent}</TableCell>
                      <TableCell align="center">{studentStats.excused}</TableCell>
                      <TableCell align="center">{studentStats.rate}%</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            {t('reports.progressDetails', 'Progress Details')}
          </Typography>

          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('fields.student', 'Student')}</TableCell>
                  {program.progress.skills.map((skill) => (
                    <TableCell key={skill} align="center">
                      {skill}
                    </TableCell>
                  ))}
                  <TableCell align="center">{t('reports.completion', 'Completion')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {students.map((student) => {
                  const skills = studentProgress[student.id] || {};
                  const completedCount = program.progress.skills.filter(
                    (skill) => skills[skill]
                  ).length;
                  const completionRate =
                    program.progress.skills.length > 0
                      ? Math.round((completedCount / program.progress.skills.length) * 100)
                      : 0;

                  return (
                    <TableRow key={student.id}>
                      <TableCell>
                        {student.firstName} {student.lastName}
                      </TableCell>
                      {program.progress.skills.map((skill) => (
                        <TableCell key={skill} align="center">
                          {skills[skill] ? '✓' : '–'}
                        </TableCell>
                      ))}
                      <TableCell align="center">{completionRate}%</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default ProgramReports;
