import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import { getPrograms } from '../../services/programService';
import { Program, ProgramType } from '../../types/program';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import {
  Add as AddIcon,
  Event as EventIcon,
  School as SchoolIcon,
  EmojiEvents as CampIcon,
} from '@mui/icons-material';

interface ProgramListProps {
  onProgramClick?: (program: Program) => void;
}

const ProgramList: React.FC<ProgramListProps> = ({ onProgramClick }) => {
  const { t } = useTranslation('programs');
  const navigate = useNavigate();
  const { currentSchool } = useSchool();

  const [programs, setPrograms] = useState<Program[]>([]);
  const [filteredPrograms, setFilteredPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    const fetchPrograms = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);
        const programsData = await getPrograms(currentSchool.id);
        setPrograms(programsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching programs:', err);
        setError(t('messages.fetchError', 'Failed to fetch programs'));
        setLoading(false);
      }
    };

    fetchPrograms();
  }, [currentSchool?.id, t]);

  useEffect(() => {
    // Apply filters
    let result = [...programs];

    // Filter by type
    if (typeFilter !== 'all') {
      result = result.filter((program) => program.type === typeFilter);
    }

    // Filter by status
    if (statusFilter !== 'all') {
      result = result.filter((program) => program.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (program) =>
          program.name.toLowerCase().includes(query) ||
          program.description.toLowerCase().includes(query)
      );
    }

    setFilteredPrograms(result);
  }, [programs, typeFilter, statusFilter, searchQuery]);

  const handleTypeFilterChange = (event: SelectChangeEvent) => {
    setTypeFilter(event.target.value);
  };

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleCreateProgram = () => {
    navigate('/programs/create');
  };

  const handleProgramClick = (program: Program) => {
    if (onProgramClick) {
      onProgramClick(program);
    } else {
      navigate(`/programs/${program.id}`);
    }
  };

  const getProgramTypeIcon = (type: ProgramType) => {
    switch (type) {
      case 'yearly':
        return <SchoolIcon />;
      case 'seasonal':
        return <EventIcon />;
      case 'camp':
        return <CampIcon />;
      default:
        return <EventIcon />;
    }
  };

  const getProgramTypeColor = (
    type: ProgramType
  ): 'primary' | 'success' | 'warning' | 'default' => {
    switch (type) {
      case 'yearly':
        return 'primary';
      case 'seasonal':
        return 'success';
      case 'camp':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getProgramStatusColor = (status: string): 'success' | 'info' | 'default' => {
    switch (status) {
      case 'active':
        return 'success';
      case 'completed':
        return 'info';
      case 'archived':
        return 'default';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2">
          {t('title', 'Programs')}
        </Typography>

        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateProgram}
        >
          {t('actions.create', 'Create Program')}
        </Button>
      </Box>

      <Box mb={3} display="flex" flexDirection={{ xs: 'column', md: 'row' }} gap={2}>
        <TextField
          label={t('filters.search', 'Search')}
          variant="outlined"
          size="small"
          fullWidth
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{ flexGrow: 1 }}
        />

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel id="type-filter-label">{t('filters.type', 'Filter by Type')}</InputLabel>
          <Select
            labelId="type-filter-label"
            id="type-filter"
            value={typeFilter}
            label={t('filters.type', 'Filter by Type')}
            onChange={handleTypeFilterChange}
          >
            <MenuItem value="all">{t('filters.all', 'All Programs')}</MenuItem>
            <MenuItem value="yearly">{t('programTypes.yearly', 'Yearly Program')}</MenuItem>
            <MenuItem value="seasonal">{t('programTypes.seasonal', 'Seasonal Program')}</MenuItem>
            <MenuItem value="camp">{t('programTypes.camp', 'Camp')}</MenuItem>
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel id="status-filter-label">
            {t('filters.status', 'Filter by Status')}
          </InputLabel>
          <Select
            labelId="status-filter-label"
            id="status-filter"
            value={statusFilter}
            label={t('filters.status', 'Filter by Status')}
            onChange={handleStatusFilterChange}
          >
            <MenuItem value="all">{t('filters.all', 'All Programs')}</MenuItem>
            <MenuItem value="active">{t('status.active', 'Active')}</MenuItem>
            <MenuItem value="completed">{t('status.completed', 'Completed')}</MenuItem>
            <MenuItem value="archived">{t('status.archived', 'Archived')}</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {filteredPrograms.length === 0 ? (
        <Alert severity="info" sx={{ my: 2 }}>
          {t('messages.noPrograms', 'No programs found')}
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {filteredPrograms.map((program) => (
            <Grid item xs={12} sm={6} md={4} key={program.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  },
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Chip
                      icon={getProgramTypeIcon(program.type)}
                      label={t(`programTypes.${program.type}`, program.type)}
                      color={getProgramTypeColor(program.type)}
                      size="small"
                    />
                    <Chip
                      label={t(`status.${program.status}`, program.status)}
                      color={getProgramStatusColor(program.status)}
                      size="small"
                      variant="outlined"
                    />
                  </Box>

                  <Typography variant="h6" component="h3" gutterBottom>
                    {program.name}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    {program.description.length > 100
                      ? `${program.description.substring(0, 100)}...`
                      : program.description}
                  </Typography>
                </CardContent>

                <CardActions>
                  <Button size="small" onClick={() => handleProgramClick(program)}>
                    {t('actions.view', 'View Program')}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default ProgramList;
