import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Program } from '../../types/program';
import { Student } from '../../types/student';
import { Instructor } from '../../types/instructor';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon,
  CalendarMonth as CalendarIcon,
  AttachMoney as MoneyIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';

interface ProgramOverviewProps {
  program: Program;
}

const ProgramOverview: React.FC<ProgramOverviewProps> = ({ program }) => {
  const { t } = useTranslation('programs');
  const { currentSchool } = useSchool();

  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPeople = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch instructors
        const instructorPromises = program.instructors.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'instructors', id))
        );

        const instructorDocs = await Promise.all(instructorPromises);
        const instructorData = instructorDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Instructor[];

        setInstructors(instructorData);

        // Fetch students
        const studentPromises = program.participants.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'students', id))
        );

        const studentDocs = await Promise.all(studentPromises);
        const studentData = studentDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Student[];

        setStudents(studentData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching people:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchPeople();
  }, [currentSchool?.id, program.instructors, program.participants, t]);

  const formatDays = (days: string[]) => {
    return days.map((day) => t(`weekdays.${day}`, day)).join(', ');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Program Details */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('sections.details', 'Program Details')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Typography variant="body1" paragraph>
                {program.description}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <LocationIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      <strong>{t('fields.location', 'Location')}:</strong>{' '}
                      {program.location || t('notSpecified', 'Not specified')}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <CalendarIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      <strong>{t('fields.schedule', 'Schedule')}:</strong>{' '}
                      {formatDays(program.schedule.days)}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <TimeIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      <strong>{t('fields.time', 'Time')}:</strong> {program.schedule.startTime} -{' '}
                      {program.schedule.endTime}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <MoneyIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      <strong>{t('fields.totalFee', 'Total Fee')}:</strong>{' '}
                      {program.pricing.totalFee} {program.pricing.currency}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box mt={3}>
                <Typography variant="subtitle1" gutterBottom>
                  <FlagIcon color="action" sx={{ mr: 1, verticalAlign: 'middle' }} />
                  {t('fields.goals', 'Goals')}
                </Typography>
                <Typography variant="body2">
                  {program.progress.goals || t('noGoalsSpecified', 'No goals specified')}
                </Typography>
              </Box>

              {program.progress.skills.length > 0 && (
                <Box mt={3}>
                  <Typography variant="subtitle1" gutterBottom>
                    <SchoolIcon color="action" sx={{ mr: 1, verticalAlign: 'middle' }} />
                    {t('fields.skills', 'Skills')}
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1}>
                    {program.progress.skills.map((skill, index) => (
                      <Chip key={index} label={skill} size="small" />
                    ))}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* People */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('sections.people', 'People')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                {t('fields.instructors', 'Instructors')}
              </Typography>

              <List dense>
                {instructors.map((instructor) => (
                  <ListItem key={instructor.id}>
                    <ListItemAvatar>
                      <Avatar>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${instructor.firstName} ${instructor.lastName}`}
                      secondary={instructor.email}
                    />
                  </ListItem>
                ))}
              </List>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                {t('fields.participants', 'Participants')} ({students.length})
              </Typography>

              {students.length > 0 ? (
                <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                  {students.map((student) => (
                    <ListItem key={student.id}>
                      <ListItemAvatar>
                        <Avatar>
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={`${student.firstName} ${student.lastName}`}
                        secondary={student.email}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  {t('noParticipants', 'No participants enrolled')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Statistics */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('sections.statistics', 'Statistics')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={3}>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {program.participants.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('stats.participants', 'Participants')}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {program.instructors.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('stats.instructors', 'Instructors')}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {/* This would be calculated from sessions */}
                      {program.schedule.days.length *
                        Math.ceil(
                          (program.schedule.endDate.toDate().getTime() -
                            program.schedule.startDate.toDate().getTime()) /
                            (7 * 24 * 60 * 60 * 1000)
                        )}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('stats.sessions', 'Sessions')}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {/* This would be calculated from attendance */}
                      0%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('stats.attendance', 'Attendance')}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProgramOverview;
