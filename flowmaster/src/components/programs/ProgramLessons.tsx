import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program, ProgramSession } from '../../types/program';
import { Lesson } from '../../types/lesson';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  setDoc,
  Timestamp,
  addDoc,
} from 'firebase/firestore';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Link as LinkIcon,
  Event as EventIcon,
  Delete as DeleteIcon,
  EventNote as EventNoteIcon,
} from '@mui/icons-material';
import LessonRescheduleDialog from '../lessons/LessonRescheduleDialog';
import { format } from 'date-fns';

interface ProgramLessonsProps {
  program: Program;
  sessions: ProgramSession[];
}

const ProgramLessons: React.FC<ProgramLessonsProps> = ({ program, sessions }) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [linkedLessons, setLinkedLessons] = useState<Record<string, Lesson[]>>({});
  const [availableLessons, setAvailableLessons] = useState<Lesson[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [selectedSession, setSelectedSession] = useState<ProgramSession | null>(null);
  const [selectedLesson, setSelectedLesson] = useState<string>('');
  const [selectedLessonObj, setSelectedLessonObj] = useState<Lesson | null>(null);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);
  const [newLessonTitle, setNewLessonTitle] = useState('');
  const [newLessonDuration, setNewLessonDuration] = useState(60);

  useEffect(() => {
    const fetchLinkedLessons = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Create a map to store lessons for each session
        const sessionsLessonsMap: Record<string, Lesson[]> = {};

        // For each session, find linked lessons
        for (const session of sessions) {
          const lessonsQuery = query(
            collection(db, 'schools', currentSchool.id, 'lessons'),
            where('programSessionId', '==', session.id)
          );

          const lessonsSnapshot = await getDocs(lessonsQuery);
          const lessonsData = lessonsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Lesson[];

          sessionsLessonsMap[session.id] = lessonsData;
        }

        setLinkedLessons(sessionsLessonsMap);

        // Fetch all available lessons that could be linked
        const allLessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('instructorId', 'in', program.instructors)
        );

        const allLessonsSnapshot = await getDocs(allLessonsQuery);
        const allLessonsData = allLessonsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Lesson[];

        // Filter out lessons that are already linked to a program session
        const linkedLessonIds = Object.values(sessionsLessonsMap)
          .flat()
          .map((lesson) => lesson.id);

        const availableLessonsData = allLessonsData.filter(
          (lesson) => !linkedLessonIds.includes(lesson.id) && !(lesson as any).programSessionId
        );

        setAvailableLessons(availableLessonsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching linked lessons:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchLinkedLessons();
  }, [currentSchool?.id, program.instructors, sessions, t]);

  const handleCreateLesson = async () => {
    if (!currentSchool?.id || !selectedSession) return;

    try {
      const lessonData = {
        title: newLessonTitle,
        type: 'group',
        discipline: program.name,
        instructorId: program.instructors[0], // Default to first instructor
        studentIds: program.participants,
        startTime: selectedSession.date,
        duration: newLessonDuration,
        level: 'intermediate', // Default level
        status: 'scheduled',
        notes: `Created from program: ${program.name}`,
        programId: program.id,
        programSessionId: selectedSession.id,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      const lessonsRef = collection(db, 'schools', currentSchool.id, 'lessons');
      const newLessonRef = await addDoc(lessonsRef, lessonData);

      // Update the linked lessons state
      setLinkedLessons((prev) => ({
        ...prev,
        [selectedSession.id]: [
          ...(prev[selectedSession.id] || []),
          { id: newLessonRef.id, ...lessonData } as unknown as Lesson,
        ],
      }));

      // Reset form
      setNewLessonTitle('');
      setNewLessonDuration(60);
      setCreateDialogOpen(false);
      setSelectedSession(null);
    } catch (err) {
      console.error('Error creating lesson:', err);
      setError(t('messages.createError', 'Failed to create lesson'));
    }
  };

  const handleLinkLesson = async () => {
    if (!currentSchool?.id || !selectedSession || !selectedLesson) return;

    try {
      // Update the lesson with the program session ID
      await setDoc(
        doc(db, 'schools', currentSchool.id, 'lessons', selectedLesson),
        {
          programId: program.id,
          programSessionId: selectedSession.id,
          updatedAt: Timestamp.now(),
        },
        { merge: true }
      );

      // Find the lesson in available lessons
      const lesson = availableLessons.find((l) => l.id === selectedLesson);

      if (lesson) {
        // Update the linked lessons state
        setLinkedLessons((prev) => ({
          ...prev,
          [selectedSession.id]: [
            ...(prev[selectedSession.id] || []),
            {
              ...lesson,
              programId: program.id,
              programSessionId: selectedSession.id,
            } as unknown as Lesson,
          ],
        }));

        // Remove from available lessons
        setAvailableLessons((prev) => prev.filter((l) => l.id !== selectedLesson));
      }

      // Reset form
      setSelectedLesson('');
      setLinkDialogOpen(false);
      setSelectedSession(null);
    } catch (err) {
      console.error('Error linking lesson:', err);
      setError(t('messages.updateError', 'Failed to link lesson'));
    }
  };

  const handleUnlinkLesson = async (sessionId: string, lessonId: string) => {
    if (!currentSchool?.id) return;

    try {
      // Update the lesson to remove the program session ID
      await setDoc(
        doc(db, 'schools', currentSchool.id, 'lessons', lessonId),
        {
          programId: null,
          programSessionId: null,
          updatedAt: Timestamp.now(),
        },
        { merge: true }
      );

      // Find the lesson in linked lessons
      const lesson = linkedLessons[sessionId]?.find((l) => l.id === lessonId);

      if (lesson) {
        // Update the linked lessons state
        setLinkedLessons((prev) => ({
          ...prev,
          [sessionId]: prev[sessionId].filter((l) => l.id !== lessonId),
        }));

        // Add back to available lessons
        setAvailableLessons((prev) => [
          ...prev,
          { ...lesson, programId: null, programSessionId: null } as unknown as Lesson,
        ]);
      }
    } catch (err) {
      console.error('Error unlinking lesson:', err);
      setError(t('messages.updateError', 'Failed to unlink lesson'));
    }
  };

  const openCreateDialog = (session: ProgramSession) => {
    setSelectedSession(session);
    setCreateDialogOpen(true);
  };

  const openLinkDialog = (session: ProgramSession) => {
    setSelectedSession(session);
    setLinkDialogOpen(true);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('lessons.title', 'Program Lessons')}
      </Typography>

      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('sessions.date', 'Date')}</TableCell>
              <TableCell>{t('lessons.linkedLessons', 'Linked Lessons')}</TableCell>
              <TableCell align="right">{t('common:ui.actions', 'Actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sessions.map((session) => (
              <TableRow key={session.id}>
                <TableCell>{format(session.date.toDate(), 'PPP')}</TableCell>
                <TableCell>
                  {linkedLessons[session.id]?.length > 0 ? (
                    <Box display="flex" flexDirection="column" gap={1}>
                      {linkedLessons[session.id].map((lesson) => (
                        <Box key={lesson.id} display="flex" alignItems="center">
                          <Chip
                            label={lesson.title}
                            color="primary"
                            variant="outlined"
                            icon={<EventIcon />}
                            onDelete={() => handleUnlinkLesson(session.id, lesson.id)}
                            sx={{ maxWidth: 300, mr: 1 }}
                          />
                          <IconButton
                            size="small"
                            color="secondary"
                            onClick={() => {
                              setSelectedLessonObj(lesson);
                              setIsRescheduleDialogOpen(true);
                            }}
                            title={t('lessons:reschedule.button', 'Reschedule')}
                          >
                            <EventNoteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      {t('lessons.noLinkedLessons', 'No linked lessons')}
                    </Typography>
                  )}
                </TableCell>
                <TableCell align="right">
                  <Box display="flex" justifyContent="flex-end">
                    <Tooltip title={t('lessons.createLesson', 'Create Lesson')}>
                      <IconButton color="primary" onClick={() => openCreateDialog(session)}>
                        <AddIcon />
                      </IconButton>
                    </Tooltip>
                    {availableLessons.length === 0 ? (
                      <IconButton color="secondary" disabled={true}>
                        <LinkIcon />
                      </IconButton>
                    ) : (
                      <Tooltip title={t('lessons.linkLesson', 'Link Existing Lesson')}>
                        <IconButton color="secondary" onClick={() => openLinkDialog(session)}>
                          <LinkIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Create Lesson Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t('lessons.createLesson', 'Create Lesson')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label={t('lessons.title', 'Lesson Title')}
              value={newLessonTitle}
              onChange={(e) => setNewLessonTitle(e.target.value)}
              fullWidth
              required
            />
            <TextField
              label={t('lessons.duration', 'Duration (minutes)')}
              type="number"
              value={newLessonDuration}
              onChange={(e) => setNewLessonDuration(parseInt(e.target.value))}
              fullWidth
              required
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleCreateLesson}
            variant="contained"
            color="primary"
            disabled={!newLessonTitle}
          >
            {t('common:ui.create', 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Link Lesson Dialog */}
      <Dialog
        open={linkDialogOpen}
        onClose={() => setLinkDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t('lessons.linkLesson', 'Link Existing Lesson')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth>
              <InputLabel>{t('lessons.selectLesson', 'Select Lesson')}</InputLabel>
              <Select
                value={selectedLesson}
                onChange={(e) => setSelectedLesson(e.target.value)}
                label={t('lessons.selectLesson', 'Select Lesson')}
              >
                {availableLessons.map((lesson) => (
                  <MenuItem key={lesson.id} value={lesson.id}>
                    {lesson.title} (
                    {format(
                      'startTime' in lesson && 'toDate' in lesson.startTime
                        ? lesson.startTime.toDate()
                        : new Date(lesson.startTime as any),
                      'PPp'
                    )}
                    )
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLinkDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleLinkLesson}
            variant="contained"
            color="primary"
            disabled={!selectedLesson}
          >
            {t('common:ui.link', 'Link')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reschedule Dialog */}
      {selectedLessonObj && (
        <LessonRescheduleDialog
          open={isRescheduleDialogOpen}
          onClose={() => setIsRescheduleDialogOpen(false)}
          lesson={selectedLessonObj}
          onReschedule={() => {
            // Refresh lessons data
            const fetchLinkedLessons = async () => {
              if (!currentSchool?.id) return;
              setLoading(true);
              try {
                // Create a map to store lessons for each session
                const sessionsLessonsMap: Record<string, Lesson[]> = {};

                // For each session, find linked lessons
                for (const session of sessions) {
                  const lessonsQuery = query(
                    collection(db, 'schools', currentSchool.id, 'lessons'),
                    where('programSessionId', '==', session.id)
                  );

                  const lessonsSnapshot = await getDocs(lessonsQuery);
                  const lessonsData = lessonsSnapshot.docs.map((doc) => ({
                    id: doc.id,
                    ...doc.data(),
                  })) as Lesson[];

                  sessionsLessonsMap[session.id] = lessonsData;
                }

                setLinkedLessons(sessionsLessonsMap);
              } catch (err) {
                console.error('Error fetching linked lessons:', err);
                setError(t('messages.fetchError', 'Failed to fetch lessons'));
              } finally {
                setLoading(false);
              }
            };

            fetchLinkedLessons();
            setIsRescheduleDialogOpen(false);
            setSelectedLessonObj(null);
          }}
        />
      )}
    </Box>
  );
};

export default ProgramLessons;
