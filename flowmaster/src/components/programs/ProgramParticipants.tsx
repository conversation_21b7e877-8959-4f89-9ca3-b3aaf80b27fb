import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Program } from '../../types/program';
import { Student } from '../../types/student';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Avatar,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';

interface ProgramParticipantsProps {
  program: Program;
}

const ProgramParticipants: React.FC<ProgramParticipantsProps> = ({ program }) => {
  const { t } = useTranslation('programs');
  const { currentSchool } = useSchool();

  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch students
        const studentPromises = program.participants.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'students', id))
        );

        const studentDocs = await Promise.all(studentPromises);
        const studentData = studentDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Student[];

        setStudents(studentData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError(t('messages.fetchError', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, program.participants, t]);

  const getPaymentStatus = (studentId: string) => {
    const status = program.pricing.paymentStatus[studentId] || 'pending';
    return status;
  };

  const getPaymentStatusColor = (status: string): 'success' | 'warning' | 'error' | 'default' => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'partial':
        return 'warning';
      case 'pending':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          {t('tabs.participants', 'Participants')} ({students.length})
        </Typography>

        <Button variant="contained" color="primary" startIcon={<AddIcon />}>
          {t('actions.addParticipant', 'Add Participant')}
        </Button>
      </Box>

      {students.length === 0 ? (
        <Alert severity="info">
          {t('messages.noParticipants', 'No participants enrolled in this program')}
        </Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('fields.name', 'Name')}</TableCell>
                <TableCell>{t('fields.contact', 'Contact')}</TableCell>
                <TableCell>{t('fields.paymentStatus', 'Payment Status')}</TableCell>
                <TableCell>{t('fields.attendance', 'Attendance')}</TableCell>
                <TableCell align="right">{t('fields.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {students.map((student) => {
                const paymentStatus = getPaymentStatus(student.id);

                return (
                  <TableRow key={student.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2 }}>
                          {student.firstName.charAt(0)}
                          {student.lastName.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body1">
                            {student.firstName} {student.lastName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ID: {student.id.substring(0, 8)}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Box display="flex" alignItems="center" mb={0.5}>
                          <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2">{student.email}</Typography>
                        </Box>
                        <Box display="flex" alignItems="center">
                          <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2">
                            {student.phone || t('notSpecified', 'Not specified')}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={t(`payment.status.${paymentStatus}`, paymentStatus)}
                        color={getPaymentStatusColor(paymentStatus)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {/* This would be calculated from attendance records */}
                        {t('notAvailable', 'Not available')}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title={t('actions.updatePayment', 'Update Payment Status')}>
                        <IconButton size="small" color="primary">
                          <PaymentIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('actions.removeParticipant', 'Remove Participant')}>
                        <IconButton size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default ProgramParticipants;
