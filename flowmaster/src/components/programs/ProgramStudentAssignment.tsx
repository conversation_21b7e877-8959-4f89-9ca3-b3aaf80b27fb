import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Program } from '../../types/program';
import { Student } from '../../types/student';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
  serverTimestamp,
} from 'firebase/firestore';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  PersonAdd as PersonAddIcon,
  PersonRemove as PersonRemoveIcon,
  Save as SaveIcon,
  DragIndicator as DragIcon,
} from '@mui/icons-material';

interface ProgramStudentAssignmentProps {
  program: Program;
  onUpdate?: () => void;
}

interface StudentListItem {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

const ProgramStudentAssignment: React.FC<ProgramStudentAssignmentProps> = ({
  program,
  onUpdate,
}) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [availableStudents, setAvailableStudents] = useState<StudentListItem[]>([]);
  const [assignedStudents, setAssignedStudents] = useState<StudentListItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [studentToRemove, setStudentToRemove] = useState<StudentListItem | null>(null);

  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch all active students
        const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
        const studentsQuery = query(studentsRef, where('status', '==', 'active'));
        const studentsSnapshot = await getDocs(studentsQuery);

        const allStudents = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          firstName: doc.data().firstName,
          lastName: doc.data().lastName,
          email: doc.data().email,
          avatar: doc.data().avatar,
        }));

        // Split into assigned and available students
        const assigned: StudentListItem[] = [];
        const available: StudentListItem[] = [];

        allStudents.forEach((student) => {
          if (program.participants.includes(student.id)) {
            assigned.push(student);
          } else {
            available.push(student);
          }
        });

        // Sort by name
        assigned.sort((a, b) =>
          `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
        );
        available.sort((a, b) =>
          `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
        );

        setAssignedStudents(assigned);
        setAvailableStudents(available);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError(t('messages.fetchError', 'Failed to fetch students'));
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, program.participants, t]);

  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    // Dropped outside a droppable area
    if (!destination) return;

    // Dropped in the same place
    if (source.droppableId === destination.droppableId && source.index === destination.index)
      return;

    // Moving within the same list
    if (source.droppableId === destination.droppableId) {
      if (source.droppableId === 'assigned') {
        const newAssigned = [...assignedStudents];
        const [removed] = newAssigned.splice(source.index, 1);
        newAssigned.splice(destination.index, 0, removed);
        setAssignedStudents(newAssigned);
        setHasChanges(true);
      } else if (source.droppableId === 'available') {
        const newAvailable = [...availableStudents];
        const [removed] = newAvailable.splice(source.index, 1);
        newAvailable.splice(destination.index, 0, removed);
        setAvailableStudents(newAvailable);
      }
    } else {
      // Moving between lists
      if (source.droppableId === 'available' && destination.droppableId === 'assigned') {
        // Moving from available to assigned
        const newAvailable = [...availableStudents];
        const newAssigned = [...assignedStudents];
        const [removed] = newAvailable.splice(source.index, 1);
        newAssigned.splice(destination.index, 0, removed);
        setAvailableStudents(newAvailable);
        setAssignedStudents(newAssigned);
        setHasChanges(true);
      } else if (source.droppableId === 'assigned' && destination.droppableId === 'available') {
        // Moving from assigned to available
        const newAssigned = [...assignedStudents];
        const newAvailable = [...availableStudents];
        const [removed] = newAssigned.splice(source.index, 1);
        newAvailable.splice(destination.index, 0, removed);
        setAssignedStudents(newAssigned);
        setAvailableStudents(newAvailable);
        setHasChanges(true);
      }
    }
  };

  const handleSaveAssignments = async () => {
    if (!currentSchool?.id) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the new list of participant IDs
      const newParticipantIds = assignedStudents.map((student) => student.id);

      // Update the program in Firestore
      const programRef = doc(db, 'schools', currentSchool.id, 'programs', program.id);
      await updateDoc(programRef, {
        participants: newParticipantIds,
        updatedAt: serverTimestamp(),
      });

      setSuccess(t('messages.saveSuccess', 'Student assignments saved successfully'));
      setHasChanges(false);

      // Notify parent component about the update
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error saving student assignments:', err);
      setError(t('messages.saveError', 'Failed to save student assignments'));
    } finally {
      setSaving(false);
    }
  };

  const handleAddStudent = (student: StudentListItem) => {
    setAvailableStudents(availableStudents.filter((s) => s.id !== student.id));
    setAssignedStudents([...assignedStudents, student]);
    setHasChanges(true);
  };

  const handleRemoveStudent = (student: StudentListItem) => {
    setStudentToRemove(student);
    setConfirmDialogOpen(true);
  };

  const confirmRemoveStudent = () => {
    if (!studentToRemove) return;

    setAssignedStudents(assignedStudents.filter((s) => s.id !== studentToRemove.id));
    setAvailableStudents([...availableStudents, studentToRemove]);
    setConfirmDialogOpen(false);
    setStudentToRemove(null);
    setHasChanges(true);
  };

  const filteredAvailableStudents = availableStudents.filter((student) => {
    const fullName = `${student.firstName} ${student.lastName}`.toLowerCase();
    const email = student.email.toLowerCase();
    const query = searchQuery.toLowerCase();

    return fullName.includes(query) || email.includes(query);
  });

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !success) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">{t('studentAssignment.title', 'Student Assignment')}</Typography>

        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSaveAssignments}
          disabled={!hasChanges || saving}
        >
          {saving ? t('common:ui.saving', 'Saving...') : t('common:ui.save', 'Save')}
        </Button>
      </Box>

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Typography variant="subtitle1" gutterBottom>
        {t(
          'studentAssignment.instructions',
          'Drag and drop students between the lists to assign them to this program'
        )}
      </Typography>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" gutterBottom>
                {t('studentAssignment.assigned', 'Assigned Students')} ({assignedStudents.length})
              </Typography>

              <Droppable droppableId="assigned">
                {(provided) => (
                  <List
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    sx={{
                      bgcolor: 'background.paper',
                      minHeight: 300,
                      border: '1px dashed',
                      borderColor: 'divider',
                      borderRadius: 1,
                      p: 1,
                    }}
                  >
                    {assignedStudents.length === 0 ? (
                      <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        height={300}
                        color="text.secondary"
                      >
                        <Typography variant="body2">
                          {t(
                            'studentAssignment.noAssignedStudents',
                            'No students assigned to this program'
                          )}
                        </Typography>
                      </Box>
                    ) : (
                      assignedStudents.map((student, index) => (
                        <Draggable key={student.id} draggableId={student.id} index={index}>
                          {(provided) => (
                            <ListItem
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              secondaryAction={
                                <IconButton
                                  edge="end"
                                  aria-label="remove"
                                  onClick={() => handleRemoveStudent(student)}
                                >
                                  <PersonRemoveIcon />
                                </IconButton>
                              }
                              divider
                            >
                              <Box
                                {...provided.dragHandleProps}
                                sx={{ mr: 1, display: 'flex', alignItems: 'center' }}
                              >
                                <DragIcon color="action" />
                              </Box>
                              <ListItemAvatar>
                                <Avatar src={student.avatar}>
                                  {student.firstName.charAt(0)}
                                  {student.lastName.charAt(0)}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={`${student.firstName} ${student.lastName}`}
                                secondary={student.email}
                              />
                            </ListItem>
                          )}
                        </Draggable>
                      ))
                    )}
                    {provided.placeholder}
                  </List>
                )}
              </Droppable>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Box mb={2}>
                <TextField
                  fullWidth
                  placeholder={t('studentAssignment.searchStudents', 'Search students...')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                {t('studentAssignment.available', 'Available Students')} (
                {filteredAvailableStudents.length})
              </Typography>

              <Droppable droppableId="available">
                {(provided) => (
                  <List
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    sx={{
                      bgcolor: 'background.paper',
                      minHeight: 300,
                      border: '1px dashed',
                      borderColor: 'divider',
                      borderRadius: 1,
                      p: 1,
                    }}
                  >
                    {filteredAvailableStudents.length === 0 ? (
                      <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        height={300}
                        color="text.secondary"
                      >
                        <Typography variant="body2">
                          {searchQuery
                            ? t(
                                'studentAssignment.noSearchResults',
                                'No students match your search'
                              )
                            : t('studentAssignment.noAvailableStudents', 'No available students')}
                        </Typography>
                      </Box>
                    ) : (
                      filteredAvailableStudents.map((student, index) => (
                        <Draggable key={student.id} draggableId={student.id} index={index}>
                          {(provided) => (
                            <ListItem
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              secondaryAction={
                                <IconButton
                                  edge="end"
                                  aria-label="add"
                                  onClick={() => handleAddStudent(student)}
                                >
                                  <PersonAddIcon />
                                </IconButton>
                              }
                              divider
                            >
                              <Box
                                {...provided.dragHandleProps}
                                sx={{ mr: 1, display: 'flex', alignItems: 'center' }}
                              >
                                <DragIcon color="action" />
                              </Box>
                              <ListItemAvatar>
                                <Avatar src={student.avatar}>
                                  {student.firstName.charAt(0)}
                                  {student.lastName.charAt(0)}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={`${student.firstName} ${student.lastName}`}
                                secondary={student.email}
                              />
                            </ListItem>
                          )}
                        </Draggable>
                      ))
                    )}
                    {provided.placeholder}
                  </List>
                )}
              </Droppable>
            </Paper>
          </Grid>
        </Grid>
      </DragDropContext>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>
        <DialogTitle>{t('studentAssignment.confirmRemove', 'Remove Student')}</DialogTitle>
        <DialogContent>
          <Typography>
            {t(
              'studentAssignment.confirmRemoveText',
              'Are you sure you want to remove {{name}} from this program?',
              {
                name: studentToRemove
                  ? `${studentToRemove.firstName} ${studentToRemove.lastName}`
                  : '',
              }
            )}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            {t('common:ui.cancel', 'Cancel')}
          </Button>
          <Button onClick={confirmRemoveStudent} color="error">
            {t('common:ui.remove', 'Remove')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProgramStudentAssignment;
