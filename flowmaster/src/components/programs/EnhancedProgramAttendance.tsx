import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Program, ProgramSession, AttendanceStatus } from '../../types/program';
import { Student, StudentType } from '../../types/student';
import { doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { markStudentsForMakeup } from '../../services/makeupSessionService';
import { format } from 'date-fns';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Chip,
  Checkbox,
  Tabs,
  Tab,
  Divider,
  Grid,
  Card,
  CardContent,
  CardActions,
  Snackbar,
  LinearProgress,
} from '@mui/material';
import {
  CheckCircle as PresentIcon,
  Cancel as AbsentIcon,
  HourglassEmpty as ExcusedIcon,
  FileDownload as DownloadIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

interface EnhancedProgramAttendanceProps {
  program: Program;
  sessions: ProgramSession[];
  onSessionUpdate?: () => void;
}

const EnhancedProgramAttendance: React.FC<EnhancedProgramAttendanceProps> = ({
  program,
  sessions,
  onSessionUpdate,
}) => {
  const { t } = useTranslation(['programs', 'common']);
  const { currentSchool } = useSchool();

  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSession, setSelectedSession] = useState<string>('');
  const [excuseDialogOpen, setExcuseDialogOpen] = useState(false);
  const [excuseNote, setExcuseNote] = useState('');
  const [selectedStudentId, setSelectedStudentId] = useState<string>('');
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [saving, setSaving] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [attendanceChanged, setAttendanceChanged] = useState(false);
  const [localAttendance, setLocalAttendance] = useState<Record<string, AttendanceStatus>>({});

  // Sort sessions by date (newest first)
  const sortedSessions = useMemo(() => {
    return [...sessions].sort((a, b) => b.date.toDate().getTime() - a.date.toDate().getTime());
  }, [sessions]);

  // Fetch students only when school or participants change
  useEffect(() => {
    const fetchStudents = async () => {
      if (!currentSchool?.id) return;

      try {
        setLoading(true);

        // Fetch students
        const studentPromises = program.participants.map((id) =>
          getDoc(doc(db, 'schools', currentSchool.id, 'students', id))
        );

        const studentDocs = await Promise.all(studentPromises);
        const studentData = studentDocs
          .filter((doc) => doc.exists())
          .map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as Student[];

        setStudents(studentData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching students:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
        setLoading(false);
      }
    };

    fetchStudents();
  }, [currentSchool?.id, program.participants, t]);

  // Initialize local attendance when session changes
  useEffect(() => {
    if (selectedSession) {
      const session = sessions.find((s) => s.id === selectedSession);
      if (session) {
        setLocalAttendance(session.attendance || {});
        setAttendanceChanged(false);
      }
    }
  }, [selectedSession, sessions]);

  const handleSessionChange = (event: SelectChangeEvent) => {
    // Check if there are unsaved changes
    if (attendanceChanged) {
      if (
        window.confirm(
          t('messages.unsavedChanges', 'You have unsaved changes. Continue?', { ns: 'common' })
        )
      ) {
        setSelectedSession(event.target.value);
      }
    } else {
      setSelectedSession(event.target.value);
    }
  };

  const getAttendanceStatus = (studentId: string): AttendanceStatus => {
    return localAttendance[studentId] || 'absent';
  };

  const handleAttendanceChange = (studentId: string, status: AttendanceStatus) => {
    if (!selectedSession) return;

    // If marking as excused, open the dialog to enter a note
    if (status === 'excused') {
      setSelectedStudentId(studentId);
      setExcuseNote('');
      setExcuseDialogOpen(true);
      return;
    }

    // Update local attendance
    setLocalAttendance((prev) => ({
      ...prev,
      [studentId]: status,
    }));
    setAttendanceChanged(true);
  };

  const handleExcuseConfirm = () => {
    if (!selectedStudentId) return;

    // Update local attendance
    setLocalAttendance((prev) => ({
      ...prev,
      [selectedStudentId]: 'excused',
    }));
    setAttendanceChanged(true);
    setExcuseDialogOpen(false);
  };

  const handleBulkAttendanceChange = (status: AttendanceStatus) => {
    if (!selectedSession) return;

    const studentsToUpdate =
      selectedStudents.length > 0 ? selectedStudents : students.map((s) => s.id);

    const updatedAttendance = { ...localAttendance };
    studentsToUpdate.forEach((studentId) => {
      updatedAttendance[studentId] = status;
    });

    setLocalAttendance(updatedAttendance);
    setAttendanceChanged(true);
  };

  const saveAttendance = async () => {
    if (!currentSchool?.id || !selectedSession) return;

    try {
      setSaving(true);
      const session = sessions.find((s) => s.id === selectedSession);
      if (!session) return;

      // Update the session in Firestore
      const sessionRef = doc(
        db,
        'schools',
        currentSchool.id,
        'programs',
        program.id,
        'sessions',
        selectedSession
      );

      // Prepare the update data
      const updateData = {
        attendance: localAttendance,
        updatedAt: serverTimestamp(),
      };

      // Process students marked as absent for makeup
      const absentStudents = Object.entries(localAttendance)
        .filter(([_, status]) => status === 'absent')
        .map(([studentId]) => studentId);

      if (absentStudents.length > 0) {
        await markStudentsForMakeup(currentSchool.id, program.id, selectedSession, absentStudents);
      }

      await updateDoc(sessionRef, updateData);
      setAttendanceChanged(false);

      setSnackbarMessage(
        t('messages.attendanceSaved', 'Attendance saved successfully', { ns: 'common' })
      );
      setSnackbarOpen(true);

      // Call the update callback if provided
      if (onSessionUpdate) {
        onSessionUpdate();
      }
    } catch (err) {
      console.error('Error updating attendance:', err);
      setError(t('messages.updateError', 'Failed to update attendance', { ns: 'common' }));
    } finally {
      setSaving(false);
    }
  };

  const handleSelectAllChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectAll(event.target.checked);
    if (event.target.checked) {
      setSelectedStudents(filteredStudents.map((student) => student.id));
    } else {
      setSelectedStudents([]);
    }
  };

  const handleStudentSelect = (studentId: string) => {
    setSelectedStudents((prev) => {
      if (prev.includes(studentId)) {
        return prev.filter((id) => id !== studentId);
      } else {
        return [...prev, studentId];
      }
    });
  };

  const getAttendanceIcon = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return <PresentIcon color="success" />;
      case 'absent':
        return <AbsentIcon color="error" />;
      case 'excused':
        return <ExcusedIcon color="warning" />;
      default:
        return <AbsentIcon color="error" />;
    }
  };

  const getCurrentSession = () => {
    return sessions.find((session) => session.id === selectedSession);
  };

  // Filter students based on status and search term
  const filteredStudents = useMemo(() => {
    return students.filter((student) => {
      // Filter by status
      if (statusFilter !== 'all') {
        const status = getAttendanceStatus(student.id);
        if (status !== statusFilter) {
          return false;
        }
      }

      // Filter by search term
      if (searchTerm) {
        const fullName = `${student.firstName} ${student.lastName}`.toLowerCase();
        return fullName.includes(searchTerm.toLowerCase());
      }

      return true;
    });
  }, [students, statusFilter, searchTerm, localAttendance]);

  // Calculate attendance statistics
  const attendanceStats = useMemo(() => {
    const total = students.length;
    let present = 0;
    let absent = 0;
    let excused = 0;

    Object.values(localAttendance).forEach((status) => {
      if (status === 'present') present++;
      else if (status === 'absent') absent++;
      else if (status === 'excused') excused++;
    });

    const notMarked = total - (present + absent + excused);

    return { total, present, absent, excused, notMarked };
  }, [students.length, localAttendance]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (students.length === 0) {
    return (
      <Alert severity="info">
        {t('messages.noParticipants', 'No participants enrolled in this program')}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header with session selector and actions */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <FormControl fullWidth size="small">
              <InputLabel id="session-select-label">{t('fields.session', 'Session')}</InputLabel>
              <Select
                labelId="session-select-label"
                id="session-select"
                value={selectedSession}
                label={t('fields.session', 'Session')}
                onChange={handleSessionChange}
                disabled={saving}
              >
                <MenuItem value="">
                  {t('attendanceActions.selectSession', 'Select a session')}
                </MenuItem>
                {sortedSessions.map((session) => (
                  <MenuItem key={session.id} value={session.id}>
                    {format(session.date.toDate(), 'PPP')}
                    {session.isMakeup && ` (${t('labels.makeup', 'Makeup')})`}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={saveAttendance}
                disabled={!selectedSession || saving || !attendanceChanged}
              >
                {saving ? (
                  <CircularProgress size={24} />
                ) : (
                  t('attendanceActions.saveAttendance', 'Save Attendance')
                )}
              </Button>
              <Button variant="outlined" startIcon={<DownloadIcon />} disabled={!selectedSession}>
                {t('attendanceActions.exportAttendance', 'Export')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {selectedSession ? (
        <>
          {/* Attendance Statistics */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              {t('labels.attendanceStats', 'Attendance Statistics')}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={2}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="primary.main">
                    {attendanceStats.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('labels.total', 'Total')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={2}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="success.main">
                    {attendanceStats.present}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('attendance.present', 'Present')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={2}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="error.main">
                    {attendanceStats.absent}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('attendance.absent', 'Absent')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={2}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="warning.main">
                    {attendanceStats.excused}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('attendance.excused', 'Excused')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={2}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="text.secondary">
                    {attendanceStats.notMarked}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('labels.notMarked', 'Not Marked')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={2}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="primary.main">
                    {attendanceStats.total > 0
                      ? Math.round((attendanceStats.present / attendanceStats.total) * 100)
                      : 0}
                    %
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('labels.attendanceRate', 'Attendance Rate')}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Filters and Bulk Actions */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel id="status-filter-label">{t('filters.status', 'Status')}</InputLabel>
                  <Select
                    labelId="status-filter-label"
                    id="status-filter"
                    value={statusFilter}
                    label={t('filters.status', 'Status')}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <MenuItem value="all">{t('filters.all', 'All', { ns: 'common' })}</MenuItem>
                    <MenuItem value="present">{t('attendance.present', 'Present')}</MenuItem>
                    <MenuItem value="absent">{t('attendance.absent', 'Absent')}</MenuItem>
                    <MenuItem value="excused">{t('attendance.excused', 'Excused')}</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  size="small"
                  label={t('filters.search', 'Search', { ns: 'common' })}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <SearchIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() => handleBulkAttendanceChange('present')}
                    disabled={!selectedSession || saving}
                    size="small"
                  >
                    {t('attendanceActions.markSelectedAs', 'Mark as Present')}
                  </Button>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={() => handleBulkAttendanceChange('absent')}
                    disabled={!selectedSession || saving}
                    size="small"
                  >
                    {t('attendanceActions.markSelectedAs', 'Mark as Absent')}
                  </Button>
                  <Button
                    variant="contained"
                    color="warning"
                    onClick={() => handleBulkAttendanceChange('excused')}
                    disabled={!selectedSession || saving}
                    size="small"
                  >
                    {t('attendanceActions.markSelectedAs', 'Mark as Excused')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Attendance Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'primary.main' }}>
                  <TableCell padding="checkbox" sx={{ color: 'white' }}>
                    <Checkbox
                      checked={selectAll}
                      onChange={handleSelectAllChange}
                      sx={{ color: 'white', '&.Mui-checked': { color: 'white' } }}
                    />
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('fields.name', 'Name')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('fields.status', 'Status')}
                  </TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                    {t('fields.actions', 'Actions')}
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredStudents.map((student) => {
                  const attendanceStatus = getAttendanceStatus(student.id);
                  const isSelected = selectedStudents.includes(student.id);

                  return (
                    <TableRow
                      key={student.id}
                      selected={isSelected}
                      sx={{
                        '&:nth-of-type(odd)': { bgcolor: 'action.hover' },
                        '&:hover': { bgcolor: 'action.selected' },
                        transition: 'background-color 0.2s ease',
                      }}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleStudentSelect(student.id)}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1">
                            {student.firstName} {student.lastName}
                          </Typography>
                          {student.type && (
                            <Chip
                              label={student.type}
                              size="small"
                              color={
                                student.type === 'program'
                                  ? 'primary'
                                  : student.type === 'lesson'
                                    ? 'secondary'
                                    : 'default'
                              }
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getAttendanceIcon(attendanceStatus)}
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {t(`attendance.${attendanceStatus}`, attendanceStatus)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title={t('attendance.present', 'Present')}>
                            <IconButton
                              size="small"
                              color={attendanceStatus === 'present' ? 'success' : 'default'}
                              onClick={() => handleAttendanceChange(student.id, 'present')}
                              disabled={saving}
                            >
                              <PresentIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={t('attendance.absent', 'Absent')}>
                            <IconButton
                              size="small"
                              color={attendanceStatus === 'absent' ? 'error' : 'default'}
                              onClick={() => handleAttendanceChange(student.id, 'absent')}
                              disabled={saving}
                            >
                              <AbsentIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={t('attendance.excused', 'Excused')}>
                            <IconButton
                              size="small"
                              color={attendanceStatus === 'excused' ? 'warning' : 'default'}
                              onClick={() => handleAttendanceChange(student.id, 'excused')}
                              disabled={saving}
                            >
                              <ExcusedIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      ) : (
        <Alert severity="info">
          {t('messages.selectSession', 'Please select a session to view attendance')}
        </Alert>
      )}

      {/* Excuse Dialog */}
      <Dialog open={excuseDialogOpen} onClose={() => setExcuseDialogOpen(false)}>
        <DialogTitle>{t('dialogs.excuseTitle', 'Excuse Absence')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('dialogs.excuseDescription', 'Please provide a reason for the excused absence:')}
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label={t('fields.reason', 'Reason')}
            fullWidth
            multiline
            rows={3}
            value={excuseNote}
            onChange={(e) => setExcuseNote(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExcuseDialogOpen(false)}>
            {t('ui.cancel', 'Cancel', { ns: 'common' })}
          </Button>
          <Button onClick={handleExcuseConfirm} variant="contained" color="primary">
            {t('ui.save', 'Save', { ns: 'common' })}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default EnhancedProgramAttendance;
