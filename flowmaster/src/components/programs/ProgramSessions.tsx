import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Program, ProgramSession } from '../../types/program';
import ProgramBulkSessions from './ProgramBulkSessions';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Event as EventIcon,
  CheckCircle as CheckCircleIcon,
  HourglassEmpty as PendingIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

interface ProgramSessionsProps {
  program: Program;
  sessions: ProgramSession[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`sessions-tabpanel-${index}`}
      aria-labelledby={`sessions-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `sessions-tab-${index}`,
    'aria-controls': `sessions-tabpanel-${index}`,
  };
}

const ProgramSessions: React.FC<ProgramSessionsProps> = ({ program, sessions }) => {
  const { t } = useTranslation('programs');
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Sort sessions by date
  const sortedSessions = [...sessions].sort(
    (a, b) => a.date.toDate().getTime() - b.date.toDate().getTime()
  );

  const getSessionStatusIcon = (session: ProgramSession) => {
    // Determine session status based on date and attendance
    const now = new Date();
    const sessionDate = session.date.toDate();

    if (sessionDate > now) {
      return <PendingIcon color="primary" />;
    }

    // Check if attendance has been taken
    const attendanceCount = Object.keys(session.attendance).length;
    if (attendanceCount === 0) {
      return <PendingIcon color="warning" />;
    }

    return <CheckCircleIcon color="success" />;
  };

  const getSessionStatusText = (session: ProgramSession): string => {
    const now = new Date();
    const sessionDate = session.date.toDate();

    if (sessionDate > now) {
      return t('sessionStatus.upcoming', 'Upcoming');
    }

    const attendanceCount = Object.keys(session.attendance).length;
    if (attendanceCount === 0) {
      return t('sessionStatus.pendingAttendance', 'Pending Attendance');
    }

    return t('sessionStatus.completed', 'Completed');
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">{t('tabs.sessions', 'Sessions')}</Typography>

        <Button variant="contained" color="primary" startIcon={<AddIcon />}>
          {t('actions.addSession', 'Add Session')}
        </Button>
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="sessions tabs">
          <Tab label={t('sessionsTabs.list', 'Session List')} {...a11yProps(0)} />
          <Tab label={t('sessionsTabs.bulk', 'Bulk Operations')} {...a11yProps(1)} />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {sortedSessions.length === 0 ? (
          <Alert severity="info">
            {t('messages.noSessions', 'No sessions found for this program')}
          </Alert>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('fields.date', 'Date')}</TableCell>
                  <TableCell>{t('fields.time', 'Time')}</TableCell>
                  <TableCell>{t('fields.status', 'Status')}</TableCell>
                  <TableCell>{t('fields.attendance', 'Attendance')}</TableCell>
                  <TableCell>{t('fields.notes', 'Notes')}</TableCell>
                  <TableCell align="right">{t('fields.actions', 'Actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedSessions.map((session) => {
                  const sessionDate = session.date.toDate();
                  const attendanceCount = Object.keys(session.attendance).length;
                  const attendancePercentage =
                    program.participants.length > 0
                      ? Math.round((attendanceCount / program.participants.length) * 100)
                      : 0;

                  return (
                    <TableRow key={session.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          {format(sessionDate, 'EEEE, MMMM d, yyyy')}
                          {session.isMakeup && (
                            <Chip
                              label={t('sessionType.makeup', 'Makeup')}
                              size="small"
                              color="info"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {program.schedule.startTime} - {program.schedule.endTime}
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          {getSessionStatusIcon(session)}
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {getSessionStatusText(session)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {program.participants.length > 0 ? (
                          <Typography variant="body2">
                            {attendanceCount} / {program.participants.length} (
                            {attendancePercentage}
                            %)
                          </Typography>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            {t('noParticipants', 'No participants')}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                          {session.notes || t('noNotes', 'No notes')}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title={t('actions.takeAttendance', 'Take Attendance')}>
                          <IconButton size="small" color="primary">
                            <CheckCircleIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('actions.editSession', 'Edit Session')}>
                          <IconButton size="small">
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('actions.deleteSession', 'Delete Session')}>
                          <IconButton size="small" color="error">
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <ProgramBulkSessions
          program={program}
          sessions={sessions}
          onUpdate={() => window.location.reload()}
        />
      </TabPanel>
    </Box>
  );
};

export default ProgramSessions;
