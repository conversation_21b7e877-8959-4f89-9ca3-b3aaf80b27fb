import { render, screen, fireEvent } from '@testing-library/react';
import TextField from '../TextField';

describe('TextField', () => {
  const defaultProps = {
    label: 'Test Field',
    name: 'testField',
  };

  describe('Rendering', () => {
    it('renders without errors', () => {
      render(<TextField {...defaultProps} />);
      expect(screen.getByLabelText('Test Field')).toBeInTheDocument();
    });

    it('renders with all props', () => {
      const props = {
        ...defaultProps,
        placeholder: 'Enter text',
        helperText: 'Helper text',
        error: true,
        disabled: true,
      };
      render(<TextField {...props} />);

      const textField = screen.getByLabelText('Test Field');
      expect(textField).toHaveAttribute('placeholder', 'Enter text');
      expect(textField).toBeDisabled();
      expect(screen.getByText('Helper text')).toBeInTheDocument();
      const textFieldRoot = textField.closest('.MuiOutlinedInput-root');
      expect(textFieldRoot).toHaveClass('Mui-error');
    });
  });

  describe('Interactions', () => {
    it('handles user input', () => {
      render(<TextField {...defaultProps} />);
      const textField = screen.getByLabelText('Test Field');

      fireEvent.change(textField, { target: { value: 'test input' } });
      expect(textField).toHaveValue('test input');
    });

    it('handles onChange callback', () => {
      const handleChange = jest.fn();
      render(<TextField {...defaultProps} onChange={handleChange} />);

      const textField = screen.getByLabelText('Test Field');
      fireEvent.change(textField, { target: { value: 'test input' } });

      expect(handleChange).toHaveBeenCalled();
    });
  });

  describe('Styling', () => {
    it('applies custom styles through sx prop', () => {
      const customSx = {
        width: '300px',
        marginTop: '16px',
      };

      render(<TextField {...defaultProps} sx={customSx} />);
      const textField = screen.getByLabelText('Test Field');
      const textFieldRoot = textField.closest('.MuiTextField-root');
      expect(textField).not.toHaveAttribute('placeholder');
      expect(textField).not.toBeDisabled();
      expect(textFieldRoot).toHaveStyle({
        width: '300px',
        marginTop: '16px',
      });
    });

    it('applies default styles', () => {
      render(<TextField {...defaultProps} />);
      const textField = screen.getByLabelText('Test Field').closest('.MuiTextField-root');

      expect(textField).toHaveStyle({
        width: '100%',
      });
    });
  });
});
