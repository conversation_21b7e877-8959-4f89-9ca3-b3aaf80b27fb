import { TextField as MuiTextField, TextFieldProps } from '@mui/material';
import { forwardRef } from 'react';

const TextField = forwardRef<HTMLInputElement, TextFieldProps>((props, ref) => {
  return (
    <MuiTextField
      ref={ref}
      variant="outlined"
      fullWidth
      margin="normal"
      {...props}
      sx={{
        '& .MuiOutlinedInput-root': {
          borderRadius: 2,
          backgroundColor: 'background.paper',
          '&:hover fieldset': {
            borderColor: 'primary.main',
          },
        },
        ...props.sx,
      }}
    />
  );
});

TextField.displayName = 'TextField';

export default TextField;
