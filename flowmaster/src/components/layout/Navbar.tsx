import { App<PERSON><PERSON>, Toolbar, IconButton, Typography, Box, Menu, MenuItem } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import AccountCircle from '@mui/icons-material/AccountCircle';
import LanguageSwitcher from '../shared/LanguageSwitcher';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

interface NavbarProps {
  onMenuClick?: () => void;
}

const Navbar = ({ onMenuClick }: NavbarProps) => {
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleProfile = () => {
    navigate('/profile');
    handleClose();
  };

  const handleSettings = () => {
    navigate('/settings');
    handleClose();
  };

  const handleLogout = async () => {
    try {
      await signOut();
      handleClose();
      navigate('/auth/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <AppBar position="static" elevation={2} sx={{ bgcolor: '#2A5C6B' }}>
      <Toolbar>
        <IconButton
          edge="start"
          color="inherit"
          aria-label="menu"
          onClick={onMenuClick}
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>
        <Typography variant="h5" fontWeight="bold" component="div" sx={{ flexGrow: 1 }}>
          FlowMaster
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <LanguageSwitcher />
          <IconButton
            color="inherit"
            onClick={handleMenu}
            aria-controls="profile-menu"
            aria-haspopup="true"
            sx={{ padding: 1 }}
          >
            <AccountCircle sx={{ fontSize: 24 }} />
          </IconButton>
          <Menu
            id="profile-menu"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            PaperProps={{
              elevation: 0,
              sx: {
                minWidth: 180,
                mt: 1,
                bgcolor: 'white',
                border: '1px solid #E0E0E0',
                borderRadius: 1,
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                '& .MuiMenuItem-root': {
                  padding: '12px 16px',
                  '& .MuiTypography-root': {
                    fontSize: '0.875rem',
                  },
                },
              },
            }}
          >
            <MenuItem
              onClick={handleProfile}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                borderBottom: '1px solid #E0E0E0',
              }}
            >
              <Typography>Profile</Typography>
              <Typography variant="body2" color="text.secondary">
                User Profile
              </Typography>
            </MenuItem>
            <MenuItem
              onClick={handleSettings}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                borderBottom: '1px solid #E0E0E0',
              }}
            >
              <Typography>Settings</Typography>
              <Typography variant="body2" color="text.secondary">
                App Settings
              </Typography>
            </MenuItem>
            <MenuItem
              onClick={handleLogout}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
              }}
            >
              <Typography>Logout</Typography>
              <Typography variant="body2" color="text.secondary">
                Sign Out
              </Typography>
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
