import { Box, useMediaQuery, useTheme } from '@mui/material';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { useState } from 'react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isSidebarOpen, setIsSidebarOpen] = useState(!isMobile);

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Desktop layout
  const desktopLayout = (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar open={isSidebarOpen} onClose={handleSidebarToggle} />
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Navbar onMenuClick={handleSidebarToggle} />
        <Box
          component="main"
          sx={{
            flex: 1,
            p: { xs: 2, sm: 3, md: 4 },
            bgcolor: 'background.default',
            overflow: 'auto',
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );

  // Mobile-optimized layout
  const mobileLayout = (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Navbar onMenuClick={handleSidebarToggle} />
      <Sidebar open={isSidebarOpen} onClose={handleSidebarToggle} />
      <Box
        component="main"
        sx={{
          flex: 1,
          p: 2,
          bgcolor: 'background.default',
          overflow: 'auto',
          mt: 0,
        }}
      >
        {children}
      </Box>
    </Box>
  );

  return isMobile ? mobileLayout : desktopLayout;
};

export default Layout;
