import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
  IconButton,
  Box,
  Collapse,
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import EventIcon from '@mui/icons-material/Event';
import SettingsIcon from '@mui/icons-material/Settings';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import PersonIcon from '@mui/icons-material/Person';
import SchoolIcon from '@mui/icons-material/School';
import GroupIcon from '@mui/icons-material/Group';
import LanguageIcon from '@mui/icons-material/Language';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import SecurityIcon from '@mui/icons-material/Security';
import SportsHandballIcon from '@mui/icons-material/SportsHandball';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import LibraryBooksIcon from '@mui/icons-material/LibraryBooks';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import AssessmentIcon from '@mui/icons-material/Assessment';
import HowToRegIcon from '@mui/icons-material/HowToReg';
import InventoryIcon from '@mui/icons-material/Inventory';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

interface SubMenuItem {
  text: string;
  path: string;
  icon: JSX.Element;
}

interface MenuItem {
  text: string;
  icon: JSX.Element;
  path: string;
  subItems?: SubMenuItem[];
}

const Sidebar = ({ open, onClose }: SidebarProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation(['common', 'lessons']);
  const [openSubMenus, setOpenSubMenus] = useState<{ [key: string]: boolean }>({});

  // Add floating button for mobile
  const FloatingButton = () => (
    <IconButton
      onClick={onClose}
      sx={{
        position: 'fixed',
        left: open ? 'auto' : 0,
        top: '50%',
        transform: 'translateY(-50%)',
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.contrastText,
        zIndex: theme.zIndex.drawer + 2,
        '&:hover': {
          backgroundColor: theme.palette.primary.dark,
        },
        ...(open
          ? {}
          : {
              width: 40,
              height: 40,
              borderRadius: '0 4px 4px 0',
            }),
      }}
    >
      <ChevronLeftIcon sx={{ transform: open ? 'none' : 'rotate(180deg)' }} />
    </IconButton>
  );

  const menuItems: MenuItem[] = [
    { text: t('menu.dashboard', 'Dashboard'), icon: <DashboardIcon />, path: '/dashboard' },
    { text: t('menu.schedule', 'Schedule'), icon: <EventIcon />, path: '/schedule' },
    {
      text: t('menu.people', 'People'),
      icon: <PeopleIcon />,
      path: '/people',
      subItems: [
        { text: t('menu.students', 'Students'), icon: <SchoolIcon />, path: '/people/students' },
        {
          text: t('menu.instructors', 'Instructors'),
          icon: <PersonIcon />,
          path: '/people/instructors',
        },
        { text: t('menu.clients', 'Clients'), icon: <GroupIcon />, path: '/people/clients' },
      ],
    },
    {
      text: t('menu.lessons', 'Lessons'),
      icon: <MenuBookIcon />,
      path: '/lessons',
      subItems: [
        {
          text: t('menu.lessonIndividual', 'Individual Lessons'),
          icon: <PersonIcon />,
          path: '/lessons/individual',
        },
        {
          text: t('menu.lessonGroup', 'Group Lessons'),
          icon: <GroupIcon />,
          path: '/lessons/group',
        },
        {
          text: t('menu.lessonChildren', 'Children Lessons'),
          icon: <SchoolIcon />,
          path: '/lessons/children',
        },
        {
          text: t('menu.weeklyCourses', 'Weekly Courses'),
          icon: <EventIcon />,
          path: '/programs/weekly',
        },
        {
          text: t('menu.dailyCourses', 'Daily Courses'),
          icon: <EventIcon />,
          path: '/programs/daily',
        },
      ],
    },
    {
      text: t('menu.programs', 'Programs'),
      icon: <LibraryBooksIcon />,
      path: '/programs',
      subItems: [
        {
          text: t('menu.groupPrograms', 'Group Programs'),
          icon: <GroupIcon />,
          path: '/programs/group',
        },
        {
          text: t('menu.individualPrograms', 'Individual Programs'),
          icon: <PersonIcon />,
          path: '/programs/individual',
        },
      ],
    },
    {
      text: t('menu.reports', 'Reports'),
      icon: <AssessmentIcon />,
      path: '/reports',
      subItems: [
        {
          text: t('menu.attendanceReports', 'Attendance Reports'),
          icon: <HowToRegIcon />,
          path: '/reports/attendance',
        },
        {
          text: t('menu.equipmentReports', 'Equipment Reports'),
          icon: <InventoryIcon />,
          path: '/reports/equipment',
        },
      ],
    },
    {
      text: t('menu.equipment', 'Equipment'),
      icon: <InventoryIcon />,
      path: '/equipment',
      subItems: [
        {
          text: t('menu.equipmentInventory', 'Inventory'),
          icon: <InventoryIcon />,
          path: '/equipment',
        },
        {
          text: t('menu.equipmentRentals', 'Rentals'),
          icon: <EventIcon />,
          path: '/equipment/rentals',
        },
      ],
    },
    {
      text: t('menu.settings', 'Settings'),
      icon: <SettingsIcon />,
      path: '/settings',
      subItems: [
        {
          text: t('menu.settingsProfile', 'Profile'),
          icon: <PersonIcon />,
          path: '/settings/profile',
        },
        {
          text: t('menu.settingsLanguage', 'Language'),
          icon: <LanguageIcon />,
          path: '/settings/language',
        },
        {
          text: t('menu.settingsNotifications', 'Notifications'),
          icon: <NotificationsActiveIcon />,
          path: '/settings/notifications',
        },
        {
          text: t('menu.settingsSchool', 'School'),
          icon: <SchoolIcon />,
          path: '/settings/school',
        },
        {
          text: t('menu.settingsSport', 'Sport'),
          icon: <SportsHandballIcon />,
          path: '/settings/sports',
        },
        {
          text: t('menu.settingsBookings', 'Bookings'),
          icon: <EventAvailableIcon />,
          path: '/settings/bookings',
        },
        {
          text: t('menu.settingsSecurity', 'Security'),
          icon: <SecurityIcon />,
          path: '/settings/security',
        },
        {
          text: t('menu.settingsUsers', 'Users'),
          icon: <PersonIcon />,
          path: '/settings/users',
        },
        {
          text: t('menu.settingsFinancial', 'Financial'),
          icon: <AccountBalanceIcon />,
          path: '/settings/financial',
        },
        {
          text: t('menu.settingsSystem', 'System'),
          icon: <SettingsIcon />,
          path: '/settings/system',
        },
      ],
    },
  ];

  const handleSubMenuClick = (path: string) => {
    setOpenSubMenus((prev) => ({
      ...prev,
      [path]: !prev[path],
    }));
    navigate(path);
    if (isMobile) onClose();
  };

  const drawerContent = (
    <>
      <Box
        sx={{
          p: 2,
          height: '128px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: open ? 'space-between' : 'center',
          borderBottom: `1px solid ${theme.palette.divider}`,
          backgroundColor: '#F5F9FA',
        }}
      >
        <IconButton
          onClick={onClose}
          sx={{
            mr: open ? 2 : 0,
            color: theme.palette.text.secondary,
          }}
        >
          <ChevronLeftIcon
            sx={{
              transform: open ? 'rotate(0deg)' : 'rotate(180deg)',
              transition: 'transform 0.3s',
            }}
          />
        </IconButton>
        {open && (
          <Box
            sx={{
              width: '160px',
              height: '80px',
              backgroundColor: 'rgba(42, 92, 107, 0.1)',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.palette.text.primary,
            }}
          >
            Logo
          </Box>
        )}
      </Box>
      <List>
        {menuItems.map((item) => (
          <Box key={item.path}>
            <ListItem
              button
              onClick={() => {
                if (item.subItems) {
                  handleSubMenuClick(item.path);
                } else {
                  navigate(item.path);
                  if (isMobile) onClose();
                }
              }}
              selected={
                location.pathname === item.path || location.pathname.startsWith(item.path + '/')
              }
              sx={{
                my: 0.5,
                mx: 1,
                borderBottom: `1px solid ${theme.palette.divider}`,
                position: 'relative',
                '&.Mui-selected': {
                  backgroundColor: '#E8F1F3',
                  color: '#2A5C6B',
                  borderLeft: `6px solid #2A5C6B`,
                  '& .MuiListItemIcon-root': {
                    color: '#2A5C6B',
                  },
                  '&:hover': {
                    backgroundColor: '#D8E7EA',
                  },
                },
                transition: 'all 0.3s ease-in-out',
                borderRadius: '8px',
                paddingRight: '16px',
                paddingLeft: '16px',
                '&:hover': {
                  backgroundColor: '#EDF4F6',
                  transform: 'translateX(4px)',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
                },
              }}
            >
              <ListItemIcon
                sx={{
                  mr: 2,
                  color:
                    location.pathname === item.path
                      ? theme.palette.primary.dark
                      : theme.palette.text.secondary,
                  minWidth: open ? 'auto' : 48,
                }}
              >
                {item.icon}
              </ListItemIcon>
              {open && (
                <>
                  <ListItemText primary={item.text} />
                  {item.subItems && (openSubMenus[item.path] ? <ExpandLess /> : <ExpandMore />)}
                </>
              )}
            </ListItem>
            {item.subItems && (
              <Collapse in={openSubMenus[item.path]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.subItems.map((subItem) => (
                    <ListItem
                      button
                      key={subItem.path}
                      onClick={() => {
                        navigate(subItem.path);
                        if (isMobile) onClose();
                      }}
                      selected={location.pathname === subItem.path}
                      sx={{
                        pl: open ? 4 : 2,
                        my: 0.1,
                        mx: 2,
                        minHeight: '28px',
                        borderRadius: '8px',
                        '&.Mui-selected': {
                          backgroundColor: '#E8F1F3',
                          color: '#2A5C6B',
                          '& .MuiListItemIcon-root': {
                            color: '#2A5C6B',
                          },
                        },
                      }}
                    >
                      {open && <ListItemText primary={subItem.text} />}
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </Box>
        ))}
      </List>
    </>
  );

  return (
    <>
      {!open && <FloatingButton />}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        anchor="left"
        open={isMobile ? open : true}
        onClose={onClose}
        sx={{
          width: open ? 240 : 72,
          flexShrink: 0,
          whiteSpace: 'nowrap',
          '& .MuiDrawer-paper': {
            width: open ? 240 : 72,
            boxSizing: 'border-box',
            backgroundColor: '#F5F9FA',
            overflowX: 'hidden',
            ...(!isMobile && {
              position: 'relative',
              borderRight: `1px solid ${theme.palette.divider}`,
              boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)',
              borderRadius: '0 0 16px 0',
              height: '100%',
            }),
            transition: theme.transitions.create(['width'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          },
        }}
        ModalProps={{
          keepMounted: true,
        }}
      >
        {drawerContent}
      </Drawer>
    </>
  );
};

export default Sidebar;
