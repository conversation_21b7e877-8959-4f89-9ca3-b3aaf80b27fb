import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Layout from '../Layout';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material';

// Mock the Firebase auth
jest.mock('firebase/auth', () => ({
  getAuth: () => ({
    onAuthStateChanged: (callback: (user: null) => void) => {
      // Simulate auth state change and return unsubscribe handler
      callback(null);
      return () => {
        // Proper unsubscribe handler implementation
        callback(null);
      };
    },
  }),
}));

// Mock the AuthContext
jest.mock('../../../context/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    user: { role: 'user' },
    isAuthenticated: true,
    isLoading: false,
  }),
}));

const mockContent = <div data-testid="mock-content">Test Content</div>;
const theme = createTheme();

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <ThemeProvider theme={theme}>
      <BrowserRouter>{component}</BrowserRouter>
    </ThemeProvider>
  );
};

describe('Layout', () => {
  describe('Rendering', () => {
    it('renders without errors', () => {
      renderWithProviders(<Layout>{mockContent}</Layout>);

      expect(screen.getByTestId('mock-content')).toBeInTheDocument();
      expect(screen.getByRole('banner')).toBeInTheDocument();
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('renders child content correctly', () => {
      renderWithProviders(<Layout>{mockContent}</Layout>);

      const content = screen.getByTestId('mock-content');
      expect(content).toBeInTheDocument();
      expect(content.textContent).toBe('Test Content');
    });

    it('renders with proper layout structure', () => {
      renderWithProviders(<Layout>{mockContent}</Layout>);

      const mainContainer = screen.getByRole('main');
      expect(mainContainer).toBeInTheDocument();
      expect(mainContainer).toHaveClass('MuiBox-root');
    });
  });
});
