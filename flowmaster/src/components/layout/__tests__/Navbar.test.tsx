import { render, screen, fireEvent } from '@testing-library/react';
import { useMediaQuery } from '@mui/material';
import Navbar from '../Navbar';
import { useAuth } from '../../../hooks/useAuth';

// Mock the hooks
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  useMediaQuery: jest.fn(),
  useTheme: jest.fn(() => ({ breakpoints: { down: jest.fn() } })),
}));

jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

describe('Navbar', () => {
  const mockOnMenuClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useMediaQuery as jest.Mock).mockReturnValue(false); // Desktop by default
    (useAuth as jest.Mock).mockReturnValue({ user: { role: 'admin' } }); // Admin by default
  });

  describe('Desktop View', () => {
    it('renders admin navbar with menu button', () => {
      render(<Navbar onMenuClick={mockOnMenuClick} />);

      expect(screen.getByText('FlowMaster')).toBeInTheDocument();
      expect(screen.getByLabelText('menu')).toBeInTheDocument();
      expect(screen.getByTestId('AccountCircleIcon')).toBeInTheDocument();
    });

    it('handles menu click', () => {
      render(<Navbar onMenuClick={mockOnMenuClick} />);

      fireEvent.click(screen.getByLabelText('menu'));
      expect(mockOnMenuClick).toHaveBeenCalled();
    });

    it('renders non-admin navbar without menu button', () => {
      (useAuth as jest.Mock).mockReturnValue({ user: { role: 'user' } });
      render(<Navbar onMenuClick={mockOnMenuClick} />);

      expect(screen.getByText('FlowMaster')).toBeInTheDocument();
      expect(screen.queryByLabelText('menu')).not.toBeInTheDocument();
      expect(screen.getByTestId('AccountCircleIcon')).toBeInTheDocument();
    });
  });

  describe('Mobile View', () => {
    beforeEach(() => {
      (useMediaQuery as jest.Mock).mockReturnValue(true);
    });

    it('renders mobile navbar for non-admin', () => {
      (useAuth as jest.Mock).mockReturnValue({ user: { role: 'user' } });
      render(<Navbar />);

      const appBar = screen.getByRole('banner');
      const toolbar = appBar.querySelector('.MuiToolbar-dense');
      expect(toolbar).toBeInTheDocument();
      expect(screen.getByText('FlowMaster')).toBeInTheDocument();
      expect(screen.getByTestId('AccountCircleIcon')).toBeInTheDocument();
    });

    it('renders desktop navbar for admin even in mobile view', () => {
      render(<Navbar onMenuClick={mockOnMenuClick} />);

      const appBar = screen.getByRole('banner');
      const toolbar = appBar.querySelector('.MuiToolbar-root');
      expect(toolbar).toBeInTheDocument();
      expect(screen.getByLabelText('menu')).toBeInTheDocument();
    });
  });

  describe('Language Switcher', () => {
    it('renders language switcher in desktop view', () => {
      render(<Navbar />);
      expect(screen.getByRole('button', { name: /language/i })).toBeInTheDocument();
    });
  });
});
