import { render, screen, fireEvent } from '@testing-library/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTheme, useMediaQuery } from '@mui/material';
import Sidebar from '../Sidebar';

// Mock hooks
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
  useLocation: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));

jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  useTheme: jest.fn(),
  useMediaQuery: jest.fn(),
}));

describe('Sidebar', () => {
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
  };

  const mockNavigate = jest.fn();
  const mockTranslations = {
    'menu.dashboard': 'Dashboard',
    'menu.students': 'Students',
    'menu.schedule': 'Schedule',
    'menu.settings': 'Settings',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (useLocation as jest.Mock).mockReturnValue({ pathname: '/dashboard' });
    (useTranslation as jest.Mock).mockReturnValue({
      t: (key: keyof typeof mockTranslations) => mockTranslations[key] || key,
    });
    (useTheme as jest.Mock).mockReturnValue({
      breakpoints: { down: jest.fn() },
      palette: {
        primary: {
          main: '#1976d2',
          dark: '#115293',
          contrastText: '#fff',
        },
      },
    });
  });

  describe('Desktop View', () => {
    beforeEach(() => {
      (useMediaQuery as jest.Mock).mockReturnValue(false); // Desktop view
    });

    it('renders all menu items with correct translations', () => {
      render(<Sidebar {...defaultProps} />);
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Students')).toBeInTheDocument();
      expect(screen.getByText('Schedule')).toBeInTheDocument();
      expect(screen.getByText('Settings')).toBeInTheDocument();
    });

    it('highlights the current route', () => {
      render(<Sidebar {...defaultProps} />);

      const dashboardItem = screen.getByText('Dashboard').closest('div.MuiListItem-root');
      expect(dashboardItem).toHaveClass('Mui-selected');
    });

    it('navigates when menu item is clicked', () => {
      render(<Sidebar {...defaultProps} />);

      fireEvent.click(screen.getByText('Students'));
      expect(mockNavigate).toHaveBeenCalledWith('/students');
    });
  });

  describe('Mobile View', () => {
    beforeEach(() => {
      (useMediaQuery as jest.Mock).mockReturnValue(true); // Mobile view
    });

    it('shows close button in mobile view', () => {
      render(<Sidebar {...defaultProps} />);

      const closeButton = screen.getByTestId('ChevronLeftIcon').closest('button');
      expect(closeButton).toBeInTheDocument();
    });

    it('calls onClose when close button is clicked', () => {
      render(<Sidebar {...defaultProps} />);

      const closeButton = screen.getByTestId('ChevronLeftIcon').closest('button');
      if (!closeButton) throw new Error('Close button not found');
      fireEvent.click(closeButton);
      expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it('calls onClose when menu item is clicked in mobile view', () => {
      render(<Sidebar {...defaultProps} />);

      fireEvent.click(screen.getByText('Students'));
      expect(defaultProps.onClose).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/students');
    });
  });
});
