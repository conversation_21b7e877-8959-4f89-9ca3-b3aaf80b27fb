import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Today as TodayIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format, addDays, subDays, startOfDay, endOfDay, isToday } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { Lesson } from '../../types/lesson';

interface DailyTimelineViewProps {
  onLessonClick: (lesson: Lesson) => void;
}

const DailyTimelineView: React.FC<DailyTimelineViewProps> = ({ onLessonClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Time slots from 8:00 to 20:00 in 15-minute increments
  const timeSlots = Array.from({ length: 49 }, (_, i) => {
    const hour = Math.floor(i / 4) + 8;
    const minute = (i % 4) * 15;
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  });

  useEffect(() => {
    const fetchLessons = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      setError(null);

      try {
        const startTimestamp = Timestamp.fromDate(startOfDay(selectedDate));
        const endTimestamp = Timestamp.fromDate(endOfDay(selectedDate));

        let lessonsQuery;

        if (user?.role === 'instructor') {
          // If user is an instructor, only show their lessons
          lessonsQuery = query(
            collection(db, 'schools', currentSchool.id, 'lessons'),
            where('instructorId', '==', user.uid),
            where('startTime', '>=', startTimestamp),
            where('startTime', '<=', endTimestamp)
          );
        } else {
          // Otherwise show all lessons for the school
          lessonsQuery = query(
            collection(db, 'schools', currentSchool.id, 'lessons'),
            where('startTime', '>=', startTimestamp),
            where('startTime', '<=', endTimestamp)
          );
        }

        const lessonsSnapshot = await getDocs(lessonsQuery);
        const lessonsData = lessonsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Lesson[];

        setLessons(lessonsData);
      } catch (err) {
        console.error('Error fetching lessons:', err);
        setError(t('schedule.error.fetchLessons', 'Failed to fetch lessons'));
      } finally {
        setLoading(false);
      }
    };

    fetchLessons();
  }, [currentSchool?.id, selectedDate, user?.role, user?.uid, t]);

  const handlePreviousDay = () => {
    setSelectedDate((prev) => subDays(prev, 1));
  };

  const handleNextDay = () => {
    setSelectedDate((prev) => addDays(prev, 1));
  };

  const handleTodayClick = () => {
    setSelectedDate(new Date());
  };

  const handleAddLesson = () => {
    navigate('/lessons/create', { state: { date: selectedDate } });
  };

  const getLessonForTimeSlot = (timeSlot: string) => {
    const [hour, minute] = timeSlot.split(':').map(Number);

    return lessons.filter((lesson) => {
      if (!lesson.startTime) return false;

      const startTime =
        lesson.startTime instanceof Timestamp
          ? lesson.startTime.toDate()
          : new Date(lesson.startTime);

      const lessonHour = startTime.getHours();
      const lessonMinute = startTime.getMinutes();

      return lessonHour === hour && lessonMinute === minute;
    });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handlePreviousDay}>
            <NavigateBeforeIcon />
          </IconButton>

          <Typography variant="h6" sx={{ mx: 2 }}>
            {format(selectedDate, 'EEEE, MMMM d, yyyy')}
            {isToday(selectedDate) && (
              <Typography
                component="span"
                sx={{
                  ml: 1,
                  bgcolor: 'primary.main',
                  color: 'primary.contrastText',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.8rem',
                }}
              >
                {t('schedule.today', 'Today')}
              </Typography>
            )}
          </Typography>

          <IconButton onClick={handleNextDay}>
            <NavigateNextIcon />
          </IconButton>
        </Box>

        <Box>
          <Button startIcon={<TodayIcon />} onClick={handleTodayClick} sx={{ mr: 1 }}>
            {t('schedule.today', 'Today')}
          </Button>

          <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddLesson}>
            {t('lessons.add', 'Add Lesson')}
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Typography color="error" sx={{ p: 2 }}>
          {error}
        </Typography>
      ) : (
        <Paper sx={{ p: 2, overflowX: 'auto' }}>
          <Box sx={{ minWidth: 600 }}>
            {timeSlots.map((timeSlot, index) => {
              const lessonsAtTimeSlot = getLessonForTimeSlot(timeSlot);
              const isHourMark = timeSlot.endsWith('00');

              return (
                <React.Fragment key={timeSlot}>
                  <Grid
                    container
                    sx={{
                      py: isHourMark ? 1 : 0.5,
                      bgcolor: isHourMark ? 'action.hover' : 'transparent',
                    }}
                  >
                    <Grid item xs={1}>
                      <Typography
                        variant={isHourMark ? 'body1' : 'body2'}
                        color={isHourMark ? 'textPrimary' : 'textSecondary'}
                        sx={{ fontWeight: isHourMark ? 'bold' : 'normal' }}
                      >
                        {timeSlot}
                      </Typography>
                    </Grid>

                    <Grid item xs={11}>
                      {lessonsAtTimeSlot.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {lessonsAtTimeSlot.map((lesson) => (
                            <Paper
                              key={lesson.id}
                              sx={{
                                p: 1,
                                bgcolor: 'primary.light',
                                color: 'primary.contrastText',
                                cursor: 'pointer',
                                '&:hover': {
                                  bgcolor: 'primary.main',
                                },
                                flex: '1 1 200px',
                                maxWidth: '300px',
                              }}
                              onClick={() => onLessonClick(lesson)}
                            >
                              <Typography variant="body2" fontWeight="bold">
                                {lesson.title}
                              </Typography>
                              <Typography variant="caption" display="block">
                                {lesson.duration} {t('lessons.minutes', 'minutes')}
                              </Typography>
                            </Paper>
                          ))}
                        </Box>
                      ) : null}
                    </Grid>
                  </Grid>

                  {isHourMark && index < timeSlots.length - 1 && <Divider />}
                </React.Fragment>
              );
            })}
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default DailyTimelineView;
