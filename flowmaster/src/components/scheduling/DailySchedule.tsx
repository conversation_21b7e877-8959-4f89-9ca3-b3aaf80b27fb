import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  useTheme,
  alpha,
} from '@mui/material';
import { Lesson } from '../../types/lesson';
import { Instructor } from '../../types/instructor';

// Define time slots for the day (30-minute intervals from 8:00 to 20:00)
const TIME_SLOTS = Array.from({ length: 25 }, (_, i) => {
  const hour = Math.floor(i / 2) + 8;
  const minute = (i % 2) * 30;
  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
});

interface LessonWithPosition {
  lesson: Lesson;
  startSlot: number;
  endSlot: number;
}

interface DailyScheduleProps {
  date: Date;
  instructors: Instructor[];
  lessons: Lesson[];
  onLessonClick?: (lesson: Lesson) => void;
  isLoading?: boolean;
  error?: string | null;
}

const DailySchedule: React.FC<DailyScheduleProps> = ({
  date,
  instructors,
  lessons,
  onLessonClick,
  isLoading = false,
  error = null,
}) => {
  const { t } = useTranslation(['scheduling', 'common']);
  const theme = useTheme();
  const [lessonMap, setLessonMap] = useState<Record<string, LessonWithPosition[]>>({});

  // Process lessons to map them to instructors and time slots
  useEffect(() => {
    const newLessonMap: Record<string, LessonWithPosition[]> = {};

    // Initialize empty arrays for each instructor
    instructors.forEach((instructor) => {
      newLessonMap[instructor.id] = [];
    });

    // Map lessons to instructors and calculate their positions in the time grid
    lessons.forEach((lesson) => {
      const startTime =
        lesson.startTime instanceof Date ? lesson.startTime : lesson.startTime.toDate();

      // Skip lessons not on the selected date
      const lessonDate = new Date(startTime);
      if (lessonDate.toDateString() !== date.toDateString()) return;

      // Calculate start and end slots
      const startHour = lessonDate.getHours();
      const startMinute = lessonDate.getMinutes();
      const startSlot = (startHour - 8) * 2 + (startMinute >= 30 ? 1 : 0);

      // Calculate end slot based on duration (in minutes)
      const durationInSlots = Math.ceil(lesson.duration / 30);
      const endSlot = startSlot + durationInSlots;

      // Add lesson to the instructor's array
      if (newLessonMap[lesson.instructorId]) {
        newLessonMap[lesson.instructorId].push({
          lesson,
          startSlot,
          endSlot,
        });
      }
    });

    setLessonMap(newLessonMap);
  }, [lessons, instructors, date]);

  // Get status color for lesson
  const getLessonStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return theme.palette.info.main;
      case 'completed':
        return theme.palette.success.main;
      case 'cancelled':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Render a lesson cell
  const renderLessonCell = (lesson: Lesson, startSlot: number, endSlot: number) => {
    const spanCount = endSlot - startSlot;
    const statusColor = getLessonStatusColor(lesson.status);

    return (
      <TableCell
        colSpan={spanCount}
        sx={{
          backgroundColor: alpha(statusColor, 0.2),
          borderLeft: `4px solid ${statusColor}`,
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: alpha(statusColor, 0.3),
          },
          p: 1,
        }}
        onClick={() => onLessonClick && onLessonClick(lesson)}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Typography variant="subtitle2" noWrap>
            {lesson.title}
          </Typography>
          <Typography variant="caption" noWrap>
            {lesson.type === 'individual'
              ? t('schedule:lessonType.individual', 'Individual')
              : lesson.type === 'group'
                ? t('schedule:lessonType.group', 'Group')
                : t('schedule:lessonType.children', 'Children')}
          </Typography>
          <Chip
            size="small"
            label={t(`schedule:status.${lesson.status}`, lesson.status)}
            sx={{
              backgroundColor: statusColor,
              color: '#fff',
              maxWidth: '100%',
              mt: 0.5,
            }}
          />
        </Box>
      </TableCell>
    );
  };

  // Render empty cells for a row
  const renderEmptyCells = (instructorId: string) => {
    const instructorLessons = lessonMap[instructorId] || [];
    const cells = [];
    let currentSlot = 0;

    // Sort lessons by start slot
    instructorLessons.sort((a, b) => a.startSlot - b.startSlot);

    // Fill in empty and lesson cells
    for (const { lesson, startSlot, endSlot } of instructorLessons) {
      // Add empty cells before the lesson
      if (startSlot > currentSlot) {
        cells.push(
          <TableCell
            key={`empty-${instructorId}-${currentSlot}`}
            colSpan={startSlot - currentSlot}
            sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}
          />
        );
      }

      // Add the lesson cell
      cells.push(
        <React.Fragment key={`lesson-${lesson.id}`}>
          {renderLessonCell(lesson, startSlot, endSlot)}
        </React.Fragment>
      );

      currentSlot = endSlot;
    }

    // Add empty cells after the last lesson
    if (currentSlot < TIME_SLOTS.length - 1) {
      cells.push(
        <TableCell
          key={`empty-${instructorId}-end`}
          colSpan={TIME_SLOTS.length - currentSlot}
          sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}
        />
      );
    }

    return cells;
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Paper sx={{ overflow: 'hidden' }}>
      <Box p={2} display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">
          {t('schedule:daily.title', 'Daily Schedule')} - {date.toLocaleDateString()}
        </Typography>
      </Box>

      <TableContainer sx={{ maxHeight: 'calc(100vh - 250px)', overflowX: 'auto' }}>
        <Table stickyHeader sx={{ minWidth: 1200 }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  minWidth: 200,
                  position: 'sticky',
                  left: 0,
                  zIndex: 3,
                  backgroundColor: theme.palette.background.paper,
                }}
              >
                {t('schedule:instructors', 'Instructors')}
              </TableCell>
              {TIME_SLOTS.map((timeSlot) => (
                <TableCell key={timeSlot} align="center" sx={{ minWidth: 80 }}>
                  {timeSlot}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {instructors.map((instructor) => (
              <TableRow key={instructor.id}>
                <TableCell
                  sx={{
                    position: 'sticky',
                    left: 0,
                    zIndex: 2,
                    backgroundColor: theme.palette.background.paper,
                    fontWeight: 'bold',
                  }}
                >
                  {instructor.firstName} {instructor.lastName}
                </TableCell>
                {renderEmptyCells(instructor.id)}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default DailySchedule;
