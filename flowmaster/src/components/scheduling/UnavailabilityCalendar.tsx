import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
} from '@mui/material';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Add as AddIcon, Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';

interface UnavailabilityPeriod {
  id: string;
  startDate: Date;
  endDate: Date;
  isRecurring: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  reason?: string;
}

interface UnavailabilityCalendarProps {
  instructorId: string;
  unavailablePeriods: UnavailabilityPeriod[];
  onAddPeriod: (period: Omit<UnavailabilityPeriod, 'id'>) => void;
  onEditPeriod: (period: UnavailabilityPeriod) => void;
  onDeletePeriod: (periodId: string) => void;
  isManager?: boolean;
}

const UnavailabilityCalendar: React.FC<UnavailabilityCalendarProps> = ({
  instructorId,
  unavailablePeriods,
  onAddPeriod,
  onEditPeriod,
  onDeletePeriod,
  isManager = false,
}) => {
  const { t } = useTranslation(['scheduling', 'common']);
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPeriod, setEditingPeriod] = useState<UnavailabilityPeriod | null>(null);
  const [formData, setFormData] = useState<{
    startDate: Date;
    endDate: Date;
    isRecurring: boolean;
    recurringPattern: 'daily' | 'weekly' | 'monthly';
    reason: string;
  }>({
    startDate: new Date(),
    endDate: new Date(),
    isRecurring: false,
    recurringPattern: 'weekly',
    reason: '',
  });

  const handleAddClick = () => {
    setEditingPeriod(null);
    setFormData({
      startDate: new Date(),
      endDate: new Date(),
      isRecurring: false,
      recurringPattern: 'weekly',
      reason: '',
    });
    setIsDialogOpen(true);
  };

  const handleEditClick = (period: UnavailabilityPeriod) => {
    setEditingPeriod(period);
    setFormData({
      startDate: period.startDate,
      endDate: period.endDate,
      isRecurring: period.isRecurring,
      recurringPattern: period.recurringPattern || 'weekly',
      reason: period.reason || '',
    });
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingPeriod(null);
  };

  const handleSave = () => {
    if (editingPeriod) {
      onEditPeriod({
        ...editingPeriod,
        ...formData,
      });
    } else {
      onAddPeriod(formData);
    }
    handleDialogClose();
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          {t('unavailability.title', 'Unavailability Schedule')} - {instructorId}
        </Typography>
        {isManager && (
          <IconButton onClick={handleAddClick} color="primary">
            <AddIcon />
          </IconButton>
        )}
      </Box>

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={7}>
            <DateCalendar
              value={selectedDate}
              onChange={(newValue) => setSelectedDate(newValue)}
              sx={{ width: '100%' }}
            />
          </Grid>
          <Grid item xs={12} md={5}>
            <Typography variant="subtitle1" gutterBottom>
              {t('unavailability.periods', 'Unavailable Periods')}
            </Typography>
            <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
              {unavailablePeriods.map((period) => (
                <Box
                  key={period.id}
                  sx={{
                    p: 2,
                    mb: 1,
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Box>
                    <Typography variant="subtitle2">
                      {period.startDate.toLocaleDateString()} -{' '}
                      {period.endDate.toLocaleDateString()}
                    </Typography>
                    {period.isRecurring && (
                      <Typography variant="body2" color="primary">
                        {t(
                          `unavailability.recurring.${period.recurringPattern}`,
                          period.recurringPattern || 'weekly'
                        )}
                      </Typography>
                    )}
                    {period.reason && (
                      <Typography variant="body2" color="text.secondary">
                        {period.reason}
                      </Typography>
                    )}
                  </Box>
                  {isManager && (
                    <Box>
                      <IconButton
                        size="small"
                        onClick={() => handleEditClick(period)}
                        sx={{ mr: 1 }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => onDeletePeriod(period.id)}
                        color="error"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )}
                </Box>
              ))}
            </Box>
          </Grid>
        </Grid>
      </LocalizationProvider>

      <Dialog open={isDialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingPeriod
            ? t('unavailability.editPeriod', 'Edit Unavailable Period')
            : t('unavailability.addPeriod', 'Add Unavailable Period')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DateCalendar
                    value={formData.startDate}
                    onChange={(date) => setFormData({ ...formData, startDate: date || new Date() })}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DateCalendar
                    value={formData.endDate}
                    onChange={(date) => setFormData({ ...formData, endDate: date || new Date() })}
                  />
                </LocalizationProvider>
              </Grid>
            </Grid>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isRecurring}
                  onChange={(e) => setFormData({ ...formData, isRecurring: e.target.checked })}
                />
              }
              label={t('unavailability.recurring.enable', 'Recurring')}
              sx={{ mt: 2 }}
            />

            {formData.isRecurring && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>
                  {t('unavailability.recurring.pattern', 'Recurring Pattern')}
                </InputLabel>
                <Select
                  value={formData.recurringPattern}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      recurringPattern: e.target.value as 'daily' | 'weekly' | 'monthly',
                    })
                  }
                >
                  <MenuItem value="daily">{t('unavailability.recurring.daily', 'Daily')}</MenuItem>
                  <MenuItem value="weekly">
                    {t('unavailability.recurring.weekly', 'Weekly')}
                  </MenuItem>
                  <MenuItem value="monthly">
                    {t('unavailability.recurring.monthly', 'Monthly')}
                  </MenuItem>
                </Select>
              </FormControl>
            )}

            <TextField
              fullWidth
              label={t('unavailability.reason', 'Reason (Optional)')}
              value={formData.reason}
              onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
              multiline
              rows={2}
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>{t('common:ui.cancel')}</Button>
          <Button onClick={handleSave} variant="contained" color="primary">
            {t('common:ui.save')}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default UnavailabilityCalendar;
