import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';

interface StudentRelationship {
  studentId: string;
  firstName: string;
  lastName: string;
  email: string;
  level: string;
  specialties: string[];
  lastLesson?: string;
  nextLesson?: string;
  totalLessons: number;
  progress: number;
}

interface StudentRelationshipsProps {
  instructorId: string;
  relationships: StudentRelationship[];
  loading?: boolean;
  onStudentClick: (studentId: string) => void;
}

const StudentRelationships: React.FC<StudentRelationshipsProps> = ({
  relationships,
  loading = false,
  onStudentClick,
}) => {
  const { t } = useTranslation(['people', 'common']);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('instructors.currentStudents')}
      </Typography>

      <List sx={{ width: '100%' }}>
        {relationships.map((relationship, index) => (
          <React.Fragment key={relationship.studentId}>
            <ListItem
              alignItems="flex-start"
              sx={{
                cursor: 'pointer',
                '&:hover': { bgcolor: 'action.hover' },
              }}
              onClick={() => onStudentClick(relationship.studentId)}
            >
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: 'primary.main' }}>{relationship.firstName[0]}</Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle1">
                      {`${relationship.firstName} ${relationship.lastName}`}
                    </Typography>
                    <Chip
                      label={relationship.level}
                      size="small"
                      sx={{ bgcolor: 'primary.light', color: 'white' }}
                    />
                  </Box>
                }
                secondary={
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {relationship.email}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 1 }}>
                      {relationship.specialties.map((specialty) => (
                        <Chip
                          key={specialty}
                          label={specialty}
                          size="small"
                          sx={{
                            bgcolor: 'primary.light',
                            color: 'white',
                            fontSize: '0.75rem',
                          }}
                        />
                      ))}
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('instructors.totalLessons', { count: relationship.totalLessons })}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          {t('instructors.progress')}: {relationship.progress}%
                        </Typography>
                        <Tooltip title={t('instructors.viewProgress')}>
                          <IconButton size="small" color="primary">
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                  </Box>
                }
              />
            </ListItem>
            {index < relationships.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );
};

export default StudentRelationships;
