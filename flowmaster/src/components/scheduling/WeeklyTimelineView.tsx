import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Button,
  ButtonGroup,
  Card,
  CardContent,
  CardActions,
  Grid,
  Divider,
  IconButton,
  Collapse,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  format,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  addWeeks,
  subWeeks,
  getDay,
} from 'date-fns';
import { Instructor } from '../../types/instructor';
import { Student } from '../../types/student';
import { Lesson, LessonStatus } from '../../types/lesson';
import { collection, getDocs, query, where, Timestamp } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { useSchool } from '../../hooks/useSchool';
import {
  ChevronLeft,
  ChevronRight,
  Today,
  Close as CloseIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Event as EventIcon,
} from '@mui/icons-material';
import LessonRescheduleDialog from '../lessons/LessonRescheduleDialog';

interface LessonWithPosition {
  lesson: Lesson;
  day: number; // 0-6 for days of the week
  startHour: number;
  endHour: number;
}

interface WeeklyTimelineViewProps {
  onLessonClick?: (lesson: Lesson) => void;
}

// Time block options
const TIME_BLOCK_OPTIONS = [
  { value: 1, label: '1hour' },
  { value: 2, label: '2hours' },
  { value: 4, label: '4hours' },
];

// Default time range
const DEFAULT_START_HOUR = 8;
const DEFAULT_END_HOUR = 20;

const WeeklyTimelineView: React.FC<WeeklyTimelineViewProps> = (props) => {
  const { onLessonClick } = props;
  const { t } = useTranslation(['scheduling', 'common']);
  const { currentSchool } = useSchool();

  // State for date and view settings
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  const [timeBlockSize, setTimeBlockSize] = useState<number>(2); // Default to 2-hour blocks

  // State for data
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for lesson display
  const [lessonMap, setLessonMap] = useState<Record<string, LessonWithPosition[]>>({});
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);
  const [selectedInstructor, setSelectedInstructor] = useState<Instructor | null>(null);
  const [detailsExpanded, setDetailsExpanded] = useState(true);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);

  // Generate time blocks based on selected size
  const generateTimeBlocks = () => {
    const blocks = [];
    for (let hour = DEFAULT_START_HOUR; hour < DEFAULT_END_HOUR; hour += timeBlockSize) {
      blocks.push({
        start: hour,
        end: Math.min(hour + timeBlockSize, DEFAULT_END_HOUR),
        label: `${hour}:00 - ${Math.min(hour + timeBlockSize, DEFAULT_END_HOUR)}:00`,
      });
    }
    return blocks;
  };

  const timeBlocks = generateTimeBlocks();

  // Update week days when selected date changes
  useEffect(() => {
    const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Start on Monday
    const end = endOfWeek(selectedDate, { weekStartsOn: 1 }); // End on Sunday
    const days = eachDayOfInterval({ start, end });

    // We only want Monday to Saturday (6 days)
    setWeekDays(days.slice(0, 6));
  }, [selectedDate]);

  // Fetch instructors and students
  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id) return;

      try {
        // Fetch instructors
        const instructorsCollection = collection(db, 'schools', currentSchool.id, 'instructors');
        const instructorsSnapshot = await getDocs(instructorsCollection);
        const instructorsData = instructorsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Instructor[];

        setInstructors(instructorsData.filter((instructor) => instructor.status === 'active'));

        // Fetch students
        const studentsCollection = collection(db, 'schools', currentSchool.id, 'students');
        const studentsSnapshot = await getDocs(studentsCollection);
        const studentsData = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Student[];

        setStudents(studentsData.filter((student) => student.status === 'active'));
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('schedule.error.data', 'Failed to fetch data'));
      }
    };

    fetchData();
  }, [currentSchool?.id, t]);

  // Fetch lessons for the selected week
  useEffect(() => {
    const fetchLessons = async () => {
      if (!currentSchool?.id || weekDays.length === 0) return;
      setLoading(true);
      setError(null);

      try {
        const startOfWeekDate = weekDays[0];
        startOfWeekDate.setHours(0, 0, 0, 0);

        const endOfWeekDate = new Date(weekDays[weekDays.length - 1]);
        endOfWeekDate.setHours(23, 59, 59, 999);

        const lessonsCollection = collection(db, 'schools', currentSchool.id, 'lessons');
        const q = query(
          lessonsCollection,
          where('startTime', '>=', Timestamp.fromDate(startOfWeekDate)),
          where('startTime', '<=', Timestamp.fromDate(endOfWeekDate))
        );

        const lessonsSnapshot = await getDocs(q);
        const lessonsData = lessonsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Lesson[];

        setLessons(lessonsData);
      } catch (err) {
        console.error('Error fetching lessons:', err);
        setError(t('schedule.error.lessons', 'Failed to fetch lessons'));
      } finally {
        setLoading(false);
      }
    };

    if (weekDays.length > 0) {
      fetchLessons();
    }
  }, [currentSchool?.id, weekDays, t]);

  // Process lessons to map them to instructors, days, and time blocks
  useEffect(() => {
    const newLessonMap: Record<string, LessonWithPosition[]> = {};

    // Initialize empty arrays for each instructor
    instructors.forEach((instructor) => {
      newLessonMap[instructor.id] = [];
    });

    // Map lessons to instructors and calculate their positions in the weekly grid
    lessons.forEach((lesson) => {
      const startTime =
        lesson.startTime instanceof Date ? lesson.startTime : lesson.startTime.toDate();

      // Get day of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
      const dayOfWeek = getDay(startTime);

      // Convert to our index (0 = Monday, ..., 5 = Saturday)
      const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

      // Skip Sunday (we only show Monday to Saturday)
      if (dayIndex > 5) return;

      // Calculate start and end hours
      const startHour = startTime.getHours();
      const endHour = Math.min(startHour + Math.ceil(lesson.duration / 60), DEFAULT_END_HOUR);

      // Add lesson to the instructor's array if it falls within our time range
      if (
        newLessonMap[lesson.instructorId] &&
        startHour >= DEFAULT_START_HOUR &&
        startHour < DEFAULT_END_HOUR
      ) {
        newLessonMap[lesson.instructorId].push({
          lesson,
          day: dayIndex,
          startHour,
          endHour,
        });
      }
    });

    setLessonMap(newLessonMap);
  }, [lessons, instructors]);

  // Handle time block size change
  const handleTimeBlockSizeChange = (event: SelectChangeEvent<number>) => {
    setTimeBlockSize(event.target.value as number);
  };

  // Handle date navigation
  const goToToday = () => {
    setSelectedDate(new Date());
  };

  const goToPreviousWeek = () => {
    setSelectedDate((prevDate) => subWeeks(prevDate, 1));
  };

  const goToNextWeek = () => {
    setSelectedDate((prevDate) => addWeeks(prevDate, 1));
  };

  // Handle lesson selection
  const handleLessonClick = (lesson: Lesson) => {
    // If the same lesson is clicked again, toggle selection
    if (selectedLesson && selectedLesson.id === lesson.id) {
      setSelectedLesson(null);
      setSelectedInstructor(null);
      return;
    }

    setSelectedLesson(lesson);

    // Find the instructor for this lesson
    const instructor = instructors.find((i) => i.id === lesson.instructorId);
    if (instructor) {
      setSelectedInstructor(instructor);
    }

    // Call the external click handler if provided
    if (onLessonClick) {
      onLessonClick(lesson);
    }
  };

  // Toggle lesson details expansion
  const toggleDetailsExpanded = () => {
    setDetailsExpanded(!detailsExpanded);
  };

  // Clear selected lesson
  const clearSelectedLesson = () => {
    setSelectedLesson(null);
    setSelectedInstructor(null);
  };

  // Get status color for lesson
  const getLessonStatusColor = (status: LessonStatus) => {
    switch (status) {
      case 'scheduled':
        return 'primary';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Render a lesson cell
  const renderLessonCell = (lessonPosition: LessonWithPosition) => {
    const { lesson, startHour, endHour } = lessonPosition;
    const statusColor = getLessonStatusColor(lesson.status);
    const isSelected = selectedLesson && selectedLesson.id === lesson.id;

    // Calculate which time block this lesson belongs to
    const blockStart = Math.floor((startHour - DEFAULT_START_HOUR) / timeBlockSize);
    const blockEnd = Math.ceil((endHour - DEFAULT_START_HOUR) / timeBlockSize);
    const rowSpan = Math.max(1, blockEnd - blockStart);

    // Format the time display
    const startTime =
      lesson.startTime instanceof Date ? lesson.startTime : lesson.startTime.toDate();
    const endTime = new Date(startTime.getTime() + lesson.duration * 60000);
    const timeDisplay = `${format(startTime, 'HH:mm')} - ${format(endTime, 'HH:mm')}`;

    return (
      <Box
        sx={{
          backgroundColor: isSelected ? `${statusColor}.dark` : `${statusColor}.main`,
          color: 'white',
          border: '1px solid',
          borderColor: `${statusColor}.dark`,
          borderRadius: 1,
          p: 1,
          m: 0.5,
          height: `calc(100% - 8px)`,
          position: 'relative',
          boxShadow: isSelected ? '0 0 0 2px #fff, 0 0 0 4px #1976d2' : '0 2px 4px rgba(0,0,0,0.1)',
          overflow: 'hidden',
          transition: 'all 0.2s ease',
          gridRow: `span ${rowSpan}`,
          width: 'auto',
          maxWidth: '100%',
          '&:hover': {
            opacity: 0.9,
            cursor: 'pointer',
            boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
          },
        }}
        onClick={() => handleLessonClick(lesson)}
      >
        <Typography
          variant="subtitle2"
          fontWeight="bold"
          sx={{
            mb: 0.5,
            fontSize: '0.875rem',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {lesson.title}
        </Typography>
        <Typography
          variant="caption"
          sx={{
            mb: 0.5,
            display: 'block',
            fontWeight: 'bold',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {timeDisplay}
        </Typography>
        <Typography
          variant="caption"
          display="block"
          sx={{
            mb: 0.5,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {lesson.type === 'individual'
            ? t('schedule:lessonType.individual', 'Individual')
            : lesson.type === 'group'
              ? t('schedule:lessonType.group', 'Group')
              : t('schedule:lessonType.children', 'Children')}
        </Typography>
        <Box sx={{ mt: 0.5 }}>
          <Chip
            size="small"
            variant="outlined"
            sx={{
              fontWeight: 'medium',
              color: 'white',
              borderColor: 'white',
              fontSize: '0.7rem',
            }}
            label={t(`schedule:status.${lesson.status}`, lesson.status)}
          />
        </Box>
      </Box>
    );
  };

  // Render lesson details card
  const renderLessonDetails = () => {
    if (!selectedLesson) return null;

    const startTime =
      selectedLesson.startTime instanceof Date
        ? selectedLesson.startTime
        : selectedLesson.startTime.toDate();
    const endTime = new Date(startTime.getTime() + selectedLesson.duration * 60000);

    return (
      <Card
        sx={{
          mt: 3,
          mb: 3,
          position: 'relative',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          borderRadius: 2,
          overflow: 'visible',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            bgcolor: 'primary.main',
            color: 'white',
            p: 2,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ mr: 1 }} />
            <Typography variant="h6">{t('schedule.lessonDetails', 'Lesson Details')}</Typography>
          </Box>
          <Box>
            <IconButton size="small" onClick={toggleDetailsExpanded} sx={{ color: 'white', mr: 1 }}>
              {detailsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
            <IconButton size="small" onClick={clearSelectedLesson} sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Collapse in={detailsExpanded}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h5" gutterBottom>
                  {selectedLesson.title}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body1">{format(startTime, 'EEEE, MMMM d, yyyy')}</Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body1">
                    {format(startTime, 'HH:mm')} - {format(endTime, 'HH:mm')} (
                    {selectedLesson.duration} {t('schedule.minutes', 'minutes')})
                  </Typography>
                </Box>

                {selectedInstructor && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body1">
                      {selectedInstructor.firstName} {selectedInstructor.lastName}
                    </Typography>
                  </Box>
                )}

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip
                    label={t(`schedule:status.${selectedLesson.status}`, selectedLesson.status)}
                    color={
                      getLessonStatusColor(selectedLesson.status) as
                        | 'primary'
                        | 'success'
                        | 'error'
                        | 'default'
                    }
                    sx={{ mr: 1 }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                  {t('schedule.lessonNotes', 'Notes')}
                </Typography>
                <Typography variant="body2" paragraph>
                  {selectedLesson.notes ||
                    t('schedule.noNotes', 'No notes available for this lesson.')}
                </Typography>

                {selectedLesson.studentIds && selectedLesson.studentIds.length > 0 && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      {t('schedule.students', 'Students')}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      {selectedLesson.studentIds.map((studentId) => {
                        const student = students.find((s) => s.id === studentId);
                        return (
                          <Box
                            key={studentId}
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              mb: 1,
                              p: 1,
                              borderRadius: 1,
                              bgcolor: 'background.paper',
                              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                            }}
                          >
                            <PersonIcon sx={{ mr: 1, color: 'primary.main', fontSize: '1rem' }} />
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {student ? `${student.firstName} ${student.lastName}` : studentId}
                              </Typography>
                              {student?.level && (
                                <Typography variant="caption" color="text.secondary">
                                  {t('schedule.level', 'Level')}: {student.level}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        );
                      })}
                    </Box>
                  </>
                )}
              </Grid>
            </Grid>
          </CardContent>

          <CardActions sx={{ p: 2, pt: 0 }}>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => onLessonClick && onLessonClick(selectedLesson)}
              sx={{ mr: 1 }}
            >
              {t('schedule.editLesson', 'Edit Lesson')}
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<EventIcon />}
              onClick={() => setIsRescheduleDialogOpen(true)}
            >
              {t('lessons:reschedule.button', 'Reschedule')}
            </Button>
          </CardActions>
        </Collapse>
      </Card>
    );
  };

  // Render instructor row with lessons
  const renderInstructorRow = (instructor: Instructor) => {
    const instructorLessons = lessonMap[instructor.id] || [];

    return (
      <Box key={instructor.id} sx={{ mb: 3 }}>
        <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
          {instructor.firstName} {instructor.lastName}
        </Typography>

        <Grid container spacing={1} sx={{ mb: 2 }}>
          {/* Time block labels column */}
          <Grid item xs={1} sx={{ maxWidth: '8.333%' }}>
            <Box
              sx={{
                height: '100%',
                minHeight: timeBlocks.length * 60 + 'px',
                display: 'grid',
                gridTemplateRows: `repeat(${timeBlocks.length}, 1fr)`,
                gap: '4px',
                pr: 1,
              }}
            >
              {timeBlocks.map((block, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    height: '100%',
                    borderBottom: index < timeBlocks.length - 1 ? '1px dashed' : 'none',
                    borderColor: 'divider',
                    pb: 0.5,
                  }}
                >
                  <Typography variant="caption" color="text.secondary" noWrap>
                    {block.label}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Grid>

          {/* Days columns */}
          {weekDays.map((_, dayIndex) => (
            <Grid item xs={1.8} key={dayIndex} sx={{ maxWidth: '15%' }}>
              <Box
                sx={{
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1,
                  p: 1,
                  height: '100%',
                  minHeight: timeBlocks.length * 60 + 'px',
                  position: 'relative',
                  display: 'grid',
                  gridTemplateRows: `repeat(${timeBlocks.length}, 1fr)`,
                  gap: '4px',
                  overflow: 'hidden',
                  width: '100%',
                }}
              >
                {/* Render time block separators */}
                {timeBlocks.map((_, index) => (
                  <Box
                    key={index}
                    sx={{
                      position: 'absolute',
                      left: 0,
                      right: 0,
                      top: `${((index + 1) / timeBlocks.length) * 100}%`,
                      height: '1px',
                      backgroundColor: 'divider',
                      zIndex: 1,
                    }}
                  />
                ))}

                {/* Render lessons for this day */}
                {instructorLessons
                  .filter((lessonPos) => lessonPos.day === dayIndex)
                  .map((lessonPos) => renderLessonCell(lessonPos))}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  if (loading && instructors.length === 0) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper
      sx={{
        p: 3,
        mb: 3,
        height: 'auto',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        maxWidth: '1500px',
        width: '100%',
        mx: 'auto',
        overflow: 'visible',
      }}
    >
      <Box
        sx={{
          mb: 3,
          backgroundColor: 'background.paper',
          zIndex: 4,
          position: 'sticky',
          top: 0,
          width: '100%',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            {t('schedule.weekly.title', 'Weekly Overview')} - {format(weekDays[0], 'MMM d')} -{' '}
            {format(weekDays[weekDays.length - 1], 'MMM d, yyyy')}
          </Typography>

          <ButtonGroup variant="outlined" size="small">
            <Button onClick={goToPreviousWeek} startIcon={<ChevronLeft />}>
              {t('common:ui.previous', 'Previous')}
            </Button>
            <Button onClick={goToToday} startIcon={<Today />}>
              {t('common:ui.today', 'Today')}
            </Button>
            <Button onClick={goToNextWeek} endIcon={<ChevronRight />}>
              {t('common:ui.next', 'Next')}
            </Button>
          </ButtonGroup>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={t('common:ui.selectDate', 'Select Date')}
              value={selectedDate}
              onChange={(newDate) => newDate && setSelectedDate(newDate)}
            />
          </LocalizationProvider>

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel id="time-block-size-label">
              {t('schedule.weekly.timeBlockSize', 'Time Block Size')}
            </InputLabel>
            <Select
              labelId="time-block-size-label"
              id="time-block-size"
              value={timeBlockSize}
              label={t('schedule.weekly.timeBlockSize', 'Time Block Size')}
              onChange={handleTimeBlockSizeChange}
            >
              {TIME_BLOCK_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {t(
                    `schedule.weekly.timeBlockOptions.${option.label}`,
                    `${option.value} hour${option.value > 1 ? 's' : ''}`
                  )}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {/* Week day headers */}
        <Grid container spacing={1} sx={{ mb: 2 }}>
          {/* Empty cell for time labels column */}
          <Grid item xs={1} sx={{ maxWidth: '8.333%' }}>
            <Box sx={{ height: '100%' }}></Box>
          </Grid>

          {/* Day headers */}
          {weekDays.map((day, index) => (
            <Grid item xs={1.8} key={index} sx={{ maxWidth: '15%' }}>
              <Box
                sx={{
                  p: 1,
                  textAlign: 'center',
                  bgcolor: 'primary.main',
                  color: 'white',
                  borderRadius: '4px 4px 0 0',
                  fontWeight: 'bold',
                }}
              >
                <Typography variant="subtitle2">{format(day, 'EEEE')}</Typography>
                <Typography variant="caption">{format(day, 'MMM d')}</Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* No time block labels above the days anymore */}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {instructors.length === 0 && !loading ? (
        <Alert severity="info">{t('schedule.noInstructors', 'No active instructors found')}</Alert>
      ) : (
        <Box sx={{ width: '100%' }}>
          <Box
            sx={{
              width: '100%',
              overflow: 'visible',
            }}
          >
            {instructors.map((instructor) => renderInstructorRow(instructor))}
          </Box>
        </Box>
      )}

      {lessons.length === 0 && !loading && instructors.length > 0 && (
        <Box mt={3}>
          <Alert severity="info">
            {t('schedule.noLessons', 'No lessons scheduled for this date')}
          </Alert>
        </Box>
      )}

      {/* Render lesson details if a lesson is selected */}
      {selectedLesson && renderLessonDetails()}

      {/* Render reschedule dialog */}
      {selectedLesson && (
        <LessonRescheduleDialog
          open={isRescheduleDialogOpen}
          onClose={() => setIsRescheduleDialogOpen(false)}
          lesson={selectedLesson}
          onReschedule={() => {
            // Refresh lessons data by changing the selected date slightly and back
            const currentDate = new Date(selectedDate);
            setSelectedDate(new Date(currentDate.getTime() + 1));
            setTimeout(() => setSelectedDate(currentDate), 10);
            setIsRescheduleDialogOpen(false);
          }}
        />
      )}
    </Paper>
  );
};

export default WeeklyTimelineView;
