import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  IconButton,
  CircularProgress,
  Badge,
} from '@mui/material';
import {
  Today as TodayIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import {
  format,
  addMonths,
  subMonths,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  startOfWeek,
  endOfWeek,
  isSameMonth,
  isToday,
  isSameDay,
} from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { Lesson } from '../../types/lesson';

interface MonthlyCalendarViewProps {
  onLessonClick: (lesson: Lesson) => void;
}

const MonthlyCalendarView: React.FC<MonthlyCalendarViewProps> = ({ onLessonClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();

  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date());
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Get days of the month including days from previous/next month to fill the calendar
  const monthStart = startOfMonth(selectedMonth);
  const monthEnd = endOfMonth(selectedMonth);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 }); // Start on Monday
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  // Group days into weeks
  const weeks: Date[][] = [];
  let week: Date[] = [];

  calendarDays.forEach((day, index) => {
    week.push(day);
    if (index % 7 === 6) {
      weeks.push(week);
      week = [];
    }
  });

  useEffect(() => {
    const fetchLessons = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      setError(null);

      try {
        const startTimestamp = Timestamp.fromDate(calendarStart);
        const endTimestamp = Timestamp.fromDate(calendarEnd);

        let lessonsQuery;

        if (user?.role === 'instructor') {
          // If user is an instructor, only show their lessons
          lessonsQuery = query(
            collection(db, 'schools', currentSchool.id, 'lessons'),
            where('instructorId', '==', user.uid),
            where('startTime', '>=', startTimestamp),
            where('startTime', '<=', endTimestamp)
          );
        } else {
          // Otherwise show all lessons for the school
          lessonsQuery = query(
            collection(db, 'schools', currentSchool.id, 'lessons'),
            where('startTime', '>=', startTimestamp),
            where('startTime', '<=', endTimestamp)
          );
        }

        const lessonsSnapshot = await getDocs(lessonsQuery);
        const lessonsData = lessonsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Lesson[];

        setLessons(lessonsData);
      } catch (err) {
        console.error('Error fetching lessons:', err);
        setError(t('schedule.error.fetchLessons', 'Failed to fetch lessons'));
      } finally {
        setLoading(false);
      }
    };

    fetchLessons();
  }, [currentSchool?.id, calendarStart, calendarEnd, user?.role, user?.uid, t]);

  const handlePreviousMonth = () => {
    setSelectedMonth((prev) => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setSelectedMonth((prev) => addMonths(prev, 1));
  };

  const handleTodayClick = () => {
    setSelectedMonth(new Date());
  };

  const handleAddLesson = () => {
    navigate('/lessons/create');
  };

  const getLessonsForDay = (day: Date) => {
    return lessons.filter((lesson) => {
      if (!lesson.startTime) return false;

      const lessonDate =
        lesson.startTime instanceof Timestamp
          ? lesson.startTime.toDate()
          : new Date(lesson.startTime);

      return isSameDay(lessonDate, day);
    });
  };

  const handleDayClick = (day: Date, dayLessons: Lesson[]) => {
    if (dayLessons.length === 1) {
      // If there's only one lesson, open it directly
      onLessonClick(dayLessons[0]);
    } else if (dayLessons.length > 1) {
      // If there are multiple lessons, navigate to daily view for that day
      navigate('/schedule', { state: { date: day, view: 'daily' } });
    } else {
      // If there are no lessons, open the create lesson form for that day
      navigate('/lessons/create', { state: { date: day } });
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handlePreviousMonth}>
            <NavigateBeforeIcon />
          </IconButton>

          <Typography variant="h6" sx={{ mx: 2 }}>
            {format(selectedMonth, 'MMMM yyyy')}
          </Typography>

          <IconButton onClick={handleNextMonth}>
            <NavigateNextIcon />
          </IconButton>
        </Box>

        <Box>
          <Button startIcon={<TodayIcon />} onClick={handleTodayClick} sx={{ mr: 1 }}>
            {t('schedule.today', 'Today')}
          </Button>

          <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddLesson}>
            {t('lessons.add', 'Add Lesson')}
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Typography color="error" sx={{ p: 2 }}>
          {error}
        </Typography>
      ) : (
        <Paper sx={{ p: 2 }}>
          <Grid container spacing={0}>
            {/* Weekday headers */}
            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(
              (day) => (
                <Grid item xs={12 / 7} key={day}>
                  <Box sx={{ p: 1, textAlign: 'center' }}>
                    <Typography variant="subtitle2">
                      {t(`schedule.days.${day.toLowerCase()}`, day)}
                    </Typography>
                  </Box>
                </Grid>
              )
            )}

            {/* Calendar days */}
            {weeks.map((week, weekIndex) => (
              <React.Fragment key={`week-${weekIndex}`}>
                {week.map((day) => {
                  const dayLessons = getLessonsForDay(day);
                  const isCurrentMonth = isSameMonth(day, selectedMonth);
                  const isCurrentDay = isToday(day);

                  return (
                    <Grid item xs={12 / 7} key={day.toString()}>
                      <Paper
                        sx={{
                          p: 1,
                          m: 0.5,
                          height: 100,
                          bgcolor: isCurrentMonth ? 'background.paper' : 'action.hover',
                          border: isCurrentDay ? 2 : 0,
                          borderColor: 'primary.main',
                          cursor: 'pointer',
                          overflow: 'hidden',
                          '&:hover': {
                            bgcolor: 'action.selected',
                          },
                        }}
                        onClick={() => handleDayClick(day, dayLessons)}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: isCurrentDay ? 'bold' : 'normal',
                              color: isCurrentMonth ? 'text.primary' : 'text.secondary',
                            }}
                          >
                            {format(day, 'd')}
                          </Typography>

                          {dayLessons.length > 0 && (
                            <Badge
                              badgeContent={dayLessons.length}
                              color="primary"
                              sx={{ '& .MuiBadge-badge': { fontSize: '0.6rem' } }}
                            />
                          )}
                        </Box>

                        {dayLessons.length > 0 && (
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 0.5,
                              overflow: 'hidden',
                              maxHeight: 70,
                            }}
                          >
                            {dayLessons
                              .slice(0, 2) // Show max 2 lessons
                              .map((lesson) => {
                                const startTime =
                                  lesson.startTime instanceof Timestamp
                                    ? lesson.startTime.toDate()
                                    : new Date(lesson.startTime);

                                return (
                                  <Box
                                    key={lesson.id}
                                    sx={{
                                      p: 0.5,
                                      bgcolor: 'primary.light',
                                      color: 'primary.contrastText',
                                      borderRadius: 0.5,
                                      fontSize: '0.7rem',
                                      whiteSpace: 'nowrap',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                    }}
                                  >
                                    {format(startTime, 'HH:mm')} {lesson.title}
                                  </Box>
                                );
                              })}

                            {dayLessons.length > 2 && (
                              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                +{dayLessons.length - 2} {t('schedule.more', 'more')}
                              </Typography>
                            )}
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  );
                })}
              </React.Fragment>
            ))}
          </Grid>
        </Paper>
      )}
    </Box>
  );
};

export default MonthlyCalendarView;
