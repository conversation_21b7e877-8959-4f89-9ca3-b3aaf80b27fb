import React from 'react';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
  recurring: boolean;
  daysOfWeek?: number[];
}

interface AvailabilityCalendarProps {
  instructorId: string;
  availableSlots: TimeSlot[];
  onAddSlot: (slot: Omit<TimeSlot, 'id'>) => void;
  onEditSlot: (slot: TimeSlot) => void;
  onDeleteSlot: (slotId: string) => void;
}

const AvailabilityCalendar: React.FC<AvailabilityCalendarProps> = () => {
  // These props will be used in future implementations
  // const { instructorId, availableSlots, onAddSlot, onEditSlot, onDeleteSlot } = props;
  const { t } = useTranslation(['scheduling', 'common']);
  const [selectedDate, setSelectedDate] = React.useState<Date | null>(new Date());
  const [isAddSlotOpen, setIsAddSlotOpen] = React.useState(false);

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
  };

  const handleAddSlot = () => {
    setIsAddSlotOpen(true);
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">{t('availability.title')}</Typography>
        <Button startIcon={<AddIcon />} variant="contained" color="primary" onClick={handleAddSlot}>
          {t('availability.addSlot', 'Add Slot')}
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <StaticDatePicker
              displayStaticWrapperAs="desktop"
              value={selectedDate}
              onChange={handleDateChange}
            />
          </LocalizationProvider>
        </Grid>
        <Grid item xs={12} md={4}>
          <Typography variant="subtitle1" gutterBottom>
            {t('availability.selectedDateSlots', 'Selected Date Slots')}
          </Typography>
          {/* TODO: Add time slots list for selected date */}
        </Grid>
      </Grid>

      {/* Add/Edit Time Slot Dialog - To be implemented */}
      <Dialog open={isAddSlotOpen} onClose={() => setIsAddSlotOpen(false)}>
        <DialogTitle>{t('availability.addSlotTitle', 'Add Slot Title')}</DialogTitle>
        <DialogContent>{/* TODO: Add form for time slot details */}</DialogContent>
        <DialogActions>
          <Button onClick={() => setIsAddSlotOpen(false)}>{t('common:ui.cancel')}</Button>
          <Button variant="contained" color="primary">
            {t('common:ui.save')}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default AvailabilityCalendar;
