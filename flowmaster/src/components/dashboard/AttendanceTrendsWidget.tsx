import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Program, ProgramSession } from '../../types/program';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Divider,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  format,
  subDays,
  eachWeekOfInterval,
  startOfWeek,
  endOfWeek,
  isWithinInterval,
} from 'date-fns';

// Interface for weekly attendance data
interface WeeklyAttendanceData {
  week: string;
  present: number;
  absent: number;
  excused: number;
  rate: number;
}

const AttendanceTrendsWidget: React.FC = () => {
  const { t } = useTranslation(['reports', 'common', 'programs']);
  const { currentSchool } = useSchool();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<string>('90');
  const [weeklyData, setWeeklyData] = useState<WeeklyAttendanceData[]>([]);

  useEffect(() => {
    const fetchAttendanceData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Get the date range based on selected time range
        const endDate = new Date();
        const startDate = subDays(endDate, parseInt(timeRange));

        // Get all programs
        const programsRef = collection(db, 'schools', currentSchool.id, 'programs');
        const programsQuery = query(programsRef);
        const programsSnapshot = await getDocs(programsQuery);
        const programs = programsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Program[];

        // Get all weeks in the date range
        const weeks = eachWeekOfInterval(
          { start: startDate, end: endDate },
          { weekStartsOn: 1 } // Week starts on Monday
        );

        // Initialize weekly data
        const weeklyAttendanceData: WeeklyAttendanceData[] = weeks.map((weekStart) => {
          const weekEnd = endOfWeek(weekStart, { weekStartsOn: 1 });
          return {
            week: format(weekStart, 'MMM d'),
            present: 0,
            absent: 0,
            excused: 0,
            rate: 0,
          };
        });

        // Process each program
        for (const program of programs) {
          // Get sessions for this program
          const sessionsRef = collection(
            db,
            'schools',
            currentSchool.id,
            'programs',
            program.id,
            'sessions'
          );
          const sessionsQuery = query(
            sessionsRef,
            where('date', '>=', startDate),
            orderBy('date', 'asc')
          );
          const sessionsSnapshot = await getDocs(sessionsQuery);
          const sessions = sessionsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as ProgramSession[];

          // Process each session
          for (const session of sessions) {
            if (!session.attendance) continue;

            const sessionDate = session.date.toDate();

            // Find which week this session belongs to
            const weekIndex = weeks.findIndex((weekStart) => {
              const weekEnd = endOfWeek(weekStart, { weekStartsOn: 1 });
              return isWithinInterval(sessionDate, { start: weekStart, end: weekEnd });
            });

            if (weekIndex === -1) continue;

            // Count attendance for this session
            Object.values(session.attendance).forEach((status) => {
              if (status === 'present') {
                weeklyAttendanceData[weekIndex].present++;
              } else if (status === 'absent') {
                weeklyAttendanceData[weekIndex].absent++;
              } else if (status === 'excused') {
                weeklyAttendanceData[weekIndex].excused++;
              }
            });
          }
        }

        // Calculate attendance rates for each week
        weeklyAttendanceData.forEach((week) => {
          const total = week.present + week.absent + week.excused;
          week.rate = total > 0 ? (week.present / total) * 100 : 0;
        });

        setWeeklyData(weeklyAttendanceData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching attendance data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
        setLoading(false);
      }
    };

    fetchAttendanceData();
  }, [currentSchool?.id, timeRange, t]);

  // Handle time range change
  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={t('dashboard.attendanceTrends', 'Attendance Trends')}
        action={
          <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="time-range-label">
              {t('common:filters.timeRange', 'Time Range')}
            </InputLabel>
            <Select
              labelId="time-range-label"
              id="time-range-select"
              value={timeRange}
              onChange={handleTimeRangeChange}
              label={t('common:filters.timeRange', 'Time Range')}
            >
              <MenuItem value="30">{t('common:filters.30days', '30 Days')}</MenuItem>
              <MenuItem value="60">{t('common:filters.60days', '60 Days')}</MenuItem>
              <MenuItem value="90">{t('common:filters.90days', '90 Days')}</MenuItem>
            </Select>
          </FormControl>
        }
      />
      <Divider />
      <CardContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : weeklyData.length === 0 ? (
          <Typography variant="body2" color="text.secondary">
            {t('attendance.noData', 'No attendance data available')}
          </Typography>
        ) : (
          <Box sx={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={weeklyData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="week" />
                <YAxis yAxisId="left" orientation="left" domain={[0, 'auto']} />
                <YAxis
                  yAxisId="right"
                  orientation="right"
                  domain={[0, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <RechartsTooltip />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="present"
                  name={t('programs:attendance.present', 'Present')}
                  stroke="#4caf50"
                  activeDot={{ r: 8 }}
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="absent"
                  name={t('programs:attendance.absent', 'Absent')}
                  stroke="#f44336"
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="excused"
                  name={t('programs:attendance.excused', 'Excused')}
                  stroke="#ff9800"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="rate"
                  name={t('attendance.rate', 'Attendance Rate')}
                  stroke="#2196f3"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default AttendanceTrendsWidget;
