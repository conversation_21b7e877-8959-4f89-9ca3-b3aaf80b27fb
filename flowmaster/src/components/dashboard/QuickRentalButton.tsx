import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Autocomplete,
  CircularProgress,
  Box,
  Typography,
} from '@mui/material';
import { ShoppingCart as ShoppingCartIcon } from '@mui/icons-material';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { useSchool } from '../../hooks/useSchool';
import { Student } from '../../types/student';

interface CustomerOption {
  id: string;
  name: string;
  type: 'student' | 'client' | 'guest';
}

const QuickRentalButton: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const navigate = useNavigate();
  const { currentSchool } = useSchool();

  // State
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerOption | null>(null);
  const [guestName, setGuestName] = useState('');
  const [customerType, setCustomerType] = useState<'student' | 'client' | 'guest'>('student');

  // Handle dialog open
  const handleOpenDialog = async () => {
    setDialogOpen(true);

    if (customers.length === 0 && currentSchool?.id) {
      setLoading(true);
      try {
        // Fetch students
        const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
        const studentsQuery = query(studentsRef, orderBy('firstName'));
        const studentsSnapshot = await getDocs(studentsQuery);
        const studentsData = studentsSnapshot.docs.map((doc) => {
          const data = doc.data() as Student;
          return {
            id: doc.id,
            name: `${data.firstName} ${data.lastName}`,
            type: 'student' as const,
          };
        });

        // Fetch clients
        const clientsRef = collection(db, 'schools', currentSchool.id, 'clients');
        const clientsQuery = query(clientsRef, orderBy('firstName'));
        const clientsSnapshot = await getDocs(clientsQuery);
        const clientsData = clientsSnapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            name: `${data.firstName} ${data.lastName}`,
            type: 'client' as const,
          };
        });

        // Combine students and clients
        setCustomers([...studentsData, ...clientsData]);
      } catch (err) {
        console.error('Error fetching customers:', err);
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedCustomer(null);
    setGuestName('');
  };

  // Handle customer type change
  const handleCustomerTypeChange = (type: 'student' | 'client' | 'guest') => {
    setCustomerType(type);
    setSelectedCustomer(null);
  };

  // Handle create rental
  const handleCreateRental = () => {
    if (customerType === 'guest' && !guestName.trim()) {
      return;
    }

    if ((customerType === 'student' || customerType === 'client') && !selectedCustomer) {
      return;
    }

    handleCloseDialog();

    if (customerType === 'guest') {
      navigate('/equipment/rentals/new', {
        state: {
          customerType: 'guest',
          guestName: guestName,
        },
      });
    } else {
      navigate('/equipment/rentals/new', {
        state: {
          customerType: selectedCustomer?.type,
          customerId: selectedCustomer?.id,
        },
      });
    }
  };

  return (
    <>
      <Button
        variant="contained"
        color="secondary"
        startIcon={<ShoppingCartIcon />}
        onClick={handleOpenDialog}
      >
        {t('dashboard.quickRental', 'New Rental')}
      </Button>

      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{t('dashboard.createRental', 'Create New Rental')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              {t('rental.form.customerType', 'Customer Type')}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
              <Button
                variant={customerType === 'student' ? 'contained' : 'outlined'}
                onClick={() => handleCustomerTypeChange('student')}
              >
                {t('rental.form.studentType', 'Student')}
              </Button>
              <Button
                variant={customerType === 'client' ? 'contained' : 'outlined'}
                onClick={() => handleCustomerTypeChange('client')}
              >
                {t('rental.form.clientType', 'Client')}
              </Button>
              <Button
                variant={customerType === 'guest' ? 'contained' : 'outlined'}
                onClick={() => handleCustomerTypeChange('guest')}
              >
                {t('rental.form.guestType', 'Guest')}
              </Button>
            </Box>

            {customerType === 'guest' ? (
              <TextField
                label={t('rental.form.guestName', 'Guest Name')}
                value={guestName}
                onChange={(e) => setGuestName(e.target.value)}
                fullWidth
                required
              />
            ) : (
              <Autocomplete
                options={customers.filter((c) => c.type === customerType)}
                getOptionLabel={(option) => option.name}
                loading={loading}
                value={selectedCustomer}
                onChange={(_event, value) => setSelectedCustomer(value)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={
                      customerType === 'student'
                        ? t('rental.form.selectStudent', 'Select Student')
                        : t('rental.form.selectClient', 'Select Client')
                    }
                    required
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {loading ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('actions.cancel', 'Cancel', { ns: 'common' })}
          </Button>
          <Button
            onClick={handleCreateRental}
            color="primary"
            variant="contained"
            disabled={
              (customerType === 'guest' && !guestName.trim()) ||
              ((customerType === 'student' || customerType === 'client') && !selectedCustomer)
            }
          >
            {t('dashboard.continueToRental', 'Continue to Rental')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default QuickRentalButton;
