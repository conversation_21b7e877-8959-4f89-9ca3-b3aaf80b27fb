import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Program, ProgramSession } from '../../types/program';
import { Student } from '../../types/student';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Divider,
  CircularProgress,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  LinearProgress,
} from '@mui/material';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip } from 'recharts';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  Event as EventIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';
import { format, subDays } from 'date-fns';
import { useNavigate } from 'react-router-dom';

// Interface for attendance data
interface AttendanceData {
  present: number;
  absent: number;
  excused: number;
  total: number;
  rate: number;
}

// Interface for recent absence
interface RecentAbsence {
  studentId: string;
  studentName: string;
  programName: string;
  date: Date;
  status: 'absent' | 'excused';
}

const AttendanceWidget: React.FC = () => {
  const { t } = useTranslation(['reports', 'common', 'programs']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [attendanceData, setAttendanceData] = useState<AttendanceData>({
    present: 0,
    absent: 0,
    excused: 0,
    total: 0,
    rate: 0,
  });
  const [recentAbsences, setRecentAbsences] = useState<RecentAbsence[]>([]);
  const [error, setError] = useState<string | null>(null);

  // COLORS for charts
  const COLORS = ['#4caf50', '#f44336', '#ff9800'];

  useEffect(() => {
    const fetchAttendanceData = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Get all programs
        const programsRef = collection(db, 'schools', currentSchool.id, 'programs');
        const programsQuery = query(programsRef);
        const programsSnapshot = await getDocs(programsQuery);
        const programs = programsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Program[];

        // Get all students
        const studentsRef = collection(db, 'schools', currentSchool.id, 'students');
        const studentsQuery = query(studentsRef);
        const studentsSnapshot = await getDocs(studentsQuery);
        const students = studentsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Student[];

        // Initialize attendance data
        let presentCount = 0;
        let absentCount = 0;
        let excusedCount = 0;
        let totalCount = 0;
        const absences: RecentAbsence[] = [];

        // Get the date 30 days ago
        const thirtyDaysAgo = subDays(new Date(), 30);

        // Process each program
        for (const program of programs) {
          // Get sessions for this program
          const sessionsRef = collection(
            db,
            'schools',
            currentSchool.id,
            'programs',
            program.id,
            'sessions'
          );
          const sessionsQuery = query(
            sessionsRef,
            where('date', '>=', thirtyDaysAgo),
            orderBy('date', 'desc')
          );
          const sessionsSnapshot = await getDocs(sessionsQuery);
          const sessions = sessionsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as ProgramSession[];

          // Process each session
          for (const session of sessions) {
            if (!session.attendance) continue;

            // Process attendance for each student
            Object.entries(session.attendance).forEach(([studentId, status]) => {
              totalCount++;

              if (status === 'present') {
                presentCount++;
              } else if (status === 'absent') {
                absentCount++;
                // Add to recent absences
                const student = students.find((s) => s.id === studentId);
                if (student) {
                  absences.push({
                    studentId,
                    studentName: `${student.firstName} ${student.lastName}`,
                    programName: program.name,
                    date: session.date.toDate(),
                    status: 'absent',
                  });
                }
              } else if (status === 'excused') {
                excusedCount++;
                // Add to recent absences
                const student = students.find((s) => s.id === studentId);
                if (student) {
                  absences.push({
                    studentId,
                    studentName: `${student.firstName} ${student.lastName}`,
                    programName: program.name,
                    date: session.date.toDate(),
                    status: 'excused',
                  });
                }
              }
            });
          }
        }

        // Calculate attendance rate
        const attendanceRate = totalCount > 0 ? (presentCount / totalCount) * 100 : 0;

        // Set attendance data
        setAttendanceData({
          present: presentCount,
          absent: absentCount,
          excused: excusedCount,
          total: totalCount,
          rate: attendanceRate,
        });

        // Sort absences by date (most recent first) and limit to 5
        const sortedAbsences = absences
          .sort((a, b) => b.date.getTime() - a.date.getTime())
          .slice(0, 5);
        setRecentAbsences(sortedAbsences);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching attendance data:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
        setLoading(false);
      }
    };

    fetchAttendanceData();
  }, [currentSchool?.id, t]);

  // Prepare chart data
  const chartData = [
    { name: t('programs:attendance.present', 'Present'), value: attendanceData.present },
    { name: t('programs:attendance.absent', 'Absent'), value: attendanceData.absent },
    { name: t('programs:attendance.excused', 'Excused'), value: attendanceData.excused },
  ];

  // Navigate to attendance reports
  const handleViewReports = () => {
    navigate('/reports/attendance');
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={t('dashboard.attendanceWidget', 'Attendance Overview')}
        action={
          <Button size="small" endIcon={<ArrowForwardIcon />} onClick={handleViewReports}>
            {t('common:actions.viewAll', 'View All')}
          </Button>
        }
      />
      <Divider />
      <CardContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : (
          <Grid container spacing={2}>
            {/* Attendance Rate */}
            <Grid item xs={12} md={6}>
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {t('attendance.rate', 'Attendance Rate')}
                </Typography>
                <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                  <CircularProgress
                    variant="determinate"
                    value={attendanceData.rate}
                    size={120}
                    thickness={5}
                    color={
                      attendanceData.rate >= 90
                        ? 'success'
                        : attendanceData.rate >= 75
                          ? 'info'
                          : attendanceData.rate >= 60
                            ? 'warning'
                            : 'error'
                    }
                  />
                  <Box
                    sx={{
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      position: 'absolute',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="h4" component="div" color="text.secondary">
                      {`${Math.round(attendanceData.rate)}%`}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 1 }}>
                  <Chip
                    icon={<EventIcon />}
                    label={`${attendanceData.total} ${t('attendance.totalSessions', 'Sessions')}`}
                    color="primary"
                    size="small"
                  />
                </Box>
              </Box>
            </Grid>

            {/* Attendance Distribution */}
            <Grid item xs={12} md={6}>
              <Box sx={{ height: 200 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }: { name: string; percent: number }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                      labelLine={false}
                    >
                      {chartData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Grid>

            {/* Recent Absences */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('dashboard.recentAbsences', 'Recent Absences')}
              </Typography>
              {recentAbsences.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  {t('attendance.noData', 'No attendance data available')}
                </Typography>
              ) : (
                <List>
                  {recentAbsences.map((absence, index) => (
                    <ListItem key={index} divider={index < recentAbsences.length - 1}>
                      <ListItemAvatar>
                        <Avatar
                          sx={{ bgcolor: absence.status === 'absent' ? '#f44336' : '#ff9800' }}
                        >
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={absence.studentName}
                        secondary={
                          <>
                            <Typography component="span" variant="body2" color="text.primary">
                              {absence.programName}
                            </Typography>
                            {` - ${format(absence.date, 'PPP')}`}
                          </>
                        }
                      />
                      <Chip
                        label={
                          absence.status === 'absent'
                            ? t('programs:attendance.absent', 'Absent')
                            : t('programs:attendance.excused', 'Excused')
                        }
                        color={absence.status === 'absent' ? 'error' : 'warning'}
                        size="small"
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default AttendanceWidget;
