import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Divider,
  CircularProgress,
  Grid,
  Button,
  Chip,
} from '@mui/material';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip } from 'recharts';
import { ArrowForward as ArrowForwardIcon, Inventory as InventoryIcon } from '@mui/icons-material';
import { getEquipmentItems } from '../../services/equipmentService';
import { EquipmentItem, EquipmentCondition } from '../../types/equipment';

// Chart colors
const COLORS = ['#4caf50', '#ff9800', '#f44336', '#9e9e9e'];

interface EquipmentStats {
  total: number;
  available: number;
  unavailable: number;
  conditions: Record<EquipmentCondition, number>;
  categories: Record<string, number>;
}

const EquipmentStatusWidget: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<EquipmentStats>({
    total: 0,
    available: 0,
    unavailable: 0,
    conditions: {
      good: 0,
      damaged: 0,
      maintenance: 0,
      lost: 0,
    },
    categories: {},
  });

  // Fetch equipment data
  useEffect(() => {
    const fetchEquipmentStats = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        const items = await getEquipmentItems(currentSchool.id);

        // Calculate stats
        const newStats: EquipmentStats = {
          total: items.length,
          available: items.filter((item) => item.available).length,
          unavailable: items.filter((item) => !item.available).length,
          conditions: {
            good: 0,
            damaged: 0,
            maintenance: 0,
            lost: 0,
          },
          categories: {},
        };

        // Count by condition
        items.forEach((item) => {
          newStats.conditions[item.condition]++;

          // Count by category
          if (!newStats.categories[item.category]) {
            newStats.categories[item.category] = 0;
          }
          newStats.categories[item.category]++;
        });

        setStats(newStats);
        setError(null);
      } catch (err) {
        console.error('Error fetching equipment stats:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchEquipmentStats();
  }, [currentSchool?.id, t]);

  // Prepare chart data
  const availabilityData = [
    { name: t('status.available', 'Available'), value: stats.available },
    { name: t('status.unavailable', 'In Use'), value: stats.unavailable },
  ];

  const conditionData = [
    { name: t('condition.good', 'Good'), value: stats.conditions.good },
    { name: t('condition.damaged', 'Damaged'), value: stats.conditions.damaged },
    { name: t('condition.maintenance', 'Needs Maintenance'), value: stats.conditions.maintenance },
    { name: t('condition.lost', 'Lost'), value: stats.conditions.lost },
  ];

  // Navigate to equipment page
  const handleViewEquipment = () => {
    navigate('/equipment');
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={t('dashboard.equipmentStatus', 'Equipment Status')}
        action={
          <Button size="small" endIcon={<ArrowForwardIcon />} onClick={handleViewEquipment}>
            {t('common:actions.viewAll', 'View All')}
          </Button>
        }
      />
      <Divider />
      <CardContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : (
          <Grid container spacing={2}>
            {/* Total Equipment */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InventoryIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h5">
                  {stats.total} {t('dashboard.totalItems', 'Total Items')}
                </Typography>
              </Box>
              <Divider sx={{ my: 2 }} />
            </Grid>

            {/* Availability Chart */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" align="center" gutterBottom>
                {t('dashboard.availability', 'Availability')}
              </Typography>
              <Box sx={{ height: 200 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={availabilityData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }: { name: string; percent: number }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                      labelLine={false}
                    >
                      {availabilityData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={index === 0 ? '#4caf50' : '#ff9800'} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Grid>

            {/* Condition Chart */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" align="center" gutterBottom>
                {t('dashboard.condition', 'Condition')}
              </Typography>
              <Box sx={{ height: 200 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={conditionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }: { name: string; percent: number }) =>
                        percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                      }
                      labelLine={false}
                    >
                      {conditionData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Grid>

            {/* Category Breakdown */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" gutterBottom>
                {t('dashboard.categories', 'Categories')}
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {Object.entries(stats.categories).map(([category, count]) => (
                  <Chip
                    key={category}
                    label={`${t(`category.${category}`, category)}: ${count}`}
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default EquipmentStatusWidget;
