import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Divider,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowForward as ArrowForwardIcon,
  Warning as WarningIcon,
  AssignmentReturn as ReturnIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { format, differenceInDays } from 'date-fns';
import { getRentalRecords } from '../../services/rentalService';
import { RentalRecord } from '../../types/equipment';

const OverdueRentalsWidget: React.FC = () => {
  const { t } = useTranslation(['equipment', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [overdueRentals, setOverdueRentals] = useState<RentalRecord[]>([]);

  // Fetch overdue rentals
  useEffect(() => {
    const fetchOverdueRentals = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        const rentals = await getRentalRecords(currentSchool.id, { status: 'overdue' });

        // Sort by most overdue first
        const sortedRentals = rentals.sort((a, b) => {
          // Handle both Firestore Timestamp objects and ISO date strings
          let dueDateA: Date;
          let dueDateB: Date;

          if (
            a.dueDate &&
            typeof a.dueDate === 'object' &&
            'toDate' in a.dueDate &&
            typeof a.dueDate.toDate === 'function'
          ) {
            dueDateA = a.dueDate.toDate();
          } else if (typeof a.dueDate === 'string') {
            dueDateA = new Date(a.dueDate);
          } else {
            dueDateA = new Date(); // Fallback
          }

          if (
            b.dueDate &&
            typeof b.dueDate === 'object' &&
            'toDate' in b.dueDate &&
            typeof b.dueDate.toDate === 'function'
          ) {
            dueDateB = b.dueDate.toDate();
          } else if (typeof b.dueDate === 'string') {
            dueDateB = new Date(b.dueDate);
          } else {
            dueDateB = new Date(); // Fallback
          }

          const daysOverdueA = differenceInDays(new Date(), dueDateA);
          const daysOverdueB = differenceInDays(new Date(), dueDateB);
          return daysOverdueB - daysOverdueA;
        });

        setOverdueRentals(sortedRentals.slice(0, 5)); // Show top 5 most overdue
        setError(null);
      } catch (err) {
        console.error('Error fetching overdue rentals:', err);
        setError(t('messages.fetchError', 'Failed to fetch data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchOverdueRentals();
  }, [currentSchool?.id, t]);

  // Navigate to rentals page
  const handleViewAllRentals = () => {
    navigate('/equipment/rentals');
  };

  // Navigate to rental detail
  const handleViewRental = (id: string) => {
    navigate(`/equipment/rentals/${id}`);
  };

  // Navigate to return rental
  const handleReturnRental = (id: string) => {
    navigate(`/equipment/rentals/${id}`, { state: { returnMode: true } });
  };

  // Format date
  const formatDate = (date: Date | string | any) => {
    if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {
      return format(date.toDate(), 'PPP');
    } else if (date instanceof Date) {
      return format(date, 'PPP');
    } else if (typeof date === 'string') {
      return format(new Date(date), 'PPP');
    }
    return 'Invalid date';
  };

  // Calculate days overdue
  const getDaysOverdue = (dueDate: Date | string | any) => {
    let date: Date;

    if (
      dueDate &&
      typeof dueDate === 'object' &&
      'toDate' in dueDate &&
      typeof dueDate.toDate === 'function'
    ) {
      date = dueDate.toDate();
    } else if (dueDate instanceof Date) {
      date = dueDate;
    } else if (typeof dueDate === 'string') {
      date = new Date(dueDate);
    } else {
      return 0; // Fallback
    }

    return differenceInDays(new Date(), date);
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={t('dashboard.overdueRentals', 'Overdue Rentals')}
        action={
          <Button size="small" endIcon={<ArrowForwardIcon />} onClick={handleViewAllRentals}>
            {t('common:actions.viewAll', 'View All')}
          </Button>
        }
      />
      <Divider />
      <CardContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : overdueRentals.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Typography variant="body1" color="text.secondary">
              {t('dashboard.noOverdueRentals', 'No overdue rentals')}
            </Typography>
          </Box>
        ) : (
          <List sx={{ width: '100%' }}>
            {overdueRentals.map((rental) => {
              const daysOverdue = getDaysOverdue(rental.dueDate);

              return (
                <React.Fragment key={rental.id}>
                  <ListItem
                    alignItems="flex-start"
                    secondaryAction={
                      <Box>
                        <Tooltip title={t('actions.view', 'View', { ns: 'common' })}>
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={() => handleViewRental(rental.id)}
                            sx={{ mr: 1 }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('rental.returnAction', 'Return')}>
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={() => handleReturnRental(rental.id)}
                          >
                            <ReturnIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'error.main' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle2" component="span">
                            {rental.customerName}
                          </Typography>
                          <Chip
                            icon={<WarningIcon />}
                            label={t('dashboard.daysOverdue', '{{days}} days overdue', {
                              days: daysOverdue,
                            })}
                            color="error"
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography variant="body2" component="span" color="text.primary">
                            {rental.items.length} {t('rental.itemsCount', 'items')}
                          </Typography>
                          {' — '}
                          <Typography variant="body2" component="span">
                            {t('rental.dueDateColumn', 'Due Date')}: {formatDate(rental.dueDate)}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              );
            })}
          </List>
        )}
      </CardContent>
    </Card>
  );
};

export default OverdueRentalsWidget;
