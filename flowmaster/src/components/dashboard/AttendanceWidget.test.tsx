import React from 'react';
import { render, screen } from '@testing-library/react';
import AttendanceWidget from './AttendanceWidget';
import { useSchool } from '../../hooks/useSchool';
import { collection, getDocs } from 'firebase/firestore';

// Mock the hooks and Firebase functions
jest.mock('../../hooks/useSchool');
jest.mock('firebase/firestore');
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, fallback: string) => fallback,
    i18n: {
      changeLanguage: jest.fn(),
    },
  }),
}));
jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
}));

describe('AttendanceWidget', () => {
  beforeEach(() => {
    // Mock useSchool hook
    (useSchool as jest.Mock).mockReturnValue({
      currentSchool: { id: 'school-1', name: 'Test School' },
    });

    // Mock Firebase functions
    (collection as jest.Mock).mockReturnValue({});
    (getDocs as jest.Mock).mockResolvedValue({
      docs: [],
    });
  });

  it('renders loading state initially', () => {
    render(<AttendanceWidget />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('displays attendance overview title', () => {
    render(<AttendanceWidget />);
    expect(screen.getByText('Attendance Overview')).toBeInTheDocument();
  });
});
