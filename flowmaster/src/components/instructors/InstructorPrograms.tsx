import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Instructor } from '../../types/instructor';
import { Program } from '../../types/program';
import { collection, query, where, getDocs } from 'firebase/firestore';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
} from '@mui/material';
import {
  School as SchoolIcon,
  Event as EventIcon,
  EmojiEvents as CampIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

interface InstructorProgramsProps {
  instructor: Instructor;
}

const InstructorPrograms: React.FC<InstructorProgramsProps> = ({ instructor }) => {
  const { t } = useTranslation(['people', 'programs', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [studentCounts, setStudentCounts] = useState<Record<string, number>>({});

  useEffect(() => {
    const fetchInstructorPrograms = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch programs where the instructor is assigned
        const programsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs'),
          where('instructors', 'array-contains', instructor.id)
        );

        const programsSnapshot = await getDocs(programsQuery);
        const programsData = programsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Program[];

        setPrograms(programsData);

        // Calculate student counts for each program
        const counts: Record<string, number> = {};
        programsData.forEach((program) => {
          counts[program.id] = program.participants.length;
        });

        setStudentCounts(counts);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching instructor programs:', err);
        setError(t('common:error.fetch', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchInstructorPrograms();
  }, [currentSchool?.id, instructor.id, t]);

  const getProgramTypeIcon = (type: string) => {
    switch (type) {
      case 'yearly':
        return <SchoolIcon />;
      case 'seasonal':
        return <EventIcon />;
      case 'camp':
        return <CampIcon />;
      default:
        return <EventIcon />;
    }
  };

  const getProgramTypeColor = (type: string): 'primary' | 'success' | 'warning' | 'default' => {
    switch (type) {
      case 'yearly':
        return 'primary';
      case 'seasonal':
        return 'success';
      case 'camp':
        return 'warning';
      default:
        return 'default';
    }
  };

  const handleProgramClick = (programId: string) => {
    navigate(`/programs/${programId}`);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (programs.length === 0) {
    return (
      <Alert severity="info">
        {t('instructors.noPrograms', 'This instructor is not assigned to any programs')}
      </Alert>
    );
  }

  // Group programs by type
  const groupedPrograms: Record<string, Program[]> = {
    yearly: programs.filter((p) => p.type === 'yearly'),
    seasonal: programs.filter((p) => p.type === 'seasonal'),
    camp: programs.filter((p) => p.type === 'camp'),
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('instructors.assignedPrograms', 'Assigned Programs')}
      </Typography>

      {Object.entries(groupedPrograms).map(([type, typePrograms]) => {
        if (typePrograms.length === 0) return null;

        return (
          <Box key={type} mb={4}>
            <Box display="flex" alignItems="center" mb={2}>
              {getProgramTypeIcon(type)}
              <Typography variant="subtitle1" ml={1} fontWeight="medium">
                {t(`programs:programTypes.${type}`, type)}
              </Typography>
            </Box>

            <Grid container spacing={3}>
              {typePrograms.map((program) => (
                <Grid item xs={12} sm={6} md={4} key={program.id}>
                  <Card>
                    <CardActionArea onClick={() => handleProgramClick(program.id)}>
                      <CardContent>
                        <Box
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                          mb={2}
                        >
                          <Chip
                            label={t(`programs:status.${program.status}`, program.status)}
                            color={program.status === 'active' ? 'success' : 'default'}
                            size="small"
                            variant="outlined"
                          />

                          <Typography variant="caption" color="text.secondary">
                            {format(program.schedule.startDate.toDate(), 'MMM d')} -{' '}
                            {format(program.schedule.endDate.toDate(), 'MMM d, yyyy')}
                          </Typography>
                        </Box>

                        <Typography variant="h6" component="h3" gutterBottom>
                          {program.name}
                        </Typography>

                        <Divider sx={{ my: 1 }} />

                        <Box display="flex" alignItems="center" justifyContent="space-between">
                          <Box display="flex" alignItems="center">
                            <PersonIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                            <Typography variant="body2">
                              {t('programs:participants.count', '{{count}} Students', {
                                count: studentCounts[program.id] || 0,
                              })}
                            </Typography>
                          </Box>

                          <Chip
                            label={program.schedule.days.join(', ')}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        );
      })}

      <Box mt={4}>
        <Typography variant="subtitle1" gutterBottom>
          {t('instructors.upcomingSessions', 'Upcoming Sessions')}
        </Typography>

        <Paper variant="outlined" sx={{ p: 2 }}>
          <List sx={{ width: '100%' }}>
            {programs.slice(0, 3).map((program) => (
              <ListItem
                key={program.id}
                alignItems="flex-start"
                sx={{ px: 1, cursor: 'pointer' }}
                onClick={() => handleProgramClick(program.id)}
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: getProgramTypeColor(program.type) }}>
                    {getProgramTypeIcon(program.type)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={program.name}
                  secondary={
                    <React.Fragment>
                      <Typography component="span" variant="body2" color="text.primary">
                        {format(program.schedule.startDate.toDate(), 'EEEE, MMMM d')}
                      </Typography>
                      {` — ${program.schedule.days.join(', ')} at ${program.schedule.startTime}`}
                    </React.Fragment>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Box>
    </Box>
  );
};

export default InstructorPrograms;
