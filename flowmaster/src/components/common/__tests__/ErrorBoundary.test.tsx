import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ErrorBoundary } from '../ErrorBoundary';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n/test-config'; // Updated import path

// Mock console.error to avoid test output noise
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});

// Mock window.location.reload
const mockReload = jest.fn();
Object.defineProperty(window, 'location', {
  value: { reload: mockReload },
  writable: true,
});

// Component that throws an error
const ThrowError = () => {
  throw new Error('Test error');
};

const renderWithI18n = (component: React.ReactNode) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>);
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    mockReload.mockClear();
  });

  it('renders children when there is no error', () => {
    const { getByText } = renderWithI18n(
      <ErrorBoundary>
        <div>Test Content</div>
      </ErrorBoundary>
    );

    expect(getByText('Test Content')).toBeInTheDocument();
  });

  it('renders fallback when there is an error', () => {
    renderWithI18n(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText('generic.title')).toBeInTheDocument();
    expect(screen.getByText('generic.message')).toBeInTheDocument();
  });

  it('calls window.location.reload when try again button is clicked', () => {
    renderWithI18n(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    );

    const tryAgainButton = screen.getByText('generic.tryAgain');
    fireEvent.click(tryAgainButton);

    expect(mockReload).toHaveBeenCalledTimes(1);
  });

  it('shows error details in development environment', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    renderWithI18n(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText(/Test error/)).toBeInTheDocument();

    process.env.NODE_ENV = originalNodeEnv;
  });
});
