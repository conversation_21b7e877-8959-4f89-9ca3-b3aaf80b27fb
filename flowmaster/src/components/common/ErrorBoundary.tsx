import React from 'react';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { useTranslation } from 'react-i18next';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Alert from '@mui/material/Alert';
import { logError } from '../../utils/logger';

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface FallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<FallbackProps> = ({ error, resetErrorBoundary }) => {
  const { t } = useTranslation('errors');

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="400px"
      p={3}
    >
      <Alert severity="error" sx={{ mb: 2, width: '100%', maxWidth: 600 }}>
        <Typography variant="h6" component="h2" gutterBottom>
          {t('generic.title')}
        </Typography>
        <Typography variant="body1" gutterBottom>
          {t('generic.message')}
        </Typography>
        {process.env.NODE_ENV === 'development' && (
          <Typography
            variant="body2"
            component="pre"
            sx={{
              mt: 2,
              p: 2,
              bgcolor: 'grey.100',
              borderRadius: 1,
              overflow: 'auto',
            }}
          >
            {error.message}
            {error.stack}
          </Typography>
        )}
      </Alert>
      <Button variant="contained" color="primary" onClick={resetErrorBoundary} sx={{ mt: 2 }}>
        {t('generic.tryAgain')}
      </Button>
    </Box>
  );
};

export const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children }) => {
  const handleError = (error: Error) => {
    logError('ErrorBoundary caught an error:', error);
  };

  return (
    <ReactErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={handleError}
      onReset={() => {
        // Perform any cleanup or reset actions
        window.location.reload();
      }}
    >
      {children}
    </ReactErrorBoundary>
  );
};
