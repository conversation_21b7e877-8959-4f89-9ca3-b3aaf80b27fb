import { Box, CircularProgress, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface LoadingScreenProps {
  error?: Error | string | null;
}

export const LoadingScreen = ({ error }: LoadingScreenProps) => {
  const { t } = useTranslation('common');
  const errorMessage = error instanceof Error ? error.message : error;

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        gap: 2,
      }}
    >
      {error ? (
        <Alert severity="error" sx={{ maxWidth: '80%' }}>
          {errorMessage}
        </Alert>
      ) : (
        <>
          <CircularProgress />
          <Box sx={{ mt: 2 }}>{t('ui.loading')}</Box>
        </>
      )}
    </Box>
  );
};
