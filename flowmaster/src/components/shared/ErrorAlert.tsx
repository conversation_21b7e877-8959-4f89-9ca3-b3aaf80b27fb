import { Alert, AlertProps, Collapse } from '@mui/material';
import { useState, useEffect } from 'react';

interface ErrorAlertProps extends Omit<AlertProps, 'severity'> {
  error?: Error | string | null;
  message?: string | null;
}

export const ErrorAlert = ({ error, message, ...props }: ErrorAlertProps) => {
  const [show, setShow] = useState(false);
  const errorContent = message || error;

  useEffect(() => {
    if (errorContent) {
      setShow(true);
    } else {
      setShow(false);
    }
  }, [errorContent]);

  if (!errorContent) return null;

  const errorMessage = errorContent instanceof Error ? errorContent.message : errorContent;

  return (
    <Collapse in={show}>
      <Alert severity="error" onClose={() => setShow(false)} sx={{ mb: 2 }} {...props}>
        {errorMessage}
      </Alert>
    </Collapse>
  );
};
