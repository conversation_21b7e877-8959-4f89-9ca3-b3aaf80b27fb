import { render, screen, fireEvent } from '@testing-library/react';
import { ErrorAlert } from '../ErrorAlert';

describe('ErrorAlert', () => {
  describe('Rendering', () => {
    it('renders nothing when error is null', () => {
      render(<ErrorAlert error={null} />);
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    it('renders error message when string error is provided', () => {
      const errorMessage = 'Test error message';
      render(<ErrorAlert error={errorMessage} />);
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('renders error message from Error object', () => {
      const error = new Error('Test error object');
      render(<ErrorAlert error={error} />);
      expect(screen.getByText(error.message)).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('closes alert when close button is clicked', () => {
      render(<ErrorAlert error="Test error" />);

      const closeButton = screen.getByRole('button');
      fireEvent.click(closeButton);

      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    it('shows alert when error changes from null to value', () => {
      const { rerender } = render(<ErrorAlert error={null} />);
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();

      rerender(<ErrorAlert error="New error" />);
      expect(screen.getByText('New error')).toBeInTheDocument();
    });

    it('hides alert when error changes to null', () => {
      const { rerender } = render(<ErrorAlert error="Initial error" />);
      expect(screen.getByRole('alert')).toBeInTheDocument();

      rerender(<ErrorAlert error={null} />);
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });
  });

  describe('Styling', () => {
    it('applies custom styles through sx prop', () => {
      const customSx = { marginTop: '20px' };
      render(<ErrorAlert error="Test error" sx={customSx} />);

      const alert = screen.getByRole('alert');
      expect(alert).toHaveStyle({ marginTop: '20px' });
    });

    it('applies default margin bottom style', () => {
      render(<ErrorAlert error="Test error" />);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveStyle({ marginBottom: '16px' });
    });
  });
});
