import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../LanguageSwitcher';

// Mock hooks
jest.mock('react-i18next');

describe('LanguageSwitcher', () => {
  const mockT = jest.fn((key: string) => key);
  const mockChangeLanguage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useTranslation as jest.Mock).mockReturnValue({
      t: mockT,
      i18n: {
        language: 'en',
        changeLanguage: mockChangeLanguage,
      },
    });
  });

  describe('Rendering', () => {
    it('renders without errors', () => {
      render(<LanguageSwitcher />);
      const button = screen.getByRole('button', { name: 'ui.language' });
      expect(button).toBeInTheDocument();
    });

    it('displays language icon', () => {
      render(<LanguageSwitcher />);
      const icon = screen.getByTestId('LanguageIcon');
      expect(icon).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('shows language options on click', async () => {
      render(<LanguageSwitcher />);
      const button = screen.getByRole('button', { name: 'ui.language' });
      fireEvent.click(button);

      // Menu should be visible
      await waitFor(() => {
        const menu = screen.getByRole('menu');
        expect(menu).toBeInTheDocument();
      });

      // Should show both language options
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(2);
    });

    it('changes language when option is selected', async () => {
      render(<LanguageSwitcher />);
      const button = screen.getByRole('button', { name: 'ui.language' });
      fireEvent.click(button);

      await waitFor(() => {
        const slovenianOption = screen.getAllByRole('menuitem')[1];
        fireEvent.click(slovenianOption);
      });

      expect(mockChangeLanguage).toHaveBeenCalledWith('sl');
    });

    it('closes menu when option is selected', async () => {
      render(<LanguageSwitcher />);
      const button = screen.getByRole('button', { name: 'ui.language' });
      fireEvent.click(button);

      await waitFor(() => {
        const slovenianOption = screen.getAllByRole('menuitem')[1];
        fireEvent.click(slovenianOption);
      });

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has correct ARIA attributes', () => {
      render(<LanguageSwitcher />);
      const button = screen.getByRole('button', { name: 'ui.language' });
      expect(button).toHaveAttribute('aria-label', 'ui.language');
    });

    it('supports keyboard navigation', async () => {
      render(<LanguageSwitcher />);
      const button = screen.getByRole('button', { name: 'ui.language' });

      // Open menu with Enter key
      fireEvent.keyDown(button, { key: 'Enter' });

      // Wait for menu to appear
      await waitFor(() => {
        expect(screen.getByRole('menu')).toBeInTheDocument();
      });

      // Wait for menu items to be available
      await waitFor(() => {
        expect(screen.getAllByRole('menuitem')).toHaveLength(2);
      });

      // Select option with Enter key
      const slovenianOption = screen.getAllByRole('menuitem')[1];
      fireEvent.keyDown(slovenianOption, { key: 'Enter' });

      // Verify language change
      expect(mockChangeLanguage).toHaveBeenCalledWith('sl');

      // Verify menu closes
      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      });
    });
  });
});
