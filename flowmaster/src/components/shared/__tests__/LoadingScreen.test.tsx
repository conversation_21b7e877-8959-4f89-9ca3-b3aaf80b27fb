import { render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import type { TFunction } from 'i18next';
import { LoadingScreen } from '../LoadingScreen';

// Mock hooks
jest.mock('react-i18next');

interface LoadingScreenProps {
  error?: Error | string | null;
}

describe('LoadingScreen', () => {
  const defaultProps: LoadingScreenProps = {
    error: null,
  };

  const defaultTranslations: Record<string, string> = {
    'ui.loading': 'Loading...',
  };

  let mockT: TFunction;

  beforeEach(() => {
    jest.clearAllMocks();
    mockT = jest.fn((key: string) => defaultTranslations[key] || key) as unknown as TFunction;
    (useTranslation as jest.Mock).mockReturnValue({
      t: mockT,
      i18n: {
        language: 'en',
        changeLanguage: jest.fn(),
      },
    });
  });

  describe('Rendering', () => {
    it('renders loading state without errors', () => {
      render(<LoadingScreen {...defaultProps} />);
      const loadingSpinner = screen.getByRole('progressbar');
      const loadingText = screen.getByText('Loading...');
      expect(loadingSpinner).toBeInTheDocument();
      expect(loadingText).toBeInTheDocument();
      expect(mockT).toHaveBeenCalledWith('ui.loading');
    });

    it('renders error message when error prop is provided', () => {
      const errorMessage = 'An error occurred';
      render(<LoadingScreen error={errorMessage} />);
      const errorElement = screen.getByText(errorMessage);
      expect(errorElement).toBeInTheDocument();
    });

    it('renders error message from Error object', () => {
      const error = new Error('Test error message');
      render(<LoadingScreen error={error} />);
      const errorElement = screen.getByText('Test error message');
      expect(errorElement).toBeInTheDocument();
    });

    it('renders loading state when error is null', () => {
      render(<LoadingScreen error={null} />);
      const loadingText = screen.getByText('Loading...');
      expect(loadingText).toBeInTheDocument();
      expect(mockT).toHaveBeenCalledWith('ui.loading');
    });
  });

  describe('Accessibility', () => {
    it('has proper loading indicator with ARIA role', () => {
      render(<LoadingScreen {...defaultProps} />);
      const spinnerElement = screen.getByRole('progressbar');
      expect(spinnerElement).toBeInTheDocument();
    });

    it('shows error alert with correct role', () => {
      render(<LoadingScreen error="Test error" />);
      const alertElement = screen.getByRole('alert');
      expect(alertElement).toBeInTheDocument();
    });
  });
});
