/** @jest-environment jsdom */
import { render, screen, fireEvent } from '@testing-library/react';
import { ServiceWorkerUpdate } from '../ServiceWorkerUpdate';
import { useServiceWorker } from '../../../hooks/useServiceWorker';

// Mock the hooks
jest.mock('react-i18next', () => ({
  // Add type annotation to the callback parameter
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('../../../hooks/useServiceWorker');

describe('ServiceWorkerUpdate', () => {
  const mockUpdate = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not render when no update is needed', () => {
    (useServiceWorker as jest.Mock).mockReturnValue({
      needsRefresh: false,
      update: mockUpdate,
    });

    const { container } = render(<ServiceWorkerUpdate />);
    expect(container.firstChild).toBeNull();
  });

  it('should render update notification when update is available', () => {
    (useServiceWorker as jest.Mock).mockReturnValue({
      needsRefresh: true,
      update: mockUpdate,
    });

    render(<ServiceWorkerUpdate />);

    expect(screen.getByText('update.newVersion')).toBeInTheDocument();
    expect(screen.getByText('update.refresh')).toBeInTheDocument();
  });

  it('should call update function when refresh button is clicked', () => {
    (useServiceWorker as jest.Mock).mockReturnValue({
      needsRefresh: true,
      update: mockUpdate,
    });

    render(<ServiceWorkerUpdate />);

    const refreshButton = screen.getByText('update.refresh');
    fireEvent.click(refreshButton);

    expect(mockUpdate).toHaveBeenCalled();
  });
});
