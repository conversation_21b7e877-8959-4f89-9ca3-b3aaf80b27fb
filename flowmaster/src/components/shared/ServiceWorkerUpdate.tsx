import React from 'react';
import { Snack<PERSON>, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useServiceWorker } from '../../hooks/useServiceWorker';

/**
 * Component that shows a notification when a new service worker update is available
 */
export const ServiceWorkerUpdate: React.FC = () => {
  const { t } = useTranslation('common');
  const { needsRefresh, update } = useServiceWorker();

  const handleUpdate = () => {
    update();
  };

  if (!needsRefresh) {
    return null;
  }

  return (
    <Snackbar
      open={needsRefresh}
      message={t('update.newVersion')}
      action={
        <Button color="secondary" size="small" onClick={handleUpdate}>
          {t('update.refresh')}
        </Button>
      }
    />
  );
};
