import { Card as Mui<PERSON><PERSON>, CardContent, CardProps as MuiCardProps, styled } from '@mui/material';

export const CARD_ICON_COLORS = {
  student: '#1976d2', // Blue
  instructor: '#2e7d32', // Green
  client: '#ed6c02', // Orange
  default: '#757575', // Grey
  individual: '#1976d2', // Blue
  group: '#2e7d32', // Green
  children: '#ed6c02', // Orange
} as const;

export type CardType = keyof typeof CARD_ICON_COLORS;

interface CardProps extends MuiCardProps {
  children: React.ReactNode;
  iconColor?: string;
  type?: CardType;
}

const StyledCard = styled(MuiCard)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
  transition: 'box-shadow 0.3s ease-in-out',
  '&:hover': {
    boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
  },
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

const Card = ({ children, iconColor, type = 'default', ...props }: CardProps) => {
  const color = iconColor || CARD_ICON_COLORS[type];
  return (
    <StyledCard {...props}>
      <CardContent sx={{ flex: 1, p: 3, '& svg': { color } }}>{children}</CardContent>
    </StyledCard>
  );
};

export default Card;
