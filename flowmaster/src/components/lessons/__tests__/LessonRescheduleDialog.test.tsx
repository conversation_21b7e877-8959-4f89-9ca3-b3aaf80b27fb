import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LessonRescheduleDialog from '../LessonRescheduleDialog';
import { Lesson } from '../../../types/lesson';
import { useAuth } from '../../../hooks/useAuth';
import { useSchool } from '../../../hooks/useSchool';

// Mock Firebase modules
jest.mock('../../../services/firebase', () => ({
  db: {},
}));

// Mock Firestore functions
const mockTimestamp = {
  seconds: **********,
  nanoseconds: 0,
  toDate: jest.fn().mockReturnValue(new Date(********** * 1000)),
};

const mockBatch = {
  update: jest.fn(),
  set: jest.fn(),
  commit: jest.fn().mockResolvedValue(undefined),
};

const mockDoc = jest.fn();
const mockCollection = jest.fn();
const mockWriteBatch = jest.fn().mockReturnValue(mockBatch);
const mockQuery = jest.fn();
const mockWhere = jest.fn();
const mockGetDocs = jest.fn().mockResolvedValue({ docs: [] });
const mockTimestampFn = jest.fn().mockImplementation((seconds, nanoseconds) => ({
  seconds: seconds || **********,
  nanoseconds: nanoseconds || 0,
  toDate: jest.fn().mockReturnValue(new Date((seconds || **********) * 1000)),
}));

jest.mock('firebase/firestore', () => ({
  doc: mockDoc,
  collection: mockCollection,
  writeBatch: mockWriteBatch,
  Timestamp: mockTimestampFn,
  serverTimestamp: jest.fn().mockReturnValue(mockTimestamp),
  query: mockQuery,
  where: mockWhere,
  getDocs: mockGetDocs,
  getFirestore: jest.fn().mockReturnValue({}),
}));

// Mock hooks
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../hooks/useSchool', () => ({
  useSchool: jest.fn(),
}));

// Mock DateTimePicker
jest.mock('@mui/x-date-pickers', () => ({
  LocalizationProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DateTimePicker: ({ value, onChange }: { value: Date; onChange: (date: Date | null) => void }) => (
    <input
      type="datetime-local"
      data-testid="date-time-picker"
      value={value instanceof Date ? value.toISOString().slice(0, 16) : ''}
      onChange={(e) => onChange(new Date(e.target.value))}
    />
  ),
}));

// Setup i18n for tests
i18n.use(initReactI18next).init({
  lng: 'en',
  fallbackLng: 'en',
  ns: ['lessons', 'common'],
  defaultNS: 'lessons',
  resources: {
    en: {
      lessons: {
        'reschedule.title': 'Reschedule Lesson',
        'reschedule.currentDateTime': 'Current Date & Time',
        'reschedule.newDateTime': 'New Date & Time',
        'reschedule.notifications': 'Notifications',
        'reschedule.notifyStudents': 'Notify Students',
        'reschedule.notifyInstructor': 'Notify Instructor',
        'reschedule.reason': 'Reason for Rescheduling',
        'reschedule.reasonPlaceholder': 'Explain why the lesson is being rescheduled...',
        'reschedule.confirm': 'Reschedule',
        'reschedule.defaultMessage': 'This lesson has been rescheduled.',
        'messages.rescheduleError': 'Failed to reschedule lesson',
        'messages.unauthorizedError': 'You do not have permission to reschedule lessons',
        'messages.instructorConflict':
          'The instructor already has a lesson scheduled during this time',
        'messages.studentConflict':
          'One or more students already have lessons scheduled during this time',
      },
      common: {
        'actions.cancel': 'Cancel',
      },
    },
  },
});

// Sample lesson data
const mockLesson: Lesson = {
  id: 'lesson-123',
  title: 'Test Lesson',
  type: 'individual',
  discipline: 'Skiing',
  instructorId: 'instructor-123',
  studentIds: ['student-1', 'student-2'],
  startTime: mockTimestampFn(**********, 0),
  duration: 60,
  level: 'Beginner',
  status: 'scheduled',
  createdBy: 'admin-123',
  updatedAt: mockTimestampFn(**********, 0),
};

// Test props
const defaultProps = {
  open: true,
  onClose: jest.fn(),
  lesson: mockLesson,
  onReschedule: jest.fn(),
};

// Setup component with providers
const renderComponent = (props = defaultProps) => {
  return render(
    <I18nextProvider i18n={i18n}>
      <LessonRescheduleDialog {...props} />
    </I18nextProvider>
  );
};

describe('LessonRescheduleDialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useAuth hook
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        uid: 'admin-123',
        role: 'admin',
      },
    });

    // Mock useSchool hook
    (useSchool as jest.Mock).mockReturnValue({
      currentSchool: {
        id: 'school-123',
        name: 'Test School',
      },
    });
  });

  it('renders the dialog with correct title', () => {
    renderComponent();
    expect(screen.getByText('Reschedule Lesson')).toBeInTheDocument();
  });

  it('displays the current date and time', () => {
    renderComponent();
    expect(screen.getByText('Current Date & Time:')).toBeInTheDocument();
  });

  it('renders the date time picker', () => {
    renderComponent();
    expect(screen.getByText('New Date & Time:')).toBeInTheDocument();
    expect(screen.getByTestId('date-time-picker')).toBeInTheDocument();
  });

  it('renders notification options', () => {
    renderComponent();
    expect(screen.getByText('Notifications:')).toBeInTheDocument();
    expect(screen.getByLabelText('Notify Students')).toBeInTheDocument();
    expect(screen.getByLabelText('Notify Instructor')).toBeInTheDocument();
  });

  it('renders reason text field', () => {
    renderComponent();
    expect(screen.getByLabelText('Reason for Rescheduling')).toBeInTheDocument();
  });

  it('renders action buttons', () => {
    renderComponent();
    expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Reschedule' })).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('prevents students from rescheduling lessons', async () => {
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        uid: 'student-123',
        role: 'student',
      },
    });

    renderComponent();

    // Click reschedule button
    fireEvent.click(screen.getByRole('button', { name: 'Reschedule' }));

    await waitFor(() => {
      expect(
        screen.getByText('You do not have permission to reschedule lessons')
      ).toBeInTheDocument();
      expect(mockBatch.commit).not.toHaveBeenCalled();
    });
  });
});
