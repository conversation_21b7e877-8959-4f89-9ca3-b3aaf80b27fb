import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
  TextField,
  CircularProgress,
  Alert,
} from '@mui/material';
import { LocalizationProvider, DateTimePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Lesson } from '../../types/lesson';
import { useSchool } from '../../hooks/useSchool';
import { useAuth } from '../../hooks/useAuth';
import { db } from '../../services/firebase';
import {
  doc,
  Timestamp,
  serverTimestamp,
  writeBatch,
  collection,
  query,
  where,
  getDocs,
} from 'firebase/firestore';

interface LessonRescheduleDialogProps {
  open: boolean;
  onClose: () => void;
  lesson: Lesson;
  onReschedule?: () => void;
}

const LessonRescheduleDialog: React.FC<LessonRescheduleDialogProps> = ({
  open,
  onClose,
  lesson,
  onReschedule,
}) => {
  const { t } = useTranslation(['lessons', 'common']);
  const { currentSchool } = useSchool();
  const { user } = useAuth();
  const [newDateTime, setNewDateTime] = useState<Date>(() => {
    // Handle different types of startTime safely
    if (lesson.startTime && typeof lesson.startTime === 'object' && 'toDate' in lesson.startTime) {
      return lesson.startTime.toDate();
    }
    return new Date(lesson.startTime as any);
  });
  const [notifyStudents, setNotifyStudents] = useState(true);
  const [notifyInstructor, setNotifyInstructor] = useState(true);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user has permission to reschedule lessons
  const canRescheduleLesson =
    user?.role === 'admin' ||
    user?.role === 'instructor' ||
    (user?.role === 'manager' && lesson.instructorId === user.uid);

  // Function to check for scheduling conflicts
  const checkForConflicts = async (): Promise<{
    hasConflict: boolean;
    conflictMessage: string;
  }> => {
    if (!currentSchool?.id) return { hasConflict: false, conflictMessage: '' };

    try {
      const newStartTime = Timestamp.fromDate(newDateTime);
      const newEndTime = Timestamp.fromDate(
        new Date(newDateTime.getTime() + lesson.duration * 60 * 1000)
      );

      // Check for instructor conflicts
      const instructorQuery = query(
        collection(db, 'schools', currentSchool.id, 'lessons'),
        where('instructorId', '==', lesson.instructorId),
        where('startTime', '<=', newEndTime),
        where('startTime', '>=', newStartTime)
      );

      const instructorSnapshot = await getDocs(instructorQuery);
      const instructorConflicts = instructorSnapshot.docs
        .filter((doc) => doc.id !== lesson.id) // Exclude the current lesson
        .map((doc) => ({ id: doc.id, ...doc.data() }));

      if (instructorConflicts.length > 0) {
        return {
          hasConflict: true,
          conflictMessage: t(
            'messages.instructorConflict',
            'The instructor already has a lesson scheduled during this time.'
          ),
        };
      }

      // Check for student conflicts
      const studentConflicts: { studentId: string; lessonId: string }[] = [];

      for (const studentId of lesson.studentIds) {
        const studentQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('studentIds', 'array-contains', studentId),
          where('startTime', '<=', newEndTime),
          where('startTime', '>=', newStartTime)
        );

        const studentSnapshot = await getDocs(studentQuery);
        const conflicts = studentSnapshot.docs
          .filter((doc) => doc.id !== lesson.id) // Exclude the current lesson
          .map((doc) => ({
            studentId,
            lessonId: doc.id,
          }));

        studentConflicts.push(...conflicts);
      }

      if (studentConflicts.length > 0) {
        return {
          hasConflict: true,
          conflictMessage: t(
            'messages.studentConflict',
            'One or more students already have lessons scheduled during this time.'
          ),
        };
      }

      return { hasConflict: false, conflictMessage: '' };
    } catch (error) {
      console.error('Error checking for conflicts:', error);
      return {
        hasConflict: false,
        conflictMessage: '',
      };
    }
  };

  const handleReschedule = async () => {
    if (!currentSchool?.id) return;

    // Check if user has permission to reschedule
    if (!canRescheduleLesson) {
      setError(t('messages.unauthorizedError', 'You do not have permission to reschedule lessons'));
      return;
    }

    setLoading(true);
    setError(null);

    // Check for scheduling conflicts
    const { hasConflict, conflictMessage } = await checkForConflicts();
    if (hasConflict) {
      setError(conflictMessage);
      setLoading(false);
      return;
    }

    try {
      const batch = writeBatch(db);

      // Update the lesson with new date/time
      const lessonRef = doc(db, 'schools', currentSchool.id, 'lessons', lesson.id);
      batch.update(lessonRef, {
        startTime: newDateTime,
        updatedAt: serverTimestamp(),
        rescheduledBy: user?.uid,
        rescheduledAt: serverTimestamp(),
        originalStartTime: lesson.startTime,
        reschedulingReason: reason || null,
      });

      // If this is a program lesson, handle it differently
      if (lesson.programId && lesson.programSessionId) {
        // Update the program session if needed
        const sessionRef = doc(
          db,
          'schools',
          currentSchool.id,
          'programs',
          lesson.programId,
          'sessions',
          lesson.programSessionId
        );
        batch.update(sessionRef, {
          date: Timestamp.fromDate(newDateTime),
          updatedAt: serverTimestamp(),
          rescheduledBy: user?.uid,
          rescheduledAt: serverTimestamp(),
        });
      }

      // Create notifications if requested
      if ((notifyStudents || notifyInstructor) && reason) {
        const recipients = [
          ...(notifyStudents ? lesson.studentIds : []),
          ...(notifyInstructor ? [lesson.instructorId] : []),
        ];

        if (recipients.length > 0) {
          const notificationsRef = doc(
            collection(db, 'schools', currentSchool.id, 'notifications')
          );
          batch.set(notificationsRef, {
            type: 'lesson_rescheduled',
            lessonId: lesson.id,
            lessonTitle: lesson.title,
            oldDateTime: lesson.startTime,
            newDateTime: newDateTime,
            message: reason || t('reschedule.defaultMessage', 'This lesson has been rescheduled.'),
            recipients,
            read: {},
            createdAt: serverTimestamp(),
            createdBy: user?.uid,
          });
        }
      }

      await batch.commit();

      onReschedule?.();
      onClose();
    } catch (err) {
      console.error('Error rescheduling lesson:', err);
      setError(t('messages.rescheduleError', 'Failed to reschedule lesson'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('reschedule.title', 'Reschedule Lesson')}</DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            {t('reschedule.currentDateTime', 'Current Date & Time')}:
          </Typography>
          <Typography variant="body1" gutterBottom>
            {lesson.startTime &&
            typeof lesson.startTime === 'object' &&
            'toDate' in lesson.startTime
              ? lesson.startTime.toDate().toLocaleString()
              : new Date(lesson.startTime as any).toLocaleString()}
          </Typography>

          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              {t('reschedule.newDateTime', 'New Date & Time')}:
            </Typography>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DateTimePicker
                value={newDateTime}
                onChange={(date) => date && setNewDateTime(date)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </Box>

          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              {t('reschedule.notifications', 'Notifications')}:
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={notifyStudents}
                  onChange={(e) => setNotifyStudents(e.target.checked)}
                />
              }
              label={t('reschedule.notifyStudents', 'Notify Students')}
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={notifyInstructor}
                  onChange={(e) => setNotifyInstructor(e.target.checked)}
                />
              }
              label={t('reschedule.notifyInstructor', 'Notify Instructor')}
            />
          </Box>

          <Box sx={{ mt: 3 }}>
            <TextField
              label={t('reschedule.reason', 'Reason for Rescheduling')}
              multiline
              rows={3}
              fullWidth
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={t(
                'reschedule.reasonPlaceholder',
                'Explain why the lesson is being rescheduled...'
              )}
            />
          </Box>

          {error && (
            <Box sx={{ mt: 2, color: 'error.main' }}>
              <Typography variant="body2">{error}</Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          {t('common:actions.cancel', 'Cancel')}
        </Button>
        <Button onClick={handleReschedule} variant="contained" color="primary" disabled={loading}>
          {loading ? (
            <CircularProgress size={24} color="inherit" />
          ) : (
            t('reschedule.confirm', 'Reschedule')
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LessonRescheduleDialog;
