import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSchool } from '../../hooks/useSchool';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Autocomplete,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import {
  getEquipmentItems,
  assignEquipmentToLesson,
  getEquipmentAssignmentsForLesson,
  removeEquipmentAssignment,
} from '../../services/equipmentService';
import { EquipmentItem, EquipmentAssignment } from '../../types/equipment';

interface LessonEquipmentProps {
  lessonId: string;
}

const LessonEquipment: React.FC<LessonEquipmentProps> = ({ lessonId }) => {
  const { t } = useTranslation(['equipment', 'common', 'lessons']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  // State
  const [assignments, setAssignments] = useState<EquipmentAssignment[]>([]);
  const [availableEquipment, setAvailableEquipment] = useState<EquipmentItem[]>([]);
  const [selectedEquipment, setSelectedEquipment] = useState<EquipmentItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [students, setStudents] = useState<{ id: string; name: string }[]>([]);

  // Fetch equipment assignments and available equipment
  useEffect(() => {
    const fetchData = async () => {
      if (!currentSchool?.id || !lessonId) return;

      setLoading(true);
      try {
        // Fetch equipment assignments for this lesson
        const assignmentData = await getEquipmentAssignmentsForLesson(currentSchool.id, lessonId);
        setAssignments(assignmentData);

        // Fetch available equipment
        const equipmentItems = await getEquipmentItems(currentSchool.id, { available: true });
        setAvailableEquipment(equipmentItems);

        // TODO: Fetch students for this lesson
        // For now, we'll use dummy data
        setStudents([
          { id: 'student1', name: 'John Doe' },
          { id: 'student2', name: 'Jane Smith' },
        ]);

        setError(null);
      } catch (err) {
        console.error('Error fetching equipment data:', err);
        setError(t('messages.fetchError', 'Failed to fetch equipment data', { ns: 'common' }));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentSchool?.id, lessonId, t]);

  // Handle dialog open/close
  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedEquipment(null);
    setSelectedStudent('');
  };

  // Handle equipment assignment
  const handleAssignEquipment = async () => {
    if (!selectedEquipment || !selectedStudent || !currentSchool?.id) {
      return;
    }

    setSaving(true);
    try {
      // Find the student name
      const student = students.find((s) => s.id === selectedStudent);
      if (!student) return;

      // Create the assignment
      const newAssignment: Omit<EquipmentAssignment, 'id'> = {
        lessonId,
        studentId: selectedStudent,
        studentName: student.name,
        equipmentItems: [
          {
            equipmentId: selectedEquipment.id,
            equipmentName: selectedEquipment.name,
            condition: selectedEquipment.condition,
          },
        ],
        assignedAt: new Date(),
      };

      // Save the assignment
      await assignEquipmentToLesson(currentSchool.id, lessonId, newAssignment);

      // Update the assignments list
      const updatedAssignments = await getEquipmentAssignmentsForLesson(currentSchool.id, lessonId);
      setAssignments(updatedAssignments);

      // Update available equipment
      const updatedEquipment = availableEquipment.filter(
        (item) => item.id !== selectedEquipment.id
      );
      setAvailableEquipment(updatedEquipment);

      // Close the dialog
      handleCloseDialog();
    } catch (err) {
      console.error('Error assigning equipment:', err);
      setError(t('messages.saveError', 'Failed to assign equipment', { ns: 'common' }));
    } finally {
      setSaving(false);
    }
  };

  // Handle equipment removal
  const handleRemoveAssignment = async (assignmentId: string) => {
    if (!currentSchool?.id) return;

    try {
      // Find the assignment
      const assignment = assignments.find((a) => a.id === assignmentId);
      if (!assignment) return;

      // Remove the assignment
      await removeEquipmentAssignment(currentSchool.id, lessonId, assignmentId);

      // Update the assignments list
      const updatedAssignments = assignments.filter((a) => a.id !== assignmentId);
      setAssignments(updatedAssignments);

      // Update available equipment
      // This would require fetching the equipment items again
      const equipmentItems = await getEquipmentItems(currentSchool.id, { available: true });
      setAvailableEquipment(equipmentItems);
    } catch (err) {
      console.error('Error removing assignment:', err);
      setError(
        t('messages.deleteError', 'Failed to remove equipment assignment', { ns: 'common' })
      );
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          {t('lessons.equipmentAssignments', 'Equipment Assignments')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
        >
          {t('lessons.assignEquipment', 'Assign Equipment')}
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : assignments.length === 0 ? (
        <Typography variant="body2" color="textSecondary">
          {t('lessons.noEquipment', 'No equipment assigned for this lesson')}
        </Typography>
      ) : (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>{t('lessons.student', 'Student')}</TableCell>
                <TableCell>{t('lessons.equipment', 'Equipment')}</TableCell>
                <TableCell>{t('lessons.assignedAt', 'Assigned At')}</TableCell>
                <TableCell align="right">{t('lessons.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {assignments.map((assignment) => (
                <TableRow key={assignment.id}>
                  <TableCell>{assignment.studentName}</TableCell>
                  <TableCell>
                    {assignment.equipmentItems.map((item, index) => (
                      <div key={index}>{item.equipmentName}</div>
                    ))}
                  </TableCell>
                  <TableCell>{new Date(assignment.assignedAt).toLocaleDateString()}</TableCell>
                  <TableCell align="right">
                    <Tooltip title={t('actions.delete', 'Delete', { ns: 'common' })}>
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveAssignment(assignment.id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Assignment Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{t('lessons.assignEquipment', 'Assign Equipment')}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <Autocomplete
              options={students}
              getOptionLabel={(option) => option.name}
              onChange={(_, value) => setSelectedStudent(value?.id || '')}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('lessons.selectStudent', 'Select Student')}
                  fullWidth
                />
              )}
            />

            <Autocomplete
              options={availableEquipment}
              getOptionLabel={(option) => `${option.name} (${option.condition})`}
              onChange={(_, value) => setSelectedEquipment(value)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('lessons.selectEquipment', 'Select Equipment')}
                  fullWidth
                />
              )}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('actions.cancel', 'Cancel', { ns: 'common' })}
          </Button>
          <Button
            onClick={handleAssignEquipment}
            color="primary"
            variant="contained"
            disabled={!selectedEquipment || !selectedStudent || saving}
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {t('actions.assign', 'Assign', { ns: 'common' })}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LessonEquipment;
