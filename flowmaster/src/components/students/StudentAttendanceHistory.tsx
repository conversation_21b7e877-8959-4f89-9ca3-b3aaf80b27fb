import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Student } from '../../types/student';
import { AttendanceHistoryItem, AttendanceFilterOptions } from '../../types/attendance';
import { getStudentAttendanceHistory } from '../../services/studentAttendanceService';
import { format } from 'date-fns';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  SelectChangeEvent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearIcon from '@mui/icons-material/Clear';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';

interface StudentAttendanceHistoryProps {
  student: Student;
}

const StudentAttendanceHistory: React.FC<StudentAttendanceHistoryProps> = ({ student }) => {
  const { t } = useTranslation(['people', 'common', 'programs']);
  const { currentSchool } = useSchool();
  const [history, setHistory] = useState<AttendanceHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<AttendanceFilterOptions>({});

  useEffect(() => {
    const fetchAttendanceHistory = async () => {
      if (!currentSchool?.id || !student.id) return;

      try {
        setLoading(true);
        const attendanceHistory = await getStudentAttendanceHistory(
          currentSchool.id,
          student.id,
          filters
        );
        setHistory(attendanceHistory);
        setError(null);
      } catch (err) {
        console.error('Error fetching attendance history:', err);
        setError(t('common:messages.fetchError', 'Failed to fetch data'));
      } finally {
        setLoading(false);
      }
    };

    fetchAttendanceHistory();
  }, [currentSchool?.id, student.id, filters, t]);

  // Only show for program students or both
  if (student.type === 'lesson') {
    return (
      <Alert severity="info">
        {t(
          'students.attendanceNotAvailable',
          'Attendance tracking is only available for program students'
        )}
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (history.length === 0) {
    return (
      <Alert severity="info">
        {t('students.noAttendanceHistory', 'No attendance history available for this student')}
      </Alert>
    );
  }

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    const value = event.target.value;
    setFilters({
      ...filters,
      status: value === 'all' ? undefined : (value as 'present' | 'absent' | 'excused'),
    });
  };

  const handleStartDateChange = (date: Date | null) => {
    setFilters({
      ...filters,
      startDate: date || undefined,
    });
  };

  const handleEndDateChange = (date: Date | null) => {
    setFilters({
      ...filters,
      endDate: date || undefined,
    });
  };

  const clearFilters = () => {
    setFilters({});
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircleIcon color="success" />;
      case 'absent':
        return <CancelIcon color="error" />;
      case 'excused':
        return <HourglassEmptyIcon color="warning" />;
      default:
        return null;
    }
  };

  const getStatusChip = (status: string) => {
    let color: 'success' | 'error' | 'warning' | 'default' = 'default';

    switch (status) {
      case 'present':
        color = 'success';
        break;
      case 'absent':
        color = 'error';
        break;
      case 'excused':
        color = 'warning';
        break;
    }

    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {getStatusIcon(status)}
        <Chip
          label={t(`programs:attendance.${status}`, status)}
          size="small"
          color={color}
          sx={{ ml: 1 }}
        />
      </Box>
    );
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          {t('students.attendanceHistory', 'Attendance History')}
        </Typography>
        <Box>
          <Tooltip title={t('common:actions.filter', 'Filter')}>
            <IconButton onClick={() => setShowFilters(!showFilters)}>
              <FilterListIcon />
            </IconButton>
          </Tooltip>
          {Object.keys(filters).length > 0 && (
            <Tooltip title={t('common:actions.clearFilters', 'Clear Filters')}>
              <IconButton onClick={clearFilters}>
                <ClearIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {showFilters && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel id="status-filter-label">
                {t('programs:fields.status', 'Status')}
              </InputLabel>
              <Select
                labelId="status-filter-label"
                id="status-filter"
                value={filters.status || 'all'}
                label={t('programs:fields.status', 'Status')}
                onChange={handleStatusFilterChange}
              >
                <MenuItem value="all">{t('common:filters.all', 'All')}</MenuItem>
                <MenuItem value="present">{t('programs:attendance.present', 'Present')}</MenuItem>
                <MenuItem value="absent">{t('programs:attendance.absent', 'Absent')}</MenuItem>
                <MenuItem value="excused">{t('programs:attendance.excused', 'Excused')}</MenuItem>
              </Select>
            </FormControl>

            <DatePicker
              label={t('common:filters.startDate', 'Start Date')}
              value={filters.startDate || null}
              onChange={handleStartDateChange}
              slotProps={{ textField: { size: 'small' } }}
            />

            <DatePicker
              label={t('common:filters.endDate', 'End Date')}
              value={filters.endDate || null}
              onChange={handleEndDateChange}
              slotProps={{ textField: { size: 'small' } }}
            />
          </Box>
        </Paper>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell sx={{ color: 'white' }}>{t('programs:fields.date', 'Date')}</TableCell>
              <TableCell sx={{ color: 'white' }}>
                {t('programs:fields.program', 'Program')}
              </TableCell>
              <TableCell sx={{ color: 'white' }}>{t('programs:fields.status', 'Status')}</TableCell>
              <TableCell sx={{ color: 'white' }}>{t('programs:fields.notes', 'Notes')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {history.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{format(item.date.toDate(), 'PPP')}</TableCell>
                <TableCell>
                  {item.programName || item.programId || t('common:notAvailable', 'N/A')}
                </TableCell>
                <TableCell>{getStatusChip(item.status)}</TableCell>
                <TableCell>{item.notes || '-'}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default StudentAttendanceHistory;
