import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { Student } from '../../types/student';
import { StudentAttendanceSummary as AttendanceSummary } from '../../types/attendance';
import { getStudentAttendanceSummary } from '../../services/studentAttendanceService';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Grid,
  Paper,
  Divider,
  Chip,
  LinearProgress,
  Tooltip,
} from '@mui/material';

interface StudentAttendanceSummaryProps {
  student: Student;
}

const StudentAttendanceSummary: React.FC<StudentAttendanceSummaryProps> = ({ student }) => {
  const { t } = useTranslation(['people', 'common']);
  const { currentSchool } = useSchool();
  const [summary, setSummary] = useState<AttendanceSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAttendanceSummary = async () => {
      if (!currentSchool?.id || !student.id) return;

      try {
        setLoading(true);
        const attendanceSummary = await getStudentAttendanceSummary(currentSchool.id, student.id);
        setSummary(attendanceSummary);
        setError(null);
      } catch (err) {
        console.error('Error fetching attendance summary:', err);
        setError(t('common:messages.fetchError', 'Failed to fetch data'));
      } finally {
        setLoading(false);
      }
    };

    fetchAttendanceSummary();
  }, [currentSchool?.id, student.id, t]);

  // Only show for program students or both
  if (student.type === 'lesson') {
    return (
      <Alert severity="info">
        {t(
          'students.attendanceNotAvailable',
          'Attendance tracking is only available for program students'
        )}
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!summary || summary.overallAttendance.total === 0) {
    return (
      <Alert severity="info">
        {t('students.noAttendanceData', 'No attendance data available for this student')}
      </Alert>
    );
  }

  const { overallAttendance } = summary;
  const attendanceRate = Math.round(overallAttendance.attendanceRate);

  // Determine color based on attendance rate
  const getAttendanceColor = (rate: number) => {
    if (rate >= 90) return 'success';
    if (rate >= 75) return 'info';
    if (rate >= 60) return 'warning';
    return 'error';
  };

  const attendanceColor = getAttendanceColor(attendanceRate);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('students.attendanceStats', 'Attendance Statistics')}
      </Typography>

      {/* Overall attendance summary */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('students.totalClasses', 'Total Classes')}
              </Typography>
              <Typography variant="h4" color="primary.main">
                {overallAttendance.total}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('students.attended', 'Attended')}
              </Typography>
              <Typography variant="h4" color="success.main">
                {overallAttendance.present}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('students.missed', 'Missed')}
              </Typography>
              <Typography variant="h4" color="error.main">
                {overallAttendance.absent}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('students.excused', 'Excused')}
              </Typography>
              <Typography variant="h4" color="warning.main">
                {overallAttendance.excused}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mt: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">
              {t('students.attendanceRate', 'Attendance Rate')}
            </Typography>
            <Typography variant="body2" fontWeight="bold" color={`${attendanceColor}.main`}>
              {attendanceRate}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={attendanceRate}
            color={attendanceColor as 'success' | 'info' | 'warning' | 'error'}
            sx={{ height: 10, borderRadius: 5 }}
          />
        </Box>
      </Paper>

      {/* Program-specific attendance */}
      {Object.entries(summary.programAttendance).length > 0 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            {t('students.programAttendance', 'Program Attendance')}
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(summary.programAttendance).map(([programId, stats]) => (
              <Grid item xs={12} md={6} key={programId}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {programId}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Chip
                      label={`${t('students.totalClasses', 'Total')}: ${stats.total}`}
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    />
                    <Chip
                      label={`${t('students.attended', 'Attended')}: ${stats.present}`}
                      size="small"
                      color="success"
                      sx={{ mr: 1 }}
                    />
                    <Chip
                      label={`${t('students.missed', 'Missed')}: ${stats.absent}`}
                      size="small"
                      color="error"
                    />
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">
                        {t('students.attendanceRate', 'Attendance Rate')}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        color={`${getAttendanceColor(stats.attendanceRate)}.main`}
                      >
                        {Math.round(stats.attendanceRate)}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.attendanceRate}
                      color={
                        getAttendanceColor(stats.attendanceRate) as
                          | 'success'
                          | 'info'
                          | 'warning'
                          | 'error'
                      }
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default StudentAttendanceSummary;
