import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSchool } from '../../hooks/useSchool';
import { db } from '../../services/firebase';
import { Student } from '../../types/student';
import { Program } from '../../types/program';
import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Chip,
  CircularProgress,
  Alert,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  School as SchoolIcon,
  Event as EventIcon,
  EmojiEvents as CampIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

interface StudentProgramsProps {
  student: Student;
}

const StudentPrograms: React.FC<StudentProgramsProps> = ({ student }) => {
  const { t } = useTranslation(['people', 'programs', 'common']);
  const { currentSchool } = useSchool();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [programProgress, setProgramProgress] = useState<
    Record<string, { skills: Record<string, boolean>; personalGoal: string }>
  >({});

  useEffect(() => {
    const fetchStudentPrograms = async () => {
      if (!currentSchool?.id) return;

      setLoading(true);
      try {
        // Fetch programs where the student is a participant
        const programsQuery = query(
          collection(db, 'schools', currentSchool.id, 'programs'),
          where('participants', 'array-contains', student.id)
        );

        const programsSnapshot = await getDocs(programsQuery);
        const programsData = programsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Program[];

        setPrograms(programsData);

        // Fetch progress data for each program
        const progressData: Record<
          string,
          { skills: Record<string, boolean>; personalGoal: string }
        > = {};

        for (const program of programsData) {
          const progressQuery = query(
            collection(db, 'schools', currentSchool.id, 'programs', program.id, 'progress'),
            where('studentId', '==', student.id)
          );

          const progressSnapshot = await getDocs(progressQuery);

          if (!progressSnapshot.empty) {
            const progressDoc = progressSnapshot.docs[0];
            progressData[program.id] = {
              skills: progressDoc.data().skills || {},
              personalGoal: progressDoc.data().personalGoal || '',
            };
          } else {
            progressData[program.id] = {
              skills: {},
              personalGoal: '',
            };
          }
        }

        setProgramProgress(progressData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching student programs:', err);
        setError(t('common:error.fetch', 'Failed to fetch data'));
        setLoading(false);
      }
    };

    fetchStudentPrograms();
  }, [currentSchool?.id, student.id, t]);

  const getProgramTypeIcon = (type: string) => {
    switch (type) {
      case 'yearly':
        return <SchoolIcon />;
      case 'seasonal':
        return <EventIcon />;
      case 'camp':
        return <CampIcon />;
      default:
        return <EventIcon />;
    }
  };

  const getProgramTypeColor = (type: string): 'primary' | 'success' | 'warning' | 'default' => {
    switch (type) {
      case 'yearly':
        return 'primary';
      case 'seasonal':
        return 'success';
      case 'camp':
        return 'warning';
      default:
        return 'default';
    }
  };

  const calculateProgress = (programId: string): number => {
    const program = programs.find((p) => p.id === programId);
    if (!program || !program.progress.skills.length) return 0;

    const skills = programProgress[programId]?.skills || {};
    const completedSkills = program.progress.skills.filter((skill) => skills[skill]);

    return Math.round((completedSkills.length / program.progress.skills.length) * 100);
  };

  const handleProgramClick = (programId: string) => {
    navigate(`/programs/${programId}`);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (programs.length === 0) {
    return (
      <Alert severity="info">
        {t('students.noPrograms', 'This student is not enrolled in any programs')}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('students.enrolledPrograms', 'Enrolled Programs')}
      </Typography>

      <Grid container spacing={3}>
        {programs.map((program) => (
          <Grid item xs={12} sm={6} md={4} key={program.id}>
            <Card>
              <CardActionArea onClick={() => handleProgramClick(program.id)}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Chip
                      icon={getProgramTypeIcon(program.type)}
                      label={t(`programs:programTypes.${program.type}`, program.type)}
                      color={getProgramTypeColor(program.type)}
                      size="small"
                    />

                    <Typography variant="caption" color="text.secondary">
                      {format(program.schedule.startDate.toDate(), 'MMM d')} -{' '}
                      {format(program.schedule.endDate.toDate(), 'MMM d, yyyy')}
                    </Typography>
                  </Box>

                  <Typography variant="h6" component="h3" gutterBottom>
                    {program.name}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    {program.description.length > 100
                      ? `${program.description.substring(0, 100)}...`
                      : program.description}
                  </Typography>

                  <Divider sx={{ my: 1 }} />

                  <Box>
                    <Box display="flex" alignItems="center" mb={1}>
                      <FlagIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                      <Typography variant="body2" fontWeight="medium">
                        {t('programs:progress.personalGoal', 'Personal Goal')}
                      </Typography>
                    </Box>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      mb={2}
                      sx={{ fontStyle: 'italic' }}
                    >
                      {programProgress[program.id]?.personalGoal
                        ? programProgress[program.id].personalGoal
                        : t('programs:progress.noPersonalGoal', 'No personal goal set')}
                    </Typography>

                    <Box sx={{ width: '100%', mb: 1 }}>
                      <Box display="flex" justifyContent="space-between" mb={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          {t('programs:progress.skillsProgress', 'Skills Progress')}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {calculateProgress(program.id)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={calculateProgress(program.id)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  </Box>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default StudentPrograms;
