import { createTheme, responsiveFontSizes, Theme } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xs: true;
    sm: true;
    md: true;
    lg: true;
    xl: true;
  }
}

const baseTheme = createTheme({
  palette: {
    primary: {
      main: '#2A5C6B',
      light: '#3D7D90',
      dark: '#1A4550',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#D98F4E',
      light: '#E5A970',
      dark: '#B67537',
      contrastText: '#ffffff',
    },
    error: {
      main: '#C75D4F',
      light: '#D47B6F',
      dark: '#A44639',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#E6B65E',
      light: '#ECC584',
      dark: '#C49642',
      contrastText: '#ffffff',
    },
    info: {
      main: '#4A90A7',
      light: '#65A7BC',
      dark: '#37778C',
      contrastText: '#ffffff',
    },
    success: {
      main: '#5B8C5A',
      light: '#76A375',
      dark: '#456E44',
      contrastText: '#ffffff',
    },
    background: {
      default: '#F5F7F7',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
});

const theme: Theme = responsiveFontSizes(baseTheme);

export default theme;
