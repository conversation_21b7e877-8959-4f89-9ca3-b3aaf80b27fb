import React, { lazy, ComponentType } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { LoadingScreen } from '../components/shared/LoadingScreen';
import { Permission } from '../types/role';

/**
 * RouteElement interface for defining route elements
 * This is used for mobile routes that need special handling
 */
interface RouteElement {
  type: 'component' | 'redirect';
  component?: ComponentType<unknown>;
  to: string;
}

// Lazy load components
const Login = lazy(() => import('../pages/auth/Login'));
const Register = lazy(() => import('../pages/auth/Register'));
const ResetPassword = lazy(() => import('../pages/auth/ResetPassword'));
const Dashboard = lazy(() => import('../pages/dashboard'));
const Programs = lazy(() => import('../pages/programs'));
const ProgramCreate = lazy(() => import('../pages/programs/ProgramCreate'));
const ProgramDetail = lazy(() => import('../pages/programs/ProgramDetail'));
const Schedule = lazy(() => import('../pages/schedule/Schedule'));
const People = lazy(() => import('../pages/people'));
const Lessons = lazy(() => import('../pages/lessons'));
const Profile = lazy(() => import('../pages/profile'));
const Settings = lazy(() => import('../pages/settings'));
const Users = lazy(() => import('../pages/users'));
const VerifyEmail = lazy(() => import('../pages/verify-email'));
const LessonDetails = lazy(() => import('../pages/lessons/lesson-details'));
const CourseDetails = lazy(() => import('../pages/lessons/courses'));
const Instructors = lazy(() => import('../pages/people/Instructors/Instructors'));
const Students = lazy(() => import('../pages/people/Students/Students'));
const Clients = lazy(() => import('../pages/people/Clients/Clients'));
const AttendanceReports = lazy(() => import('../pages/reports/AttendanceReports'));
const EquipmentReports = lazy(() => import('../pages/reports/EquipmentReports'));
const EquipmentModule = lazy(() => import('../pages/equipment/EquipmentModule'));
const IndividualLessons = lazy(() => import('../pages/lessons/individual/IndividualLessons'));
const GroupLessons = lazy(() => import('../pages/lessons/group/GroupLessons'));
const ChildrenLessons = lazy(() => import('../pages/lessons/children/ChildrenLessons'));
const WeeklyCourses = lazy(() => import('../pages/lessons/courses/WeeklyCourses'));
const DailyCourses = lazy(() => import('../pages/lessons/courses/DailyCourses'));

/**
 * Protected Route component that checks for authentication
 * and redirects to login if user is not authenticated
 */
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    return <Navigate to="/auth/login" />;
  }

  return <>{children}</>;
};

/**
 * Public Route component that redirects to dashboard
 * if user is already authenticated
 */
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (user) {
    return <Navigate to="/dashboard" />;
  }

  return <>{children}</>;
};

// Local interface that extends the RouteElement from types.ts
interface RouteConfig {
  path: string;
  element: React.ReactNode | RouteElement;
  protected?: boolean;
  requiredPermission?: Permission | Permission[];
  requiredPermissionType?: 'all' | 'any';
}

export const routes: RouteConfig[] = [
  // Auth routes
  {
    path: '/auth/login',
    element: <Login />,
  },
  {
    path: '/auth/register',
    element: <Register />,
  },
  {
    path: '/auth/reset-password',
    element: <ResetPassword />,
  },
  {
    path: '/auth/verify-email',
    element: <VerifyEmail />,
  },

  // Main routes
  {
    path: '/',
    element: <Navigate to="/dashboard" />,
  },
  {
    path: '/dashboard',
    element: <Dashboard />,
    protected: true,
  },
  {
    path: '/programs',
    element: <Programs />,
    protected: true,
    requiredPermission: 'view_programs',
  },
  {
    path: '/programs/create',
    element: <ProgramCreate />,
    protected: true,
    requiredPermission: 'manage_programs',
  },
  {
    path: '/programs/:id',
    element: <ProgramDetail />,
    protected: true,
    requiredPermission: 'view_programs',
  },
  {
    path: '/schedule',
    element: <Schedule />,
    protected: true,
  },
  {
    path: '/people',
    element: <People />,
    protected: true,
  },
  {
    path: '/lessons',
    element: <Lessons />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/individual',
    element: <IndividualLessons />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/group',
    element: <GroupLessons />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/children',
    element: <ChildrenLessons />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/individual/:id',
    element: <LessonDetails />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/group/:id',
    element: <LessonDetails />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/children/:id',
    element: <LessonDetails />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/:id',
    element: <LessonDetails />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/profile',
    element: <Profile />,
    protected: true,
    requiredPermission: 'edit_profile',
  },
  {
    path: '/users',
    element: <Users />,
    protected: true,
    requiredPermission: 'manage_users',
  },
  {
    path: '/settings',
    element: <Settings />,
    protected: true,
    requiredPermission: ['edit_profile', 'manage_settings'],
    requiredPermissionType: 'all',
  },
  {
    path: '/settings/profile',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/profile/Profile')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'edit_profile',
  },
  {
    path: '/settings/language',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/language/Language')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'edit_profile',
  },
  {
    path: '/settings/notifications',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/notifications/Notifications')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'edit_profile',
  },
  {
    path: '/settings/school',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/school/School')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_settings',
  },
  {
    path: '/settings/sports',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/sports/Sports')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_settings',
  },
  {
    path: '/settings/bookings',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/bookings/Bookings')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_settings',
  },
  {
    path: '/settings/security',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/security/Security')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_settings',
  },
  {
    path: '/settings/users',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/users/Users')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_users',
  },
  {
    path: '/settings/financial',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/finance/Finance')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_settings',
  },
  {
    path: '/settings/system',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/settings/system/System')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'manage_settings',
  },
  {
    path: '/people/instructors',
    element: <Instructors />,
    protected: true,
    requiredPermission: 'view_people',
  },
  {
    path: '/people/students',
    element: <Students />,
    protected: true,
    requiredPermission: 'view_people',
  },
  {
    path: '/people/clients',
    element: <Clients />,
    protected: true,
    requiredPermission: 'view_people',
  },
  {
    path: '/people/instructors/:id',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/people/Instructors/InstructorProfile')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'view_people',
  },
  {
    path: '/people/students/:id',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/people/Students/StudentProfile')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'view_people',
  },
  {
    path: '/people/clients/:id',
    element: (
      <React.Suspense fallback={<LoadingScreen />}>
        {React.createElement(lazy(() => import('../pages/people/Clients/ClientProfile')))}
      </React.Suspense>
    ),
    protected: true,
    requiredPermission: 'view_people',
  },
  {
    path: '/lessons/courses/weekly',
    element: <WeeklyCourses />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/courses/daily',
    element: <DailyCourses />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/lessons/courses/:id',
    element: <CourseDetails />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/courses/:id',
    element: <CourseDetails />,
    protected: true,
    requiredPermission: 'view_lessons',
  },
  {
    path: '/reports/attendance',
    element: <AttendanceReports />,
    protected: true,
    requiredPermission: 'view_programs',
  },
  {
    path: '/reports/equipment',
    element: <EquipmentReports />,
    protected: true,
    requiredPermission: 'manage_equipment',
  },
  {
    path: '/equipment/*',
    element: <EquipmentModule />,
    protected: true,
    requiredPermission: 'manage_equipment',
  },
];

/**
 * AppRoutes component that defines all application routes
 * and their corresponding components
 */
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {routes.map((route) => {
        // Handle different types of route elements
        let routeElement: React.ReactNode;

        // Check if the element is a RouteElement object (from mobile routes)
        if (
          route.element &&
          typeof route.element === 'object' &&
          !React.isValidElement(route.element) &&
          'type' in route.element
        ) {
          // This is a mobile route element
          const mobileElement = route.element as RouteElement;

          if (mobileElement.type === 'redirect') {
            routeElement = <Navigate to={mobileElement.to} replace />;
          } else if (mobileElement.type === 'component' && mobileElement.component) {
            const Component = mobileElement.component;
            routeElement = <Component />;
          }
        } else {
          // This is a regular React element
          routeElement = route.element;
        }

        // Wrap with protection if needed
        const element = route.protected ? (
          <ProtectedRoute>{routeElement}</ProtectedRoute>
        ) : (
          <PublicRoute>{routeElement}</PublicRoute>
        );

        return <Route key={route.path} path={route.path} element={element} />;
      })}
      <Route path="*" element={<div>Not Found</div>} />
    </Routes>
  );
};

export default AppRoutes;
