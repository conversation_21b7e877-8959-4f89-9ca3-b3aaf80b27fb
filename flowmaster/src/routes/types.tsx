import { ComponentType } from 'react';

/**
 * RouteElement interface for defining route elements
 * This is used for mobile routes that need special handling
 */
export interface RouteElement {
  type: 'component' | 'redirect';
  component?: ComponentType<any>;
  to?: string;
}

/**
 * RouteConfig interface for defining routes
 * This is used for both web and mobile routes
 */
export interface RouteConfig {
  path: string;
  element: React.ReactNode | RouteElement;
  protected?: boolean;
  requiredPermission?: string | string[];
  requiredPermissionType?: 'all' | 'any';
}

/**
 * MobileRouteConfig interface for defining mobile routes
 * This extends the base RouteConfig with mobile-specific properties
 */
export interface MobileRouteConfig extends RouteConfig {
  icon?: string;
  label?: string;
  showInMenu?: boolean;
  roles?: string[];
}
