# FlowMaster Routes Documentation

This directory contains the routing configuration for the FlowMaster application. It defines all available routes and their corresponding components, along with authentication protection.

## Overview

The routing system is built using React Router v6 and includes:
- Protected routes (requiring authentication)
- Public routes (accessible without authentication)
- Redirect logic for authenticated/unauthenticated users
- 404 handling

## Route Types

### Public Routes
Routes that don't require authentication:
- `/login` - User login page
- `/register` - New user registration
- `/reset-password` - Password reset functionality

### Protected Routes
Routes that require authentication:
- `/dashboard` - Main dashboard
- `/programs` - Program management
- `/lessons` - Lesson management
- `/profile` - User profile
- `/settings` - Application settings
- `/schedule` - Schedule managment
- `/people` - People managment



## Components

### `ProtectedRoute`
Higher-order component that ensures routes are only accessible to authenticated users.

Features:
- Checks for user authentication
- <PERSON><PERSON> loading states
- Redirects to login if not authenticated

Usage:
```tsx
<ProtectedRoute>
  <YourComponent />
</ProtectedRoute>
```

### `PublicRoute`
Higher-order component for routes that should only be accessible to unauthenticated users.

Features:
- Prevents authenticated users from accessing login/register pages
- Redirects to dashboard if already authenticated
- <PERSON><PERSON> loading states

Usage:
```tsx
<PublicRoute>
  <LoginComponent />
</PublicRoute>
```

### `AppRoutes`
Main routing component that defines all application routes.

Features:
- Centralized route definitions
- Integration with authentication system
- 404 handling
- Root path redirection

## Authentication Integration

The routing system integrates with the authentication system through the `useAuth` hook:

```typescript
const { user, loading } = useAuth();
```

This provides:
- Current user state
- Loading state for auth operations
- Automatic route protection
- Redirection based on auth state

## Best Practices

1. **Route Organization**
   - Keep routes grouped by access level
   - Use consistent naming conventions
   - Implement proper route protection

2. **Authentication**
   - Always wrap protected content
   - Handle loading states
   - Provide clear feedback for unauthorized access

3. **Performance**
   - Implement code splitting for routes
   - Use lazy loading for route components
   - Optimize route transitions

4. **Error Handling**
   - Implement proper 404 handling
   - Handle authentication errors
   - Provide user feedback for routing errors

## Testing

When testing routes:
1. Test protected route access
2. Test public route access
3. Test redirections
4. Test loading states
5. Test 404 handling

Example test:
```typescript
describe('ProtectedRoute', () => {
  it('redirects to login when user is not authenticated', () => {
    // Test implementation
  });

  it('renders children when user is authenticated', () => {
    // Test implementation
  });
});
```

## Contributing

When adding new routes:
1. Determine if route should be protected
2. Add route to appropriate section
3. Implement proper authentication checks
4. Add tests for new route
5. Update documentation

## Route Structure

```
/
├── /login
├── /register
├── /reset-password
├── /dashboard
├── /programs
├── /lessons
├── /profile
└── /settings
```

Each route is configured with appropriate protection and fallback behavior.
