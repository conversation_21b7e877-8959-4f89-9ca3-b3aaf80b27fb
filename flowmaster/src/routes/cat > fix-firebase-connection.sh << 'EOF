cat > fix-firebase-connection.sh << 'EOF'
#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Fixing Firebase connection issues...${NC}"

# Stop any running emulators
echo -e "${YELLOW}Stopping any running Firebase emulators...${NC}"
pkill -f "firebase emulators" || true

# Verify environment variables
echo -e "${YELLOW}Checking environment variables...${NC}"
if grep -q "REACT_APP_USE_EMULATORS=true" .env.development; then
    echo -e "${RED}Warning: REACT_APP_USE_EMULATORS is set to true in .env.development${NC}"
    echo -e "${YELLOW}Setting it to false...${NC}"
    sed -i '' 's/REACT_APP_USE_EMULATORS=true/REACT_APP_USE_EMULATORS=false/g' .env.development
    echo -e "${GREEN}Updated REACT_APP_USE_EMULATORS to false${NC}"
else
    echo -e "${GREEN}REACT_APP_USE_EMULATORS is already set to false${NC}"
fi

# Clear build cache
echo -e "${YELLOW}Clearing build cache...${NC}"
rm -rf node_modules/.cache
rm -rf build

# Create a temporary file to verify environment variables
echo -e "${YELLOW}Creating a temporary file to verify environment variables...${NC}"
mkdir -p src/temp
cat > src/temp/env-check.js << EOFJS
console.log('Environment variables:');
console.log('REACT_APP_USE_EMULATORS:', process.env.REACT_APP_USE_EMULATORS);
console.log('NODE_ENV:', process.env.NODE_ENV);
EOFJS

# Start the application
echo -e "${GREEN}Starting the application...${NC}"
echo -e "${YELLOW}Check the console for environment variable values${NC}"
npm start
EOF

chmod +x fix-firebase-connection.sh