import { routes } from '../index';

describe('routes configuration', () => {
  it('should have all required routes defined', () => {
    const requiredPaths = [
      '/',
      '/login',
      '/register',
      '/verify-email',
      '/profile',
      '/lessons',
      '/lessons/:id',
      '/lessons/create',
      '/lessons/:id/edit',
      '/users',
      '/settings',
    ];

    requiredPaths.forEach((path) => {
      const route = routes.find((r) => r.path === path);
      expect(route).toBeDefined();
      expect(route?.element).toBeDefined();
    });
  });

  it('should have valid paths for all routes', () => {
    routes.forEach((route) => {
      expect(route.path).toBeTruthy();
      expect(typeof route.path).toBe('string');
    });
  });

  it('should have proper permission configuration where required', () => {
    const routesWithPermissions = [
      { path: '/profile', permissions: ['edit_profile'] },
      { path: '/lessons', permissions: ['view_lessons'] },
      { path: '/lessons/:id', permissions: ['view_lessons'] },
      { path: '/lessons/create', permissions: ['manage_lessons'] },
      { path: '/lessons/:id/edit', permissions: ['manage_lessons'] },
      { path: '/users', permissions: ['manage_users'] },
      { path: '/settings', permissions: ['edit_profile', 'manage_settings'] },
    ];

    routesWithPermissions.forEach(({ path, permissions }) => {
      const route = routes.find((r) => r.path === path);
      expect(route?.requiredPermission).toBeDefined();

      if (Array.isArray(route?.requiredPermission)) {
        permissions.forEach((permission) => {
          expect(route?.requiredPermission).toContain(permission);
        });
      } else {
        expect(permissions).toContain(route?.requiredPermission);
      }
    });
  });

  it('should have proper permission type for routes with multiple permissions', () => {
    const settingsRoute = routes.find((r) => r.path === '/settings');
    expect(settingsRoute?.requiredPermissionType).toBe('all');
  });

  it('should have proper path patterns for dynamic routes', () => {
    const lessonDetailsPath = routes.find((r) => r.path === '/lessons/:id')?.path;
    const editLessonPath = routes.find((r) => r.path === '/lessons/:id/edit')?.path;
    expect(lessonDetailsPath).toBe('/lessons/:id');
    expect(editLessonPath).toBe('/lessons/:id/edit');
  });

  it('should have proper protection status for routes', () => {
    const publicRoutes = ['/login', '/register', '/verify-email', '/reset-password'];
    const protectedRoutes = ['/', '/programs', '/lessons', '/profile', '/users', '/settings'];

    publicRoutes.forEach((path) => {
      const route = routes.find((r) => r.path === path);
      expect(route?.protected).toBeFalsy();
    });

    protectedRoutes.forEach((path) => {
      const route = routes.find((r) => r.path === path);
      expect(route?.protected).toBe(true);
    });
  });
});
