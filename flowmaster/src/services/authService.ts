import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail as sendReset,
  signOut as firebaseSignOut,
  sendEmailVerification as sendVerification,
  User,
  UserCredential,
  getIdTokenResult,
} from 'firebase/auth';
import { doc, getDoc, setDoc, Timestamp } from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { auth, db } from './firebase';
import { User as CustomUser, UserProfile, UserSettings } from '../types/user';
import { RoleType } from '../types/role';
import { AuthError } from '../types/auth';
import { initializeRoles, setUserRole } from './roleService';
import { createSchool, getSchoolByCode } from './schoolService';

/**
 * Signs in a user with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @returns {Promise<UserCredential>} Firebase user credential
 */
export const signIn = async (email: string, password: string): Promise<UserCredential> => {
  try {
    return await signInWithEmailAndPassword(auth, email, password);
  } catch (error) {
    throw new Error(getErrorMessage(error as AuthError));
  }
};

/**
 * Creates a new user account and profile
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @param {string} displayName - User's display name
 * @returns {Promise<UserCredential>} Firebase user credential
 */
export const signUp = async (
  email: string,
  password: string,
  displayName: string,
  options: {
    schoolName?: string;
    schoolDescription?: string;
    schoolCode?: string;
    role?: RoleType;
  }
): Promise<UserCredential> => {
  try {
    // Initialize roles first to ensure they exist
    await initializeRoles();

    // Create default user settings
    const settings: UserSettings = {
      language: 'sl',
      theme: 'light',
      notifications: true,
    };

    let schoolId = '';
    let userRole = options.role || RoleType.CLIENT;

    // Handle school association
    if (options.schoolName && options.schoolDescription) {
      try {
        // Create new school - assign ADMIN role for school creators
        schoolId = await createSchool(options.schoolName, options.schoolDescription);
        userRole = RoleType.ADMIN;
      } catch (error) {
        throw new Error('Failed to create school. Please try again.');
      }
    } else if (options.schoolCode) {
      // Find existing school
      const school = await getSchoolByCode(options.schoolCode);
      if (!school) {
        throw new Error('Invalid school code. Please check and try again.');
      }
      schoolId = school.id;

      // Validate role for existing school
      if (!options.role) {
        throw new Error('Role selection is required when joining an existing school.');
      }
    } else {
      throw new Error(
        'School information is required. Please either create a new school or provide a school code.'
      );
    }

    // Create the user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const { user } = userCredential;

    // Create user profile with default values
    const newUser: Partial<CustomUser> = {
      roleId: userRole,
      schoolId,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      profile: {
        skills: [],
        certifications: [],
      },
      settings,
    };

    // Merge Firebase user properties with custom properties
    const userDoc = {
      ...newUser,
      displayName,
      email: user.email || '',
      emailVerified: false,
    };

    // Save profile to Firestore
    await setDoc(doc(db, 'users', user.uid), userDoc);

    // Set custom claims for the user
    await setCustomClaims(user.uid, {
      role: userRole,
      schoolId,
    });

    // Send verification email
    await sendVerification(user);

    return userCredential;
  } catch (error) {
    throw new Error(getErrorMessage(error as AuthError));
  }
};

/**
 * Signs out the current user
 * @returns {Promise<void>}
 */
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    throw new Error(getErrorMessage(error as AuthError));
  }
};

/**
 * Sends a password reset email
 * @param {string} email - User's email
 * @returns {Promise<void>}
 */
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendReset(auth, email);
  } catch (error) {
    throw new Error(getErrorMessage(error as AuthError));
  }
};

/**
 * Sends an email verification to the user
 * @param {User} user - Firebase user object
 * @returns {Promise<void>}
 */
export const verifyEmail = async (user: User): Promise<void> => {
  try {
    await sendVerification(user);
  } catch (error) {
    const errorMessage = getErrorMessage(error as AuthError);
    throw new Error(errorMessage);
  }
};

/**
 * Gets the user profile from Firestore
 * @param {string} uid - User's unique ID
 * @returns {Promise<UserProfile>} User profile
 */
export const getUserProfile = async (uid: string): Promise<UserProfile> => {
  try {
    const docRef = doc(db, 'users', uid);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      throw new Error('User profile not found');
    }

    return docSnap.data() as UserProfile;
  } catch (error) {
    throw new Error(getErrorMessage(error as AuthError));
  }
};

/**
 * Sets custom claims for a user
 * @param {string} uid - User ID
 * @param {object} claims - Custom claims to set
 * @returns {Promise<void>}
 */
export const setCustomClaims = async (
  uid: string,
  claims: { role?: RoleType; schoolId?: string }
): Promise<void> => {
  try {
    const functions = getFunctions();
    const setCustomUserClaimsFunction = httpsCallable(functions, 'setCustomUserClaims');

    await setCustomUserClaimsFunction({
      uid,
      claims,
    });

    // Force token refresh to get the new claims
    if (auth.currentUser) {
      await auth.currentUser.getIdToken(true);
    }
  } catch (error) {
    console.error('Error setting custom claims:', error);
    throw error;
  }
};

/**
 * Gets custom claims for the current user
 * @returns {Promise<{role?: RoleType; schoolId?: string}>} Custom claims
 */
export const getCustomClaims = async (): Promise<{ role?: RoleType; schoolId?: string }> => {
  try {
    if (!auth.currentUser) {
      throw new Error('No user is signed in');
    }

    const tokenResult = await getIdTokenResult(auth.currentUser);
    return {
      role: tokenResult.claims.role as RoleType,
      schoolId: tokenResult.claims.schoolId as string,
    };
  } catch (error) {
    console.error('Error getting custom claims:', error);
    throw error;
  }
};

/**
 * Creates a new user with Firebase Functions
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @param {string} displayName - User's display name
 * @param {object} claims - Custom claims to set
 * @returns {Promise<{uid: string}>} Created user's UID
 */
export const createUserWithClaims = async (
  email: string,
  password: string,
  displayName: string,
  claims: { role: RoleType; schoolId: string }
): Promise<{ uid: string }> => {
  try {
    const functions = getFunctions();
    const createUserFunction = httpsCallable(functions, 'createUser');

    const result = await createUserFunction({
      email,
      password,
      displayName,
      claims,
    });

    return result.data as { uid: string };
  } catch (error) {
    console.error('Error creating user with claims:', error);
    throw error;
  }
};

/**
 * Gets a user-friendly error message from Firebase error codes
 * @param {AuthError} error - Firebase auth error
 * @returns {string} User-friendly error message
 */
const getErrorMessage = (error: AuthError): string => {
  // Check for client-side blocking first
  if (
    error.message?.includes('ERR_BLOCKED_BY_CLIENT') ||
    error.message?.includes('net::ERR_BLOCKED_BY_CLIENT')
  ) {
    return 'Request blocked by browser. Please disable ad blockers or privacy extensions for this site.';
  }

  switch (error.code) {
    case 'auth/email-already-in-use':
      return 'Email is already registered';
    case 'auth/invalid-email':
      return 'Invalid email address';
    case 'auth/operation-not-allowed':
      return 'Operation not allowed';
    case 'auth/weak-password':
      return 'Password is too weak';
    case 'auth/user-disabled':
      return 'Account has been disabled';
    case 'auth/user-not-found':
      return 'User not found';
    case 'auth/wrong-password':
      return 'Invalid password';
    case 'auth/too-many-requests':
      return 'Too many attempts. Please try again later';
    default:
      return error.message || 'An error occurred';
  }
};
