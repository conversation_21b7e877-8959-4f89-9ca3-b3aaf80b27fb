import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  orderBy,
  Timestamp,
  serverTimestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Program, ProgramSession, ProgramFormData, WeekDay } from '../types/program';
import { format, addDays } from 'date-fns';

/**
 * Get all programs for a school
 */
export const getPrograms = async (schoolId: string): Promise<Program[]> => {
  try {
    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const q = query(programsRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Program[];
  } catch (error) {
    console.error('Error getting programs:', error);

    // If there's a permission error, return mock data for development
    if (error instanceof Error && error.toString().includes('permission')) {
      console.log('Using mock data due to permission error');
      return getMockPrograms();
    }

    throw error;
  }
};

// Mock data for development when Firestore rules are not properly set up
const getMockPrograms = (): Program[] => {
  const now = new Date();
  const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // First day of current month
  const endDate = new Date(now.getFullYear(), now.getMonth() + 3, 0); // Last day of current month + 3

  return [
    {
      id: 'mock-program-1',
      name: 'Swimming Lessons 2023',
      type: 'yearly',
      description: 'Year-round swimming lessons for all levels',
      schedule: {
        days: ['monday', 'wednesday', 'friday'],
        startTime: '16:00',
        endTime: '18:00',
        startDate: Timestamp.fromDate(startDate),
        endDate: Timestamp.fromDate(endDate),
      },
      location: 'Main Pool',
      participants: ['student1', 'student2', 'student3'],
      instructors: ['instructor1', 'instructor2'],
      pricing: {
        totalFee: 500,
        currency: 'EUR',
        paymentStatus: {
          student1: 'paid',
          student2: 'partial',
          student3: 'pending',
        },
      },
      progress: {
        skills: ['Basic floating', 'Freestyle', 'Backstroke', 'Breaststroke'],
        goals: 'Complete all four swimming styles with proper technique',
      },
      status: 'active',
      createdAt: Timestamp.fromDate(new Date(now.getFullYear(), now.getMonth() - 1, 1)),
      updatedAt: Timestamp.fromDate(new Date()),
    },
    {
      id: 'mock-program-2',
      name: 'Summer Tennis Camp',
      type: 'camp',
      description: 'Intensive tennis training for juniors during summer break',
      schedule: {
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        startTime: '09:00',
        endTime: '15:00',
        startDate: Timestamp.fromDate(new Date(now.getFullYear(), 5, 15)), // June 15
        endDate: Timestamp.fromDate(new Date(now.getFullYear(), 7, 15)), // August 15
      },
      location: 'Tennis Courts',
      participants: ['student4', 'student5', 'student6'],
      instructors: ['instructor3'],
      pricing: {
        totalFee: 800,
        currency: 'EUR',
        paymentStatus: {
          student4: 'paid',
          student5: 'paid',
          student6: 'pending',
        },
      },
      progress: {
        skills: ['Forehand', 'Backhand', 'Serve', 'Volley', 'Match play'],
        goals: 'Develop competitive tennis skills and strategy',
      },
      status: 'active',
      createdAt: Timestamp.fromDate(new Date(now.getFullYear(), now.getMonth() - 2, 15)),
      updatedAt: Timestamp.fromDate(new Date()),
    },
  ];
};

/**
 * Get a specific program by ID
 */
export const getProgram = async (schoolId: string, programId: string): Promise<Program | null> => {
  try {
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);
    const programDoc = await getDoc(programRef);

    if (programDoc.exists()) {
      return {
        id: programDoc.id,
        ...programDoc.data(),
      } as Program;
    }

    return null;
  } catch (error) {
    console.error('Error getting program:', error);

    // If there's a permission error, return mock data for development
    if (error instanceof Error && error.toString().includes('permission')) {
      console.log('Using mock data due to permission error');
      const mockPrograms = getMockPrograms();
      const mockProgram = mockPrograms.find((p) => p.id === programId) || mockPrograms[0];
      return mockProgram;
    }

    throw error;
  }
};

/**
 * Create a new program
 */
export const createProgram = async (
  schoolId: string,
  programData: ProgramFormData
): Promise<string> => {
  try {
    // Convert form dates to Firestore timestamps
    const program = {
      ...programData,
      schedule: {
        ...programData.schedule,
        startDate: Timestamp.fromDate(programData.schedule.startDate),
        endDate: Timestamp.fromDate(programData.schedule.endDate),
      },
      pricing: {
        ...programData.pricing,
        paymentStatus: programData.participants.reduce(
          (acc, studentId) => {
            acc[studentId] = 'pending';
            return acc;
          },
          {} as Record<string, 'paid' | 'partial' | 'pending'>
        ),
        paidAmounts: programData.participants.reduce(
          (acc, studentId) => {
            acc[studentId] = 0;
            return acc;
          },
          {} as Record<string, number>
        ),
        dueDate: programData.pricing.dueDate
          ? Timestamp.fromDate(programData.pricing.dueDate)
          : null,
      },
      status: 'active',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const docRef = await addDoc(programsRef, program);

    // Generate sessions for this program
    await generateProgramSessions(schoolId, docRef.id, programData);

    return docRef.id;
  } catch (error) {
    console.error('Error creating program:', error);
    throw error;
  }
};

/**
 * Update an existing program
 */
export const updateProgram = async (
  schoolId: string,
  programId: string,
  programData: Partial<ProgramFormData>
): Promise<void> => {
  try {
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);

    // Create a new object for Firestore update (different from ProgramFormData)
    // Using any here because Firestore's updateDoc has complex typing requirements
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const firestoreData: Record<string, any> = {};

    // Copy non-schedule properties
    Object.keys(programData).forEach((key) => {
      if (key !== 'schedule') {
        firestoreData[key] = programData[key as keyof ProgramFormData];
      }
    });

    // Handle schedule separately to convert Date objects to Timestamps
    if (programData.schedule) {
      firestoreData.schedule = {
        days: programData.schedule.days,
        startTime: programData.schedule.startTime,
        endTime: programData.schedule.endTime,
        // Convert Date objects to Timestamps
        startDate:
          programData.schedule.startDate instanceof Date
            ? Timestamp.fromDate(programData.schedule.startDate)
            : programData.schedule.startDate,
        endDate:
          programData.schedule.endDate instanceof Date
            ? Timestamp.fromDate(programData.schedule.endDate)
            : programData.schedule.endDate,
      };
    }

    // Handle pricing separately to convert Date objects to Timestamps
    if (programData.pricing) {
      firestoreData.pricing = {
        ...programData.pricing,
        dueDate: programData.pricing.dueDate
          ? Timestamp.fromDate(programData.pricing.dueDate)
          : null,
      };
    }

    firestoreData.updatedAt = serverTimestamp();

    await updateDoc(programRef, firestoreData);
  } catch (error) {
    console.error('Error updating program:', error);
    throw error;
  }
};

/**
 * Delete a program
 */
export const deleteProgram = async (schoolId: string, programId: string): Promise<void> => {
  try {
    // First, delete all sessions for this program
    await deleteProgramSessions(schoolId, programId);

    // Then delete the program itself
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);
    await deleteDoc(programRef);
  } catch (error) {
    console.error('Error deleting program:', error);
    throw error;
  }
};

/**
 * Generate sessions for a program based on its schedule
 */
export const generateProgramSessions = async (
  schoolId: string,
  programId: string,
  programData: ProgramFormData
): Promise<void> => {
  try {
    const { schedule } = programData;
    const startDate = schedule.startDate;
    const endDate = schedule.endDate;
    const days = schedule.days;

    const sessions: Omit<ProgramSession, 'id'>[] = [];
    let currentDate = new Date(startDate);

    // Generate sessions for each day in the schedule
    while (currentDate <= endDate) {
      const dayName = format(currentDate, 'EEEE').toLowerCase() as WeekDay;

      if (days.includes(dayName)) {
        sessions.push({
          programId,
          date: Timestamp.fromDate(currentDate),
          attendance: {},
          notes: '',
          skills: {},
          isMakeup: false,
          createdAt: Timestamp.fromDate(new Date()),
          updatedAt: Timestamp.fromDate(new Date()),
        });
      }

      // Move to next day
      currentDate = addDays(currentDate, 1);
    }

    // Batch write sessions to Firestore
    const batch = writeBatch(db);
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');

    sessions.forEach((session) => {
      const newSessionRef = doc(sessionsRef);
      batch.set(newSessionRef, session);
    });

    await batch.commit();
  } catch (error) {
    console.error('Error generating program sessions:', error);
    throw error;
  }
};

/**
 * Get all sessions for a program
 */
export const getProgramSessions = async (
  schoolId: string,
  programId: string
): Promise<ProgramSession[]> => {
  try {
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const q = query(sessionsRef, orderBy('date', 'asc'));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as ProgramSession[];
  } catch (error) {
    console.error('Error getting program sessions:', error);

    // If there's a permission error, return mock data for development
    if (error instanceof Error && error.toString().includes('permission')) {
      console.log('Using mock sessions data due to permission error');
      return getMockSessions(programId);
    }

    throw error;
  }
};

// Mock sessions data for development
const getMockSessions = (programId: string): ProgramSession[] => {
  const now = new Date();
  const sessions: ProgramSession[] = [];

  // Create 10 mock sessions
  for (let i = 0; i < 10; i++) {
    const sessionDate = new Date(now);
    sessionDate.setDate(now.getDate() + i * 7); // Weekly sessions

    sessions.push({
      id: `mock-session-${i}`,
      programId,
      date: Timestamp.fromDate(sessionDate),
      attendance: {},
      notes: `Session ${i + 1} notes`,
      skills: {},
      isMakeup: i % 5 === 0, // Every 5th session is a makeup
      createdAt: Timestamp.fromDate(new Date(now.getFullYear(), now.getMonth() - 1, 1)),
      updatedAt: Timestamp.fromDate(new Date()),
    });
  }

  return sessions;
};

/**
 * Delete all sessions for a program
 */
export const deleteProgramSessions = async (schoolId: string, programId: string): Promise<void> => {
  try {
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const querySnapshot = await getDocs(sessionsRef);

    const batch = writeBatch(db);
    querySnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
  } catch (error) {
    console.error('Error deleting program sessions:', error);
    throw error;
  }
};
