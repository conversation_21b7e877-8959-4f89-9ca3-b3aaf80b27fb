import { initializeApp, getApps } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { getAnalytics } from 'firebase/analytics';
import { getPerformance } from 'firebase/performance';

const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

// Log config without sensitive data
console.log('Firebase Config:', {
  ...firebaseConfig,
  apiKey: firebaseConfig.apiKey ? '***' : undefined,
});

// Initialize Firebase only if it hasn't been initialized yet
const app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
console.log('Firebase app initialized');

// Initialize services
export const auth = getAuth(app);
console.log('Firebase auth initialized');

export const db = getFirestore(app);
console.log('Firebase firestore initialized');

export const storage = getStorage(app);
console.log('Firebase storage initialized');

export const functions = getFunctions(app);
console.log('Firebase functions initialized');

// Connect to emulators in development environment only if USE_EMULATORS is set
if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_EMULATORS === 'true') {
  try {
    // Connect to Auth emulator
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    console.log('Connected to Auth emulator on port 9099');

    // Connect to Firestore emulator
    connectFirestoreEmulator(db, 'localhost', 8082);
    console.log('Connected to Firestore emulator on port 8082');

    // Connect to Storage emulator
    connectStorageEmulator(storage, 'localhost', 9199);
    console.log('Connected to Storage emulator on port 9199');

    // Connect to Functions emulator
    connectFunctionsEmulator(functions, 'localhost', 5001);
    console.log('Connected to Functions emulator on port 5001');

    console.log('Successfully connected to all Firebase emulators');
  } catch (error) {
    console.error('Error connecting to Firebase emulators:', error);
    console.warn('Please ensure Firebase emulators are running using: firebase emulators:start');
  }
} else {
  console.log('Using production Firebase services');
}

// Only initialize analytics and performance monitoring in production
if (process.env.NODE_ENV === 'production') {
  try {
    getAnalytics(app);
    console.log('Firebase analytics initialized');
    getPerformance(app);
    console.log('Firebase performance initialized');
  } catch (error) {
    console.warn('Error initializing Firebase analytics or performance:', error);
  }
}

export default app;
