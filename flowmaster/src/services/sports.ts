import { doc, getDoc } from 'firebase/firestore';
import { db } from './firebase';

export interface Sport {
  id: string;
  name: string;
  displayName: string;
}

interface FirestoreSport {
  id: string;
  name: string;
  isCustom: boolean;
}

export const fetchSports = async (schoolId: string): Promise<Sport[]> => {
  try {
    console.log('Fetching sports for school ID:', schoolId);

    const schoolDocRef = doc(db, 'schools', schoolId);
    console.log('School doc reference:', schoolDocRef);

    const schoolDoc = await getDoc(schoolDocRef);
    console.log('School doc exists:', schoolDoc.exists());

    const schoolData = schoolDoc.data();
    console.log('Raw school data:', schoolData);

    if (!schoolData?.sports) {
      console.log('No sports found in school data');
      return [];
    }

    // Verify the structure of sports array
    console.log('Sports array from Firestore:', schoolData.sports);

    const mappedSports = schoolData.sports.map((sport: FirestoreSport) => {
      console.log('Processing sport:', sport);
      return {
        id: sport.id,
        name: sport.name,
        displayName: sport.name,
      };
    });

    console.log('Final mapped sports:', mappedSports);
    return mappedSports;
  } catch (error) {
    console.error('Error fetching sports:', error);
    throw error; // Let's throw the error to see what's happening
  }
};
