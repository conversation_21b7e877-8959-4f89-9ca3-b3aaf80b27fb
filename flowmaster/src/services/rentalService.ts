import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
  QueryConstraint,
  runTransaction,
} from 'firebase/firestore';
import { db } from './firebase';
import {
  RentalRecord,
  RentalFilterOptions,
  RentalStatus,
  RentalItem,
  LessonEquipmentAssignment,
} from '../types/equipment';
import { updateEquipmentAvailability } from './equipmentService';

/**
 * Get all rental records for a school
 * @param schoolId - The school ID
 * @param filters - Optional filter options
 * @returns Promise with array of rental records
 */
export const getRentalRecords = async (
  schoolId: string,
  filters?: RentalFilterOptions
): Promise<RentalRecord[]> => {
  try {
    const rentalsRef = collection(db, 'schools', schoolId, 'rentals');
    const constraints: QueryConstraint[] = [];

    // Apply filters if provided
    if (filters) {
      if (filters.status) {
        constraints.push(where('status', '==', filters.status));
      }
      if (filters.customerId) {
        constraints.push(where('customerId', '==', filters.customerId));
      }
      if (filters.lessonId) {
        constraints.push(where('relatedLessonId', '==', filters.lessonId));
      }
      if (filters.dateFrom) {
        constraints.push(where('date', '>=', Timestamp.fromDate(filters.dateFrom)));
      }
      if (filters.dateTo) {
        constraints.push(where('date', '<=', Timestamp.fromDate(filters.dateTo)));
      }
    }

    // Always order by date (descending)
    constraints.push(orderBy('date', 'desc'));

    const rentalsQuery = query(rentalsRef, ...constraints);
    const snapshot = await getDocs(rentalsQuery);

    let rentals = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as RentalRecord[];

    // Apply search filter if provided
    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      rentals = rentals.filter(
        (rental) =>
          rental.customerName.toLowerCase().includes(searchLower) ||
          rental.items.some((item) => item.equipmentName.toLowerCase().includes(searchLower))
      );
    }

    return rentals;
  } catch (error) {
    console.error('Error getting rental records:', error);
    throw error;
  }
};

/**
 * Get a single rental record by ID
 * @param schoolId - The school ID
 * @param rentalId - The rental record ID
 * @returns Promise with the rental record
 */
export const getRentalRecord = async (
  schoolId: string,
  rentalId: string
): Promise<RentalRecord> => {
  try {
    const rentalRef = doc(db, 'schools', schoolId, 'rentals', rentalId);
    const rentalDoc = await getDoc(rentalRef);

    if (!rentalDoc.exists()) {
      throw new Error('Rental record not found');
    }

    return {
      id: rentalDoc.id,
      ...rentalDoc.data(),
    } as RentalRecord;
  } catch (error) {
    console.error('Error getting rental record:', error);
    throw error;
  }
};

/**
 * Create a new rental record
 * @param schoolId - The school ID
 * @param rental - The rental record data
 * @returns Promise with the new rental record ID
 */
export const createRentalRecord = async (
  schoolId: string,
  rental: Omit<RentalRecord, 'id' | 'status' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    // Use a transaction to update equipment availability and create rental
    return await runTransaction(db, async (transaction) => {
      // Check if all equipment items are available
      for (const item of rental.items) {
        const equipmentRef = doc(db, 'schools', schoolId, 'equipment', item.equipmentId);
        const equipmentDoc = await transaction.get(equipmentRef);
        
        if (!equipmentDoc.exists()) {
          throw new Error(`Equipment item ${item.equipmentId} not found`);
        }
        
        const equipmentData = equipmentDoc.data();
        if (!equipmentData.available) {
          throw new Error(`Equipment item ${item.equipmentName} is not available`);
        }
        
        // Mark equipment as unavailable
        transaction.update(equipmentRef, { 
          available: false,
          updatedAt: serverTimestamp()
        });
      }
      
      // Create the rental record
      const rentalsRef = collection(db, 'schools', schoolId, 'rentals');
      const newRental = {
        ...rental,
        status: 'active' as RentalStatus,
        returned: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };
      
      const newRentalRef = doc(rentalsRef);
      transaction.set(newRentalRef, newRental);
      
      return newRentalRef.id;
    });
  } catch (error) {
    console.error('Error creating rental record:', error);
    throw error;
  }
};

/**
 * Update an existing rental record
 * @param schoolId - The school ID
 * @param rentalId - The rental record ID
 * @param updates - The fields to update
 * @returns Promise that resolves when the update is complete
 */
export const updateRentalRecord = async (
  schoolId: string,
  rentalId: string,
  updates: Partial<Omit<RentalRecord, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const rentalRef = doc(db, 'schools', schoolId, 'rentals', rentalId);
    
    // Add updated timestamp
    const updatedData = {
      ...updates,
      updatedAt: serverTimestamp(),
    };

    await updateDoc(rentalRef, updatedData);
  } catch (error) {
    console.error('Error updating rental record:', error);
    throw error;
  }
};

/**
 * Mark a rental as returned
 * @param schoolId - The school ID
 * @param rentalId - The rental record ID
 * @param returnDate - The return date (defaults to now)
 * @param itemUpdates - Optional updates to equipment items (e.g., condition changes)
 * @returns Promise that resolves when the return is complete
 */
export const returnRental = async (
  schoolId: string,
  rentalId: string,
  returnDate: Date = new Date(),
  itemUpdates?: { equipmentId: string; condition: string; notes?: string }[]
): Promise<void> => {
  try {
    // Use a transaction to update equipment availability and rental status
    await runTransaction(db, async (transaction) => {
      // Get the rental record
      const rentalRef = doc(db, 'schools', schoolId, 'rentals', rentalId);
      const rentalDoc = await transaction.get(rentalRef);
      
      if (!rentalDoc.exists()) {
        throw new Error('Rental record not found');
      }
      
      const rentalData = rentalDoc.data() as RentalRecord;
      
      // Update each equipment item
      for (const item of rentalData.items) {
        const equipmentRef = doc(db, 'schools', schoolId, 'equipment', item.equipmentId);
        
        // Check if there are specific updates for this item
        const itemUpdate = itemUpdates?.find(update => update.equipmentId === item.equipmentId);
        
        // Mark equipment as available and update condition if needed
        const updates: any = { 
          available: true,
          updatedAt: serverTimestamp()
        };
        
        if (itemUpdate) {
          updates.condition = itemUpdate.condition;
          if (itemUpdate.notes) {
            updates.notes = itemUpdate.notes;
          }
        }
        
        transaction.update(equipmentRef, updates);
      }
      
      // Update the rental record
      transaction.update(rentalRef, {
        returned: true,
        returnDate: Timestamp.fromDate(returnDate),
        status: 'returned',
        updatedAt: serverTimestamp(),
      });
    });
  } catch (error) {
    console.error('Error returning rental:', error);
    throw error;
  }
};

/**
 * Get active rentals for a student
 * @param schoolId - The school ID
 * @param studentId - The student ID
 * @returns Promise with array of active rental records
 */
export const getStudentActiveRentals = async (
  schoolId: string,
  studentId: string
): Promise<RentalRecord[]> => {
  try {
    const rentalsRef = collection(db, 'schools', schoolId, 'rentals');
    const rentalsQuery = query(
      rentalsRef,
      where('customerId', '==', studentId),
      where('returned', '==', false)
    );
    
    const snapshot = await getDocs(rentalsQuery);
    
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as RentalRecord[];
  } catch (error) {
    console.error('Error getting student active rentals:', error);
    throw error;
  }
};

/**
 * Get equipment assignments for a lesson
 * @param schoolId - The school ID
 * @param lessonId - The lesson ID
 * @returns Promise with array of equipment assignments
 */
export const getLessonEquipmentAssignments = async (
  schoolId: string,
  lessonId: string
): Promise<LessonEquipmentAssignment[]> => {
  try {
    const assignmentsRef = collection(
      db,
      'schools',
      schoolId,
      'lessons',
      lessonId,
      'equipmentAssignments'
    );
    
    const snapshot = await getDocs(assignmentsRef);
    
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as LessonEquipmentAssignment[];
  } catch (error) {
    console.error('Error getting lesson equipment assignments:', error);
    throw error;
  }
};

/**
 * Assign equipment to a student in a lesson
 * @param schoolId - The school ID
 * @param lessonId - The lesson ID
 * @param assignment - The equipment assignment data
 * @returns Promise with the assignment ID
 */
export const assignEquipmentToLesson = async (
  schoolId: string,
  lessonId: string,
  assignment: Omit<LessonEquipmentAssignment, 'id'>
): Promise<string> => {
  try {
    // Create a rental record if equipment is being issued
    let rentalId: string | undefined;
    
    if (assignment.issued && assignment.equipmentItems.length > 0) {
      // Get lesson details for the rental record
      const lessonRef = doc(db, 'schools', schoolId, 'lessons', lessonId);
      const lessonDoc = await getDoc(lessonRef);
      
      if (!lessonDoc.exists()) {
        throw new Error('Lesson not found');
      }
      
      const lessonData = lessonDoc.data();
      
      // Create a rental record
      rentalId = await createRentalRecord(schoolId, {
        date: Timestamp.fromDate(new Date()),
        dueDate: Timestamp.fromDate(new Date()), // Set to end of lesson
        customerId: assignment.studentId,
        customerName: assignment.studentName,
        customerType: 'student',
        items: assignment.equipmentItems,
        relatedLessonId: lessonId,
        returned: false,
        notes: `Assigned for lesson on ${lessonData.date.toDate().toLocaleDateString()}`,
        createdBy: 'system', // This should be the current user ID in a real app
      });
      
      // Update the assignment with the rental ID
      assignment.rentalId = rentalId;
    }
    
    // Save the assignment
    const assignmentsRef = collection(
      db,
      'schools',
      schoolId,
      'lessons',
      lessonId,
      'equipmentAssignments'
    );
    
    const docRef = await addDoc(assignmentsRef, {
      ...assignment,
      updatedAt: serverTimestamp(),
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error assigning equipment to lesson:', error);
    throw error;
  }
};

/**
 * Return equipment from a lesson
 * @param schoolId - The school ID
 * @param lessonId - The lesson ID
 * @param assignmentId - The assignment ID
 * @param itemUpdates - Optional updates to equipment items
 * @returns Promise that resolves when the return is complete
 */
export const returnEquipmentFromLesson = async (
  schoolId: string,
  lessonId: string,
  assignmentId: string,
  itemUpdates?: { equipmentId: string; condition: string; notes?: string }[]
): Promise<void> => {
  try {
    // Get the assignment
    const assignmentRef = doc(
      db,
      'schools',
      schoolId,
      'lessons',
      lessonId,
      'equipmentAssignments',
      assignmentId
    );
    
    const assignmentDoc = await getDoc(assignmentRef);
    
    if (!assignmentDoc.exists()) {
      throw new Error('Assignment not found');
    }
    
    const assignment = assignmentDoc.data() as LessonEquipmentAssignment;
    
    // If there's a rental ID, mark it as returned
    if (assignment.rentalId) {
      await returnRental(schoolId, assignment.rentalId, new Date(), itemUpdates);
    }
    
    // Update the assignment
    await updateDoc(assignmentRef, {
      returned: true,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error returning equipment from lesson:', error);
    throw error;
  }
};
