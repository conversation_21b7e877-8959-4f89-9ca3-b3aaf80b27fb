import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  setDoc,
  updateDoc,
  writeBatch,
  limit,
} from 'firebase/firestore';
import { db } from './firebase';
import { Program, ProgramSession, AttendanceStatus } from '../types/program';
import { Student } from '../types/student';
import {
  AttendanceRecord,
  AttendanceStats,
  StudentAttendanceSummary,
  AttendanceHistoryItem,
  AttendanceFilterOptions,
} from '../types/attendance';

/**
 * Get attendance history for a student
 */
export const getStudentAttendanceHistory = async (
  schoolId: string,
  studentId: string,
  options: AttendanceFilterOptions = {}
): Promise<AttendanceHistoryItem[]> => {
  try {
    const history: AttendanceHistoryItem[] = [];

    // Get all programs the student is enrolled in
    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const programsQuery = query(programsRef, where('participants', 'array-contains', studentId));
    const programsSnapshot = await getDocs(programsQuery);

    // For each program, get all sessions and their attendance
    for (const programDoc of programsSnapshot.docs) {
      const program = { id: programDoc.id, ...programDoc.data() } as Program;

      // Get all sessions for this program
      const sessionsRef = collection(db, 'schools', schoolId, 'programs', program.id, 'sessions');
      let sessionsQuery = query(sessionsRef, orderBy('date', 'desc'));

      // Apply date filters if provided
      if (options.startDate) {
        sessionsQuery = query(
          sessionsQuery,
          where('date', '>=', Timestamp.fromDate(options.startDate))
        );
      }

      if (options.endDate) {
        sessionsQuery = query(
          sessionsQuery,
          where('date', '<=', Timestamp.fromDate(options.endDate))
        );
      }

      const sessionsSnapshot = await getDocs(sessionsQuery);

      // Process each session
      for (const sessionDoc of sessionsSnapshot.docs) {
        const session = { id: sessionDoc.id, ...sessionDoc.data() } as ProgramSession;

        // Check if student has attendance record for this session
        if (session.attendance && session.attendance[studentId]) {
          const status = session.attendance[studentId];

          // Skip if filtering by status and this doesn't match
          if (options.status && status !== options.status) {
            continue;
          }

          history.push({
            id: sessionDoc.id,
            date: session.date,
            status,
            programId: program.id,
            programName: program.name,
            sessionId: sessionDoc.id,
            notes: session.notes,
          });
        }
      }
    }

    // Sort by date (newest first)
    return history.sort((a, b) => b.date.toMillis() - a.date.toMillis());
  } catch (error) {
    console.error('Error getting student attendance history:', error);
    throw error;
  }
};

/**
 * Calculate attendance statistics for a student
 */
export const calculateStudentAttendanceStats = async (
  schoolId: string,
  studentId: string
): Promise<StudentAttendanceSummary> => {
  try {
    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const programsQuery = query(programsRef, where('participants', 'array-contains', studentId));
    const programsSnapshot = await getDocs(programsQuery);

    const programAttendance: Record<string, AttendanceStats> = {};
    let totalSessions = 0;
    let totalPresent = 0;
    let totalAbsent = 0;
    let totalExcused = 0;

    // Process each program
    for (const programDoc of programsSnapshot.docs) {
      const programId = programDoc.id;
      const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
      const sessionsSnapshot = await getDocs(sessionsRef);

      let programSessions = 0;
      let programPresent = 0;
      let programAbsent = 0;
      let programExcused = 0;

      // Process each session
      for (const sessionDoc of sessionsSnapshot.docs) {
        const session = sessionDoc.data() as ProgramSession;

        // Skip future sessions
        if (session.date.toMillis() > Date.now()) {
          continue;
        }

        // Check if student has attendance record for this session
        if (session.attendance && session.attendance[studentId]) {
          programSessions++;

          switch (session.attendance[studentId]) {
            case 'present':
              programPresent++;
              break;
            case 'absent':
              programAbsent++;
              break;
            case 'excused':
              programExcused++;
              break;
          }
        }
      }

      // Calculate program attendance rate
      const attendanceRate = programSessions > 0 ? (programPresent / programSessions) * 100 : 0;

      // Store program stats
      programAttendance[programId] = {
        total: programSessions,
        present: programPresent,
        absent: programAbsent,
        excused: programExcused,
        attendanceRate,
      };

      // Add to overall totals
      totalSessions += programSessions;
      totalPresent += programPresent;
      totalAbsent += programAbsent;
      totalExcused += programExcused;
    }

    // Calculate overall attendance rate
    const overallAttendanceRate = totalSessions > 0 ? (totalPresent / totalSessions) * 100 : 0;

    // Create summary object
    const summary: StudentAttendanceSummary = {
      studentId,
      programAttendance,
      overallAttendance: {
        total: totalSessions,
        present: totalPresent,
        absent: totalAbsent,
        excused: totalExcused,
        attendanceRate: overallAttendanceRate,
      },
      lastUpdated: Timestamp.now(),
    };

    return summary;
  } catch (error) {
    console.error('Error calculating student attendance stats:', error);
    throw error;
  }
};

/**
 * Store attendance summary for a student
 */
export const storeStudentAttendanceSummary = async (
  schoolId: string,
  summary: StudentAttendanceSummary
): Promise<void> => {
  try {
    const summaryRef = doc(
      db,
      'schools',
      schoolId,
      'students',
      summary.studentId,
      'attendance',
      'summary'
    );

    await setDoc(summaryRef, summary);
  } catch (error) {
    console.error('Error storing student attendance summary:', error);
    throw error;
  }
};

/**
 * Get attendance summary for a student
 */
export const getStudentAttendanceSummary = async (
  schoolId: string,
  studentId: string
): Promise<StudentAttendanceSummary> => {
  try {
    const summaryRef = doc(db, 'schools', schoolId, 'students', studentId, 'attendance', 'summary');
    const summaryDoc = await getDoc(summaryRef);

    if (summaryDoc.exists()) {
      return summaryDoc.data() as StudentAttendanceSummary;
    }

    // If summary doesn't exist, calculate it
    const summary = await calculateStudentAttendanceStats(schoolId, studentId);
    await storeStudentAttendanceSummary(schoolId, summary);

    return summary;
  } catch (error) {
    console.error('Error getting student attendance summary:', error);
    throw error;
  }
};
