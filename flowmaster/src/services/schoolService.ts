import { collection, doc, setDoc, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '../config/firebase';

interface School {
  id: string;
  name: string;
  description: string;
  code: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Creates a new school in Firestore
 * @param {string} name - School name
 * @param {string} description - School description
 * @returns {Promise<string>} School ID
 */
export const createSchool = async (name: string, description: string): Promise<string> => {
  try {
    const schoolsRef = collection(db, 'schools');
    const code = generateSchoolCode();

    const school: School = {
      id: '', // Will be set after document creation
      name,
      description,
      code,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    const docRef = doc(schoolsRef);
    school.id = docRef.id;
    await setDoc(docRef, school);

    return docRef.id;
  } catch (error) {
    console.error('Error creating school:', error);
    throw error;
  }
};

/**
 * Gets a school by its code
 * @param {string} code - School code
 * @returns {Promise<School | null>} School object or null if not found
 */
export const getSchoolByCode = async (code: string): Promise<School | null> => {
  try {
    const schoolsRef = collection(db, 'schools');
    const q = query(schoolsRef, where('code', '==', code));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const schoolDoc = querySnapshot.docs[0];
    return { ...schoolDoc.data(), id: schoolDoc.id } as School;
  } catch (error) {
    console.error('Error getting school by code:', error);
    throw error;
  }
};

/**
 * Generates a unique school code
 * @returns {string} School code
 */
const generateSchoolCode = (): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const codeLength = 6;
  let code = '';

  for (let i = 0; i < codeLength; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    code += characters[randomIndex];
  }

  return code;
};
