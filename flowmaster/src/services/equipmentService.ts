import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
  QueryConstraint,
} from 'firebase/firestore';
import { db } from './firebase';
import {
  EquipmentItem,
  EquipmentFilterOptions,
  EquipmentCategory,
  EquipmentCondition,
  EquipmentAssignment,
} from '../types/equipment';

/**
 * Get all equipment items for a school
 * @param schoolId - The school ID
 * @param filters - Optional filter options
 * @returns Promise with array of equipment items
 */
export const getEquipmentItems = async (
  schoolId: string,
  filters?: EquipmentFilterOptions
): Promise<EquipmentItem[]> => {
  try {
    const equipmentRef = collection(db, 'schools', schoolId, 'equipment');
    const constraints: QueryConstraint[] = [];

    // Apply filters if provided
    if (filters) {
      if (filters.category) {
        constraints.push(where('category', '==', filters.category));
      }
      if (filters.condition) {
        constraints.push(where('condition', '==', filters.condition));
      }
      if (filters.available !== undefined) {
        constraints.push(where('available', '==', filters.available));
      }
      // Note: search filter is applied client-side after fetching
    }

    // Always order by name
    constraints.push(orderBy('name'));

    const equipmentQuery = query(equipmentRef, ...constraints);
    const snapshot = await getDocs(equipmentQuery);

    let items = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as EquipmentItem[];

    // Apply search filter if provided
    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      items = items.filter(
        (item) =>
          item.name.toLowerCase().includes(searchLower) ||
          item.serialNumber?.toLowerCase().includes(searchLower) ||
          item.size?.toLowerCase().includes(searchLower)
      );
    }

    return items;
  } catch (error) {
    console.error('Error getting equipment items:', error);
    throw error;
  }
};

/**
 * Get a single equipment item by ID
 * @param schoolId - The school ID
 * @param equipmentId - The equipment item ID
 * @returns Promise with the equipment item
 */
export const getEquipmentItem = async (
  schoolId: string,
  equipmentId: string
): Promise<EquipmentItem> => {
  try {
    // Check if equipmentId is valid
    if (!equipmentId || equipmentId === 'new') {
      throw new Error('Invalid equipment ID');
    }

    const equipmentRef = doc(db, 'schools', schoolId, 'equipment', equipmentId);
    const equipmentDoc = await getDoc(equipmentRef);

    if (!equipmentDoc.exists()) {
      throw new Error('Equipment item not found');
    }

    return {
      id: equipmentDoc.id,
      ...equipmentDoc.data(),
    } as EquipmentItem;
  } catch (error) {
    console.error('Error getting equipment item:', error);
    throw error;
  }
};

/**
 * Create a new equipment item
 * @param schoolId - The school ID
 * @param item - The equipment item data
 * @returns Promise with the new equipment item ID
 */
export const createEquipmentItem = async (
  schoolId: string,
  item: Omit<EquipmentItem, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    const equipmentRef = collection(db, 'schools', schoolId, 'equipment');

    // Set default values
    const newItem = {
      ...item,
      available: item.available ?? true,
      condition: item.condition || 'good',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(equipmentRef, newItem);
    return docRef.id;
  } catch (error) {
    console.error('Error creating equipment item:', error);
    throw error;
  }
};

/**
 * Update an existing equipment item
 * @param schoolId - The school ID
 * @param equipmentId - The equipment item ID
 * @param updates - The fields to update
 * @returns Promise that resolves when the update is complete
 */
export const updateEquipmentItem = async (
  schoolId: string,
  equipmentId: string,
  updates: Partial<Omit<EquipmentItem, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const equipmentRef = doc(db, 'schools', schoolId, 'equipment', equipmentId);

    // Add updated timestamp
    const updatedData = {
      ...updates,
      updatedAt: serverTimestamp(),
    };

    await updateDoc(equipmentRef, updatedData);
  } catch (error) {
    console.error('Error updating equipment item:', error);
    throw error;
  }
};

/**
 * Delete an equipment item
 * @param schoolId - The school ID
 * @param equipmentId - The equipment item ID
 * @returns Promise that resolves when the deletion is complete
 */
export const deleteEquipmentItem = async (schoolId: string, equipmentId: string): Promise<void> => {
  try {
    const equipmentRef = doc(db, 'schools', schoolId, 'equipment', equipmentId);
    await deleteDoc(equipmentRef);
  } catch (error) {
    console.error('Error deleting equipment item:', error);
    throw error;
  }
};

/**
 * Update equipment availability
 * @param schoolId - The school ID
 * @param equipmentId - The equipment item ID
 * @param available - Whether the equipment is available
 * @returns Promise that resolves when the update is complete
 */
export const updateEquipmentAvailability = async (
  schoolId: string,
  equipmentId: string,
  available: boolean
): Promise<void> => {
  try {
    await updateEquipmentItem(schoolId, equipmentId, { available });
  } catch (error) {
    console.error('Error updating equipment availability:', error);
    throw error;
  }
};

/**
 * Update equipment condition
 * @param schoolId - The school ID
 * @param equipmentId - The equipment item ID
 * @param condition - The new condition
 * @returns Promise that resolves when the update is complete
 */
export const updateEquipmentCondition = async (
  schoolId: string,
  equipmentId: string,
  condition: EquipmentCondition
): Promise<void> => {
  try {
    await updateEquipmentItem(schoolId, equipmentId, { condition });
  } catch (error) {
    console.error('Error updating equipment condition:', error);
    throw error;
  }
};

/**
 * Get equipment categories
 * @returns Array of equipment categories
 */
export const getEquipmentCategories = (): EquipmentCategory[] => {
  return ['kite', 'board', 'harness', 'wetsuit', 'helmet', 'other'];
};

/**
 * Get equipment conditions
 * @returns Array of equipment conditions
 */
export const getEquipmentConditions = (): EquipmentCondition[] => {
  return ['good', 'damaged', 'maintenance', 'lost'];
};

/**
 * Get equipment assignments for a lesson
 * @param schoolId - The school ID
 * @param lessonId - The lesson ID
 * @returns Promise with array of equipment assignments
 */
export const getEquipmentAssignmentsForLesson = async (
  schoolId: string,
  lessonId: string
): Promise<EquipmentAssignment[]> => {
  try {
    const assignmentsRef = collection(
      db,
      'schools',
      schoolId,
      'lessons',
      lessonId,
      'equipmentAssignments'
    );
    const snapshot = await getDocs(assignmentsRef);

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      assignedAt: doc.data().assignedAt?.toDate() || new Date(),
    })) as EquipmentAssignment[];
  } catch (error) {
    console.error('Error getting equipment assignments:', error);
    throw error;
  }
};

/**
 * Assign equipment to a lesson
 * @param schoolId - The school ID
 * @param lessonId - The lesson ID
 * @param assignment - The equipment assignment data
 * @returns Promise with the new assignment ID
 */
export const assignEquipmentToLesson = async (
  schoolId: string,
  lessonId: string,
  assignment: Omit<EquipmentAssignment, 'id'>
): Promise<string> => {
  try {
    // Create the assignment
    const assignmentsRef = collection(
      db,
      'schools',
      schoolId,
      'lessons',
      lessonId,
      'equipmentAssignments'
    );
    const docRef = await addDoc(assignmentsRef, {
      ...assignment,
      assignedAt: Timestamp.fromDate(assignment.assignedAt || new Date()),
    });

    // Update equipment availability
    for (const item of assignment.equipmentItems) {
      await updateEquipmentAvailability(schoolId, item.equipmentId, false);
    }

    return docRef.id;
  } catch (error) {
    console.error('Error assigning equipment:', error);
    throw error;
  }
};

/**
 * Remove an equipment assignment
 * @param schoolId - The school ID
 * @param lessonId - The lesson ID
 * @param assignmentId - The assignment ID
 * @returns Promise that resolves when the removal is complete
 */
export const removeEquipmentAssignment = async (
  schoolId: string,
  lessonId: string,
  assignmentId: string
): Promise<void> => {
  try {
    // Get the assignment first to know which equipment to update
    const assignmentRef = doc(
      db,
      'schools',
      schoolId,
      'lessons',
      lessonId,
      'equipmentAssignments',
      assignmentId
    );
    const assignmentDoc = await getDoc(assignmentRef);

    if (!assignmentDoc.exists()) {
      throw new Error('Assignment not found');
    }

    const assignment = assignmentDoc.data() as EquipmentAssignment;

    // Delete the assignment
    await deleteDoc(assignmentRef);

    // Update equipment availability
    for (const item of assignment.equipmentItems) {
      await updateEquipmentAvailability(schoolId, item.equipmentId, true);
    }
  } catch (error) {
    console.error('Error removing equipment assignment:', error);
    throw error;
  }
};
