import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  query,
  where,
  writeBatch as firestoreWriteBatch,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Role, RoleType, DEFAULT_ROLES, Permission } from '../types/role';
/**
 * Initialize default roles in Firestore
 * @returns {Promise<void>}
 */
export const initializeRoles = async (): Promise<void> => {
  try {
    const writeBatch = firestoreWriteBatch(db);
    for (const [id, role] of Object.entries(DEFAULT_ROLES)) {
      const roleRef = doc(db, 'roles', id);
      const roleDoc = await getDoc(roleRef);
      if (!roleDoc.exists()) {
        console.log(`Creating role: ${id}`);
        writeBatch.set(roleRef, {
          ...role,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    }
    await writeBatch.commit();
  } catch (error) {
    console.error('Error initializing roles:', error);
    throw error;
  }
};
/**
 * Get a role by ID
 * @param {RoleType} roleId - Role identifier
 * @returns {Promise<Role>} Role object
 */
export const getRole = async (roleId: RoleType): Promise<Role> => {
  try {
    const roleRef = doc(db, 'roles', roleId);
    const roleDoc = await getDoc(roleRef);
    if (!roleDoc.exists()) {
      throw new Error(`Role ${roleId} not found`);
    }
    return roleDoc.data() as Role;
  } catch (error) {
    console.error('Error getting role:', error);
    throw error;
  }
};
/**
 * Get all roles
 * @returns {Promise<Role[]>} Array of roles
 */
export const getAllRoles = async (): Promise<Role[]> => {
  try {
    const rolesRef = collection(db, 'roles');
    const rolesSnapshot = await getDocs(rolesRef);
    return rolesSnapshot.docs.map((doc) => doc.data() as Role);
  } catch (error) {
    console.error('Error getting roles:', error);
    throw error;
  }
};
/**
 * Update a role's permissions
 * @param {RoleType} roleId - Role identifier
 * @param {string[]} permissions - Array of permission IDs
 * @returns {Promise<void>}
 */
export const updateRolePermissions = async (
  roleId: RoleType,
  permissions: string[]
): Promise<void> => {
  try {
    const roleRef = doc(db, 'roles', roleId);
    await updateDoc(roleRef, {
      permissions,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating role permissions:', error);
    throw error;
  }
};
/**
 * Get a user's role from Firestore
 * @param userId The ID of the user to get the role for
 * @returns The user's role
 */
export const getUserRole = async (userId: string): Promise<Role> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists()) {
      // If user doesn't exist, return default CLIENT role
      return DEFAULT_ROLES[RoleType.CLIENT];
    }

    const userData = userDoc.data();
    const roleId = (userData.roleId as RoleType) || RoleType.CLIENT;

    // Get the role details
    const roleDoc = await getDoc(doc(db, 'roles', roleId));

    if (!roleDoc.exists()) {
      console.warn(`Role ${roleId} not found, falling back to CLIENT role`);
      return DEFAULT_ROLES[RoleType.CLIENT];
    }

    return roleDoc.data() as Role;
  } catch (error) {
    console.error('Error getting user role:', error);
    // In case of error, return CLIENT role as fallback
    return DEFAULT_ROLES[RoleType.CLIENT];
  }
};

/**
 * Get all users with a specific role
 * @param roleId The role ID to filter by
 * @returns Array of user IDs with the specified role
 */
export const getUsersByRole = async (roleId: RoleType): Promise<string[]> => {
  const usersRef = collection(db, 'users');
  const q = query(usersRef, where('roleId', '==', roleId));
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map((doc) => doc.id);
};
/**
 * Set a user's role
 * @param userId The ID of the user to set the role for
 * @param roleId The ID of the role to set
 */
export const setUserRole = async (userId: string, roleId: RoleType): Promise<void> => {
  const userRef = doc(db, 'users', userId);
  await setDoc(userRef, { roleId }, { merge: true });
};
/**
 * Check if a user has a specific permission
 * @param userId The ID of the user to check
 * @param permission The permission to check for
 * @returns Whether the user has the permission
 */
export const hasPermission = async (userId: string, permission: Permission): Promise<boolean> => {
  try {
    const userRole = await getUserRole(userId);
    return userRole.permissions.includes(permission);
  } catch (error) {
    console.error('Error checking permission:', error);
    return false; // Default to no permission on error
  }
};
