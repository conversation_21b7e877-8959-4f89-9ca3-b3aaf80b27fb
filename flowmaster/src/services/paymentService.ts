import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from './firebase';
import { Payment, PaymentMethod, Program } from '../types/program';

/**
 * Create a new payment
 */
export const createPayment = async (
  schoolId: string,
  programId: string,
  paymentData: Omit<Payment, 'id' | 'programId' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    const paymentsRef = collection(db, 'schools', schoolId, 'programs', programId, 'payments');

    const payment = {
      ...paymentData,
      programId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(paymentsRef, payment);

    // Update the program's payment status and paid amounts
    await updateProgramPaymentStatus(schoolId, programId, paymentData.studentId);

    return docRef.id;
  } catch (error) {
    console.error('Error creating payment:', error);
    throw error;
  }
};

/**
 * Update an existing payment
 */
export const updatePayment = async (
  schoolId: string,
  programId: string,
  paymentId: string,
  paymentData: Partial<Payment>
): Promise<void> => {
  try {
    const paymentRef = doc(db, 'schools', schoolId, 'programs', programId, 'payments', paymentId);

    const updatedData = {
      ...paymentData,
      updatedAt: serverTimestamp(),
    };

    await updateDoc(paymentRef, updatedData);

    // Update the program's payment status if the amount changed
    if (paymentData.amount !== undefined) {
      const paymentDoc = await getDoc(paymentRef);
      if (paymentDoc.exists()) {
        const payment = paymentDoc.data() as Payment;
        await updateProgramPaymentStatus(schoolId, programId, payment.studentId);
      }
    }
  } catch (error) {
    console.error('Error updating payment:', error);
    throw error;
  }
};

/**
 * Delete a payment
 */
export const deletePayment = async (
  schoolId: string,
  programId: string,
  paymentId: string
): Promise<void> => {
  try {
    // Get the payment to know which student's status to update
    const paymentRef = doc(db, 'schools', schoolId, 'programs', programId, 'payments', paymentId);
    const paymentDoc = await getDoc(paymentRef);

    if (!paymentDoc.exists()) {
      throw new Error('Payment not found');
    }

    const payment = paymentDoc.data() as Payment;

    // Delete the payment
    await deleteDoc(paymentRef);

    // Update the program's payment status
    await updateProgramPaymentStatus(schoolId, programId, payment.studentId);
  } catch (error) {
    console.error('Error deleting payment:', error);
    throw error;
  }
};

/**
 * Get all payments for a program
 */
export const getProgramPayments = async (
  schoolId: string,
  programId: string
): Promise<Payment[]> => {
  try {
    const paymentsRef = collection(db, 'schools', schoolId, 'programs', programId, 'payments');
    const q = query(paymentsRef, orderBy('date', 'desc'));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Payment[];
  } catch (error) {
    console.error('Error getting program payments:', error);

    // If there's a permission error, return mock data for development
    if (error instanceof Error && error.toString().includes('permission')) {
      console.log('Using mock payments data due to permission error');
      return getMockPayments(programId);
    }

    throw error;
  }
};

/**
 * Get all payments for a student in a program
 */
export const getStudentProgramPayments = async (
  schoolId: string,
  programId: string,
  studentId: string
): Promise<Payment[]> => {
  try {
    const paymentsRef = collection(db, 'schools', schoolId, 'programs', programId, 'payments');
    const q = query(paymentsRef, where('studentId', '==', studentId), orderBy('date', 'desc'));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Payment[];
  } catch (error) {
    console.error('Error getting student program payments:', error);
    throw error;
  }
};

/**
 * Get all payments for a student across all programs
 */
export const getStudentPayments = async (
  schoolId: string,
  studentId: string
): Promise<Payment[]> => {
  try {
    // Get all programs
    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const programsSnapshot = await getDocs(programsRef);
    const programs = programsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Program[];

    // Get payments for each program
    const paymentsPromises = programs.map((program) =>
      getStudentProgramPayments(schoolId, program.id, studentId)
    );

    const paymentsArrays = await Promise.all(paymentsPromises);

    // Flatten the arrays
    return paymentsArrays.flat();
  } catch (error) {
    console.error('Error getting student payments:', error);
    throw error;
  }
};

/**
 * Update a program's payment status for a student based on their payments
 */
export const updateProgramPaymentStatus = async (
  schoolId: string,
  programId: string,
  studentId: string
): Promise<void> => {
  try {
    // Get the program
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);
    const programDoc = await getDoc(programRef);

    if (!programDoc.exists()) {
      throw new Error('Program not found');
    }

    const program = programDoc.data() as Program;

    // Get all payments for this student in this program
    const payments = await getStudentProgramPayments(schoolId, programId, studentId);

    // Calculate total paid amount
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

    // Determine payment status
    let paymentStatus: 'paid' | 'partial' | 'pending' = 'pending';

    if (totalPaid >= program.pricing.totalFee) {
      paymentStatus = 'paid';
    } else if (totalPaid > 0) {
      paymentStatus = 'partial';
    }

    // Update the program's payment status and paid amounts
    const paymentStatusUpdate = {
      [`pricing.paymentStatus.${studentId}`]: paymentStatus,
      [`pricing.paidAmounts.${studentId}`]: totalPaid,
      updatedAt: serverTimestamp(),
    };

    await updateDoc(programRef, paymentStatusUpdate);
  } catch (error) {
    console.error('Error updating program payment status:', error);
    throw error;
  }
};

/**
 * Calculate the remaining balance for a student in a program
 */
export const calculateRemainingBalance = async (
  schoolId: string,
  programId: string,
  studentId: string
): Promise<number> => {
  try {
    // Get the program
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);
    const programDoc = await getDoc(programRef);

    if (!programDoc.exists()) {
      throw new Error('Program not found');
    }

    const program = programDoc.data() as Program;

    // Get all payments for this student in this program
    const payments = await getStudentProgramPayments(schoolId, programId, studentId);

    // Calculate total paid amount
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

    // Calculate discount if any
    const discount = program.pricing.discounts?.[studentId] || 0;

    // Calculate total fee after discount
    const totalFeeAfterDiscount = program.pricing.totalFee - discount;

    // Calculate remaining balance
    return Math.max(0, totalFeeAfterDiscount - totalPaid);
  } catch (error) {
    console.error('Error calculating remaining balance:', error);
    throw error;
  }
};

// Mock payments data for development
const getMockPayments = (programId: string): Payment[] => {
  const now = new Date();
  const payments: Payment[] = [];

  // Create mock payments
  const mockData = [
    {
      studentId: 'student1',
      amount: 200,
      method: 'card' as PaymentMethod,
      date: new Date(now.getFullYear(), now.getMonth() - 2, 15),
    },
    {
      studentId: 'student1',
      amount: 300,
      method: 'transfer' as PaymentMethod,
      date: new Date(now.getFullYear(), now.getMonth() - 1, 10),
    },
    {
      studentId: 'student2',
      amount: 250,
      method: 'cash' as PaymentMethod,
      date: new Date(now.getFullYear(), now.getMonth() - 1, 5),
    },
    {
      studentId: 'student3',
      amount: 100,
      method: 'check' as PaymentMethod,
      date: new Date(now.getFullYear(), now.getMonth(), 3),
    },
  ];

  mockData.forEach((data, index) => {
    payments.push({
      id: `mock-payment-${index}`,
      programId,
      studentId: data.studentId,
      amount: data.amount,
      currency: 'EUR',
      method: data.method,
      date: Timestamp.fromDate(data.date),
      notes: `Payment ${index + 1}`,
      receiptNumber: `R-${1000 + index}`,
      createdAt: Timestamp.fromDate(new Date(now.getFullYear(), now.getMonth() - 2, 1)),
      updatedAt: Timestamp.fromDate(new Date()),
    });
  });

  return payments;
};
