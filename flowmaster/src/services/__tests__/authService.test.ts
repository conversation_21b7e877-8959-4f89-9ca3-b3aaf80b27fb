import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signOut,
  sendEmailVerification,
  User,
  UserCredential,
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';
import {
  signIn,
  signUp,
  signOut as authSignOut,
  resetPassword,
  verifyEmail,
  getUserProfile,
} from '../authService';
import { RoleType } from '../../types/role';

// Mock Firebase modules
jest.mock('firebase/auth');
jest.mock('firebase/firestore', () => ({
  doc: jest.fn(),
  getDoc: jest.fn(),
  setDoc: jest.fn(),
  Timestamp: {
    now: () => ({ seconds: 1234567890, nanoseconds: 0 }),
  },
}));
jest.mock('../firebase', () => ({
  auth: {},
  db: {},
}));

describe('Auth Service', () => {
  const mockUser = {
    uid: 'test-uid',
    email: '<EMAIL>',
    emailVerified: false,
  };

  const mockUserCredential = {
    user: mockUser,
  } as UserCredential;

  const mockUserProfile = {
    uid: mockUser.uid,
    email: mockUser.email,
    displayName: 'Test User',
    emailVerified: false,
    role: RoleType.CLIENT,
    createdAt: '2023-01-01T00:00:00.000Z',
    updatedAt: '2023-01-01T00:00:00.000Z',
    settings: {
      language: 'sl',
      theme: 'light',
      notifications: true,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('signIn', () => {
    it('should sign in user successfully', async () => {
      (signInWithEmailAndPassword as jest.Mock).mockResolvedValueOnce(mockUserCredential);

      const result = await signIn('<EMAIL>', 'password');
      expect(signInWithEmailAndPassword).toHaveBeenCalledWith(auth, '<EMAIL>', 'password');
      expect(result).toEqual(mockUserCredential);
    });

    it('should handle sign in error', async () => {
      const error = { code: 'auth/wrong-password', message: 'Invalid password' };
      (signInWithEmailAndPassword as jest.Mock).mockRejectedValueOnce(error);

      await expect(signIn('<EMAIL>', 'wrong-password')).rejects.toThrow(
        'Invalid password'
      );
    });
  });

  describe('signUp', () => {
    it('should create new user and profile successfully', async () => {
      (createUserWithEmailAndPassword as jest.Mock).mockResolvedValueOnce(mockUserCredential);
      (setDoc as jest.Mock).mockResolvedValueOnce(undefined);
      (sendEmailVerification as jest.Mock).mockResolvedValueOnce(undefined);

      const result = await signUp('<EMAIL>', 'password', 'Test User');
      expect(createUserWithEmailAndPassword).toHaveBeenCalledWith(
        auth,
        '<EMAIL>',
        'password'
      );
      expect(setDoc).toHaveBeenCalled();
      expect(sendEmailVerification).toHaveBeenCalledWith(mockUser);
      expect(result).toEqual(mockUserCredential);
    });

    it('should handle sign up error', async () => {
      const error = { code: 'auth/email-already-in-use', message: 'Email already in use' };
      (createUserWithEmailAndPassword as jest.Mock).mockRejectedValueOnce(error);

      await expect(signUp('<EMAIL>', 'password', 'Test User')).rejects.toThrow(
        'Email is already registered'
      );
    });
  });

  describe('signOut', () => {
    it('should sign out user successfully', async () => {
      (signOut as jest.Mock).mockResolvedValueOnce(undefined);

      await authSignOut();
      expect(signOut).toHaveBeenCalledWith(auth);
    });

    it('should handle sign out error', async () => {
      const error = { code: 'auth/no-current-user', message: 'No user signed in' };
      (signOut as jest.Mock).mockRejectedValueOnce(error);

      await expect(authSignOut()).rejects.toThrow('No user signed in');
    });
  });

  describe('resetPassword', () => {
    it('should send reset password email successfully', async () => {
      (sendPasswordResetEmail as jest.Mock).mockResolvedValueOnce(undefined);

      await resetPassword('<EMAIL>');
      expect(sendPasswordResetEmail).toHaveBeenCalledWith(auth, '<EMAIL>');
    });

    it('should handle reset password error', async () => {
      const error = { code: 'auth/user-not-found', message: 'User not found' };
      (sendPasswordResetEmail as jest.Mock).mockRejectedValueOnce(error);

      await expect(resetPassword('<EMAIL>')).rejects.toThrow('User not found');
    });
  });

  describe('verifyEmail', () => {
    it('should send verification email successfully', async () => {
      (sendEmailVerification as jest.Mock).mockResolvedValueOnce(undefined);

      await verifyEmail(mockUser as User);
      expect(sendEmailVerification).toHaveBeenCalledWith(mockUser);
    });

    it('should handle verification email error', async () => {
      const error = { code: 'auth/too-many-requests', message: 'Too many requests' };
      (sendEmailVerification as jest.Mock).mockRejectedValueOnce(error);

      try {
        await verifyEmail(mockUser as User);
        fail('Expected verifyEmail to throw an error');
      } catch (e) {
        if (e instanceof Error) {
          expect(e.message).toBe('Too many attempts. Please try again later');
        } else {
          fail('Expected error to be instance of Error');
        }
      }
    });
  });

  describe('getUserProfile', () => {
    it('should get user profile successfully', async () => {
      const mockDocSnap = {
        exists: () => true,
        data: () => mockUserProfile,
      };
      (getDoc as jest.Mock).mockResolvedValueOnce(mockDocSnap);

      const result = await getUserProfile('test-uid');
      expect(doc).toHaveBeenCalledWith(db, 'users', 'test-uid');
      expect(getDoc).toHaveBeenCalled();
      expect(result).toEqual(mockUserProfile);
    });

    it('should handle profile not found error', async () => {
      const mockDocSnap = {
        exists: () => false,
      };
      (getDoc as jest.Mock).mockResolvedValueOnce(mockDocSnap);

      await expect(getUserProfile('test-uid')).rejects.toThrow('User profile not found');
    });
  });
});
