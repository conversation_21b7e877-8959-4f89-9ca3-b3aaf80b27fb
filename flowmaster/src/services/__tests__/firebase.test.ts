import { connectAuthEmulator } from 'firebase/auth';
import { connectFirestoreEmulator } from 'firebase/firestore';
import { connectStorageEmulator } from 'firebase/storage';

// Mock Firebase modules
jest.mock('firebase/app', () => {
  const mockApp = { name: 'test-app' };
  return {
    initializeApp: jest.fn().mockReturnValue(mockApp),
    getApps: jest.fn().mockReturnValue([]),
    __esModule: true,
  };
});

jest.mock('firebase/auth', () => {
  const mockAuth = { currentUser: null };
  return {
    getAuth: jest.fn().mockReturnValue(mockAuth),
    connectAuthEmulator: jest.fn(),
    __esModule: true,
  };
});

jest.mock('firebase/firestore', () => {
  const mockDb = { collection: jest.fn() };
  return {
    getFirestore: jest.fn().mockReturnValue(mockDb),
    connectFirestoreEmulator: jest.fn(),
    __esModule: true,
  };
});

jest.mock('firebase/storage', () => {
  const mockStorage = { ref: jest.fn() };
  return {
    getStorage: jest.fn().mockReturnValue(mockStorage),
    connectStorageEmulator: jest.fn(),
    __esModule: true,
  };
});

jest.mock('firebase/functions', () => ({
  getFunctions: jest.fn().mockReturnValue({ httpsCallable: jest.fn() }),
  __esModule: true,
}));

jest.mock('firebase/analytics', () => ({
  getAnalytics: jest.fn().mockReturnValue({ logEvent: jest.fn() }),
  __esModule: true,
}));

jest.mock('firebase/performance', () => ({
  getPerformance: jest.fn().mockReturnValue({ trace: jest.fn() }),
  __esModule: true,
}));

describe('Firebase Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    process.env = {
      ...originalEnv,
      NODE_ENV: 'development',
      REACT_APP_ENV: 'test',
      REACT_APP_FIREBASE_API_KEY: 'test-api-key',
      REACT_APP_FIREBASE_AUTH_DOMAIN: 'test-auth-domain',
      REACT_APP_FIREBASE_PROJECT_ID: 'test-project-id',
      REACT_APP_FIREBASE_STORAGE_BUCKET: 'test-storage-bucket',
      REACT_APP_FIREBASE_MESSAGING_SENDER_ID: 'test-sender-id',
      REACT_APP_FIREBASE_APP_ID: 'test-app-id',
      REACT_APP_FIREBASE_MEASUREMENT_ID: 'test-measurement-id',
    };
  });

  afterEach(() => {
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  it('should initialize Firebase app with correct config', async () => {
    const { initializeApp } = await import('firebase/app');
    await import('../firebase');

    expect(initializeApp).toHaveBeenCalledWith({
      apiKey: 'test-api-key',
      authDomain: 'test-auth-domain',
      projectId: 'test-project-id',
      storageBucket: 'test-storage-bucket',
      messagingSenderId: 'test-sender-id',
      appId: 'test-app-id',
      measurementId: 'test-measurement-id',
    });
    expect(initializeApp).toHaveBeenCalledTimes(1);
  });

  it('should initialize Firebase services', async () => {
    const { getAuth } = await import('firebase/auth');
    const { getFirestore } = await import('firebase/firestore');
    const { getStorage } = await import('firebase/storage');
    const { getFunctions } = await import('firebase/functions');
    await import('../firebase');
    expect(getAuth).toHaveBeenCalled();
    expect(getFirestore).toHaveBeenCalled();
    expect(getStorage).toHaveBeenCalled();
    expect(getFunctions).toHaveBeenCalled();
    const { auth, db, storage, functions } = await import('../firebase');
    expect(auth).toBeDefined();
    expect(db).toBeDefined();
    expect(storage).toBeDefined();
    expect(functions).toBeDefined();
  });

  it('should initialize analytics and performance in production', async () => {
    process.env.NODE_ENV = 'production';
    const { getAnalytics } = await import('firebase/analytics');
    const { getPerformance } = await import('firebase/performance');
    await import('../firebase');
    expect(getAnalytics).toHaveBeenCalled();
    expect(getPerformance).toHaveBeenCalled();
  });

  it('should connect to emulators in development environment', async () => {
    process.env.REACT_APP_ENV = 'development';
    const { connectAuthEmulator } = await import('firebase/auth');
    const { connectFirestoreEmulator } = await import('firebase/firestore');
    const { connectStorageEmulator } = await import('firebase/storage');
    await import('../firebase');
    expect(connectAuthEmulator).toHaveBeenCalledWith(expect.anything(), 'http://localhost:9099', {
      disableWarnings: true,
    });
    expect(connectFirestoreEmulator).toHaveBeenCalledWith(expect.anything(), 'localhost', 8082);
    expect(connectStorageEmulator).toHaveBeenCalledWith(expect.anything(), 'localhost', 9199);
  });

  it('should not connect to emulators in production environment', async () => {
    process.env.REACT_APP_ENV = 'production';
    await import('../firebase');
    expect(connectAuthEmulator).not.toHaveBeenCalled();
    expect(connectFirestoreEmulator).not.toHaveBeenCalled();
    expect(connectStorageEmulator).not.toHaveBeenCalled();
  });
});
