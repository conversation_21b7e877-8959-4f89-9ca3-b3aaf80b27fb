import {
  collection,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  addDoc,
  query,
  getDocs,
  Timestamp,
} from 'firebase/firestore';
import { RoleType } from '../../types/role';
import { DatabaseService } from '../database';
import { db } from '../firebase';

// Mock Firebase
jest.mock('../firebase', () => ({
  db: {},
}));

jest.mock('firebase/firestore', () => {
  const mockTimestampInstance = {
    seconds: 1234567890,
    nanoseconds: 0,
    toDate: jest.fn().mockReturnValue(new Date(1234567890 * 1000)),
  };

  const TimestampMock = jest.fn().mockImplementation((seconds, nanoseconds) => ({
    seconds,
    nanoseconds,
    toDate: jest.fn().mockReturnValue(new Date(seconds * 1000)),
  })) as jest.Mock & { now: jest.Mock; fromDate: jest.Mock };

  TimestampMock.now = jest.fn().mockReturnValue(mockTimestampInstance);
  TimestampMock.fromDate = jest.fn().mockImplementation((date) => ({
    seconds: Math.floor(date.getTime() / 1000),
    nanoseconds: 0,
    toDate: jest.fn().mockReturnValue(date),
  }));

  const mockDocRef = {
    id: 'test-id',
    path: 'test/path',
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
  };

  return {
    collection: jest.fn().mockReturnValue(mockDocRef),
    doc: jest.fn().mockReturnValue(mockDocRef),
    getDoc: jest.fn(),
    setDoc: jest.fn(),
    updateDoc: jest.fn(),
    addDoc: jest.fn(),
    query: jest.fn().mockReturnValue(mockQueryBuilder),
    getDocs: jest.fn(),
    Timestamp: TimestampMock,
    where: jest.fn().mockReturnValue(mockQueryBuilder),
    orderBy: jest.fn().mockReturnValue(mockQueryBuilder),
    limit: jest.fn().mockReturnValue(mockQueryBuilder),
  };
});

describe('DatabaseService', () => {
  const mockTimestamp = new Timestamp(1234567890, 0);
  const mockDoc = { id: 'test-id', data: () => ({}) };
  const mockQuerySnapshot = { docs: [mockDoc] };

  beforeEach(() => {
    jest.clearAllMocks();
    (Timestamp.now as jest.Mock).mockReturnValue(mockTimestamp);
    (getDoc as jest.Mock).mockResolvedValue({ exists: () => true, data: () => ({}) });
    (getDocs as jest.Mock).mockResolvedValue(mockQuerySnapshot);
    (addDoc as jest.Mock).mockResolvedValue({ id: 'test-id' });
  });

  describe('Users', () => {
    const mockUser = {
      displayName: 'Test User',
      email: '<EMAIL>',
      role: 'student' as RoleType,
      emailVerified: false,
      schoolId: 'school-1',
      profile: {
        skills: [],
        certifications: [],
      },
      settings: {
        language: 'en',
        notifications: true,
        theme: 'light' as const,
      },
    };

    it('should create a user', async () => {
      await DatabaseService.createUser('test-uid', mockUser);

      expect(doc).toHaveBeenCalledWith(db, 'users', 'test-uid');
      expect(setDoc).toHaveBeenCalledWith(expect.any(Object), {
        ...mockUser,
        createdAt: mockTimestamp,
        updatedAt: mockTimestamp,
      });
    });

    it('should update a user', async () => {
      await DatabaseService.updateUser('test-uid', mockUser);

      expect(doc).toHaveBeenCalledWith(db, 'users', 'test-uid');
      expect(updateDoc).toHaveBeenCalledWith(expect.any(Object), {
        ...mockUser,
        updatedAt: mockTimestamp,
      });
    });

    it('should get a user', async () => {
      await DatabaseService.getUser('test-uid');

      expect(doc).toHaveBeenCalledWith(db, 'users', 'test-uid');
      expect(getDoc).toHaveBeenCalled();
    });
  });

  describe('Lessons', () => {
    const mockLesson = {
      type: 'individual' as const,
      date: mockTimestamp,
      duration: 60,
      studentIds: ['student-1'],
      instructorId: 'instructor-1',
      equipment: [],
      status: 'scheduled' as const,
    };

    it('should create a lesson', async () => {
      const result = await DatabaseService.createLesson(mockLesson);

      expect(collection).toHaveBeenCalledWith(db, 'lessons');
      expect(addDoc).toHaveBeenCalledWith(expect.any(Object), mockLesson);
      expect(result).toBe('test-id');
    });

    it('should get upcoming lessons', async () => {
      await DatabaseService.getUpcomingLessons('student-1', 5);

      expect(query).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Function),
        expect.any(Function),
        expect.any(Function),
        expect.any(Function),
        expect.any(Function)
      );
      expect(getDocs).toHaveBeenCalled();
    });
  });

  describe('Programs', () => {
    const mockProgram = {
      type: 'school' as const,
      name: 'Test Program',
      startDate: mockTimestamp,
      endDate: mockTimestamp,
      instructorIds: ['instructor-1'],
      studentIds: [],
      goals: [],
      status: 'active' as const,
    };

    it('should create a program', async () => {
      const result = await DatabaseService.createProgram(mockProgram);

      expect(collection).toHaveBeenCalledWith(db, 'programs');
      expect(addDoc).toHaveBeenCalledWith(expect.any(Object), mockProgram);
      expect(result).toBe('test-id');
    });
  });

  describe('Schedules', () => {
    const mockSchedule = {
      instructorId: 'instructor-1',
      date: mockTimestamp,
      lessons: [],
      notes: 'Test schedule',
    };

    it('should create a schedule', async () => {
      const result = await DatabaseService.createSchedule(mockSchedule);

      expect(collection).toHaveBeenCalledWith(db, 'schedules');
      expect(addDoc).toHaveBeenCalledWith(expect.any(Object), mockSchedule);
      expect(result).toBe('test-id');
    });

    it('should get instructor schedule', async () => {
      const testDate = new Date();
      await DatabaseService.getInstructorSchedule('instructor-1', testDate);

      expect(query).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Function),
        expect.any(Function),
        expect.any(Function),
        expect.any(Function)
      );
      expect(getDocs).toHaveBeenCalled();
    });
  });

  describe('Schools', () => {
    const mockSchool = {
      name: 'Test School',
      sports: [],
      programs: [],
      settings: {
        language: 'en',
        currency: 'USD',
        timezone: 'UTC',
      },
    };

    it('should create a school', async () => {
      const result = await DatabaseService.createSchool(mockSchool);

      expect(collection).toHaveBeenCalledWith(db, 'schools');
      expect(addDoc).toHaveBeenCalledWith(expect.any(Object), {
        ...mockSchool,
        createdAt: mockTimestamp,
      });
      expect(result).toBe('test-id');
    });
  });

  describe('Attendance', () => {
    const mockAttendance = {
      studentId: 'user-1',
      referenceId: 'lesson-1',
      referenceType: 'lesson' as const,
      date: mockTimestamp,
      status: 'present' as const,
    };

    it('should create attendance', async () => {
      const result = await DatabaseService.createAttendance(mockAttendance);

      expect(collection).toHaveBeenCalledWith(db, 'attendance');
      expect(addDoc).toHaveBeenCalledWith(expect.any(Object), {
        ...mockAttendance,
        createdAt: mockTimestamp,
      });
      expect(result).toBe('test-id');
    });

    it('should get attendance by reference', async () => {
      await DatabaseService.getAttendanceByReference('lesson-1', 'lesson');

      expect(query).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Function),
        expect.any(Function)
      );
      expect(getDocs).toHaveBeenCalled();
    });
  });
});
