import {
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  doc,
  DocumentSnapshot,
  QuerySnapshot,
  DocumentData,
  collection,
  query,
  where,
} from 'firebase/firestore';
import {
  initializeRoles,
  getRole,
  getAllRoles,
  updateRolePermissions,
  getUserRole,
  hasPermission,
  getUsersByRole,
  setUserRole,
} from '../roleService';
import { Role, RoleType, DEFAULT_ROLES } from '../../types/role';

// Mock Firebase
jest.mock('firebase/firestore');
jest.mock('../firebase', () => ({
  db: {},
}));

// Mock document reference
const mockDocRef = { id: RoleType.INSTRUCTOR };
(doc as jest.Mock).mockReturnValue(mockDocRef);

describe('Role Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (doc as jest.Mock).mockReturnValue(mockDocRef);
  });

  describe('initializeRoles', () => {
    it('initializes default roles if they do not exist', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      const mockSetDoc = setDoc as jest.MockedFunction<typeof setDoc>;

      // Mock that roles don't exist
      mockGetDoc.mockResolvedValue({
        exists: () => false,
      } as unknown as DocumentSnapshot<DocumentData>);

      mockSetDoc.mockResolvedValue(undefined);

      await initializeRoles();

      // Should try to set each default role
      expect(mockSetDoc).toHaveBeenCalledTimes(Object.keys(DEFAULT_ROLES).length);
      expect(mockGetDoc).toHaveBeenCalledTimes(Object.keys(DEFAULT_ROLES).length);
    });

    it('skips existing roles', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      const mockSetDoc = setDoc as jest.MockedFunction<typeof setDoc>;

      // Mock that roles exist
      mockGetDoc.mockResolvedValue({
        exists: () => true,
      } as unknown as DocumentSnapshot<DocumentData>);

      mockSetDoc.mockResolvedValue(undefined);

      await initializeRoles();

      // Should check each role but not set any
      expect(mockSetDoc).not.toHaveBeenCalled();
      expect(mockGetDoc).toHaveBeenCalledTimes(Object.keys(DEFAULT_ROLES).length);
    });

    it('handles errors gracefully', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      mockGetDoc.mockRejectedValue(new Error('Firebase error'));

      await expect(initializeRoles()).rejects.toThrow('Firebase error');
    });
  });

  describe('getRole', () => {
    it('returns role when it exists', async () => {
      const mockRole: Role = {
        id: RoleType.ADMIN,
        name: 'Administrator',
        description: 'Full system access',
        permissions: ['manage_users'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      mockGetDoc.mockResolvedValue({
        exists: () => true,
        data: () => mockRole,
      } as unknown as DocumentSnapshot<Role>);

      const role = await getRole(RoleType.ADMIN);
      expect(role).toEqual(mockRole);
    });

    it('throws error when role does not exist', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      mockGetDoc.mockResolvedValue({
        exists: () => false,
      } as unknown as DocumentSnapshot<Role>);

      await expect(getRole(RoleType.ADMIN)).rejects.toThrow('Role admin not found');
    });
  });

  describe('getAllRoles', () => {
    it('returns all roles', async () => {
      const mockRoles = Object.values(DEFAULT_ROLES);
      const mockGetDocs = getDocs as jest.MockedFunction<typeof getDocs>;
      mockGetDocs.mockResolvedValue({
        docs: mockRoles.map((role) => ({
          data: () => role,
        })),
      } as unknown as QuerySnapshot<Role>);

      const roles = await getAllRoles();
      expect(roles).toEqual(mockRoles);
    });

    it('handles empty result', async () => {
      const mockGetDocs = getDocs as jest.MockedFunction<typeof getDocs>;
      mockGetDocs.mockResolvedValue({
        docs: [],
      } as unknown as QuerySnapshot<Role>);

      const roles = await getAllRoles();
      expect(roles).toEqual([]);
    });
  });

  describe('updateRolePermissions', () => {
    it('updates role permissions successfully', async () => {
      const mockUpdateDoc = updateDoc as jest.MockedFunction<typeof updateDoc>;
      const newPermissions = ['view_lessons', 'manage_lessons'];

      await updateRolePermissions(RoleType.INSTRUCTOR, newPermissions);

      expect(mockUpdateDoc).toHaveBeenCalledWith(
        mockDocRef,
        expect.objectContaining({
          permissions: newPermissions,
          updatedAt: expect.any(String),
        })
      );
    });

    it('handles errors gracefully', async () => {
      const mockUpdateDoc = updateDoc as jest.MockedFunction<typeof updateDoc>;
      mockUpdateDoc.mockRejectedValue(new Error('Update failed'));

      await expect(updateRolePermissions(RoleType.ADMIN, ['manage_users'])).rejects.toThrow(
        'Update failed'
      );
    });
  });

  describe('getUserRole', () => {
    it('returns user role when it exists', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      const mockUserData = {
        roleId: RoleType.ADMIN,
      };
      const mockRole = DEFAULT_ROLES[RoleType.ADMIN];

      // Mock user document
      mockGetDoc.mockResolvedValueOnce({
        exists: () => true,
        data: () => mockUserData,
      } as unknown as DocumentSnapshot<{ roleId: RoleType }>);

      // Mock role document
      mockGetDoc.mockResolvedValueOnce({
        exists: () => true,
        data: () => mockRole,
      } as unknown as DocumentSnapshot<Role>);

      const role = await getUserRole('user123');
      expect(role).toEqual(mockRole);
    });

    it('returns default USER role when user does not exist', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      mockGetDoc.mockResolvedValueOnce({
        exists: () => false,
      } as unknown as DocumentSnapshot<{ roleId: RoleType }>);

      const role = await getUserRole('user123');
      expect(role).toEqual(DEFAULT_ROLES[RoleType.CLIENT]);
    });
  });

  describe('hasPermission', () => {
    it('returns true when user has permission', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      const mockRole = DEFAULT_ROLES[RoleType.ADMIN];

      // Mock user document
      mockGetDoc.mockResolvedValueOnce({
        exists: () => true,
        data: () => ({ roleId: RoleType.ADMIN }),
      } as unknown as DocumentSnapshot<{ roleId: RoleType }>);

      // Mock role document
      mockGetDoc.mockResolvedValueOnce({
        exists: () => true,
        data: () => mockRole,
      } as unknown as DocumentSnapshot<Role>);

      const result = await hasPermission('user123', 'manage_users');
      expect(result).toBe(true);
    });

    it('returns false when user does not have permission', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      const mockRole = DEFAULT_ROLES[RoleType.CLIENT];

      // Mock user document
      mockGetDoc.mockResolvedValueOnce({
        exists: () => true,
        data: () => ({ roleId: RoleType.CLIENT }),
      } as unknown as DocumentSnapshot<{ roleId: RoleType }>);

      // Mock role document
      mockGetDoc.mockResolvedValueOnce({
        exists: () => true,
        data: () => mockRole,
      } as unknown as DocumentSnapshot<Role>);

      const result = await hasPermission('user123', 'manage_users');
      expect(result).toBe(false);
    });

    it('returns false on error', async () => {
      const mockGetDoc = getDoc as jest.MockedFunction<typeof getDoc>;
      mockGetDoc.mockRejectedValue(new Error('Firebase error'));

      const result = await hasPermission('user123', 'manage_users');
      expect(result).toBe(false);
    });
  });

  describe('getUsersByRole', () => {
    it('returns array of user IDs with specified role', async () => {
      const mockUsers = ['user1', 'user2', 'user3'];
      const mockCollection = collection as jest.MockedFunction<typeof collection>;
      const mockQuery = query as jest.MockedFunction<typeof query>;
      const mockWhere = where as jest.MockedFunction<typeof where>;
      const mockGetDocs = getDocs as jest.MockedFunction<typeof getDocs>;

      mockCollection.mockReturnValue({} as ReturnType<typeof collection>);
      mockQuery.mockReturnValue({} as ReturnType<typeof query>);
      mockWhere.mockReturnValue({} as ReturnType<typeof where>);
      mockGetDocs.mockResolvedValue({
        docs: mockUsers.map((id) => ({ id })),
      } as unknown as QuerySnapshot<DocumentData>);

      const result = await getUsersByRole(RoleType.INSTRUCTOR);
      expect(result).toEqual(mockUsers);
      expect(mockCollection).toHaveBeenCalledWith({}, 'users');
      expect(mockWhere).toHaveBeenCalledWith('roleId', '==', RoleType.INSTRUCTOR);
    });

    it('handles empty result', async () => {
      const mockGetDocs = getDocs as jest.MockedFunction<typeof getDocs>;
      mockGetDocs.mockResolvedValue({
        docs: [],
      } as unknown as QuerySnapshot<DocumentData>);

      const result = await getUsersByRole(RoleType.INSTRUCTOR);
      expect(result).toEqual([]);
    });
  });

  describe('setUserRole', () => {
    it('sets user role successfully', async () => {
      const mockSetDoc = setDoc as jest.MockedFunction<typeof setDoc>;
      const userId = 'user123';
      const roleId = RoleType.INSTRUCTOR;

      await setUserRole(userId, roleId);

      expect(doc).toHaveBeenCalledWith({}, 'users', userId);
      expect(mockSetDoc).toHaveBeenCalledWith(expect.anything(), { roleId }, { merge: true });
    });

    it('handles errors gracefully', async () => {
      const mockSetDoc = setDoc as jest.MockedFunction<typeof setDoc>;
      mockSetDoc.mockRejectedValue(new Error('Failed to set role'));

      await expect(setUserRole('user123', RoleType.INSTRUCTOR)).rejects.toThrow(
        'Failed to set role'
      );
    });
  });
});
