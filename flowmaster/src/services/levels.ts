import { doc, getDoc } from 'firebase/firestore';
import { db } from './firebase';

export interface Level {
  id: string;
  name: string;
  displayName: string;
}

interface FirestoreLevel {
  id: string;
  name: string;
  isCustom: boolean;
}

export const fetchLevels = async (schoolId: string): Promise<Level[]> => {
  try {
    console.log('Fetching levels for school ID:', schoolId);

    const schoolDocRef = doc(db, 'schools', schoolId);
    const schoolDoc = await getDoc(schoolDocRef);

    if (!schoolDoc.exists()) {
      console.log('School document not found');
      return [];
    }

    const schoolData = schoolDoc.data();
    if (!schoolData?.levels) {
      console.log('No levels found in school data');
      return [];
    }

    console.log('Levels array from Firestore:', schoolData.levels);

    const mappedLevels = schoolData.levels.map((level: FirestoreLevel) => ({
      id: level.id,
      name: level.name,
      displayName: level.name,
    }));

    console.log('Final mapped levels:', mappedLevels);
    return mappedLevels;
  } catch (error) {
    console.error('Error fetching levels:', error);
    throw error;
  }
};
