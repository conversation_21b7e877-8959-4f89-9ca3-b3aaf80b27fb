import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from './firebase';
import { Program, ProgramSession, AttendanceStatus } from '../types/program';
import { Student } from '../types/student';

/**
 * Create a new makeup session
 */
export const createMakeupSession = async (
  schoolId: string,
  programId: string,
  originalSessionId: string,
  studentIds: string[],
  date: Date,
  reason: string
): Promise<string> => {
  try {
    // Get the original session
    const originalSessionRef = doc(
      db,
      'schools',
      schoolId,
      'programs',
      programId,
      'sessions',
      originalSessionId
    );
    const originalSessionDoc = await getDoc(originalSessionRef);

    if (!originalSessionDoc.exists()) {
      throw new Error('Original session not found');
    }

    const originalSession = originalSessionDoc.data() as ProgramSession;

    // Get the program
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);
    const programDoc = await getDoc(programRef);

    if (!programDoc.exists()) {
      throw new Error('Program not found');
    }

    const program = programDoc.data() as Program;

    // Create the makeup session
    const makeupSession: Omit<ProgramSession, 'id'> = {
      programId,
      date: Timestamp.fromDate(date),
      attendance: {},
      notes: `Makeup session for ${new Date(originalSession.date.toDate()).toLocaleDateString()}`,
      skills: {},
      isMakeup: true,
      makeupDetails: {
        originalSessionId,
        forStudents: studentIds,
        reason,
      },
      createdAt: Timestamp.fromDate(new Date()),
      updatedAt: Timestamp.fromDate(new Date()),
    };

    // Add the makeup session to Firestore
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const makeupSessionRef = await addDoc(sessionsRef, makeupSession);

    // Update the original session to mark these students as having a makeup scheduled
    const missedStudents = originalSession.missedStudents || [];
    const updatedMissedStudents = missedStudents.filter((id) => !studentIds.includes(id));

    await updateDoc(originalSessionRef, {
      missedStudents: updatedMissedStudents,
      updatedAt: serverTimestamp(),
    });

    return makeupSessionRef.id;
  } catch (error) {
    console.error('Error creating makeup session:', error);
    throw error;
  }
};

/**
 * Get all makeup sessions for a program
 */
export const getMakeupSessions = async (
  schoolId: string,
  programId: string
): Promise<ProgramSession[]> => {
  try {
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const q = query(sessionsRef, where('isMakeup', '==', true), orderBy('date', 'asc'));

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as ProgramSession[];
  } catch (error) {
    console.error('Error getting makeup sessions:', error);
    throw error;
  }
};

/**
 * Get all makeup sessions for a specific student in a program
 */
export const getStudentMakeupSessions = async (
  schoolId: string,
  programId: string,
  studentId: string
): Promise<ProgramSession[]> => {
  try {
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const q = query(
      sessionsRef,
      where('isMakeup', '==', true),
      where('makeupDetails.forStudents', 'array-contains', studentId),
      orderBy('date', 'asc')
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as ProgramSession[];
  } catch (error) {
    console.error('Error getting student makeup sessions:', error);
    throw error;
  }
};

/**
 * Get all sessions that a student has missed and needs makeup for
 */
export const getMissedSessions = async (
  schoolId: string,
  programId: string,
  studentId: string
): Promise<ProgramSession[]> => {
  try {
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const q = query(
      sessionsRef,
      where('missedStudents', 'array-contains', studentId),
      orderBy('date', 'asc')
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as ProgramSession[];
  } catch (error) {
    console.error('Error getting missed sessions:', error);
    throw error;
  }
};

/**
 * Mark students as needing a makeup session
 */
export const markStudentsForMakeup = async (
  schoolId: string,
  programId: string,
  sessionId: string,
  studentIds: string[]
): Promise<void> => {
  try {
    const sessionRef = doc(db, 'schools', schoolId, 'programs', programId, 'sessions', sessionId);
    const sessionDoc = await getDoc(sessionRef);

    if (!sessionDoc.exists()) {
      throw new Error('Session not found');
    }

    const session = sessionDoc.data() as ProgramSession;
    const missedStudents = session.missedStudents || [];

    // Add students to missedStudents if they're not already there
    const updatedMissedStudents = Array.from(new Set([...missedStudents, ...studentIds]));

    await updateDoc(sessionRef, {
      missedStudents: updatedMissedStudents,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error marking students for makeup:', error);
    throw error;
  }
};

/**
 * Update attendance for a makeup session
 */
export const updateMakeupAttendance = async (
  schoolId: string,
  programId: string,
  sessionId: string,
  attendance: Record<string, AttendanceStatus>
): Promise<void> => {
  try {
    const sessionRef = doc(db, 'schools', schoolId, 'programs', programId, 'sessions', sessionId);

    await updateDoc(sessionRef, {
      attendance,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating makeup attendance:', error);
    throw error;
  }
};

/**
 * Get students who need makeup sessions
 */
export const getStudentsNeedingMakeup = async (
  schoolId: string,
  programId: string
): Promise<{ student: Student; missedSessions: ProgramSession[] }[]> => {
  try {
    // Get all sessions with missedStudents
    const sessionsRef = collection(db, 'schools', schoolId, 'programs', programId, 'sessions');
    const q = query(sessionsRef, orderBy('date', 'asc'));
    const querySnapshot = await getDocs(q);

    const sessions = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as ProgramSession[];

    // Get all students in the program
    const programRef = doc(db, 'schools', schoolId, 'programs', programId);
    const programDoc = await getDoc(programRef);

    if (!programDoc.exists()) {
      throw new Error('Program not found');
    }

    const program = programDoc.data() as Program;
    const studentIds = program.participants;

    // Get student details
    const studentsData: Record<string, Student> = {};

    for (const studentId of studentIds) {
      const studentDoc = await getDoc(doc(db, 'schools', schoolId, 'students', studentId));

      if (studentDoc.exists()) {
        studentsData[studentId] = {
          id: studentDoc.id,
          ...studentDoc.data(),
        } as Student;
      }
    }

    // Find students who need makeup sessions
    const studentsNeedingMakeup: { student: Student; missedSessions: ProgramSession[] }[] = [];

    for (const studentId of studentIds) {
      const student = studentsData[studentId];

      if (student) {
        const missedSessions = sessions.filter((session) =>
          session.missedStudents?.includes(studentId)
        );

        if (missedSessions.length > 0) {
          studentsNeedingMakeup.push({
            student,
            missedSessions,
          });
        }
      }
    }

    return studentsNeedingMakeup;
  } catch (error) {
    console.error('Error getting students needing makeup:', error);
    throw error;
  }
};
