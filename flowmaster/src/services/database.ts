import {
  collection,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  Timestamp,
} from 'firebase/firestore';
import { db } from './firebase';
import { User, Lesson, Program, Schedule, School, Attendance } from '../types/database';

export const DatabaseService = {
  // Users
  async createUser(uid: string, userData: Partial<User>): Promise<void> {
    const now = Timestamp.now();
    await setDoc(doc(db, 'users', uid), {
      ...userData,
      createdAt: now,
      updatedAt: now,
    });
  },

  async updateUser(uid: string, userData: Partial<User>): Promise<void> {
    await updateDoc(doc(db, 'users', uid), {
      ...userData,
      updatedAt: Timestamp.now(),
    });
  },

  async getUser(uid: string): Promise<User | null> {
    const userDoc = await getDoc(doc(db, 'users', uid));
    return userDoc.exists() ? (userDoc.data() as User) : null;
  },

  // Lessons
  async createLesson(lessonData: Omit<Lesson, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'lessons'), lessonData);
    return docRef.id;
  },

  async updateLesson(lessonId: string, lessonData: Partial<Lesson>): Promise<void> {
    await updateDoc(doc(db, 'lessons', lessonId), lessonData);
  },

  async getLesson(lessonId: string): Promise<Lesson | null> {
    const lessonDoc = await getDoc(doc(db, 'lessons', lessonId));
    return lessonDoc.exists() ? (lessonDoc.data() as Lesson) : null;
  },

  async getUpcomingLessons(userId: string, limitCount = 10): Promise<Lesson[]> {
    const q = query(
      collection(db, 'lessons'),
      where('studentIds', 'array-contains', userId),
      where('date', '>=', Timestamp.now()),
      where('status', '==', 'scheduled'),
      orderBy('date'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map((doc) => doc.data() as Lesson);
  },

  // Programs
  async createProgram(programData: Omit<Program, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'programs'), programData);
    return docRef.id;
  },

  async updateProgram(programId: string, programData: Partial<Program>): Promise<void> {
    await updateDoc(doc(db, 'programs', programId), programData);
  },

  async getProgram(programId: string): Promise<Program | null> {
    const programDoc = await getDoc(doc(db, 'programs', programId));
    return programDoc.exists() ? (programDoc.data() as Program) : null;
  },

  // Schedules
  async createSchedule(scheduleData: Omit<Schedule, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'schedules'), scheduleData);
    return docRef.id;
  },

  async updateSchedule(scheduleId: string, scheduleData: Partial<Schedule>): Promise<void> {
    await updateDoc(doc(db, 'schedules', scheduleId), scheduleData);
  },

  async getInstructorSchedule(instructorId: string, date: Date): Promise<Schedule[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const q = query(
      collection(db, 'schedules'),
      where('instructorId', '==', instructorId),
      where('date', '>=', Timestamp.fromDate(startOfDay)),
      where('date', '<=', Timestamp.fromDate(endOfDay)),
      orderBy('date')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map((doc) => doc.data() as Schedule);
  },

  // Schools
  async createSchool(schoolData: Omit<School, 'id' | 'createdAt'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'schools'), {
      ...schoolData,
      createdAt: Timestamp.now(),
    });
    return docRef.id;
  },

  async updateSchool(schoolId: string, schoolData: Partial<School>): Promise<void> {
    await updateDoc(doc(db, 'schools', schoolId), schoolData);
  },

  async getSchool(schoolId: string): Promise<School | null> {
    const schoolDoc = await getDoc(doc(db, 'schools', schoolId));
    return schoolDoc.exists() ? (schoolDoc.data() as School) : null;
  },

  // Attendance
  async createAttendance(attendanceData: Omit<Attendance, 'id' | 'createdAt'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'attendance'), {
      ...attendanceData,
      createdAt: Timestamp.now(),
    });
    return docRef.id;
  },

  async updateAttendance(attendanceId: string, attendanceData: Partial<Attendance>): Promise<void> {
    await updateDoc(doc(db, 'attendance', attendanceId), attendanceData);
  },

  async getAttendanceByReference(
    referenceId: string,
    referenceType: 'lesson' | 'program'
  ): Promise<Attendance[]> {
    const q = query(
      collection(db, 'attendance'),
      where('referenceId', '==', referenceId),
      where('referenceType', '==', referenceType)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map((doc) => doc.data() as Attendance);
  },
};
