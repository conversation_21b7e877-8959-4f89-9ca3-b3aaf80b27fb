name: Override Build

on:
  push:
    branches: [ main, feature/*, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'
    
    - name: Install dependencies
      working-directory: ./flowmaster
      run: npm ci
    
    - name: Run standalone build
      working-directory: ./flowmaster
      run: |
        echo "Running standalone build..."
        node ./standalone-build.js
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build
        path: flowmaster/build
