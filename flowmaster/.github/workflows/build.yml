name: Build

on:
  push:
    branches: [ main, feature/*, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: 'flowmaster/package-lock.json'

    - name: Install dependencies
      working-directory: ./flowmaster
      run: npm ci

    - name: Build
      working-directory: ./flowmaster
      run: |
        # Optimize node_modules size
        echo "Removing test files from node_modules..."
        find node_modules -type d -name "test" -o -name "tests" | xargs rm -rf
        echo "Removing documentation from node_modules..."
        find node_modules -type d -name "docs" -o -name "doc" | xargs rm -rf
        echo "Removing example files from node_modules..."
        find node_modules -type d -name "example" -o -name "examples" | xargs rm -rf

        # Set environment variables
        export NODE_OPTIONS="--max-old-space-size=8192 --no-global-gc-scheduling --optimize-for-size"
        export GENERATE_SOURCEMAP=false
        export DISABLE_ESLINT_PLUGIN=true
        export DISABLE_TYPESCRIPT=true
        export CI=false
        export BABEL_ENV=production
        export NODE_ENV=production

        # Use worker build which was successful locally
        echo "Running worker build..."
        npm run build:worker
      env:
        CI: false
