name: Build with Extreme CI Approach

on:
  push:
    branches: [ main, feature/*, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: 'flowmaster/package-lock.json'
    
    - name: Free up disk space
      run: |
        echo "Freeing up disk space..."
        df -h
        sudo rm -rf /usr/share/dotnet
        sudo rm -rf /usr/local/lib/android
        sudo rm -rf /opt/ghc
        sudo rm -rf /opt/hostedtoolcache/CodeQL
        df -h
    
    - name: Install dependencies
      working-directory: ./flowmaster
      run: npm ci
    
    - name: Optimize node_modules
      working-directory: ./flowmaster
      run: |
        # Remove test files from node_modules
        echo "Removing test files from node_modules..."
        find node_modules -type d -name "test" -o -name "tests" | xargs rm -rf
        # Remove documentation from node_modules
        echo "Removing documentation from node_modules..."
        find node_modules -type d -name "docs" -o -name "doc" | xargs rm -rf
        # Remove example files from node_modules
        echo "Removing example files from node_modules..."
        find node_modules -type d -name "example" -o -name "examples" | xargs rm -rf
    
    - name: Build with Extreme CI Approach
      working-directory: ./flowmaster
      run: |
        # Set environment variables
        export NODE_OPTIONS="--max-old-space-size=4096 --no-global-gc-scheduling --optimize-for-size"
        export GENERATE_SOURCEMAP=false
        export DISABLE_ESLINT_PLUGIN=true
        export DISABLE_TYPESCRIPT=true
        export CI=false
        export BABEL_ENV=production
        export NODE_ENV=production
        
        # Run extreme CI build
        echo "Running extreme CI build..."
        npm run build:ci-extreme
      env:
        CI: false
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build
        path: flowmaster/build
