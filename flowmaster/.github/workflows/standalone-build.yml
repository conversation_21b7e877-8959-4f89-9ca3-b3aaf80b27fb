name: Standalone Build

on:
  push:
    branches: [ main, feature/*, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'

    - name: Debug CI Environment
      working-directory: ./flowmaster
      run: |
        # Run the debug script
        node ./scripts/debug-ci.js

    - name: Standalone Build
      working-directory: ./flowmaster
      run: |
        # Run the standalone build script
        node ./standalone-build.js

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build
        path: flowmaster/build
