name: Main Workflow

on:
  push:
    branches: [ main, feature/*, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: 'flowmaster/package-lock.json'
    
    - name: Free up disk space
      run: |
        echo "Freeing up disk space..."
        df -h
        sudo rm -rf /usr/share/dotnet
        sudo rm -rf /usr/local/lib/android
        sudo rm -rf /opt/ghc
        sudo rm -rf /opt/hostedtoolcache/CodeQL
        df -h
    
    - name: Install dependencies
      working-directory: ./flowmaster
      run: npm ci
    
    - name: Debug CI Environment
      working-directory: ./flowmaster
      run: |
        echo "Running debug script..."
        node ./scripts/debug-ci.js
    
    - name: Standalone Build
      working-directory: ./flowmaster
      run: |
        echo "Running standalone build..."
        node ./standalone-build.js
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build
        path: flowmaster/build
