{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@components/*": ["src/components/*"], "@context/*": ["src/context/*"], "@i18n/*": ["src/i18n/*"]}}, "include": ["src/**/*", "scripts/migrateStudentType.js"], "exclude": ["node_modules"]}