const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const { InjectManifest } = require('workbox-webpack-plugin');
const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');

// Declare process for ESLint
/* global process */

module.exports = {
  webpack: {
    alias: {
      '@components': path.resolve(__dirname, 'src/components'),
      '@services': path.resolve(__dirname, 'src/services'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@context': path.resolve(__dirname, 'src/context'),
      '@pages': path.resolve(__dirname, 'src/pages'),
    },
    plugins: [
      ...(process.env.ANALYZE
        ? [
            new BundleAnalyzerPlugin({
              analyzerMode: 'static',
              reportFilename: 'bundle-report.html',
            }),
          ]
        : []),
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/,
        threshold: 10240,
        minRatio: 0.8,
      }),
      // Service worker configuration will be added when service-worker.ts is implemented
      ...(process.env.NODE_ENV === 'production' ? [] : []),
    ],
    configure: (webpackConfig) => {
      // Only apply strict rules in production
      if (process.env.NODE_ENV !== 'production') {
        webpackConfig.module.rules.push({
          test: /\.(js|mjs|jsx|ts|tsx)$/,
          enforce: 'pre',
          loader: require.resolve('eslint-loader'),
          options: {
            eslintPath: require.resolve('eslint'),
            failOnError: false,
            failOnWarning: false,
            emitWarning: false,
            parserOptions: {
              ecmaVersion: 2021,
              sourceType: 'module'
            }
          },
        });
      }

      // Disable source maps in production to save memory
      if (process.env.GENERATE_SOURCEMAP === 'false') {
        webpackConfig.devtool = false;
      }

      // Optimize chunks
      webpackConfig.optimization = {
        ...webpackConfig.optimization,
        minimize: true,
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: process.env.NODE_ENV === 'production',
                drop_debugger: true,
                passes: 2,
              },
              output: {
                comments: false,
              },
              mangle: true,
            },
            extractComments: false,
            parallel: true,
          }),
        ],
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          minRemainingSize: 0,
          minChunks: 1,
          maxAsyncRequests: 30,
          maxInitialRequests: 30,
          enforceSizeThreshold: 50000,
          cacheGroups: {
            defaultVendors: {
              test: /[\\/]node_modules[\\/]/,
              priority: -10,
              reuseExistingChunk: true,
            },
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            firebase: {
              test: /[\\/]node_modules[\\/](@firebase|firebase)/,
              name: 'firebase',
              chunks: 'all',
              priority: 10,
            },
            mui: {
              test: /[\\/]node_modules[\\/]@mui/,
              name: 'mui',
              chunks: 'all',
              priority: 10,
            },
            i18n: {
              test: /[\\/]node_modules[\\/](i18next|react-i18next)/,
              name: 'i18n',
              chunks: 'all',
              priority: 10,
            },
          },
        },
        // Enable persistent caching
        runtimeChunk: 'single',
        moduleIds: 'deterministic',
        chunkIds: 'deterministic',
      };

      // Enable build caching
      webpackConfig.cache = {
        type: 'filesystem',
        version: '1.0',
        buildDependencies: {
          config: [__filename],
        },
      };

      return webpackConfig;
    },
  },
  jest: {
    configure: {
      moduleNameMapper: {
        '^@components/(.*)$': '<rootDir>/src/components/$1',
        '^@services/(.*)$': '<rootDir>/src/services/$1',
        '^@utils/(.*)$': '<rootDir>/src/utils/$1',
        '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
        '^@types/(.*)$': '<rootDir>/src/types/$1',
        '^@context/(.*)$': '<rootDir>/src/context/$1',
        '^@pages/(.*)$': '<rootDir>/src/pages/$1',
      },
    },
  },
};
