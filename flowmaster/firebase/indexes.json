{"indexes": [{"collectionGroup": "lessons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "instructorId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "ASCENDING"}]}, {"collectionGroup": "lessons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "startTime", "order": "ASCENDING"}]}, {"collectionGroup": "lessons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "ASCENDING"}]}, {"collectionGroup": "lessons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "ASCENDING"}]}, {"collectionGroup": "students", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "instructors", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "programs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}]}, {"collectionGroup": "equipment", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}]}, {"collectionGroup": "rentals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "schoolId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "programId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "rentals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}], "fieldOverrides": []}