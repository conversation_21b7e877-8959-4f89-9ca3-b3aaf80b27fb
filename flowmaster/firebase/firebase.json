{"hosting": {"public": "build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "firestore": {"rules": "rules/firestore.rules", "indexes": "indexes.json"}, "storage": {"rules": "rules/storage.rules"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8082}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true, "export-on-exit": "./data", "import-on-startup": "./data"}}