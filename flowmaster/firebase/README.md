# Firebase Configuration

This directory contains all Firebase-related configurations and tests for the FlowMaster application.

## Directory Structure

```
firebase/
├── rules/              # Security rules for Firebase services
│   ├── firestore.rules # Firestore security rules
│   └── storage.rules   # Storage security rules
├── __tests__/          # Tests for Firebase configurations
│   └── rules/          # Security rules tests
├── indexes.json        # Firestore indexes configuration
├── firebase.json       # Firebase project configuration
└── README.md          # This file
```

## Components

### Security Rules
- `rules/firestore.rules`: Defines access control and data validation for Firestore
- `rules/storage.rules`: Defines access control and file validation for Storage

### Indexes
- `indexes.json`: Defines composite indexes for optimizing Firestore queries

### Configuration
- `firebase.json`: Main configuration file for Firebase services and emulators

## Development

### Running Emulators
The emulators are configured in docker-compose.yml and will start automatically with:
```bash
./scripts/init-dev.sh
```

Access the emulators at:
- Authentication: http://localhost:9099
- Firestore: http://localhost:8080
- Storage: http://localhost:9199
- Emulator UI: http://localhost:4000

### Testing
Security rules tests are located in the `__tests__/rules/` directory.

To run tests:
```bash
cd firebase
npm test
```

## Deployment
To deploy Firebase configurations:
```bash
firebase deploy --only firestore:rules,storage:rules
```

## Documentation
For detailed documentation on the security rules and their implementation, see:
- [Firebase Security Documentation](../docs/firebase-security.md)
