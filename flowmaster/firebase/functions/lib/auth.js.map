{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../src/auth.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AAExC;;;;GAIG;AACU,QAAA,mBAAmB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAChF,qCAAqC;IACrC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,iBAAiB,EACjB,kDAAkD,CACnD,CAAC;IACJ,CAAC;IAED,uCAAuC;IACvC,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;IACnC,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzD,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAE,CAAC;IAEnD,0EAA0E;IAC1E,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,KAAK,OAAO,CAAC;IAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,KAAK,SAAS,CAAC;IAE5C,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9B,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,mBAAmB,EACnB,oDAAoD,CACrD,CAAC;IACJ,CAAC;IAED,oBAAoB;IACpB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;IACnF,CAAC;IAED,IAAI,CAAC;QACH,yBAAyB;QACzB,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE1D,4BAA4B;QAC5B,MAAM,aAAa,GAAG,YAAY,CAAC,YAAY,IAAI,EAAE,CAAC;QACtD,MAAM,SAAS,mCACV,aAAa,GACb,IAAI,CAAC,MAAM,CACf,CAAC;QAEF,wBAAwB;QACxB,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAE5D,iBAAiB;QACjB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IACvE,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,+BAA+B,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC;IACzH,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACU,QAAA,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IACvE,qCAAqC;IACrC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,iBAAiB,EACjB,kDAAkD,CACnD,CAAC;IACJ,CAAC;IAED,uCAAuC;IACvC,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;IACnC,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzD,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAE,CAAC;IAEnD,kCAAkC;IAClC,IAAI,YAAY,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAClC,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,mBAAmB,EACnB,+BAA+B,CAChC,CAAC;IACJ,CAAC;IAED,oBAAoB;IACpB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,kCAAkC,CACnC,CAAC;IACJ,CAAC;IAED,IAAI,CAAC;QACH,mCAAmC;QACnC,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC;YAC/C,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;YACnC,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,KAAK;SACrB,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3E,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAE/D,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI,CAAC,eAAe,IAAI,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;gBACvD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;gBACvD,OAAO,EAAE;oBACP,QAAQ,EAAE,EAAE;oBACZ,GAAG,EAAE,EAAE;oBACP,MAAM,EAAE,EAAE;oBACV,cAAc,EAAE,EAAE;iBACnB;gBACD,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,OAAO;oBACd,aAAa,EAAE,IAAI;iBACpB;aACF,CAAC;YAEF,oBAAoB;YACpB,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEjF,8DAA8D;YAC9D,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;gBAC1G,MAAM,cAAc,GAAG;oBACrB,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACjE,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;oBAChF,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;oBACvB,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,EAAE;oBACf,KAAK,EAAE,EAAE;oBACT,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;oBACvD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;iBACxD,CAAC;gBAEF,MAAM,KAAK,CAAC,SAAS,EAAE;qBACpB,UAAU,CAAC,SAAS,CAAC;qBACrB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAClB,UAAU,CAAC,aAAa,CAAC;qBACzB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;qBACnB,GAAG,CAAC,cAAc,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,2DAA2D;QAE3D,yBAAyB;QACzB,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,4BAA4B;YACrC,GAAG,EAAE,UAAU,CAAC,GAAG;SACpB,CAAC;IACJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,yBAAyB,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC;IACnH,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IACzE,IAAI,CAAC;QACH,4BAA4B;QAC5B,MAAM,aAAa,GAAG;YACpB,IAAI,EAAE,SAAS,EAAE,eAAe;YAChC,QAAQ,EAAE,IAAI,EAAG,uBAAuB;SACzC,CAAC;QAEF,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QAEhE,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC,CAAC"}