"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.helloWorld = exports.onUserCreated = exports.createUser = exports.setCustomUserClaims = void 0;
const admin = require("firebase-admin");
const functions = require("firebase-functions");
const cors = require("cors");
const auth = require("./auth");
// Initialize Firebase Admin
admin.initializeApp();
// Initialize CORS middleware
const corsHandler = cors({ origin: true });
// Export auth functions
exports.setCustomUserClaims = auth.setCustomUserClaims;
exports.createUser = auth.createUser;
exports.onUserCreated = auth.onUserCreated;
// Hello World function for testing
exports.helloWorld = functions.https.onRequest((request, response) => {
    corsHandler(request, response, () => {
        functions.logger.info("Hello logs!", { structuredData: true });
        response.send("Hello from Firebase!");
    });
});
//# sourceMappingURL=index.js.map