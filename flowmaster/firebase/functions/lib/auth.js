"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onUserCreated = exports.createUser = exports.setCustomUserClaims = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
/**
 * Sets custom claims for a user
 * This function is called by the client to set custom claims for a user
 * Only admins can set custom claims for other users
 */
exports.setCustomUserClaims = functions.https.onCall(async (data, context) => {
    // Check if the user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }
    // Get the calling user's custom claims
    const callerUid = context.auth.uid;
    const callerUser = await admin.auth().getUser(callerUid);
    const callerClaims = callerUser.customClaims || {};
    // Check if the caller is an admin or is trying to update their own claims
    const isAdmin = callerClaims.role === 'admin';
    const isSelfUpdate = data.uid === callerUid;
    if (!isAdmin && !isSelfUpdate) {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can set custom claims for other users.');
    }
    // Validate the data
    if (!data.uid) {
        throw new functions.https.HttpsError('invalid-argument', 'User ID is required.');
    }
    try {
        // Get the user to update
        const userToUpdate = await admin.auth().getUser(data.uid);
        // Prepare the claims to set
        const currentClaims = userToUpdate.customClaims || {};
        const newClaims = Object.assign(Object.assign({}, currentClaims), data.claims);
        // Set the custom claims
        await admin.auth().setCustomUserClaims(data.uid, newClaims);
        // Return success
        return { success: true, message: 'Custom claims set successfully.' };
    }
    catch (error) {
        console.error('Error setting custom claims:', error);
        throw new functions.https.HttpsError('internal', 'Failed to set custom claims: ' + (error.message || 'Unknown error'));
    }
});
/**
 * Creates a new user with custom claims and Firestore document
 * This function is called by the client to create a new user
 * Only admins can create users
 */
exports.createUser = functions.https.onCall(async (data, context) => {
    // Check if the user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }
    // Get the calling user's custom claims
    const callerUid = context.auth.uid;
    const callerUser = await admin.auth().getUser(callerUid);
    const callerClaims = callerUser.customClaims || {};
    // Check if the caller is an admin
    if (callerClaims.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can create users.');
    }
    // Validate the data
    if (!data.email || !data.password) {
        throw new functions.https.HttpsError('invalid-argument', 'Email and password are required.');
    }
    try {
        // Create the user in Firebase Auth
        const userRecord = await admin.auth().createUser({
            email: data.email,
            password: data.password,
            displayName: data.displayName || '',
            disabled: false,
            emailVerified: false,
        });
        // Set custom claims
        const claims = data.claims || { role: 'student', schoolId: data.schoolId };
        await admin.auth().setCustomUserClaims(userRecord.uid, claims);
        // Create user document in Firestore
        if (data.schoolId) {
            const userDocData = {
                email: data.email,
                displayName: data.displayName || '',
                roleId: claims.role,
                roles: data.additionalRoles || [],
                schoolId: data.schoolId,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                profile: {
                    photoURL: '',
                    bio: '',
                    skills: [],
                    certifications: []
                },
                settings: {
                    language: 'sl',
                    theme: 'light',
                    notifications: true
                }
            };
            // Save to Firestore
            await admin.firestore().collection('users').doc(userRecord.uid).set(userDocData);
            // If the user is an instructor, create an instructor document
            if (claims.role === 'instructor' || (data.additionalRoles && data.additionalRoles.includes('instructor'))) {
                const instructorData = {
                    firstName: data.displayName ? data.displayName.split(' ')[0] : '',
                    lastName: data.displayName ? data.displayName.split(' ').slice(1).join(' ') : '',
                    email: data.email,
                    phone: data.phone || '',
                    status: 'active',
                    specialties: [],
                    notes: '',
                    createdAt: admin.firestore.FieldValue.serverTimestamp(),
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                };
                await admin.firestore()
                    .collection('schools')
                    .doc(data.schoolId)
                    .collection('instructors')
                    .doc(userRecord.uid)
                    .set(instructorData);
            }
        }
        // Send welcome email (optional)
        // This would require additional setup with a mail provider
        // Return the user record
        return {
            success: true,
            message: 'User created successfully.',
            uid: userRecord.uid,
        };
    }
    catch (error) {
        console.error('Error creating user:', error);
        throw new functions.https.HttpsError('internal', 'Failed to create user: ' + (error.message || 'Unknown error'));
    }
});
/**
 * Trigger function that runs when a new user is created in Firebase Auth
 * Sets default custom claims for the user
 */
exports.onUserCreated = functions.auth.user().onCreate(async (user) => {
    try {
        // Set default custom claims
        const defaultClaims = {
            role: 'student', // Default role
            schoolId: null, // No school by default
        };
        await admin.auth().setCustomUserClaims(user.uid, defaultClaims);
        console.log(`Set default claims for user ${user.uid}`);
        return true;
    }
    catch (error) {
        console.error('Error setting default claims:', error);
        return false;
    }
});
//# sourceMappingURL=auth.js.map