import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';
import * as auth from './auth';

// Initialize Firebase Admin
admin.initializeApp();

// Export auth functions
export const setCustomUserClaims = auth.setCustomUserClaims;
export const createUser = auth.createUser;
export const onUserCreated = auth.onUserCreated;

// Hello World function for testing
export const helloWorld = functions.https.onRequest((request, response) => {
  functions.logger.info("Hello logs!", {structuredData: true});
  response.send("Hello from Firebase!");
});
