import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
// Import v1 functions for auth triggers
import * as functionsV1 from 'firebase-functions/v1';

// Define interfaces for our callable functions
interface SetCustomUserClaimsData {
  uid: string;
  claims: Record<string, unknown>;
}

interface CreateUserData {
  email: string;
  password: string;
  displayName?: string;
  claims?: Record<string, unknown>;
}

/**
 * Sets custom claims for a user
 * This function is called by the client to set custom claims for a user
 * Only admins can set custom claims for other users
 */
export const setCustomUserClaims = functions.https.onCall(
  async (data: unknown, context: unknown) => {
    // Cast data to our expected type
    const typedData = data as SetCustomUserClaimsData;
    const typedContext = context as { auth: { uid: string } };

    // Check if the user is authenticated
    if (!typedContext?.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'The function must be called while authenticated.'
      );
    }

    // Get the calling user's custom claims
    const callerUid = typedContext.auth.uid;
    const callerUser = await admin.auth().getUser(callerUid);
    const callerClaims = callerUser.customClaims || {};

    // Check if the caller is an admin or is trying to update their own claims
    const isAdmin = callerClaims.role === 'admin';
    const isSelfUpdate = typedData.uid === callerUid;

    if (!isAdmin && !isSelfUpdate) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can set custom claims for other users.'
      );
    }

    // Validate the data
    if (!typedData.uid) {
      throw new functions.https.HttpsError('invalid-argument', 'User ID is required.');
    }

    try {
      // Get the user to update
      const userToUpdate = await admin.auth().getUser(typedData.uid);

      // Prepare the claims to set
      const currentClaims = userToUpdate.customClaims || {};
      const newClaims = {
        ...currentClaims,
        ...typedData.claims,
      };

      // Set the custom claims
      await admin.auth().setCustomUserClaims(typedData.uid, newClaims);

      // Return success
      return { success: true, message: 'Custom claims set successfully.' };
    } catch (error) {
      console.error('Error setting custom claims:', error);
      throw new functions.https.HttpsError('internal', 'Failed to set custom claims.');
    }
  }
);

/**
 * Creates a new user with custom claims
 * This function is called by the client to create a new user
 * Only admins can create users
 */
export const createUser = functions.https.onCall(async (data: unknown, context: unknown) => {
  // Cast data to our expected type
  const typedData = data as CreateUserData;
  const typedContext = context as { auth: { uid: string } };

  // Check if the user is authenticated
  if (!typedContext?.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  // Get the calling user's custom claims
  const callerUid = typedContext.auth.uid;
  const callerUser = await admin.auth().getUser(callerUid);
  const callerClaims = callerUser.customClaims || {};

  // Check if the caller is an admin
  if (callerClaims.role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can create users.');
  }

  // Validate the data
  if (!typedData.email || !typedData.password) {
    throw new functions.https.HttpsError('invalid-argument', 'Email and password are required.');
  }

  try {
    // Create the user
    const userRecord = await admin.auth().createUser({
      email: typedData.email,
      password: typedData.password,
      displayName: typedData.displayName || '',
      disabled: false,
      emailVerified: false,
    });

    // Set custom claims if provided
    if (typedData.claims) {
      await admin.auth().setCustomUserClaims(userRecord.uid, typedData.claims);
    }

    // Return the user record
    return {
      success: true,
      message: 'User created successfully.',
      uid: userRecord.uid,
    };
  } catch (error) {
    console.error('Error creating user:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create user.');
  }
});

/**
 * Trigger function that runs when a new user document is created in Firestore
 * Sets default custom claims for the user
 */
export const onUserCreated = functionsV1.auth.user().onCreate(async (user: { uid: string }) => {
  try {
    // Set default custom claims
    const defaultClaims = {
      role: 'student', // Default role
      schoolId: null, // No school by default
    };

    await admin.auth().setCustomUserClaims(user.uid, defaultClaims);

    console.log(`Set default claims for user ${user.uid}`);
    return true;
  } catch (error) {
    console.error('Error setting default claims:', error);
    return false;
  }
});
