rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow all operations during development
    // IMPORTANT: Remove this rule before deploying to production
    match /{document=**} {
      allow read, write: if true;
    }

    // Original rules below
    // Common functions
    function isSignedIn() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isSignedIn() && request.auth.uid == userId;
    }

    // Use custom claims for role checks
    function hasRole(role) {
      // First try to use custom claims
      if (isSignedIn() && request.auth.token.role == role) {
        return true;
      }

      // Fall back to Firestore document if claims are not set
      return isSignedIn() && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.roleId == role;
    }

    function isAdmin() {
      return hasRole('admin');
    }

    function isInstructor() {
      return hasRole('instructor') || isAdmin();
    }

    // Use custom claims for school membership checks
    function isSchoolMember(schoolId) {
      // First try to use custom claims
      if (isSignedIn() && request.auth.token.schoolId == schoolId) {
        return true;
      }

      // Fall back to Firestore document if claims are not set
      return isSignedIn() &&
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.schoolId == schoolId || isAdmin());
    }

    // Users collection
    match /users/{userId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }

    // Schools collection
    match /schools/{schoolId} {
      allow read: if isSignedIn();
      allow create: if isAdmin();
      allow update: if isAdmin() || isSchoolMember(schoolId);
      allow delete: if isAdmin();

      // Nested collections within a school
      match /{collection}/{docId} {
        allow read: if isSchoolMember(schoolId);
        allow write: if isSchoolMember(schoolId);

        // Allow access to nested documents within collections
        match /{nestedCollection}/{nestedDocId} {
          allow read: if isSchoolMember(schoolId);
          allow write: if isSchoolMember(schoolId);
        }
      }

      // Instructor-specific rules for lessons
      match /lessons/{lessonId} {
        // Instructors can only read and write their own lessons
        allow read: if isSchoolMember(schoolId) &&
          (isAdmin() ||
           isInstructor() && resource.data.instructorId == request.auth.uid);
        allow create: if isSchoolMember(schoolId) &&
          (isAdmin() ||
           isInstructor() && request.resource.data.instructorId == request.auth.uid);
        allow update, delete: if isSchoolMember(schoolId) &&
          (isAdmin() ||
           isInstructor() && resource.data.instructorId == request.auth.uid);
      }

      // Instructor-specific rules for students
      match /students/{studentId} {
        // Instructors can only read and write their own students
        allow read: if isSchoolMember(schoolId) &&
          (isAdmin() ||
           isInstructor() && resource.data.instructorId == request.auth.uid);
        allow create: if isSchoolMember(schoolId) &&
          (isAdmin() ||
           isInstructor() && request.resource.data.instructorId == request.auth.uid);
        allow update, delete: if isSchoolMember(schoolId) &&
          (isAdmin() ||
           isInstructor() && resource.data.instructorId == request.auth.uid);
      }

      // Explicitly allow access to attendance collection
      match /attendance/{docId} {
        allow read: if isSchoolMember(schoolId);
        allow write: if isSchoolMember(schoolId);
      }

      // Explicitly allow access to programs collection
      match /programs/{programId} {
        allow read: if isSchoolMember(schoolId);
        allow write: if isSchoolMember(schoolId);

        // Allow access to program sessions
        match /sessions/{sessionId} {
          allow read: if isSchoolMember(schoolId);
          allow write: if isSchoolMember(schoolId);
        }
      }
    }

    // Public data that anyone can read
    match /public/{docId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Roles collection
    match /roles/{roleId} {
      allow read: if isSignedIn();
      allow write: if isAdmin();
    }

    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
