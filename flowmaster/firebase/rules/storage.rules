rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return request.auth.token.role == role;
    }
    
    function isAdmin() {
      return hasRole('admin');
    }
    
    function isInstructor() {
      return hasRole('instructor');
    }
    
    function isValidImage() {
      return request.resource.contentType.matches('image/.*')
        && request.resource.size < 5 * 1024 * 1024; // 5MB
    }
    
    // Profile images
    match /profiles/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() 
        && isOwner(userId) 
        && isValidImage();
    }
    
    // Program images
    match /programs/{programId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if (isInstructor() || isAdmin()) 
        && isValidImage();
    }
    
    // Lesson attachments
    match /lessons/{lessonId}/attachments/{fileName} {
      allow read: if isAuthenticated();
      allow write: if (isInstructor() || isAdmin())
        && request.resource.size < 10 * 1024 * 1024; // 10MB
    }
  }
}
