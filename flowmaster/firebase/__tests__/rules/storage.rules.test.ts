import {
  assertFails,
  assertSucceeds,
  initializeTestEnvironment,
  RulesTestEnvironment,
} from '@firebase/rules-unit-testing';
import { ref, uploadBytes } from 'firebase/storage';
import * as fs from 'fs';
import { join } from 'path';
import { beforeAll, beforeEach, afterAll, describe, it } from '@jest/globals';

const RULES_FILE_PATH = join(__dirname, '../../rules/storage.rules');

let testEnv: RulesTestEnvironment;

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'demo-flowmaster',
    storage: {
      rules: fs.readFileSync(RULES_FILE_PATH, 'utf8'),
      host: 'localhost',
      port: 9199,
    },
  });
});

beforeEach(async () => {
  await testEnv.clearStorage();
});

afterAll(async () => {
  await testEnv.cleanup();
});

describe('Storage Security Rules', () => {
  describe('Profile Images', () => {
    const validImagePath = 'profiles/user123/profile.jpg';
    const invalidImagePath = 'profiles/user123/profile.exe';

    it('allows authenticated users to upload their own profile image', async () => {
      const auth = testEnv.authenticatedContext('user123', { role: 'student' });
      const storage = auth.storage();
      const imageRef = ref(storage, validImagePath);
      const imageBytes = new Uint8Array([0xff, 0xd8, 0xff]); // JPEG magic numbers
      const metadata = { contentType: 'image/jpeg' };

      await assertSucceeds(uploadBytes(imageRef, imageBytes, metadata));
    });

    it("denies uploading to another user's profile", async () => {
      const auth = testEnv.authenticatedContext('user456');
      const storage = auth.storage();
      const imageRef = ref(storage, validImagePath);
      const imageBytes = new Uint8Array([0xff, 0xd8, 0xff]);
      const metadata = { contentType: 'image/jpeg' };

      await assertFails(uploadBytes(imageRef, imageBytes, metadata));
    });

    it('denies uploading non-image files', async () => {
      const auth = testEnv.authenticatedContext('user123');
      const storage = auth.storage();
      const imageRef = ref(storage, invalidImagePath);
      const imageBytes = new Uint8Array([0x4d, 0x5a]); // EXE magic numbers
      const metadata = { contentType: 'application/x-msdownload' };

      await assertFails(uploadBytes(imageRef, imageBytes, metadata));
    });
  });

  describe('Program Images', () => {
    const validProgramPath = 'programs/prog123/cover.jpg';

    it('allows admin to upload program images', async () => {
      const auth = testEnv.authenticatedContext('admin', { role: 'admin' });
      const storage = auth.storage();
      const imageRef = ref(storage, validProgramPath);
      const imageBytes = new Uint8Array([0xff, 0xd8, 0xff]);
      const metadata = { contentType: 'image/jpeg' };

      await assertSucceeds(uploadBytes(imageRef, imageBytes, metadata));
    });

    it('denies non-admin users from uploading program images', async () => {
      const auth = testEnv.authenticatedContext('user123');
      const storage = auth.storage();
      const imageRef = ref(storage, validProgramPath);
      const imageBytes = new Uint8Array([0xff, 0xd8, 0xff]);
      const metadata = { contentType: 'image/jpeg' };

      await assertFails(uploadBytes(imageRef, imageBytes, metadata));
    });
  });

  describe('Lesson Attachments', () => {
    const validAttachmentPath = 'lessons/lesson123/attachments/doc.pdf';
    const invalidAttachmentPath = 'lessons/lesson123/attachments/script.js';

    it('allows instructors to upload lesson attachments', async () => {
      const auth = testEnv.authenticatedContext('instructor', { role: 'instructor' });
      const storage = auth.storage();
      const attachmentRef = ref(storage, validAttachmentPath);
      const attachmentBytes = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // PDF magic numbers
      const metadata = { contentType: 'application/pdf' };

      await assertSucceeds(uploadBytes(attachmentRef, attachmentBytes, metadata));
    });

    it('denies uploading potentially harmful files', async () => {
      const auth = testEnv.authenticatedContext('instructor', { role: 'instructor' });
      const storage = auth.storage();
      const attachmentRef = ref(storage, invalidAttachmentPath);
      const attachmentBytes = new Uint8Array([0x2f, 0x2f]); // JS file content
      const metadata = { contentType: 'application/javascript' };

      await assertFails(uploadBytes(attachmentRef, attachmentBytes, metadata));
    });

    it('denies students from uploading attachments', async () => {
      const auth = testEnv.authenticatedContext('student');
      const storage = auth.storage();
      const attachmentRef = ref(storage, validAttachmentPath);
      const attachmentBytes = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // PDF magic numbers
      const metadata = { contentType: 'application/pdf' };

      await assertFails(uploadBytes(attachmentRef, attachmentBytes, metadata));
    });
  });
});
