import {
  initializeTestEnvironment,
  RulesTestEnvironment,
  assertFails,
  assertSucceeds,
} from '@firebase/rules-unit-testing';
import { readFileSync } from 'fs';
import { resolve } from 'path';

const PROJECT_ID = 'flowmaster-test';
const RULES_FILE_PATH = resolve(__dirname, '../../rules/firestore.rules');

describe('Firestore Security Rules', () => {
  let testEnv: RulesTestEnvironment;

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: PROJECT_ID,
      firestore: {
        rules: readFileSync(RULES_FILE_PATH, 'utf8'),
        host: 'localhost',
        port: 8081,
      },
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  beforeEach(async () => {
    await testEnv.clearFirestore();
  });

  describe('Users Collection', () => {
    it('allows users to read their own profile', async () => {
      const userId = 'user123';
      const context = testEnv.authenticatedContext(userId);
      const userDoc = context.firestore().collection('users').doc(userId);

      await assertSucceeds(userDoc.get());
    });

    it('prevents users from reading other users profiles', async () => {
      const userId = 'user123';
      const otherUserId = 'user456';
      const context = testEnv.authenticatedContext(userId);
      const otherUserDoc = context.firestore().collection('users').doc(otherUserId);

      await assertFails(otherUserDoc.get());
    });

    it('allows users to create their own profile with valid data', async () => {
      const userId = 'user123';
      const context = testEnv.authenticatedContext(userId);
      const userDoc = context.firestore().collection('users').doc(userId);

      await assertSucceeds(
        userDoc.set({
          email: '<EMAIL>',
          role: 'student',
          displayName: 'Test User',
        })
      );
    });

    it('prevents users from creating invalid profiles', async () => {
      const userId = 'user123';
      const context = testEnv.authenticatedContext(userId);
      const userDoc = context.firestore().collection('users').doc(userId);

      await assertFails(
        userDoc.set({
          email: 'invalid-email',
          role: 'student',
        })
      );
    });
  });

  describe('Lessons Collection', () => {
    it('allows instructors to create lessons', async () => {
      const instructorId = 'instructor123';
      const context = testEnv.authenticatedContext(instructorId, { role: 'instructor' });

      // First create the instructor user
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('users').doc(instructorId).set({
          role: 'instructor',
          email: '<EMAIL>',
          displayName: 'Test Instructor',
        });
      });

      const lessonDoc = context.firestore().collection('lessons').doc('lesson123');
      await assertSucceeds(
        lessonDoc.set({
          title: 'Test Lesson',
          instructorId: instructorId,
          startTime: new Date(),
          endTime: new Date(),
          studentIds: ['student1', 'student2'],
        })
      );
    });

    it('allows students to read their lessons', async () => {
      const studentId = 'student1';
      const lessonId = 'lesson123';

      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context
          .firestore()
          .collection('lessons')
          .doc(lessonId)
          .set({
            title: 'Test Lesson',
            studentIds: [studentId],
            instructorId: 'instructor123',
          });
      });

      const context = testEnv.authenticatedContext(studentId, { role: 'student' });
      const lessonDoc = context.firestore().collection('lessons').doc(lessonId);

      await assertSucceeds(lessonDoc.get());
    });
  });

  describe('Programs Collection', () => {
    it('allows managers to create programs', async () => {
      const managerId = 'manager123';

      // First create the manager user
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('users').doc(managerId).set({
          role: 'manager',
          email: '<EMAIL>',
          displayName: 'Test Manager',
        });
      });

      const context = testEnv.authenticatedContext(managerId);
      const programDoc = context.firestore().collection('programs').doc('program123');

      await assertSucceeds(
        programDoc.set({
          title: 'Test Program',
          description: 'Test Description',
          studentIds: ['student1'],
        })
      );
    });
  });

  describe('Schedules Collection', () => {
    it('allows instructors to create schedules', async () => {
      const instructorId = 'instructor123';

      // First create the instructor user
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('users').doc(instructorId).set({
          role: 'instructor',
          email: '<EMAIL>',
          displayName: 'Test Instructor',
        });
      });

      const context = testEnv.authenticatedContext(instructorId);
      const scheduleDoc = context.firestore().collection('schedules').doc('schedule123');

      await assertSucceeds(
        scheduleDoc.set({
          instructorId: instructorId,
          date: new Date(),
          slots: [],
        })
      );
    });
  });

  describe('Schools Collection', () => {
    it('allows users to read their school', async () => {
      const userId = 'user123';
      const schoolId = 'school123';

      // First create the user and school
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('users').doc(userId).set({
          schoolId: schoolId,
          role: 'student',
          email: '<EMAIL>',
          displayName: 'Test Student',
        });
        await context.firestore().collection('schools').doc(schoolId).set({
          name: 'Test School',
        });
      });

      const context = testEnv.authenticatedContext(userId);
      const schoolDoc = context.firestore().collection('schools').doc(schoolId);

      await assertSucceeds(schoolDoc.get());

      // Clean up
      await testEnv.clearFirestore();
    });
  });

  describe('Attendance Collection', () => {
    it('allows instructors to create attendance records', async () => {
      const instructorId = 'instructor123';

      // First create the instructor user
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('users').doc(instructorId).set({
          role: 'instructor',
          email: '<EMAIL>',
          displayName: 'Test Instructor',
        });
      });

      const context = testEnv.authenticatedContext(instructorId);
      const attendanceDoc = context.firestore().collection('attendance').doc('attendance123');

      await assertSucceeds(
        attendanceDoc.set({
          lessonId: 'lesson123',
          studentId: 'student1',
          date: new Date(),
          status: 'present',
        })
      );
    });

    it('allows students to read their own attendance records', async () => {
      const studentId = 'student1';
      const attendanceId = 'attendance123';

      await testEnv.withSecurityRulesDisabled(async (context) => {
        await context.firestore().collection('attendance').doc(attendanceId).set({
          lessonId: 'lesson123',
          studentId: studentId,
          date: new Date(),
          status: 'present',
        });
      });

      const context = testEnv.authenticatedContext(studentId, { role: 'student' });
      const attendanceDoc = context.firestore().collection('attendance').doc(attendanceId);

      await assertSucceeds(attendanceDoc.get());
    });
  });
});
