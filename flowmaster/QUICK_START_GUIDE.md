# 🚀 FlowMaster Quick Start Guide

## 🎉 Congratulations! Your FlowMaster Database is Ready

The FlowMaster mountain biking school management system has been successfully seeded with comprehensive test data and is ready for use!

## 📊 What's Available

### 🏫 **Mountain Biking Academy**
- **School ID**: `mountain-biking-academy`
- **Complete mountain biking focused curriculum**
- **Real equipment inventory and rental system**

### 👥 **Users (87 total)**
- **7 Instructors** with MTB specializations
- **50 Students** across all skill levels
- **30 Clients** for equipment rentals

### 🚵‍♂️ **Equipment (110 items)**
- **45+ Mountain Bikes** (Trek, Specialized, Giant, Santa Cruz, Yeti)
- **20 Helmets** in various sizes
- **60+ Protective Gear** items

### 📚 **Lessons (65 total)**
- **30 Individual** coaching sessions
- **20 Group** lessons
- **15 Children's** lessons

### 🏕️ **Programs (6 total)**
- **3 Seasonal Programs**: Spring Trail Mastery, Summer Downhill Development, Fall Enduro Training
- **3 Camp Programs**: Summer Shred Camp, Skills Development Weekend, Youth MTB Academy

### 📦 **Equipment Rentals (43 total)**
- **15 Active** rentals
- **25 Completed** rentals
- **3 Overdue** rentals

---

## 🔐 Login & Access

### **Test Admin Account**
```
Email: <EMAIL>
Password: TestPassword123!
Role: Admin (Full Access)
School: Mountain Biking Academy
```

### **How to Access the Data**

1. **Start the Application**:
   ```bash
   npm start
   ```

2. **Open in Browser**:
   - Navigate to `http://localhost:3000`

3. **Sign In**:
   - Use the test admin credentials above
   - You'll have full access to all seeded data

4. **Explore the Data**:
   - **Dashboard**: Overview of all activities
   - **Lessons**: Individual, Group, and Children's lessons
   - **Programs**: Seasonal programs and camps
   - **Equipment**: Mountain bikes, helmets, protective gear
   - **Rentals**: Active and historical rental records

---

## 🛠️ Available Scripts

### **Database Management**
```bash
# Seed the database with mountain biking data
npm run seed-db

# Verify seeded data
npm run verify-db

# Create test admin user
npm run create-test-user

# Set up user claims (if needed)
npm run setup-user
```

### **Development**
```bash
# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build
```

---

## 🎯 Mountain Biking Features

### **Disciplines Covered**
- Cross Country (XC)
- Downhill (DH)
- Enduro
- Trail Riding
- Freeride
- Dirt Jumping
- BMX
- Fat Biking

### **Skills & Training**
- Basic Bike Handling
- Cornering Techniques
- Climbing Skills
- Descending Techniques
- Jumping and Air Time
- Technical Trail Navigation
- Bike Maintenance
- Safety and Risk Management

### **Equipment Brands**
- Trek, Specialized, Giant
- Santa Cruz, Yeti, Pivot
- Cannondale, Scott
- Professional protective gear

---

## 🔧 Technical Details

### **Data Structure**
- **Nested Collections**: `schools/{schoolId}/lessons`, `schools/{schoolId}/programs`, etc.
- **Proper Relationships**: All foreign keys maintained
- **School Isolation**: Data properly isolated by school
- **Realistic Timestamps**: Past and future dates for testing

### **Authentication**
- **Firebase Auth**: User authentication
- **Custom Claims**: Role and school-based access
- **Firestore Fallback**: Claims read from user documents
- **Multi-role Support**: Admin, Instructor, Student, Client

### **Security**
- **Firestore Rules**: School-based data isolation
- **Role-based Access**: Proper permission checks
- **Data Validation**: Clean, validated data structure

---

## 🚨 Troubleshooting

### **Can't See Data?**
1. **Check Login**: Make sure you're signed in with the test admin account
2. **Verify School**: Ensure user has `schoolId: 'mountain-biking-academy'`
3. **Check Console**: Look for permission errors in browser console
4. **Refresh Token**: Try signing out and back in
5. **Data Structure**: Users are now in nested collections (`schools/{schoolId}/students`, etc.)

### **Permission Errors?**
1. **Firestore Rules**: Currently set to "allow all" for development
2. **Custom Claims**: May need to be set via Firebase Functions
3. **User Document**: Claims are read from Firestore as fallback

### **Need More Data?**
```bash
# Re-run seeding (adds more data)
npm run seed-db

# Verify what was created
npm run verify-db
```

---

## 🎉 You're Ready to Go!

The FlowMaster mountain biking school management system is now fully operational with:

✅ **Complete mountain biking curriculum**  
✅ **Realistic equipment inventory**  
✅ **Active rental system**  
✅ **Comprehensive user base**  
✅ **Structured programs and lessons**  
✅ **Admin access configured**  

### **Next Steps**
1. **Explore the Application**: Navigate through all modules
2. **Test Functionality**: Create, edit, and manage data
3. **Customize as Needed**: Modify seeding scripts for specific requirements
4. **Deploy**: When ready, deploy to production environment

**Happy mountain biking! 🚵‍♂️🏔️**

---

*For technical support or questions, refer to the comprehensive documentation in the `scripts/` folder or the main `PROJECT_DOCUMENTATION.md` file.*
