# Mock Data Replacement Guide

This document identifies areas in the FlowMaster application where mock data is currently being used and needs to be replaced with real Firebase API calls.

## Overview

Throughout the development process, several components were implemented with mock data to facilitate rapid prototyping and UI development. These mock data instances need to be replaced with real Firebase API calls to make the application fully functional.

## Mock Data Instances

### 1. Dashboard Page

**File:** `src/pages/dashboard/Dashboard.tsx`

**Issue:** Using hardcoded mock data for dashboard statistics and charts

**Mock Data:**
- Total programs count (5)
- Total lessons count (25)
- Completed lessons count (15)
- Upcoming lessons count (10)
- Recent activities list with 3 mock entries
- Lessons trend data with mock dates and counts

**Current Implementation:**
```typescript
const fetchDashboardData = async () => {
  try {
    // TODO: Implement API call to fetch dashboard data
    // For now, using mock data
    setStats({
      totalPrograms: 5,
      totalLessons: 25,
      completedLessons: 15,
      upcomingLessons: 10,
      recentActivities: [
        {
          id: '1',
          type: 'lesson',
          description: 'New lesson scheduled with <PERSON>',
          date: '2024-01-15',
        },
        // More mock entries...
      ],
      lessonsTrend: [
        { date: '2024-01-10', lessons: 4 },
        // More mock trend data...
      ],
    });
  } catch (err) {
    setError(t('dashboard.error.fetch', 'Failed to fetch dashboard data'));
  } finally {
    setLoading(false);
  }
};
```

### 2. Schedule Page

**File:** `src/pages/schedule/Schedule.tsx`

**Issue:** Using a timeout to simulate loading instead of fetching real schedule data

**Current Implementation:**
```typescript
const fetchScheduleData = async () => {
  try {
    // TODO: Implement API call to fetch schedule data
    // For now, just simulating loading
    await new Promise((resolve) => setTimeout(resolve, 1000));
  } catch (err) {
    setError(t('schedule.error.fetch', 'Failed to fetch schedule data'));
  } finally {
    setLoading(false);
  }
};
```

### 3. Instructor Profile Page

**File:** `src/pages/people/Instructors/InstructorProfile.tsx`

**Issue:** Using empty arrays and console.log instead of implementing functionality

**Mock Data:**
- Empty unavailable periods array
- Placeholder functions for period management
- Empty relationships array

**Current Implementation:**
```typescript
<UnavailabilityCalendar
  instructorId={instructor.id}
  unavailablePeriods={[]}
  onAddPeriod={(period) => {
    // TODO: Implement period addition
    console.log('Add period:', period);
  }}
  onEditPeriod={(period) => {
    // TODO: Implement period editing
    console.log('Edit period:', period);
  }}
  onDeletePeriod={(periodId) => {
    // TODO: Implement period deletion
    console.log('Delete period:', periodId);
  }}
  isManager={true}
/>

<StudentRelationships
  instructorId={instructor.id}
  relationships={[]}
  onStudentClick={(studentId) => {
    // TODO: Implement student profile navigation
    console.log('Navigate to student:', studentId);
  }}
/>
```

### 4. Student Profile Page

**File:** `src/pages/people/Students/StudentProfile.tsx`

**Issue:** Placeholder sections with "Coming soon" messages

**Current Implementation:**
```typescript
<Paper sx={{ p: 3, mb: 3 }}>
  <Typography variant="h6" gutterBottom>
    {t('students.groupAssignments', 'Group Assignments')}
  </Typography>
  <Box sx={{ mt: 2 }}>
    {/* TODO: Implement group assignments list */}
    <Typography variant="body2" color="text.secondary">
      {t('common:ui.comingSoon', 'Coming soon')}
    </Typography>
  </Box>
</Paper>

<Paper sx={{ p: 3, mb: 3 }}>
  <Typography variant="h6" gutterBottom>
    {t('students.progressTracking', 'Progress Tracking')}
  </Typography>
  <Box sx={{ mt: 2 }}>
    {/* TODO: Implement progress tracking */}
    <Typography variant="body2" color="text.secondary">
      {t('common:ui.comingSoon', 'Coming soon')}
    </Typography>
  </Box>
</Paper>

<Paper sx={{ p: 3 }}>
  <Typography variant="h6" gutterBottom>
    {t('students.recentActivity', 'Recent Activity')}
  </Typography>
  <Box sx={{ mt: 2 }}>
    {/* TODO: Implement recent activity feed */}
    <Typography variant="body2" color="text.secondary">
      {t('common:ui.comingSoon', 'Coming soon')}
    </Typography>
  </Box>
</Paper>
```

### 5. Client Profile Page

**File:** `src/pages/people/Clients/ClientProfile.tsx`

**Issue:** Placeholder sections with "Coming soon" messages

**Current Implementation:**
```typescript
<Paper sx={{ p: 3, mb: 3 }}>
  <Typography variant="h6" gutterBottom>
    {t('clients.upcomingLessons', 'Upcoming Lessons')}
  </Typography>
  {/* TODO: Add upcoming lessons section */}
  <Typography variant="body1" color="text.secondary">
    {t('common:ui.comingSoon', 'Coming soon')}
  </Typography>
</Paper>

<Paper sx={{ p: 3 }}>
  <Typography variant="h6" gutterBottom>
    {t('clients.recentActivity', 'Recent Activity')}
  </Typography>
  {/* TODO: Add recent activity section */}
  <Typography variant="body1" color="text.secondary">
    {t('common:ui.comingSoon', 'Coming soon')}
  </Typography>
</Paper>
```

### 6. Settings/Notifications Page

**File:** `src/pages/settings/notifications/Notifications.tsx`

**Issue:** Using a timeout to simulate API call instead of saving to Firebase

**Current Implementation:**
```typescript
const handleSave = async () => {
  setLoading(true);
  setError(null);
  setSuccess(null);

  try {
    // TODO: Implement API call to save notification settings
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setSuccess(t('notifications.updateSuccess', 'Notification settings updated successfully'));
  } catch (err) {
    setError(t('notifications.updateError', 'Failed to update notification settings'));
  } finally {
    setLoading(false);
  }
};
```

### 7. Availability Calendar Component

**File:** `src/components/scheduling/AvailabilityCalendar.tsx`

**Issue:** Placeholder dialog content for time slot details

**Current Implementation:**
```typescript
<Dialog open={isAddSlotOpen} onClose={() => setIsAddSlotOpen(false)}>
  <DialogTitle>{t('availability.addSlotTitle', 'Add Slot Title')}</DialogTitle>
  <DialogContent>{/* TODO: Add form for time slot details */}</DialogContent>
  <DialogActions>
    <Button onClick={() => setIsAddSlotOpen(false)}>{t('common:ui.cancel')}</Button>
    <Button variant="contained" color="primary">
      {t('common:ui.save')}
    </Button>
  </DialogActions>
</Dialog>
```

## Implementation Recommendations

For each of these instances, implement real Firebase API calls to:

1. **Fetch data** from Firestore collections
2. **Create/update data** in Firestore
3. **Delete data** when needed
4. **Query data** with appropriate filters

### Example Implementation Pattern

```typescript
// Example for Dashboard data
const fetchDashboardData = async () => {
  try {
    const schoolId = currentSchool?.id;
    if (!schoolId) return;
    
    // Get programs count
    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const programsSnapshot = await getDocs(programsRef);
    const totalPrograms = programsSnapshot.size;
    
    // Get lessons with queries
    const lessonsRef = collection(db, 'schools', schoolId, 'lessons');
    const lessonsSnapshot = await getDocs(lessonsRef);
    const totalLessons = lessonsSnapshot.size;
    
    // Get completed lessons
    const completedLessonsQuery = query(
      lessonsRef,
      where('status', '==', 'completed')
    );
    const completedLessonsSnapshot = await getDocs(completedLessonsQuery);
    const completedLessons = completedLessonsSnapshot.size;
    
    // Get upcoming lessons
    const now = Timestamp.now();
    const upcomingLessonsQuery = query(
      lessonsRef,
      where('startTime', '>=', now),
      where('status', '==', 'scheduled')
    );
    const upcomingLessonsSnapshot = await getDocs(upcomingLessonsQuery);
    const upcomingLessons = upcomingLessonsSnapshot.size;
    
    // Get recent activities
    const activitiesRef = collection(db, 'schools', schoolId, 'activities');
    const activitiesQuery = query(
      activitiesRef,
      orderBy('date', 'desc'),
      limit(5)
    );
    const activitiesSnapshot = await getDocs(activitiesQuery);
    const recentActivities = activitiesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    // Set the data
    setStats({
      totalPrograms,
      totalLessons,
      completedLessons,
      upcomingLessons,
      recentActivities,
      // Calculate lessons trend from data
      lessonsTrend: calculateLessonsTrend(lessonsSnapshot.docs),
    });
  } catch (err) {
    console.error('Error fetching dashboard data:', err);
    setError(t('dashboard.error.fetch', 'Failed to fetch dashboard data'));
  } finally {
    setLoading(false);
  }
};
```

## Priority Order

When implementing these changes, consider the following priority order:

1. Dashboard Page - Most visible and important for user experience
2. Schedule Page - Core functionality for the application
3. Instructor/Student/Client Profiles - Important for user management
4. Settings Pages - Less critical but needed for full functionality
5. Component-level implementations - Can be addressed as needed

## Testing Considerations

When replacing mock data with real Firebase calls:

1. Test with the Firebase emulator first
2. Ensure proper error handling
3. Add loading states for better UX
4. Verify data consistency across components
5. Update tests to mock the new Firebase calls
