# Programs Module Implementation Plan

This document outlines the phased implementation plan for the Programs module in FlowMaster, based on the approach described in `programs-implementation.md`.

## Phase 1: Basic Program Structure and Session Generation
- [x] Create program types and interfaces
  - [x] Define `Program` interface with fields for name, type, description, schedule, etc.
  - [x] Define `Session` interface for individual program sessions
  - [x] Update database.ts with new types
- [x] Implement Firebase schema
  - [x] Set up programs collection
  - [x] Set up sessions subcollection
  - [ ] Create necessary indexes
- [x] Create program management UI
  - [x] Programs list view with filtering options
  - [x] Program details view with tabs
  - [x] Program creation/edit form
- [x] Implement session generation logic
  - [x] Auto-generate sessions based on program schedule
  - [x] Handle recurring sessions (weekly patterns)
  - [ ] Visualize generated sessions in calendar view
- [x] Set up routing
  - [x] Add routes for program pages
  - [x] Update permissions in role types
  - [x] Add program detail route

## Phase 2: Attendance Tracking
- [x] Create attendance data structure
  - [x] Define `Attendance` interface with present/absent/excused status
  - [x] Set up attendance storage in Firestore
- [x] Implement attendance UI
  - [x] Session attendance view with student list
  - [x] Bulk marking capabilities
  - [x] Color-coded status indicators
  - [x] Notes field for excused absences
- [ ] Add attendance summary views
  - [x] Per-session attendance overview
  - [ ] Per-student attendance history
  - [ ] Basic attendance statistics

## Phase 3: Progress Tracking and Reporting
- [x] Implement skill milestone tracking
  - [x] Define skill data structures
  - [x] Create UI for marking skills as completed
  - [x] Implement progress visualization
- [x] Add goals tracking
  - [x] Program-level goals setting
  - [x] Student-specific goals
  - [x] Progress indicators
- [x] Create reporting features
  - [x] Attendance reports with export options
  - [x] Progress reports
  - [ ] Instructor performance metrics
  - [x] Visual charts and graphs

## User Experience Improvements
- [x] Add loading indicators during data processing
- [x] Implement error handling for edge cases
- [x] Add confirmation dialogs for destructive actions
- [x] Enhance reporting with interactive charts
- [x] Add export functionality for reports

## Integration with Other Modules
- [x] Connect with the Lessons module
  - [x] Link program sessions to lessons
  - [x] Create lessons from program sessions
  - [x] Show program-related lessons in the program details
- [x] Integrate with the Students module
  - [x] Show program enrollment in student profiles
  - [x] Display student progress across programs
  - [x] Allow easy navigation between student profiles and their programs
- [x] Connect with the Instructors module
  - [x] Show program assignments in instructor profiles
  - [x] Display instructor workload across programs
  - [x] Allow easy navigation between instructor profiles and their programs

## Phase 4: Payment Tracking and Advanced Features
- [x] Implement payment tracking
  - [x] Payment status per student
  - [x] Payment history
  - [x] Outstanding balance indicators
  - [x] Payment management UI
  - [x] Receipt tracking
- [x] Add makeup session functionality
  - [x] UI for scheduling makeup sessions
  - [x] Linking makeup sessions to missed regular sessions
  - [x] Automatic marking of absent students for makeup
  - [x] Tracking of makeup sessions and missed sessions
- [ ] Integrate with notifications system
  - [ ] Session reminders
  - [ ] Attendance notifications
  - [ ] Payment reminders
- [x] Implement advanced features
  - [x] Drag-and-drop student assignment
  - [x] Bulk operations for sessions
  - [x] Schedule change handling

## Integration Points
- [x] Connect with existing Lessons module
  - [x] Link program sessions to lessons
  - [x] Share attendance data
- [x] Integrate with Students module
  - [x] Show program enrollment in student profiles
  - [x] Display student progress across programs
- [x] Connect with Instructors module
  - [x] Show program assignments in instructor schedules
  - [x] Manage instructor workload

## Technical Implementation Details

### Database Schema
```
programs/
  ├─ programId
  │   ├─ name: string
  │   ├─ type: "seasonal" | "yearly" | "camp"
  │   ├─ description: string
  │   ├─ schedule: {
  │   │   days: string[],
  │   │   time: string,
  │   │   startDate: timestamp,
  │   │   endDate: timestamp
  │   │ }
  │   ├─ participants: string[] // studentIds
  │   ├─ instructors: string[] // instructorIds
  │   ├─ sessions: subcollection/
  │   │   ├─ sessionId
  │   │   │   ├─ date: timestamp
  │   │   │   ├─ attendance: {
  │   │   │   │   studentId: "present" | "absent" | "excused"
  │   │   │   │ }
  │   │   │   ├─ notes: string
  │   │   │   ├─ skills: { skillId: boolean }
  │   │   │   ├─ isMakeup: boolean
  │   │   │   ├─ makeupDetails?: {
  │   │   │   │   originalSessionId?: string
  │   │   │   │   forStudents: string[]
  │   │   │   │   reason: string
  │   │   │   │ }
  │   │   │   └─ missedStudents?: string[]
  │   ├─ pricing: {
  │   │   totalFee: number,
  │   │   currency: string,
  │   │   paymentStatus: { studentId: "paid" | "partial" | "pending" },
  │   │   paidAmounts: { studentId: number },
  │   │   installments?: number,
  │   │   dueDate?: timestamp,
  │   │   earlyBirdDiscount?: number,
  │   │   discounts?: { studentId: number }
  │   │ }
  │   ├─ payments: subcollection/
  │   │   ├─ paymentId
  │   │   │   ├─ studentId: string
  │   │   │   ├─ amount: number
  │   │   │   ├─ currency: string
  │   │   │   ├─ method: "cash" | "card" | "transfer" | "check" | "other"
  │   │   │   ├─ date: timestamp
  │   │   │   ├─ notes?: string
  │   │   │   ├─ receiptNumber?: string
  │   │   │   ├─ createdAt: timestamp
  │   │   │   └─ updatedAt: timestamp
  │   └─ progress: {
  │       skills: string[],
  │       goals: string
  │     }
```

### Session Generation Logic
```javascript
const generateSessions = (program) => {
  const sessions = [];
  let currentDate = new Date(program.startDate);
  const endDate = new Date(program.endDate);

  while (currentDate <= endDate) {
    const dayName = format(currentDate, 'EEEE').toLowerCase();

    if (program.schedule.days.includes(dayName)) {
      sessions.push({
        date: currentDate,
        attendance: {},
        notes: "",
        skills: {}
      });
    }

    // Move to next day
    currentDate = addDays(currentDate, 1);
  }

  return sessions;
};
```

## UI Components to Create

### Program List View
- Filterable list of programs
- Card view with program details
- Quick actions (view, edit, archive)
- Status indicators

### Program Creation Form
- Basic information section
- Schedule configuration
- Participant selection
- Instructor assignment
- Pricing setup

### Program Detail View
- Tabs for different sections:
  - Overview
  - Sessions
  - Participants
  - Attendance
  - Progress
  - Lessons
  - Makeups
  - Payments
  - Reports
  - Assignment
  - Schedule
- Action buttons for common operations

### Attendance Tracking UI
- Session list with dates
- Student list with status toggles
- Notes field
- Bulk actions

### Progress Tracking UI
- Skill checklist
- Progress visualization
- Goal tracking
- Student comparison

### Payment Tracking UI
- Payment status overview
- Payment history per student
- Balance calculations
- Receipt management
- Payment method tracking
- Due date indicators

### Makeup Sessions UI
- Scheduled makeup sessions list
- Students needing makeup list
- Makeup session creation form
- Linking to original sessions
- Automatic marking of absent students
- Tracking of makeup attendance

### Schedule Change UI
- Current schedule display
- New schedule configuration
- Options for applying changes to existing sessions
- Notification options for students and instructors
- Preview of affected sessions
- Confirmation dialog for changes

## Next Steps
1. Start by implementing the data structures and basic UI components
2. Focus on core functionality first (program creation and session generation)
3. Build incrementally, testing each component thoroughly
4. Get user feedback early and iterate
