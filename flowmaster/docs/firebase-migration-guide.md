# Migrating from Firebase Emulators to Real Firebase

This guide provides step-by-step instructions for migrating your FlowMaster application from Firebase emulators to real Firebase services.

## Migration Checklist

- [x] Update Firebase configuration to use real Firebase
- [x] Update security rules for production
- [ ] Deploy security rules to real Firebase
- [ ] Create necessary Firestore indexes
- [ ] Create initial data in real Firebase
- [ ] Test the application with real Firebase

## Step 1: Update Firebase Configuration

We've already updated the Firebase configuration to use real Firebase services by default:

1. Modified `src/config/firebase.ts` to only connect to emulators if `REACT_APP_USE_EMULATORS` is set to `'true'`
2. Updated `.env.development` and `.env.production` to set `REACT_APP_USE_EMULATORS=false` by default

To switch between emulators and real Firebase:
- Set `REACT_APP_USE_EMULATORS=true` to use emulators
- Set `REACT_APP_USE_EMULATORS=false` to use real Firebase

## Step 2: Deploy Security Rules

We've updated the security rules for production use. Now, we need to deploy them to the real Firebase project:

```bash
chmod +x scripts/deploy-firebase-config.sh
./scripts/deploy-firebase-config.sh
```

This script will:
1. Check if Firebase CLI is installed
2. Verify you're logged in to Firebase
3. Deploy Firestore and Storage rules to production
4. Deploy Firestore indexes to production

## Step 3: Create Initial Data

We've created a script to generate initial data in your real Firebase project:

```bash
# Install Firebase Admin SDK if not already installed
npm install firebase-admin

# Run the script
node scripts/create-initial-data.js
```

This script will create:
1. A demo school
2. Sample students
3. Sample instructors
4. Sample programs

## Step 4: Test the Application

Now that you've deployed the rules, indexes, and created initial data, it's time to test the application with real Firebase:

1. Start the application:
   ```bash
   npm start
   ```

2. Sign in with your admin user
3. Verify that you can read and write data to Firestore
4. Check that all features work correctly with real Firebase

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

1. Check the browser console for errors
2. Verify that you're using the correct Firebase project
3. Ensure that the user exists in Firebase Authentication
4. Check that the user has the correct role in Firestore

### Firestore Access Issues

If you encounter Firestore access issues:

1. Check the browser console for errors
2. Verify that the security rules are deployed correctly
3. Ensure that the user has the necessary permissions
4. Check if you need to create additional indexes

To create a new index when you encounter a query error:

1. Look for the error message in the console
2. Click on the link provided in the error message
3. Follow the instructions to create the required index
4. Wait for the index to be created (may take a few minutes)

### Missing Indexes

If you see an error like "The query requires an index", you need to create the index:

1. Click on the link in the error message
2. Create the index in the Firebase Console
3. Wait for the index to be created
4. Add the index to `firebase/indexes.json`
5. Deploy the updated indexes:
   ```bash
   cd firebase
   firebase deploy --only firestore:indexes
   ```

## Switching Between Emulators and Real Firebase

During development, you might want to switch between emulators and real Firebase:

### To use emulators:
1. Set `REACT_APP_USE_EMULATORS=true` in `.env.development`
2. Start the emulators:
   ```bash
   cd firebase
   firebase emulators:start
   ```
3. Start the application:
   ```bash
   npm start
   ```

### To use real Firebase:
1. Set `REACT_APP_USE_EMULATORS=false` in `.env.development`
2. Start the application:
   ```bash
   npm start
   ```

## Additional Resources

- [Firebase Console](https://console.firebase.google.com/u/0/project/flowmaster-e3947/overview)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firestore Indexes](https://firebase.google.com/docs/firestore/query-data/indexing)
- [Firebase Authentication](https://firebase.google.com/docs/auth)
