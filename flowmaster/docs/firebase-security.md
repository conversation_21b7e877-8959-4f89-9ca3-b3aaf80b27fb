# Firebase Security Configuration

## Overview
This document outlines the security rules and best practices implemented for Firebase services in the FlowMaster application.

## Security Rules Structure

### Firestore Rules
Located in `firebase/rules/firestore.rules`, these rules implement:

1. Role-Based Access Control (RBAC)
   - Admin: Full access to all collections
   - Manager: Can manage programs and lessons
   - Instructor: Can manage lessons
   - User: Read-only access to most collections

2. Data Validation
   - Required fields validation
   - Data type checking
   - Size limits
   - Format validation

3. Helper Functions
   ```javascript
   isAuthenticated()  // Checks if user is logged in
   isOwner(userId)    // Checks if user owns the resource
   hasRole(role)      // Checks if user has specific role
   isAdmin()          // Checks if user has admin role
   isInstructor()     // Checks if user has instructor role
   isManager()        // Checks if user has manager role
   isValidEmail()     // Validates email format
   isValidPhone()     // Validates phone number format
   ```

### Storage Rules
Located in `firebase/rules/storage.rules`, these rules implement:

1. File Access Control
   - Profile images: Owner access
   - Program images: Manager/Admin access
   - Lesson materials: Instructor/Manager/Admin access

2. File Validation
   - Image type verification
   - File size limits
     - Profile/Program images: 5MB max
     - Lesson attachments: 10MB max
   - Content type validation

## Testing

### Running Security Rules Tests
```bash
cd firebase
npm test
```

### Test Coverage
The test suite in `firebase/rules/__tests__/` covers:
1. User permissions
2. Role-based access
3. Data validation
4. File upload restrictions

## Best Practices Implemented

### 1. Authentication
- Required for all write operations
- Role verification for sensitive operations
- Owner-only access for personal data

### 2. Data Validation
- Required fields checking
- Data type validation
- Size and format restrictions
- Cross-collection validation

### 3. Resource Access
- Principle of least privilege
- Role-based permissions
- Collection-level security
- Document-level security

### 4. File Storage
- Size limits
- Type restrictions
- Access control
- Owner verification

## Firebase Emulator

### Local Development
1. Start the emulator:
   ```bash
   ./scripts/init-dev.sh
   ```

2. Access the Firebase Emulator UI:
   - Authentication: http://localhost:9099
   - Firestore: http://localhost:8080
   - Storage: http://localhost:9199

### Testing with Emulator
1. Security rules are automatically loaded
2. Test data is isolated
3. Rules can be modified without affecting production

## Maintenance

### Updating Rules
1. Modify rules in `firebase/rules/`
2. Run tests to verify changes
3. Deploy using Firebase CLI:
   ```bash
   firebase deploy --only firestore:rules,storage:rules
   ```

### Monitoring
1. Review Firebase Security Debug Logs
2. Monitor failed access attempts
3. Regular security audits

## Security Checklist

### Before Deployment
- [ ] All tests passing
- [ ] Rules syntax validated
- [ ] Access patterns tested
- [ ] File restrictions verified
- [ ] Role permissions confirmed

### Regular Maintenance
- [ ] Review access patterns
- [ ] Update role permissions
- [ ] Check for new security features
- [ ] Audit security logs
- [ ] Update documentation
