# Using Firebase MCP with Augment for FlowMaster

This guide explains how to use Firebase MCP (Model Context Protocol) with the Augment VS Code extension for FlowMaster development.

## Setup Status

Firebase MCP has been configured for the Augment VS Code extension with the following settings:

- **Service Account Key Path**: `/Users/<USER>/dev/FlowMaster/flowmaster/firebase-service-account.json`
- **Firebase Storage Bucket**: `flowmaster-e3947.appspot.com`

## How to Use Firebase MCP with Augment

Once the Firebase MCP server is configured, you can use Augment to interact with your Firebase services. Here's how to get started:

### 1. Restart VS Code

After configuring the MCP server, restart VS Code to apply the changes.

### 2. Test the Firebase MCP Server

Run the test script to verify that the Firebase MCP server is working correctly:

```bash
chmod +x scripts/test-firebase-mcp.sh
./scripts/test-firebase-mcp.sh
```

### 3. Use Augment to Interact with Firebase

Open the Augment chat panel in VS Code and ask it to test the Firebase MCP tools:

```
Please test all Firebase MCP tools.
```

Augment should be able to connect to your Firebase project and test the available tools.

## Available Firebase MCP Tools

### Firestore Tools

| Tool | Description | Required Parameters |
|------|-------------|---------------------|
| `firestore_add_document` | Add a document to a collection | `collection`, `data` |
| `firestore_list_documents` | List documents with filtering | `collection` |
| `firestore_get_document` | Get a specific document | `collection`, `id` |
| `firestore_update_document` | Update an existing document | `collection`, `id`, `data` |
| `firestore_delete_document` | Delete a document | `collection`, `id` |
| `firestore_list_collections` | List root collections | None |
| `firestore_query_collection_group` | Query across subcollections | `collectionId` |

### Storage Tools

| Tool | Description | Required Parameters |
|------|-------------|---------------------|
| `storage_list_files` | List files in a directory | None (optional: `directoryPath`) |
| `storage_get_file_info` | Get file metadata and URL | `filePath` |
| `storage_upload` | Upload file from content | `filePath`, `content` |
| `storage_upload_from_url` | Upload file from URL | `filePath`, `url` |

### Authentication Tools

| Tool | Description | Required Parameters |
|------|-------------|---------------------|
| `auth_get_user` | Get user by ID or email | `identifier` |

## Example Prompts for Augment

Here are some examples of what you can ask Augment to do with Firebase MCP:

### Firestore Examples

```
Please list all Firestore collections in the FlowMaster database.
```

```
Show me all documents in the 'schools' collection.
```

```
Add a new document to the 'students' collection with the following data:
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "grade": "A",
  "enrollmentDate": "2023-09-01"
}
```

### Storage Examples

```
List all files in the Firebase Storage bucket.
```

```
Upload this image to Firebase Storage at 'images/logo.png':
[Base64 encoded image content]
```

### Authentication Examples

```
Get information about the user with email '<EMAIL>'.
```

## Troubleshooting

### Common Issues

#### Service Account Key Not Found

If you see "SERVICE_ACCOUNT_KEY_PATH not set" or "Service account key not found":

1. Make sure the service account key exists at `/Users/<USER>/dev/FlowMaster/flowmaster/firebase-service-account.json`
2. Check that the path in `.vscode/settings.json` is correct
3. Try running the test script again

#### Firebase Initialization Failed

If you see "Firebase is not initialized" error:

1. Check that your service account key is valid
2. Ensure the service account has proper permissions for Firebase services
3. Verify that the project ID in the service account key matches your Firebase project

#### Storage Bucket Not Found

If you see "The specified bucket does not exist" error:

1. Verify your bucket name in Firebase Console → Storage
2. Update the `FIREBASE_STORAGE_BUCKET` value in `.vscode/settings.json`

## Additional Resources

- [Firebase MCP GitHub Repository](https://github.com/gannonh/firebase-mcp)
- [Augment Documentation](https://docs.augmentcode.com/)
- [Firebase Documentation](https://firebase.google.com/docs)
