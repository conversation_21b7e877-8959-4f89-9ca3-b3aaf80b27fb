# Firebase MCP Integration for FlowMaster

This document explains how to set up and use Firebase MCP (Model Context Protocol) with FlowMaster, enabling AI assistants like Augment Code to interact directly with your Firebase services.

## What is Firebase MCP?

Firebase MCP is a server that implements the Model Context Protocol, allowing AI assistants to directly interact with Firebase services including:

- **Firestore**: Document database operations
- **Storage**: File management with robust upload capabilities
- **Authentication**: User management and verification

## Benefits for FlowMaster Development

Integrating Firebase MCP with FlowMaster provides several advantages:

1. **Direct Database Access**: AI assistants can read and write to your Firestore database directly
2. **File Management**: Upload, download, and manage files in Firebase Storage
3. **User Management**: Query and manage user accounts in Firebase Authentication
4. **Streamlined Development**: Reduce the need for manual Firebase operations during development

## Setup Instructions

### Prerequisites

- Firebase project with service account credentials
- Node.js environment
- FlowMaster codebase

### Step 1: Generate Firebase Service Account Key

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your FlowMaster project
3. Navigate to Project Settings → Service Accounts
4. Click "Generate new private key"
5. Save the JSON file securely

### Step 2: Run the Setup Script

We've provided a setup script that automates the installation and configuration process:

```bash
cd flowmaster
chmod +x scripts/setup-firebase-mcp.sh
./scripts/setup-firebase-mcp.sh
```

The script will:
- Install Firebase MCP globally
- Copy your service account key to a secure location
- Configure MCP for Augment Code, Claude Desktop, and Cursor

### Step 3: Environment Variables

Firebase MCP uses several environment variables for configuration:

#### Required Variables
- `SERVICE_ACCOUNT_KEY_PATH`: Path to your Firebase service account key JSON file

#### Optional Variables
- `FIREBASE_STORAGE_BUCKET`: Bucket name for Firebase Storage (defaults to `[projectId].appspot.com`)
- `MCP_TRANSPORT`: Transport type to use (`stdio` or `http`) (defaults to `stdio`)
- `MCP_HTTP_PORT`: Port for HTTP transport (defaults to `3000`)
- `MCP_HTTP_HOST`: Host for HTTP transport (defaults to `localhost`)
- `MCP_HTTP_PATH`: Path for HTTP transport (defaults to `/mcp`)
- `DEBUG_LOG_FILE`: Enable file logging:
  - Set to `true` to log to `~/.firebase-mcp/debug.log`
  - Set to a file path to log to a custom location

These variables can be set in your MCP configuration file or passed as environment variables when running the MCP server.

### Step 4: Verify the Installation

To verify that Firebase MCP is working correctly, ask your AI assistant:

```
Please test all Firebase MCP tools.
```

The assistant should be able to connect to your Firebase project and test the available tools.

## Available Firebase MCP Tools

### Firestore Tools

| Tool | Description | Required Parameters |
|------|-------------|---------------------|
| `firestore_add_document` | Add a document to a collection | `collection`, `data` |
| `firestore_list_documents` | List documents with filtering | `collection` |
| `firestore_get_document` | Get a specific document | `collection`, `id` |
| `firestore_update_document` | Update an existing document | `collection`, `id`, `data` |
| `firestore_delete_document` | Delete a document | `collection`, `id` |
| `firestore_list_collections` | List root collections | None |
| `firestore_query_collection_group` | Query across subcollections | `collectionId` |

### Storage Tools

| Tool | Description | Required Parameters |
|------|-------------|---------------------|
| `storage_list_files` | List files in a directory | None (optional: `directoryPath`) |
| `storage_get_file_info` | Get file metadata and URL | `filePath` |
| `storage_upload` | Upload file from content | `filePath`, `content` |
| `storage_upload_from_url` | Upload file from URL | `filePath`, `url` |

### Authentication Tools

| Tool | Description | Required Parameters |
|------|-------------|---------------------|
| `auth_get_user` | Get user by ID or email | `identifier` |

## Usage Examples

Here are some examples of how to use Firebase MCP with your AI assistant:

### Firestore Examples

#### List Collections

```
Please list all Firestore collections in the FlowMaster database.
```

#### Query Documents

```
Show me all documents in the 'schools' collection.
```

#### Add a Document

```
Add a new document to the 'students' collection with the following data:
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "grade": "A",
  "enrollmentDate": "2023-09-01"
}
```

### Storage Examples

#### List Files

```
List all files in the Firebase Storage bucket.
```

#### Upload a File

```
Upload this image to Firebase Storage at 'images/logo.png':
[Base64 encoded image content]
```

### Authentication Examples

#### Get User Information

```
Get information about the user with email '<EMAIL>'.
```

## Advanced Configuration

### HTTP Transport

By default, Firebase MCP uses stdio transport, which means it communicates with the AI assistant through standard input/output. However, you can also configure it to use HTTP transport, which allows multiple clients to connect to the same server instance.

To run Firebase MCP with HTTP transport:

```bash
# Using environment variables
MCP_TRANSPORT=http MCP_HTTP_PORT=3000 npx @gannonh/firebase-mcp
```

When using HTTP transport, configure your MCP client to connect to the HTTP endpoint:

```json
{
  "firebase-mcp": {
    "url": "http://localhost:3000/mcp"
  }
}
```

Our setup script provides options to configure HTTP transport during installation.

### Session Management

The HTTP transport supports session management, allowing multiple clients to connect to the same server instance. Each client receives a unique session ID that is used to maintain state between requests.

## Troubleshooting

### Common Issues

#### Storage Bucket Not Found

If you see "The specified bucket does not exist" error:

1. Verify your bucket name in Firebase Console → Storage
2. Set the correct bucket name in your MCP configuration

#### Firebase Initialization Failed

If you see "Firebase is not initialized" error:

1. Check that your service account key path is correct and absolute
2. Ensure the service account has proper permissions for Firebase services

#### Composite Index Required

If you receive "This query requires a composite index" error:

1. Look for the provided URL in the error message
2. Follow the link to create the required index in Firebase Console
3. Retry your query after the index is created (may take a few minutes)

#### Zod Validation Error with `firestore_list_collections`

If you see a Zod validation error with message "Expected object, received boolean" when using the `firestore_list_collections` tool:

This is a known issue with the MCP SDK. Despite the error message, the query still works correctly and returns the proper collection data. This is a log-level error that doesn't affect functionality.

## Security Considerations

- The service account key provides full access to your Firebase project
- Store the key securely and never commit it to version control
- Consider using Firebase Security Rules to restrict access to your data
- For production environments, use a service account with limited permissions

## Additional Resources

- [Firebase MCP GitHub Repository](https://github.com/gannonh/firebase-mcp)
- [Model Context Protocol Documentation](https://github.com/modelcontextprotocol)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firebase Admin SDK](https://firebase.google.com/docs/admin/setup)
