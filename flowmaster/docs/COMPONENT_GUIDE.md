# FlowMaster Component Guide

## Overview

This guide outlines the component architecture, best practices, and usage patterns for FlowMaster's React components.

## Component Organization

### Directory Structure
```
src/
├── components/
│   ├── layout/           # Layout components
│   │   ├── Header
│   │   ├── Sidebar
│   │   └── Footer
│   ├── forms/            # Form components
│   │   ├── TextField
│   │   ├── Select
│   │   └── DatePicker
│   └── shared/           # Shared components
│       ├── Button
│       ├── Card
│       └── Modal
├── pages/                # Page components
└── features/             # Feature-specific components
```

## Component Guidelines

### 1. Component Creation

```typescript
// Good Example
import React from 'react';
import { Typography, Box } from '@mui/material';

interface CardProps {
  title: string;
  children: React.ReactNode;
  onAction?: () => void;
}

const Card: React.FC<CardProps> = ({ title, children, onAction }) => {
  return (
    <Box className="card">
      <Typography variant="h5">{title}</Typography>
      {children}
    </Box>
  );
};

export default Card;
```

### 2. Props Interface

- Use TypeScript interfaces
- Document all props
- Make optional props explicit
- Use proper types

```typescript
interface ButtonProps {
  /** The text content of the button */
  label: string;
  /** Optional click handler */
  onClick?: () => void;
  /** Button variant */
  variant?: 'contained' | 'outlined' | 'text';
  /** Whether the button is disabled */
  disabled?: boolean;
}
```

### 3. Styling

1. **Material-UI Styling**
```typescript
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme }) => ({
  margin: theme.spacing(1),
  padding: theme.spacing(2),
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
  },
}));
```

2. **CSS Modules**
```typescript
import styles from './Button.module.css';

const Button: React.FC<ButtonProps> = ({ label }) => (
  <button className={styles.button}>{label}</button>
);
```

### 4. Error Handling

```typescript
const DataDisplay: React.FC<DataDisplayProps> = ({ data }) => {
  if (!data) {
    return <ErrorBoundary message="No data available" />;
  }

  try {
    return <div>{processData(data)}</div>;
  } catch (error) {
    return <ErrorBoundary error={error} />;
  }
};
```

### 5. Loading States

```typescript
const DataFetcher: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<Data | null>(null);

  return (
    <div>
      {loading ? (
        <LoadingSpinner />
      ) : (
        <DataDisplay data={data} />
      )}
    </div>
  );
};
```

## Component Types

### 1. Layout Components

```typescript
// src/components/layout/PageLayout.tsx
const PageLayout: React.FC<PageLayoutProps> = ({
  header,
  sidebar,
  content,
  footer,
}) => (
  <Box className="page-layout">
    <Header>{header}</Header>
    <Box className="content-wrapper">
      <Sidebar>{sidebar}</Sidebar>
      <main>{content}</main>
    </Box>
    <Footer>{footer}</Footer>
  </Box>
);
```

### 2. Form Components

```typescript
// src/components/forms/TextField.tsx
const TextField: React.FC<TextFieldProps> = ({
  label,
  value,
  onChange,
  error,
}) => (
  <FormControl error={!!error}>
    <InputLabel>{label}</InputLabel>
    <Input value={value} onChange={onChange} />
    {error && <FormHelperText>{error}</FormHelperText>}
  </FormControl>
);
```

### 3. Data Display Components

```typescript
// src/components/shared/DataTable.tsx
const DataTable: React.FC<DataTableProps> = ({
  data,
  columns,
  onSort,
  onFilter,
}) => (
  <Table>
    <TableHeader columns={columns} onSort={onSort} />
    <TableBody data={data} columns={columns} />
    <TableFooter onFilter={onFilter} />
  </Table>
);
```

## Component Testing

### 1. Basic Component Test

```typescript
import { render, screen } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('renders with label', () => {
    render(<Button label="Click me" />);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
});
```

### 2. Interactive Component Test

```typescript
import { render, fireEvent } from '@testing-library/react';
import TextField from './TextField';

describe('TextField', () => {
  it('handles user input', () => {
    const onChange = jest.fn();
    render(<TextField onChange={onChange} />);
    
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'test' },
    });
    
    expect(onChange).toHaveBeenCalled();
  });
});
```

## Best Practices

1. **Component Size**
   - Keep components focused and small
   - Extract reusable logic to custom hooks
   - Split large components into smaller ones

2. **Props**
   - Use proper prop types
   - Provide default props when needed
   - Document props with JSDoc comments

3. **State Management**
   - Use local state for UI-only state
   - Use context for shared state
   - Keep state close to where it's used

4. **Performance**
   - Use React.memo for expensive renders
   - Implement proper key props
   - Avoid unnecessary re-renders

5. **Accessibility**
   - Use semantic HTML
   - Implement ARIA attributes
   - Support keyboard navigation

## Component Documentation

### 1. JSDoc Comments

```typescript
/**
 * A reusable button component with custom styling and behavior.
 *
 * @component
 * @example
 * ```tsx
 * <Button
 *   label="Submit"
 *   variant="contained"
 *   onClick={() => console.log('clicked')}
 * />
 * ```
 */
const Button: React.FC<ButtonProps> = ({ label, variant, onClick }) => {
  // ...
};
```

### 2. Storybook Stories

```typescript
// Button.stories.tsx
import { Story, Meta } from '@storybook/react';
import Button from './Button';

export default {
  title: 'Components/Button',
  component: Button,
} as Meta;

const Template: Story = (args) => <Button {...args} />;

export const Primary = Template.bind({});
Primary.args = {
  label: 'Primary Button',
  variant: 'contained',
};
```

## Internationalization

```typescript
// Using react-i18next
const Welcome: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <Typography>
      {t('welcome.message')}
    </Typography>
  );
};
```
