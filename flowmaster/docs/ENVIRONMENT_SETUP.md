# FlowMaster Environment Setup Guide

## Development Environment Setup

### Prerequisites

1. **Node.js and npm**
   ```bash
   # Check versions
   node --version  # Should be v16 or higher
   npm --version   # Should be v8 or higher
   ```

2. **Docker**
   ```bash
   # Check versions
   docker --version
   docker-compose --version
   ```

3. **Python (for development tools)**
   ```bash
   # Check version
   python --version  # Should be 3.8+
   ```

### Firebase Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com)
   - Create a new project
   - Enable Authentication services
   - Set up Firestore Database
   - Configure Storage

2. **Firebase Configuration**
   - Go to Project Settings
   - Find the Firebase configuration object
   - Create environment files:

   `.env.development`:
   ```
   REACT_APP_FIREBASE_API_KEY=your_api_key
   REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
   REACT_APP_FIREBASE_PROJECT_ID=your_project_id
   REACT_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   REACT_APP_FIREBASE_APP_ID=your_app_id
   ```

   `.env.production`:
   ```
   REACT_APP_FIREBASE_API_KEY=your_prod_api_key
   REACT_APP_FIREBASE_AUTH_DOMAIN=your_prod_auth_domain
   REACT_APP_FIREBASE_PROJECT_ID=your_prod_project_id
   REACT_APP_FIREBASE_STORAGE_BUCKET=your_prod_storage_bucket
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_prod_messaging_sender_id
   REACT_APP_FIREBASE_APP_ID=your_prod_app_id
   ```

3. **Firebase CLI Setup**
   ```bash
   npm install -g firebase-tools
   firebase login
   firebase init
   ```

### Local Development Setup

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd flowmaster
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Python Virtual Environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Unix/macOS
   pip install -r requirements-dev.txt
   ```

4. **Start Development Server**
   
   Using npm:
   ```bash
   npm start
   ```
   
   Using Docker:
   ```bash
   docker-compose up
   ```

### Development Tools

1. **VS Code Extensions**
   - ESLint
   - Prettier
   - TypeScript and JavaScript Language Features
   - Firebase Rules
   - Docker

2. **Browser Extensions**
   - React Developer Tools
   - Redux DevTools (if using Redux)
   - Firebase Console

### Environment Variables

| Variable | Development | Production | Description |
|----------|------------|------------|-------------|
| NODE_ENV | development | production | Environment mode |
| REACT_APP_FIREBASE_* | Dev credentials | Prod credentials | Firebase config |
| CHOKIDAR_USEPOLLING | true | false | Hot reload in Docker |

### Docker Configuration

1. **Development**
   ```bash
   # Start containers
   docker-compose up

   # Rebuild containers
   docker-compose up --build

   # Stop containers
   docker-compose down
   ```

2. **Production**
   ```bash
   # Build production image
   docker build -t flowmaster:prod -f Dockerfile .

   # Run production container
   docker run -p 80:80 flowmaster:prod
   ```

### Troubleshooting

1. **Node Modules Issues**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules
   npm install
   ```

2. **Docker Issues**
   ```bash
   # Remove containers and volumes
   docker-compose down -v
   
   # Rebuild from scratch
   docker-compose up --build
   ```

3. **Firebase Issues**
   ```bash
   # Clear Firebase cache
   firebase logout
   firebase login
   
   # Reinitialize Firebase
   firebase init
   ```

### Continuous Integration Setup

1. **GitHub Actions**
   - Automated testing
   - Linting checks
   - Type checking
   - Build verification

2. **Environment Secrets**
   - Add Firebase credentials
   - Add deployment tokens
   - Add service account keys

### Security Considerations

1. **Environment Variables**
   - Never commit .env files
   - Use different credentials for each environment
   - Rotate keys regularly

2. **Firebase Security**
   - Implement proper security rules
   - Use service accounts for CI/CD
   - Enable MFA for Firebase Console

3. **Development Security**
   - Use HTTPS in development
   - Implement proper CORS policies
   - Follow security best practices
