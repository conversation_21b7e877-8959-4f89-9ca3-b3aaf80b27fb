# 📘 Development Plan: Programs Module in FlowMaster (v2)

## Overview

This document outlines the implementation plan for the **Programs** module in the FlowMaster application. The system is already handling daily and weekly lessons. This new module will support longer-term structured learning formats such as yearly sports schools, seasonal courses, and holiday camps. These programs require specialized handling for group management, attendance tracking, and detailed analytics.

---

## 🧩 Program Types

We define three distinct types of programs:

```ts
export type ProgramType = 'yearly' | 'seasonal' | 'holiday';

yearly – Full-year structured courses (e.g., school-year cycling school)
	•	seasonal – Courses aligned with a season (e.g., winter ski program)
	•	holiday – Short-term programs (e.g., summer camp, spring break workshop)

These types allow filtering, analytics, and better organization within the UI.


## Data Structure Extensions

1. Extend StudentFormData in student.ts

export interface StudentFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  enrollmentDate: string;
  level: string;
  specialties: string[];
  status: StudentStatus;
  programType?: ProgramType; // optional and flexible
}

2. New Entity: Program

interface Program {
  id: string;
  name: string;
  type: ProgramType;
  description?: string;
  startDate: string;
  endDate: string; 
  location?: string; // optional
  groups: ProgramGroup[];
  instructors: string[]; // user IDs
}

3. New Entity: ProgramGroup

interface ProgramGroup {
  id: string;
  name: string;
  maxCapacity: number;
  students: string[]; // student IDs
}

4. Attendance Tracking

interface AttendanceRecord {
  studentId: string;
  programId: string;
  groupId: string;
  date: string;
  status: 'present' | 'absent' | 'excused';
  notes?: string;
}



📋 Functional Modules

1. Programs Dashboard
	•	List view of all programs
	•	Filter by ProgramType
	•	Quick stats (enrollment count, instructor load)
	•	Create / edit / archive programs

2. Inside a Program View

Tab 1: Groups
	•	List of groups with assigned students
	•	Drag-and-drop to assign/reassign
	•	Capacity management

Tab 2: Schedule
	•	Timeline/calendar of sessions
	•	Link to core lesson system
	•	Set repeatable sessions (e.g., every Tuesday 16:00)

Tab 3: Attendance
	•	Grid/table with students vs dates
	•	Color-coded status cells (green, red, yellow)
	•	Monthly and cumulative stats
	•	CSV / PDF export

Tab 4: Statistics
	•	Attendance % by student and group
	•	Instructor time allocation
	•	Visual graphs (bar, line, pie)

⸻

🎨 UI/UX Implementation Plan

Side Menu
	•	New item: Programs
	•	Subcategories (optional): yearly / seasonal / holiday

Program Overview Page
	•	Card or table layout
	•	Color badge for program type
	•	Buttons: View / Edit / Archive

Program Detail View
	•	Tabs for navigation (Groups | Schedule | Attendance | Stats)
	•	Responsive layout with collapsible sections on mobile
	•	Floating action buttons (FABs) for quick actions on mobile

Attendance UI
	•	Table:
	•	Columns = Dates
	•	Rows = Students
	•	Cells = Status icons
	•	Filters: by group / by date range
	•	Export options (CSV, PDF)

⸻

🧱 Backend (Firebase)
	•	programs/: each document is a Program
	•	groups/: subcollections under programs or global
	•	attendance/: subcollection of each program
	•	Indexed by programType, startDate, studentId

⸻

🔔 Optional Features
	•	Instructor mobile check-in for attendance
	•	Push notifications for schedule changes
	•	Certificate generation for completed programs
	•	Integration with external calendar (iCal, Google Calendar)
