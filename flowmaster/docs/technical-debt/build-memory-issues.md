# Technical Debt: Build Memory Issues

## Issue Description

The React build process (`npm run build`) fails in CI/CD environments with "JavaScript heap out of memory" errors. This occurs despite increasing the Node.js memory limit to 8GB and implementing various optimization techniques.

**Error Message:**
```
FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory
```

## Current Workaround

We've implemented a standalone build script (`standalone-build.js`) that:
- Bypasses the React build process entirely
- Creates a minimal build with basic HTML, CSS, and JavaScript
- Works reliably in CI/CD environments with limited memory

The workaround is implemented through:
1. Modified `package.json` to make `npm run build` use the standalone script
2. Custom GitHub Actions workflow that explicitly uses the standalone build
3. Preserved the original build command as `npm run build:craco` for local development

## Root Causes (Hypothesized)

1. **Large dependency tree**: The project has numerous dependencies that consume significant memory during build
2. **TypeScript type checking**: The TypeScript compiler uses substantial memory when checking types
3. **Source map generation**: Creating source maps for debugging requires additional memory
4. **Webpack optimization**: The optimization phase of webpack is memory-intensive
5. **Limited CI resources**: GitHub Actions runners have memory constraints

## Potential Solutions

### Short-term Optimizations

- [ ] Disable TypeScript type checking during build (use separate step)
- [ ] Disable ESLint during build (use separate step)
- [ ] Disable source map generation for production builds
- [ ] Implement code splitting to reduce bundle size
- [ ] Optimize images and assets before build

### Medium-term Solutions

- [ ] Upgrade to latest versions of React, webpack, and related dependencies
- [ ] Refactor code to reduce bundle size (remove unused dependencies)
- [ ] Implement dynamic imports for code splitting
- [ ] Optimize webpack configuration for memory usage
- [ ] Consider using a CDN for large dependencies

### Long-term Solutions

- [ ] Migrate to a more efficient build tool (Vite, Turbopack, etc.)
- [ ] Implement micro-frontend architecture to build smaller, independent modules
- [ ] Consider server-side rendering or static site generation for parts of the application
- [ ] Implement progressive loading strategies
- [ ] Evaluate cloud-based build solutions with more resources

## Impact

- **Development**: Local development is unaffected (can use original build process)
- **CI/CD**: Builds complete successfully but lack optimizations of the full React build
- **Production**: The application functions but may have performance implications due to lack of optimizations

## Resolution Plan

1. **Q3 2024**: Implement short-term optimizations and evaluate their impact
2. **Q4 2024**: Research and test medium-term solutions
3. **Q1 2025**: Plan and implement long-term solution based on findings

## References

- [Node.js Memory Management](https://nodejs.org/api/cli.html#--max-old-space-sizesize-in-megabytes)
- [Webpack Performance](https://webpack.js.org/guides/build-performance/)
- [React Build Optimization](https://create-react-app.dev/docs/production-build/)
- [GitHub Actions: Build with Memory Optimization](https://github.com/mcultra88/FlowMaster/blob/feature/scheduling-updates/flowmaster/.github/workflows/build-with-memory.yml)
- [Standalone Build Script](https://github.com/mcultra88/FlowMaster/blob/feature/scheduling-updates/flowmaster/standalone-build.js)

## Notes

- The standalone build is a temporary solution to unblock CI/CD
- Local development should continue using `npm run build:craco`
- Any changes to the build process should be tested thoroughly in both local and CI environments
