# Database Implementation Plan

## Overview
This document outlines the implementation plan for FlowMaster's Firebase/Firestore database structure, including collections, security rules, and best practices.

## Database Schema

### Collections Structure

#### 1. Users Collection
```typescript
users/
  ├── uid (document ID)
  │   ├── email: string
  │   ├── displayName: string
  │   ├── role: string ("manager", "instructor", "client")
  │   ├── createdAt: timestamp
  │   ├── updatedAt: timestamp
  │   ├── schoolId: string
  │   ├── profile: {
  │   │   photoURL: string,
  │   │   bio: string,
  │   │   skills: string[],
  │   │   certifications: string[]
  │   │ }
  │   └── settings: {
  │       language: string,
  │       notifications: boolean,
  │       theme: string
  │   }
```

#### 2. Lessons Collection
```typescript
lessons/
  ├── lessonId (document ID)
  │   ├── type: string
  │   ├── date: timestamp
  │   ├── duration: number
  │   ├── instructorId: string
  │   ├── studentIds: string[]
  │   ├── equipment: string[]
  │   ├── notes: string
  │   └── status: string
```

#### 3. Programs Collection
```typescript
programs/
  ├── programId (document ID)
  │   ├── type: string
  │   ├── name: string
  │   ├── startDate: timestamp
  │   ├── endDate: timestamp
  │   ├── instructorIds: string[]
  │   ├── studentIds: string[]
  │   ├── goals: string[]
  │   └── status: string
```

#### 4. Schedules Collection
```typescript
schedules/
  ├── scheduleId (document ID)
  │   ├── date: timestamp
  │   ├── lessons: string[]
  │   ├── instructorId: string
  │   └── notes: string
```

#### 5. Schools Collection
```typescript
schools/
  ├── schoolId (document ID)
  │   ├── name: string
  │   ├── sports: string[]
  │   ├── programs: string[]
  │   ├── settings: {
  │   │   language: string,
  │   │   currency: string,
  │   │   timezone: string
  │   │ }
  │   └── createdAt: timestamp
```

#### 6. Attendance Collection
```typescript
attendance/
  ├── attendanceId (document ID)
  │   ├── studentId: string
  │   ├── referenceId: string // lessonId or programId
  │   ├── referenceType: string // 'lesson' or 'program'
  │   ├── date: timestamp
  │   ├── status: string // 'present', 'absent', 'late'
  │   ├── notes?: string
  │   └── createdAt: timestamp
```

## Implementation Steps

### 1. Database Setup

1. Enable Firestore in Firebase Console
   ```bash
   firebase init firestore
   ```

2. Configure Indexes
   Create `firebase/indexes.json`:
   ```json
   {
     "indexes": [
       {
         "collectionGroup": "lessons",
         "queryScope": "COLLECTION",
         "fields": [
           { "fieldPath": "instructorId", "order": "ASCENDING" },
           { "fieldPath": "date", "order": "ASCENDING" }
         ]
       },
       {
         "collectionGroup": "programs",
         "queryScope": "COLLECTION",
         "fields": [
           { "fieldPath": "schoolId", "order": "ASCENDING" },
           { "fieldPath": "startDate", "order": "ASCENDING" }
         ]
       }
     ]
   }
   ```

### 2. Security Rules

Update `firebase/rules/firestore.rules`:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper Functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isUserInSchool(schoolId) {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.schoolId == schoolId;
    }

    function hasRole(role) {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }

    // Users Collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.auth.uid == userId;
      allow update: if isAuthenticated() && 
        (request.auth.uid == userId || hasRole('manager'));
      allow delete: if hasRole('manager');
    }

    // Lessons Collection
    match /lessons/{lessonId} {
      allow read: if isAuthenticated() && 
        (hasRole('instructor') || 
         hasRole('manager') || 
         request.auth.uid in resource.data.studentIds);
      allow create, update: if hasRole('instructor') || hasRole('manager');
      allow delete: if hasRole('manager');
    }

    // Programs Collection
    match /programs/{programId} {
      allow read: if isAuthenticated() && 
        (hasRole('instructor') || 
         hasRole('manager') || 
         request.auth.uid in resource.data.studentIds);
      allow write: if hasRole('manager');
    }

    // Schedules Collection
    match /schedules/{scheduleId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('instructor') || hasRole('manager');
    }

    // Schools Collection
    match /schools/{schoolId} {
      allow read: if isUserInSchool(schoolId);
      allow write: if hasRole('manager');
    }

    // Attendance Collection
    match /attendance/{attendanceId} {
      allow read: if isAuthenticated() && 
        (hasRole('instructor') || 
         hasRole('manager') || 
         request.auth.uid == resource.data.studentId);
      allow create, update: if hasRole('instructor') || hasRole('manager');
      allow delete: if hasRole('manager');
    }
  }
}
```

### 3. Data Validation

Implement TypeScript interfaces for type safety:

```typescript
// src/types/database.ts

export interface User {
  email: string;
  displayName: string;
  role: 'manager' | 'instructor' | 'client';
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt: FirebaseFirestore.Timestamp;
  schoolId: string;
  profile: {
    photoURL?: string;
    bio?: string;
    skills: string[];
    certifications: string[];
  };
  settings: {
    language: string;
    notifications: boolean;
    theme: 'light' | 'dark';
  };
}

export interface Lesson {
  type: 'individual' | 'group' | 'children';
  date: FirebaseFirestore.Timestamp;
  duration: number;
  instructorId: string;
  studentIds: string[];
  equipment: string[];
  notes?: string;
  status: 'scheduled' | 'completed' | 'canceled';
}

export interface Program {
  type: 'school' | 'course';
  name: string;
  startDate: FirebaseFirestore.Timestamp;
  endDate: FirebaseFirestore.Timestamp;
  instructorIds: string[];
  studentIds: string[];
  goals: string[];
  status: 'active' | 'completed';
}

export interface Schedule {
  date: FirebaseFirestore.Timestamp;
  lessons: string[];
  instructorId: string;
  notes?: string;
}

export interface School {
  name: string;
  sports: string[];
  programs: string[];
  settings: {
    language: string;
    currency: string;
    timezone: string;
  };
  createdAt: FirebaseFirestore.Timestamp;
}

export interface Attendance {
  studentId: string;
  referenceId: string;
  referenceType: 'lesson' | 'program';
  date: FirebaseFirestore.Timestamp;
  status: 'present' | 'absent' | 'late';
  notes?: string;
  createdAt: FirebaseFirestore.Timestamp;
}
```

### 4. Database Service Layer

Create service functions for database operations:

```typescript
// src/services/database.ts

import { db } from './firebase';
import { User, Lesson, Program, Schedule, School } from '../types/database';

export const DatabaseService = {
  // Users
  async createUser(uid: string, userData: Partial<User>): Promise<void> {
    await db.collection('users').doc(uid).set({
      ...userData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  },

  async updateUser(uid: string, userData: Partial<User>): Promise<void> {
    await db.collection('users').doc(uid).update({
      ...userData,
      updatedAt: new Date()
    });
  },

  // Lessons
  async createLesson(lessonData: Omit<Lesson, 'id'>): Promise<string> {
    const docRef = await db.collection('lessons').add(lessonData);
    return docRef.id;
  },

  async updateLesson(lessonId: string, lessonData: Partial<Lesson>): Promise<void> {
    await db.collection('lessons').doc(lessonId).update(lessonData);
  },

  // Programs
  async createProgram(programData: Omit<Program, 'id'>): Promise<string> {
    const docRef = await db.collection('programs').add(programData);
    return docRef.id;
  },

  // Schedules
  async createSchedule(scheduleData: Omit<Schedule, 'id'>): Promise<string> {
    const docRef = await db.collection('schedules').add(scheduleData);
    return docRef.id;
  },

  // Schools
  async createSchool(schoolData: Omit<School, 'id'>): Promise<string> {
    const docRef = await db.collection('schools').add({
      ...schoolData,
      createdAt: new Date()
    });
    return docRef.id;
  },

  // Attendance
  async createAttendance(attendanceData: Omit<Attendance, 'id' | 'createdAt'>): Promise<string> {
    const docRef = await db.collection('attendance').add({
      ...attendanceData,
      createdAt: new Date()
    });
    return docRef.id;
  },

  async updateAttendance(attendanceId: string, attendanceData: Partial<Attendance>): Promise<void> {
    await db.collection('attendance').doc(attendanceId).update(attendanceData);
  },

  async getAttendanceByReference(referenceId: string, referenceType: 'lesson' | 'program'): Promise<FirebaseFirestore.QuerySnapshot> {
    return db.collection('attendance')
      .where('referenceId', '==', referenceId)
      .where('referenceType', '==', referenceType)
      .get();
  }
};
```

## Query Patterns

### Common Queries

1. Get User's Upcoming Lessons
```typescript
const getUpcomingLessons = async (userId: string) => {
  const now = new Date();
  return db.collection('lessons')
    .where('studentIds', 'array-contains', userId)
    .where('date', '>=', now)
    .where('status', '==', 'scheduled')
    .orderBy('date')
    .limit(10)
    .get();
};
```

2. Get Instructor's Schedule
```typescript
const getInstructorSchedule = async (instructorId: string, date: Date) => {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  return db.collection('lessons')
    .where('instructorId', '==', instructorId)
    .where('date', '>=', startOfDay)
    .where('date', '<=', endOfDay)
    .orderBy('date')
    .get();
};
```

3. Get School Programs
```typescript
const getSchoolPrograms = async (schoolId: string) => {
  return db.collection('programs')
    .where('schoolId', '==', schoolId)
    .where('status', '==', 'active')
    .orderBy('startDate')
    .get();
};
```

## Best Practices

1. **Data Denormalization**
   - Store frequently accessed data in multiple places
   - Keep related data in the same document when possible
   - Use batch writes for consistency

2. **Security**
   - Always validate user permissions
   - Use security rules to enforce data access patterns
   - Implement role-based access control

3. **Performance**
   - Create necessary composite indexes
   - Limit query results
   - Use pagination for large result sets

4. **Data Integrity**
   - Use transactions for related updates
   - Implement data validation
   - Maintain data consistency across collections

## Testing

1. Create test data:
```typescript
// src/__tests__/database/mockData.ts

export const mockUser = {
  email: '<EMAIL>',
  displayName: 'Test User',
  role: 'instructor',
  schoolId: 'school1',
  profile: {
    skills: ['skiing'],
    certifications: ['PSIA Level 1']
  },
  settings: {
    language: 'en',
    notifications: true,
    theme: 'light'
  }
};
```

2. Test database operations:
```typescript
// src/__tests__/database/operations.test.ts

import { DatabaseService } from '../../services/database';
import { mockUser } from './mockData';

describe('Database Operations', () => {
  it('should create a new user', async () => {
    const uid = 'testUser1';
    await DatabaseService.createUser(uid, mockUser);
    const userDoc = await db.collection('users').doc(uid).get();
    expect(userDoc.exists).toBe(true);
    expect(userDoc.data()).toMatchObject(mockUser);
  });
});
```

## Deployment

1. Deploy Security Rules:
```bash
firebase deploy --only firestore:rules
```

2. Deploy Indexes:
```bash
firebase deploy --only firestore:indexes
```



## Monitoring

1. Set up Firebase Performance Monitoring
2. Monitor query patterns and optimize as needed
3. Set up alerts for security rule violations
4. Regular backup of critical data

---
## Conclusion
The database implementation plan for FlowMaster is well-defined, covering all necessary collections, security rules, and best practices. The implementation includes:

- Database Types: All required TypeScript interfaces (User, Lesson, Program, Schedule, School, Attendance) are properly implemented in`/src/types/database.ts` with correct types and structures.
- Database Service Layer: The DatabaseService in`/src/services/database.ts` has comprehensive CRUD operations and query patterns implemented for all collections, including:
  
  - User management functions
  - Lesson operations with upcoming lessons query
  - Program management
  - Schedule handling with instructor schedule queries
  - School operations
  - Attendance tracking with reference-based queries
- Security Rules: Firestore security rules are properly configured in`/firebase/rules/firestore.rules` with:
  
  - Role-based access control
  - Helper functions for authentication and school membership
  - Proper read/write rules for all collections
The implementation matches the planned schema and includes all necessary security measures, data validation, and query patterns as outlined in the documentation.