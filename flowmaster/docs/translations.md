# FlowMaster Translation System

## Overview

FlowMaster uses a type-safe internationalization (i18n) system built on top of `react-i18next`. The system is designed to provide:
- Type safety for translation keys
- Multiple language support
- Namespace organization
- Runtime language switching
- Automatic language detection

## Directory Structure

```
flowmaster/
├── public/
│   └── locales/           # Translation JSON files
│       ├── en/           # English translations
│       │   ├── common.json
│       │   ├── errors.json
│       │   ├── auth.json
│       │   ├── profile.json
│       │   └── settings.json
              translations.json
│       └── sl/           # Slovenian translations
│           └── [...same structure as en]
├── src/
    ├── i18n/
    │   ├── config.ts     # i18next configuration
    │   └── test-config.ts # Test-specific i18n setup
    ├── types/
    │   └── i18next.d.ts  # Type definitions for translations
    └── hooks/
        └── useTranslations.ts # Custom translation hook
```

## Usage Guide

### 1. Basic Usage

```typescript
import { useTranslations } from '@/hooks/useTranslations';

function MyComponent() {
  const { t } = useTranslations();
  
  return (
    <div>
      <h1>{t('common.title')}</h1>
      <p>{t('auth.login.subtitle')}</p>
    </div>
  );
}
```

### 2. Translation Parameters

When using translations with numeric values, you need to provide both the default value and the interpolation parameters:

```typescript
// Wrong - only providing interpolation parameters
t('lessons.duration', { minutes: duration })

// Correct - providing both default value and interpolation parameters
t('lessons.duration', String(duration), { minutes: duration })
```

This ensures that the translation system has a fallback value if the translation key is not found, while still allowing proper interpolation of the numeric value in the translated string.
```

### 2. Type Safety

Translation keys are fully typed through the `i18next.d.ts` file. This provides:
- Autocomplete for translation keys
- Type errors for missing translations
- Type checking for interpolation values

### 3. Namespaces

Available namespaces:
- `common`: Shared translations across the app
- `errors`: Error messages
- `auth`: Authentication-related text
- `profile`: User profile texts
- `settings`: Settings-related content

## Adding New Translations

1. Add the translation key and text to the appropriate JSON file in `/public/locales/en/`
2. Add the same key to all other language files
3. Add the type definition in `src/types/i18next.d.ts`:

```typescript
interface CustomTypeOptions {
  resources: {
    [namespace]: {
      'new.translation.key': string;
    };
  };
}
```

## Configuration

The i18n system is configured in `src/i18n/config.ts`:

```typescript
{
  fallbackLng: 'en',           // Default language
  debug: process.env.NODE_ENV === 'development',
  defaultNS: 'common',         // Default namespace
  ns: ['common', 'errors', 'auth', 'profile', 'settings'],
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
  detection: {
    order: ['localStorage', 'navigator'],
    caches: ['localStorage'],
  }
}
```

## Best Practices

1. **Namespace Organization**
   - Keep common, reusable texts in `common.json`
   - Use specific namespaces for feature-specific texts
   - Don't duplicate translations across namespaces

2. **Key Structure**
   - Use dot notation: `feature.subfeature.element`
   - Keep keys descriptive but concise
   - Use consistent naming patterns

3. **Type Safety**
   - Always update `i18next.d.ts` when adding new translations
   - Use the `useTranslations` hook instead of direct `useTranslation`
   - Leverage TypeScript's type checking for translation keys

## Common Issues and Solutions

1. **Missing Translations**
   - Error: Key not found in runtime
   - Solution: Ensure key exists in both translation files and type definitions

2. **Type Errors**
   - Error: TS2345 - Argument type not assignable
   - Solution: Check `i18next.d.ts` for correct type definition

3. **Namespace Loading**
   - Error: Namespace not loaded
   - Solution: Ensure namespace is included in `i18n/config.ts`

4. **Wrong Namespace Configuration**
   - Error: Translation key not found despite existing in files
   - Cause: Component is using default namespace instead of the intended one
   - Solution: Specify the correct namespace in useTranslation hook
   ```typescript
   // Wrong - uses default namespace
   const { t } = useTranslation();
   
   // Correct - specifies the intended namespace
   const { t } = useTranslation('programs');
   ```

## Testing Translations

Use `src/i18n/test-config.ts` for testing environments:
- Provides synchronous loading of translations
- Includes mock translations for testing
- Enables consistent test behavior

## Runtime Language Switching

```typescript
const { i18n } = useTranslations();

// Switch language
await i18n.changeLanguage('sl');

// Get current language
const currentLang = i18n.language;
```

## Contributing New Languages

1. Create a new directory under `/public/locales/` with the language code
2. Copy all JSON files from the `en` directory
3. Translate all strings while keeping the keys identical
4. Update language detection configuration if needed

## Performance Considerations

- Translations are loaded lazily by namespace
- Browser caching is enabled by default
- Language detection results are cached in localStorage
- Bundle size is optimized through code splitting


--------


When accessing common translations, components explicitly specify the namespace:

- Using the pattern:`t('ui.key', 'Default Text', { ns: 'common' })`
- Common UI elements like 'Back', 'Save', etc. are consistently accessed from the common namespace