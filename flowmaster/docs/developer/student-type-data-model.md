# Student Type and Attendance Data Model

## Overview

This document describes the data model changes implemented to replace the generic student "status" field with a more specific "type" field and enhance the attendance tracking functionality.

## Student Model Changes

### Previous Model

```typescript
interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: string; // Generic status field
  // Other fields...
}
```

### Updated Model

```typescript
interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  type: 'program' | 'lesson' | 'both'; // Specific type field
  // Other fields...
}
```

### Type Field Values

- **program**: Student is enrolled in structured programs with regular sessions
- **lesson**: Student takes individual lessons scheduled on an as-needed basis
- **both**: Student participates in both programs and individual lessons

## Attendance Data Model

### Program Attendance

The program attendance model tracks attendance for each student in each program session:

```typescript
interface ProgramSession {
  id: string;
  programId: string;
  date: Timestamp;
  startTime: string;
  endTime: string;
  instructorId: string;
  attendance: {
    [studentId: string]: 'present' | 'absent' | 'excused';
  };
  notes: string;
}
```

### Attendance Aggregation

To support efficient reporting, we've added aggregation fields to track attendance statistics:

```typescript
interface AttendanceData {
  programId: string;
  programName: string;
  totalSessions: number;
  totalStudents: number;
  attendanceRate: number;
  presentCount: number;
  absentCount: number;
  excusedCount: number;
  studentAttendance: {
    [studentId: string]: {
      present: number;
      absent: number;
      excused: number;
      total: number;
    };
  };
}

interface StudentAttendanceData {
  studentId: string;
  studentName: string;
  totalSessions: number;
  presentCount: number;
  absentCount: number;
  excusedCount: number;
  attendanceRate: number;
  programs: string[];
}
```

## Firestore Schema

### Students Collection

Path: `/schools/{schoolId}/students/{studentId}`

```
{
  firstName: string,
  lastName: string,
  email: string,
  phone: string,
  type: string, // 'program', 'lesson', or 'both'
  createdAt: timestamp,
  updatedAt: timestamp,
  // Other fields...
}
```

### Programs Collection

Path: `/schools/{schoolId}/programs/{programId}`

```
{
  name: string,
  description: string,
  startDate: timestamp,
  endDate: timestamp,
  instructorId: string,
  participants: string[], // Array of student IDs
  // Other fields...
}
```

### Program Sessions Collection

Path: `/schools/{schoolId}/programs/{programId}/sessions/{sessionId}`

```
{
  date: timestamp,
  startTime: string,
  endTime: string,
  instructorId: string,
  attendance: {
    [studentId]: string, // 'present', 'absent', or 'excused'
  },
  notes: string,
  // Other fields...
}
```

## Firestore Security Rules

The security rules have been updated to accommodate the new student type field and attendance data:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // School-level access
    match /schools/{schoolId} {
      // Allow read if user belongs to the school
      allow read: if request.auth.token.schoolId == schoolId;
      
      // Students collection
      match /students/{studentId} {
        // Allow read if user belongs to the school
        allow read: if request.auth.token.schoolId == schoolId;
        
        // Allow write if user is an admin or instructor
        allow write: if request.auth.token.schoolId == schoolId && 
                      (request.auth.token.role == 'admin' || 
                       request.auth.token.role == 'instructor');
                       
        // Validate student type field
        function isValidStudentType() {
          return request.resource.data.type in ['program', 'lesson', 'both'];
        }
        
        // Enforce validation on create and update
        allow create: if isValidStudentType();
        allow update: if isValidStudentType();
      }
      
      // Programs collection
      match /programs/{programId} {
        // Allow read if user belongs to the school
        allow read: if request.auth.token.schoolId == schoolId;
        
        // Allow write if user is an admin or instructor
        allow write: if request.auth.token.schoolId == schoolId && 
                      (request.auth.token.role == 'admin' || 
                       request.auth.token.role == 'instructor');
        
        // Program sessions collection
        match /sessions/{sessionId} {
          // Allow read if user belongs to the school
          allow read: if request.auth.token.schoolId == schoolId;
          
          // Allow write if user is an admin or instructor
          allow write: if request.auth.token.schoolId == schoolId && 
                        (request.auth.token.role == 'admin' || 
                         request.auth.token.role == 'instructor');
                         
          // Validate attendance data
          function isValidAttendanceStatus() {
            return request.resource.data.attendance == null || 
                   request.resource.data.attendance.size() == 0 || 
                   request.resource.data.attendance.values().hasOnly(['present', 'absent', 'excused']);
          }
          
          // Enforce validation on create and update
          allow create: if isValidAttendanceStatus();
          allow update: if isValidAttendanceStatus();
        }
      }
    }
  }
}
```

## API Documentation

### Student API

#### Get Student

```typescript
async function getStudent(schoolId: string, studentId: string): Promise<Student> {
  const studentRef = doc(db, 'schools', schoolId, 'students', studentId);
  const studentDoc = await getDoc(studentRef);
  
  if (!studentDoc.exists()) {
    throw new Error('Student not found');
  }
  
  return {
    id: studentDoc.id,
    ...studentDoc.data(),
  } as Student;
}
```

#### Create/Update Student

```typescript
async function saveStudent(schoolId: string, student: Student): Promise<string> {
  // Validate student type
  if (!['program', 'lesson', 'both'].includes(student.type)) {
    throw new Error('Invalid student type');
  }
  
  const studentData = {
    firstName: student.firstName,
    lastName: student.lastName,
    email: student.email,
    phone: student.phone,
    type: student.type,
    updatedAt: serverTimestamp(),
  };
  
  if (student.id) {
    // Update existing student
    const studentRef = doc(db, 'schools', schoolId, 'students', student.id);
    await updateDoc(studentRef, studentData);
    return student.id;
  } else {
    // Create new student
    const studentsRef = collection(db, 'schools', schoolId, 'students');
    const newStudentRef = await addDoc(studentsRef, {
      ...studentData,
      createdAt: serverTimestamp(),
    });
    return newStudentRef.id;
  }
}
```

### Attendance API

#### Get Program Attendance

```typescript
async function getProgramAttendance(
  schoolId: string,
  programId: string,
  sessionId: string
): Promise<Record<string, string>> {
  const sessionRef = doc(
    db,
    'schools',
    schoolId,
    'programs',
    programId,
    'sessions',
    sessionId
  );
  const sessionDoc = await getDoc(sessionRef);
  
  if (!sessionDoc.exists()) {
    throw new Error('Session not found');
  }
  
  return sessionDoc.data().attendance || {};
}
```

#### Update Program Attendance

```typescript
async function updateProgramAttendance(
  schoolId: string,
  programId: string,
  sessionId: string,
  attendance: Record<string, string>
): Promise<void> {
  // Validate attendance statuses
  const validStatuses = ['present', 'absent', 'excused'];
  const allStatusesValid = Object.values(attendance).every(status => 
    validStatuses.includes(status)
  );
  
  if (!allStatusesValid) {
    throw new Error('Invalid attendance status');
  }
  
  const sessionRef = doc(
    db,
    'schools',
    schoolId,
    'programs',
    programId,
    'sessions',
    sessionId
  );
  
  await updateDoc(sessionRef, {
    attendance,
    updatedAt: serverTimestamp(),
  });
}
```

## Migration

A migration script was created to update existing student records from the old "status" field to the new "type" field:

```typescript
async function migrateStudentStatus(schoolId: string): Promise<void> {
  const studentsRef = collection(db, 'schools', schoolId, 'students');
  const studentsSnapshot = await getDocs(studentsRef);
  
  const batch = writeBatch(db);
  
  studentsSnapshot.forEach(doc => {
    const data = doc.data();
    
    // Skip if already migrated
    if (data.type) return;
    
    // Map old status to new type
    let type = 'lesson'; // Default
    
    if (data.status === 'active' || data.status === 'enrolled') {
      // Check if student is in any programs
      const isInProgram = data.programIds && data.programIds.length > 0;
      const hasLessons = data.lessonCount > 0;
      
      if (isInProgram && hasLessons) {
        type = 'both';
      } else if (isInProgram) {
        type = 'program';
      } else {
        type = 'lesson';
      }
    }
    
    // Update the document
    const studentRef = doc(db, 'schools', schoolId, 'students', doc.id);
    batch.update(studentRef, {
      type,
      // Keep the old status field for backward compatibility
      // but mark it as deprecated
      status_deprecated: data.status,
    });
  });
  
  // Commit the batch
  await batch.commit();
}
```
