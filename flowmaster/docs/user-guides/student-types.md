# Student Types Guide

## Overview

FlowMaster now uses a more specific "type" field for students instead of the generic "status" field. This guide explains the different student types and how to use them effectively.

## Student Types

### Program Student

A program student is enrolled in one or more structured programs offered by your school. These programs typically run for a specific duration (e.g., a semester or year) and have regular sessions.

**Characteristics:**
- Enrolled in structured programs
- Attendance is tracked for each program session
- Progress is measured across the program duration

**Best for:**
- Students in semester-long courses
- Group classes with regular schedules
- Students in performance ensembles

### Lesson Student

A lesson student primarily takes individual lessons that are scheduled on an as-needed basis. These students may not follow a structured program but instead focus on personalized instruction.

**Characteristics:**
- Takes individual or small group lessons
- Lessons are scheduled individually
- Progress is tracked lesson-by-lesson

**Best for:**
- Students taking private lessons
- Occasional or drop-in students
- Students with irregular schedules

### Both

Some students may participate in both structured programs and take individual lessons. This type allows you to track both aspects of their engagement with your school.

**Characteristics:**
- Participates in programs and takes individual lessons
- Attendance is tracked for both contexts
- Provides a comprehensive view of the student's involvement

**Best for:**
- Advanced students with multiple engagements
- Students transitioning between lesson types
- Students supplementing program learning with private instruction

## Managing Student Types

### Setting Student Type

1. When creating a new student:
   - Navigate to the Students section
   - Click "Add Student"
   - Fill in the student details
   - Select the appropriate type from the dropdown menu
   - Complete the form and save

2. Changing an existing student's type:
   - Navigate to the Students section
   - Find the student in the list
   - Click "Edit"
   - Change the type field
   - Save the changes

### Filtering Students by Type

1. In the Students list:
   - Click the "Filter" button
   - Select the desired type from the "Type" dropdown
   - The list will update to show only students of the selected type

### Student Type in Reports

The student type affects how attendance is tracked and reported:

- Program students will appear in program attendance reports
- Lesson students will appear in lesson attendance reports
- Students marked as "Both" will appear in both types of reports

## Best Practices

1. **Be consistent** in how you assign types to ensure accurate reporting.
2. **Review student types periodically** to ensure they still reflect the student's current engagement.
3. **Use the "Both" type sparingly** to maintain clear distinctions between your primary program and lesson students.
4. **Consider your reporting needs** when assigning types, as this will affect how students appear in attendance and progress reports.

## Attendance Tracking by Student Type

### Program Students

For program students, attendance is tracked for each session of the programs they're enrolled in:

1. Navigate to the Programs section
2. Select the program
3. Click on the "Sessions" tab
4. Select a session
5. Mark attendance for each student (Present, Absent, or Excused)

### Lesson Students

For lesson students, attendance is tracked for each individual lesson:

1. Navigate to the Lessons section
2. Select the lesson
3. Update the attendance status (Completed, Cancelled, or No-show)

### Students Marked as "Both"

For students marked as "Both," you'll need to track attendance in both contexts:

- Mark their attendance in program sessions as described above
- Track their individual lesson attendance as described above

## Attendance Reports

The Attendance Reports section provides comprehensive insights into student attendance:

1. Navigate to the Reports section
2. Select "Attendance Reports"
3. Use the tabs to switch between Program and Student views
4. Apply filters to focus on specific programs, students, or date ranges
5. Export the data as needed for further analysis

The dashboard also includes attendance widgets that provide at-a-glance information about attendance rates and recent absences.
