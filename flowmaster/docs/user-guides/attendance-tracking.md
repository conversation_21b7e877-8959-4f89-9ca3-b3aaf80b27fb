# Attendance Tracking Guide

## Overview

FlowMaster's attendance tracking system allows you to monitor student participation across programs and individual lessons. This guide explains how to use the attendance features effectively.

## Taking Attendance

### Program Attendance

1. **Navigate to the program:**
   - Go to the Programs section
   - Select the program you want to manage

2. **Access the session:**
   - Click on the "Sessions" tab
   - Select an existing session or create a new one

3. **Mark attendance:**
   - For each student, select one of the following statuses:
     - **Present**: Student attended the session
     - **Absent**: Student did not attend without prior notice
     - **Excused**: Student was absent with prior notification or valid reason

4. **Batch operations:**
   - Use the "Mark All Present" button to quickly mark all students as present
   - Use the "Mark All Absent" button if needed for a cancelled session
   - Individual statuses can still be adjusted after using batch operations

5. **Save attendance:**
   - Click the "Save" button to record the attendance

### Lesson Attendance

1. **Navigate to the lesson:**
   - Go to the Lessons section
   - Select the lesson you want to manage

2. **Update lesson status:**
   - Set the lesson status to one of the following:
     - **Completed**: Lesson took place as scheduled
     - **Cancelled**: Lesson was cancelled (by instructor or school)
     - **No-show**: Student did not attend without prior notice

3. **Add notes if needed:**
   - Use the notes field to record any relevant information about the attendance

4. **Save changes:**
   - Click the "Save" button to record the attendance

## Viewing Attendance

### Student Profile Attendance

1. **Navigate to the student profile:**
   - Go to the Students section
   - Click on a student's name to view their profile

2. **View attendance summary:**
   - The attendance section shows overall statistics:
     - Attendance rate
     - Total sessions
     - Present/absent/excused counts

3. **View attendance history:**
   - Scroll down to see the attendance timeline
   - Each entry shows the date, program/lesson, and attendance status

4. **Filter attendance history:**
   - Use the date range picker to focus on a specific period
   - Use the program filter to view attendance for a specific program

### Program Attendance Overview

1. **Navigate to the program:**
   - Go to the Programs section
   - Select the program you want to view

2. **View attendance statistics:**
   - The overview tab shows attendance statistics for the program:
     - Overall attendance rate
     - Session counts
     - Student participation metrics

3. **View session attendance:**
   - Click on the "Sessions" tab
   - Select a session to view detailed attendance for that day

## Attendance Reports

### Accessing Reports

1. **Navigate to Reports:**
   - Go to the Reports section
   - Select "Attendance Reports"

2. **Choose report type:**
   - Use the tabs to switch between:
     - **Programs**: Attendance statistics by program
     - **Students**: Attendance statistics by student

### Program Attendance Reports

1. **View overall statistics:**
   - See attendance rates across all programs
   - Compare attendance between different programs
   - Identify trends in program attendance

2. **View program details:**
   - Select a program to see detailed attendance statistics:
     - Session counts
     - Student participation
     - Attendance rates over time

3. **Filter the data:**
   - Use the date range picker to focus on a specific period
   - Filter by program to view specific program data
   - Filter by student to see a specific student's attendance across programs

### Student Attendance Reports

1. **View student statistics:**
   - See attendance rates for all students
   - Identify students with high or low attendance
   - Compare attendance across different student types

2. **View student details:**
   - Select a student to see detailed attendance statistics:
     - Programs attended
     - Attendance rates by program
     - Attendance history timeline

3. **Filter the data:**
   - Use the date range picker to focus on a specific period
   - Filter by student to view specific student data
   - Filter by program to see attendance for a specific program

### Exporting Reports

1. **Generate export:**
   - Click the "Export" button in the reports section
   - The system will generate a CSV file with the current report data

2. **Download the file:**
   - The file will be downloaded to your computer
   - The filename includes the report type and date

3. **Use the data:**
   - Open the CSV file in spreadsheet software like Excel or Google Sheets
   - Use the data for further analysis or reporting

## Dashboard Widgets

### Attendance Overview Widget

The dashboard includes an attendance overview widget that shows:
- Overall attendance rate
- Attendance distribution (present/absent/excused)
- Recent absences with student names and dates

### Attendance Trends Widget

The attendance trends widget shows:
- Attendance rates over time
- Trends in present/absent/excused counts
- Comparative data across different time periods

## Best Practices

1. **Take attendance consistently** at each session to ensure accurate reporting.
2. **Use the correct attendance status** to distinguish between unexcused absences and excused ones.
3. **Review attendance reports regularly** to identify patterns and address issues early.
4. **Follow up on absences** to improve student engagement and retention.
5. **Use the attendance data** when planning programs and scheduling to optimize participation.
6. **Export attendance data periodically** for record-keeping and compliance purposes.
