# 📘 Development Plan: Equipment Rental Module – FlowMaster

## Overview

This document outlines the development plan for implementing an **Equipment Rental** module in FlowMaster. This system will allow managers and instructors to manage equipment inventory, track rentals, assign gear to students during lessons, and monitor equipment usage and condition.

The module is intended to serve two key user flows:
- On-demand rentals for students or walk-in clients.
- Pre-assigned equipment for instructors running scheduled lessons.

---

## 🧪 Usage Scenarios

### ✅ Scenario 1: Student wants to rent equipment

A student arrives at the school and needs to borrow gear for the day (e.g., a kite, harness, wetsuit).

**App flow:**
1. Staff member opens the Equipment Rental section.
2. Creates a new rental:
   - Links to the student (or creates a temporary client)
   - Selects gear to be rented
   - Notes deposit, return date, or condition
3. Rental is saved and inventory is updated.
4. Upon return, the equipment is marked as returned and its condition updated if needed.

---

### ✅ Scenario 2: Instructor needs equipment for a scheduled lesson

An instructor is conducting a group lesson and needs to assign gear to participants.

**App flow:**
1. In<PERSON><PERSON>ctor opens the lesson detail view.
2. Enters the "Equipment" tab.
3. Assigns equipment to students (individually or by group).
4. System checks availability and reserves gear.
5. Gear is marked as issued before the lesson and returned afterward.

---

## 🧱 Data Model

### 1. `EquipmentItem`

```ts
interface EquipmentItem {
  id: string;
  name: string;                 // e.g., "Kite 9m", "Harness M"
  category: string;             // e.g., kite, board, wetsuit, helmet
  size?: string;                // e.g., M, L, 132cm
  serialNumber?: string;
  condition: 'good' | 'damaged' | 'lost';
  available: boolean;
}

2. RentalRecord

interface RentalRecord {
  id: string;
  date: string;
  customerId: string;             // Link to student or guest
  items: string[];                // Array of EquipmentItem IDs
  relatedLessonId?: string;       // Optional: links to a lesson
  returned: boolean;
  notes?: string;
}

📋 Feature Modules

1. Equipment Inventory
	•	View all gear items with:
	•	Category
	•	Availability
	•	Size/condition
	•	Filters: available / damaged / assigned
	•	Actions: add item, mark unavailable, edit details

⸻

2. Rental Management
	•	View all rentals (filter by active / overdue / returned)
	•	Create new rental
	•	Mark gear as returned with optional condition note
	•	Link to student or walk-in

⸻

3. Rentals in Lesson View
	•	New tab: Equipment
	•	Assign gear to students per lesson
	•	Real-time inventory check
	•	“Issue all” / “Return all” buttons

⸻

4. Student Profile Extension
	•	New tab: Rentals
	•	Shows current and past rentals
	•	Status (returned, pending)
	•	Notes about gear usage

⸻

🎨 UI/UX Plan

Main Sidebar
	•	New section: Equipment
	•	Submenu:
	•	Inventory
	•	Rentals

Equipment Inventory Page
	•	Table of all items
	•	Badge for availability
	•	Condition indicator
	•	Add/edit/delete buttons

Rentals Overview Page
	•	Table of all rentals
	•	Filters: today / active / overdue / returned
	•	Action: create new rental

Rentals in Lessons
	•	Tab with table: students × gear
	•	Status indicators (gear assigned/returned)
	•	Click to assign specific item from inventory

⸻

🔧 Backend (Firebase)
	•	equipment/ – Collection for all gear
	•	rentals/ – Collection or subcollection under user or global
	•	lessons/lessonId/rentals/ – Optional link from lesson to rental record

⸻

🧠 Future Features (Phase 2)
	•	QR code for gear items (scan on issue/return)
	•	Automatic alerts for overdue returns
	•	Reporting on gear usage frequency
	•	Tag items as “under maintenance”
	•	Low stock notifications per category
