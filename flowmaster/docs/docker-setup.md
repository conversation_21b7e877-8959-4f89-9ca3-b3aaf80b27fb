# Docker Configuration Documentation

## Overview
This document describes the Docker configuration for the FlowMaster application, covering both development and production environments. The setup uses Docker Compose with separate configurations for different environments, ensuring consistency and isolation.

## Directory Structure
```
flowmaster/
├── Dockerfile              # Production Dockerfile
├── Dockerfile.dev         # Development Dockerfile
├── docker-compose.base.yml    # Shared Docker Compose configuration
├── docker-compose.override.yml # Development overrides
├── docker-compose.prod.yml    # Production configuration
├── nginx.conf             # Nginx configuration for production
├── .env.example          # Environment variable template
└── scripts/
    ├── init-dev.sh       # Development environment setup
    └── deploy-prod.sh    # Production deployment script
```

## Configuration Files

### 1. Dockerfiles

#### Development (Dockerfile.dev)
- Base image: `node:16-alpine`
- Includes development tools:
  - git
  - python3
  - make
  - g++
- Features:
  - Hot reloading
  - Source code mounting
  - Development server
  - Health check endpoint

#### Production (Dockerfile)
- Multi-stage build:
  1. Build stage (node:16-alpine)
     - Builds the React application
     - Handles environment variables
  2. Production stage (nginx:alpine)
     - Serves static files
     - Includes health check
     - Security hardening
     - Gzip compression

### 2. Docker Compose Files

#### Base Configuration (docker-compose.base.yml)
- Shared settings for all environments
- Common environment variables
- Logging configuration
- Health check definitions

#### Development Override (docker-compose.override.yml)
- Development-specific settings
- Firebase emulator integration
- Volume mounts for hot reloading
- Development network configuration
- Resource limits for development

#### Production Configuration (docker-compose.prod.yml)
- Production-specific settings
- Resource limits
- Restart policies
- Production network configuration
- Port mappings

### 3. Nginx Configuration (nginx.conf)
- Static file serving
- SPA routing support
- Security headers
- Gzip compression
- Cache control
- Health check endpoint

## Networks

### Development
- `app-network`: External communication
- `dev-network`: Internal services (Firebase emulators)
  - Isolated from external access
  - Used for service-to-service communication

### Production
- `app-network`: Application network
  - Isolated environment
  - Controlled external access

## Volumes

### Development
- Source code mounting
- Node modules persistence
- Firebase emulator data persistence
  - Name: `flowmaster_firebase_data`
  - Purpose: Preserve emulator state between restarts

## Environment Variables

### Required Variables
```bash
# Firebase Configuration
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# Firebase Emulator (Development Only)
FIREBASE_TOKEN=your_firebase_cli_token
```

## Usage

### Development Environment Setup
1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your configuration values

3. Run the initialization script:
   ```bash
   ./scripts/init-dev.sh
   ```

4. Access the application:
   - Web App: http://localhost:3000
   - Firebase Emulators:
     - Authentication: http://localhost:9099
     - Firestore: http://localhost:8080
     - Functions: http://localhost:5001
     - Storage: http://localhost:9199

### Production Deployment
1. Ensure environment variables are set
2. Run the deployment script:
   ```bash
   ./scripts/deploy-prod.sh
   ```
3. Access the application at http://localhost

## Resource Management

### Development
- App Service:
  - CPU: 2 cores
  - Memory: 2GB
- Firebase Emulator:
  - CPU: 1 core
  - Memory: 1GB

### Production
- App Service:
  - CPU: 0.5 cores
  - Memory: 512MB
  - Restart Policy: on-failure
  - Max Restart Attempts: 3

## Health Checks
- Interval: 30s
- Timeout: 10s
- Start Period: 40s
- Retries: 3
- Endpoints:
  - Development: http://localhost:3000/health
  - Production: http://localhost/health

## Logging
- Driver: json-file
- Rotation:
  - Max Size: 10MB
  - Max Files: 3

## Security Considerations
1. Network Isolation
   - Internal services use isolated networks
   - Limited port exposure

2. Environment Variables
   - Separate development and production configs
   - No sensitive data in image layers

3. Container Hardening
   - Non-root nginx user
   - Read-only root filesystem
   - Security headers
   - Limited resource access

## Troubleshooting

### Common Issues
1. Port Conflicts
   ```bash
   # Check for port usage
   lsof -i :3000
   lsof -i :80
   ```

2. Volume Permissions
   ```bash
   # Reset volume permissions
   docker volume rm flowmaster_firebase_data
   ./scripts/init-dev.sh
   ```

3. Network Issues
   ```bash
   # Recreate networks
   docker network prune
   ./scripts/init-dev.sh
   ```

### Logs
```bash
# View service logs
docker compose logs app
docker compose logs firebase
```

## Maintenance

### Updating Dependencies
1. Update package.json
2. Rebuild containers:
   ```bash
   docker compose build --no-cache
   ```

### Cleaning Up
```bash
# Remove unused resources
docker system prune -a
docker volume prune
```
