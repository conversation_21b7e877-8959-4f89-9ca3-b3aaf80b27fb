# Error Handling Testing Guide

## Overview

This guide outlines best practices for testing error handling in FlowMaster components, particularly focusing on components that use error boundaries and error alerts.

## Test Setup

### 1. Console Error Mocking

Always mock `console.error` when testing error boundaries to prevent test output noise:

```typescript
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});
```

### 2. Window Location Mocking

For components that use page reloads in error recovery:

```typescript
const mockReload = jest.fn();
Object.defineProperty(window, 'location', {
  value: { reload: mockReload },
  writable: true,
});
```

### 3. i18n Setup

When testing components that use translations:

```typescript
import { I18nextProvider } from 'react-i18next';
import i18n from '@/i18n/test-config';

const renderWithI18n = (component: React.ReactNode) => {
  return render(
    <I18nextProvider i18n={i18n}>
      {component}
    </I18nextProvider>
  );
};
```

## Testing Patterns

### 1. Error Boundary Tests

```typescript
describe('ErrorBoundary', () => {
  // Component that throws an error for testing
  const ThrowError = () => {
    throw new Error('Test error');
  };

  it('renders fallback UI when error occurs', () => {
    renderWithI18n(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText('errors:generic.title')).toBeInTheDocument();
    expect(screen.getByText('errors:generic.message')).toBeInTheDocument();
  });

  it('calls error logger when error occurs', () => {
    const mockLogger = jest.fn();
    renderWithI18n(
      <ErrorBoundary onError={mockLogger}>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(mockLogger).toHaveBeenCalled();
  });
});
```

### 2. Error Alert Tests

```typescript
describe('ErrorAlert', () => {
  it('renders nothing when error is null', () => {
    render(<ErrorAlert error={null} />);
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  it('renders string error message', () => {
    render(<ErrorAlert error="Test error message" />);
    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  it('renders Error object message', () => {
    const error = new Error('Test error object');
    render(<ErrorAlert error={error} />);
    expect(screen.getByText(error.message)).toBeInTheDocument();
  });

  it('closes on user interaction', () => {
    render(<ErrorAlert error="Test error" />);
    fireEvent.click(screen.getByRole('button'));
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });
});
```

### 3. API Error Handling Tests

```typescript
describe('API Error Handling', () => {
  it('displays error alert on API failure', async () => {
    const mockError = new Error('API Error');
    mockApiCall.mockRejectedValueOnce(mockError);

    render(<YourComponent />);
    
    await waitFor(() => {
      expect(screen.getByText(mockError.message)).toBeInTheDocument();
    });
  });

  it('handles specific API error types', async () => {
    const mockError = { code: 'PERMISSION_DENIED', message: 'No access' };
    mockApiCall.mockRejectedValueOnce(mockError);

    render(<YourComponent />);
    
    await waitFor(() => {
      expect(screen.getByText('errors:generic.permission')).toBeInTheDocument();
    });
  });
});
```

## Common Testing Scenarios

### 1. Testing Delete Operations

When testing delete operations that involve confirmation dialogs and Firebase operations, follow this pattern:

```typescript
describe('Delete Operations', () => {
  it('handles delete operation with confirmation dialog', async () => {
    // 1. Mock Firebase deleteDoc
    const mockDeleteDoc = jest.fn();
    jest.spyOn(firebaseFirestore, 'deleteDoc').mockImplementation(mockDeleteDoc);
    
    // 2. Mock Firebase doc reference
    const mockDocRef = {} as DocumentReference;
    jest.spyOn(firebaseFirestore, 'doc').mockReturnValue(mockDocRef);

    // 3. Render component with necessary providers
    render(
      <SchoolProvider school={{ id: 'school-123' }}>
        <YourComponent />
      </SchoolProvider>
    );

    // 4. Find and click delete button
    const deleteButtons = screen.getAllByLabelText('Delete');
    fireEvent.click(deleteButtons[0]);

    // 5. Find and interact with confirmation dialog
    const dialog = await screen.findByRole('dialog');
    expect(dialog).toBeInTheDocument();

    const confirmButton = await screen.findByText('Delete');
    fireEvent.click(confirmButton);

    // 6. Verify deletion
    await waitFor(() => {
      // Verify Firebase doc was called with correct path
      expect(firebaseFirestore.doc).toHaveBeenCalledWith(
        expect.any(Object),
        'schools',
        'school-123',
        'collection-name',
        'item-id'
      );
      // Verify deleteDoc was called
      expect(mockDeleteDoc).toHaveBeenCalledWith(mockDocRef);
      // Verify item is removed from UI
      expect(screen.queryByText('Item Name')).not.toBeInTheDocument();
    });
  });

  it('handles delete operation failure', async () => {
    // 1. Mock Firebase deleteDoc to fail
    const mockDeleteDoc = jest.fn().mockRejectedValue(new Error('Delete failed'));
    jest.spyOn(firebaseFirestore, 'deleteDoc').mockImplementation(mockDeleteDoc);

    // 2. Render and trigger delete as before
    render(<YourComponent />);
    
    const deleteButton = screen.getAllByLabelText('Delete')[0];
    fireEvent.click(deleteButton);
    
    const confirmButton = await screen.findByText('Delete');
    fireEvent.click(confirmButton);

    // 3. Verify error handling
    await waitFor(() => {
      expect(screen.getByText('Delete failed')).toBeInTheDocument();
      // Verify item still exists in UI
      expect(screen.getByText('Item Name')).toBeInTheDocument();
    });
  });
});
```

Key Points for Delete Operation Testing:
1. **Mock Firebase Operations**
   - Mock both `deleteDoc` and `doc` functions
   - Prepare for both success and failure scenarios

2. **Dialog Interaction**
   - Wait for dialog to appear using `findByRole('dialog')`
   - Find and click confirmation button
   - Verify dialog disappears after operation

3. **State Updates**
   - Verify item removal from UI on success
   - Verify item retention on failure
   - Check error message display on failure

4. **Cleanup**
   - Reset all mocks between tests
   - Clear any error states
   - Reset component state

Common Issues and Solutions:
- If delete button is not found, check for proper `aria-label` attributes
- If confirmation dialog is not found, ensure proper role attributes
- Use `waitFor` to handle async state updates
- Ensure proper mock cleanup between tests

## Best Practices

1. **Always Test Error States**
   - Test both successful and error scenarios
   - Include tests for different types of errors
   - Verify error recovery mechanisms

2. **Translation Testing**
   - Use translation keys in tests
   - Verify error messages in all supported languages
   - Test fallback behavior

3. **Async Error Handling**
   - Use `waitFor` for async error states
   - Test loading states during error recovery
   - Verify error cleanup

4. **User Interaction**
   - Test error dismissal
   - Verify retry mechanisms
   - Test error state persistence

## Common Pitfalls

1. **Uncaught Console Errors**
   - Always mock console.error in error boundary tests
   - Restore original console.error after tests

2. **Missing Translation Setup**
   - Ensure i18n is properly configured in tests
   - Provide necessary translation keys

3. **Async Timing Issues**
   - Use proper async/await patterns
   - Implement proper cleanup in tests
   - Handle race conditions

4. **Memory Leaks**
   - Clean up subscriptions and timeouts
   - Reset mocks between tests
   - Clear error states properly
