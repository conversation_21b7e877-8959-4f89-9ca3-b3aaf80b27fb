# Migrating from Firebase Emulators to Production

This guide explains how to transition from using Firebase emulators during development to using the real Firebase services in production.

## Overview

During development, we've been using Firebase emulators to simulate Firebase services locally. Now, we're ready to transition to using the real Firebase project (`flowmaster-e3947`).

## Prerequisites

1. Firebase CLI installed:
   ```bash
   npm install -g firebase-tools
   ```

2. Firebase Admin SDK:
   ```bash
   npm install firebase-admin
   ```

3. Firebase service account key (for data migration)

## Step 1: Update Firebase Configuration

We've updated the Firebase configuration to use real Firebase services by default:

1. Modified `src/config/firebase.ts` to only connect to emulators if `REACT_APP_USE_EMULATORS` is set to `'true'`
2. Updated `.env.development` to set `REACT_APP_USE_EMULATORS=false` by default

To switch between emulators and real Firebase:
- Set `REACT_APP_USE_EMULATORS=true` to use emulators
- Set `REACT_APP_USE_EMULATORS=false` to use real Firebase

## Step 2: Update Security Rules

For production use, we need secure Firestore and Storage rules:

1. Updated `firebase/rules/firestore.rules` with production-ready security rules
2. Verified `firebase/rules/storage.rules` for production use

## Step 3: Deploy Rules to Production

Use the provided script to deploy the updated rules to the production Firebase project:

```bash
chmod +x scripts/deploy-firebase-rules.sh
./scripts/deploy-firebase-rules.sh
```

This script will:
1. Check if Firebase CLI is installed
2. Verify you're logged in to Firebase
3. Deploy Firestore and Storage rules to production

## Step 4: Migrate Data (Optional)

If you want to migrate data from the emulators to the production Firebase project, use the provided script:

```bash
# First, export data from emulators (if not already done)
cd firebase
firebase emulators:export ./data

# Then, run the import script
cd ..
node scripts/import-emulator-data.js
```

This script will:
1. Read data from the emulator export directory
2. Import it into the real Firebase project using the Firebase Admin SDK

**Note:** You'll need a Firebase service account key for this step. Place it at `firebase-service-account.json` in the project root or set the `SERVICE_ACCOUNT_KEY_PATH` environment variable.

## Step 5: Create Initial Admin User

For security reasons, you should create an initial admin user in the production Firebase project:

1. Go to the Firebase Console: https://console.firebase.google.com/u/0/project/flowmaster-e3947/overview
2. Navigate to Authentication → Users
3. Add a new user or select an existing user
4. In Firestore, create a document in the `users` collection with the user's UID
5. Set the `role` field to `"admin"` and add any other required fields

## Step 6: Verify the Setup

To verify that everything is working correctly:

1. Start the application with `npm start`
2. Sign in with the admin user you created
3. Verify that you can read and write data to Firestore
4. Check that file uploads to Storage work correctly

## Switching Between Emulators and Production

During development, you might want to switch between emulators and production:

### To use emulators:
1. Set `REACT_APP_USE_EMULATORS=true` in `.env.development`
2. Start the emulators: `cd firebase && firebase emulators:start`
3. Start the application: `npm start`

### To use production:
1. Set `REACT_APP_USE_EMULATORS=false` in `.env.development`
2. Start the application: `npm start`

## Troubleshooting

### Authentication Issues
- Verify that you're using the correct Firebase project
- Check that the user exists in Firebase Authentication
- Ensure the user has the correct role in Firestore

### Firestore Access Issues
- Verify that the security rules are deployed correctly
- Check that the user has the necessary permissions
- Look for errors in the browser console

### Storage Access Issues
- Verify that the security rules are deployed correctly
- Check that the file size and type meet the requirements
- Ensure the user has the necessary permissions

## Additional Resources

- [Firebase Console](https://console.firebase.google.com/u/0/project/flowmaster-e3947/overview)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Storage Security Rules](https://firebase.google.com/docs/storage/security/get-started)
