<!DOCTYPE html>
<html>
<head>
    <title>FlowMaster Architecture Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagram-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        h2 {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .mermaid {
            display: flex;
            justify-content: center;
        }
    </style>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            themeVariables: {
                fontSize: '16px'
            }
        });
    </script>
</head>
<body>
    <h1>FlowMaster Architecture Diagrams</h1>
    
    <h2>Development Environment Architecture</h2>
    <div class="diagram-container">
        <pre class="mermaid">
graph TB
    subgraph "Docker Environment"
        subgraph "App Container"
            React["React App<br/>(port: 3000)"]
            HotReload["Hot Reloading"]
        end
        
        subgraph "Firebase Container"
            EmulatorUI["Emulator UI<br/>(port: 4000)"]
            subgraph "Firebase Emulators"
                Auth["Authentication<br/>(port: 9099)"]
                Firestore["Firestore<br/>(port: 8080)"]
                Storage["Storage<br/>(port: 9199)"]
                Functions["Functions<br/>(port: 5001)"]
            end
        end
        
        Volume["Firebase Data<br/>Volume"]
    end
    
    Developer["Developer"] -->|Code Changes| HotReload
    HotReload -->|Updates| React
    React --> Auth
    React --> Firestore
    React --> Storage
    React --> Functions
    EmulatorUI --> Auth
    EmulatorUI --> Firestore
    EmulatorUI --> Storage
    EmulatorUI --> Functions
    Auth --> Volume
    Firestore --> Volume
    Storage --> Volume
    Functions --> Volume
    
    style Developer fill:#f9f,stroke:#333,stroke-width:2px
    style React fill:#61dafb,stroke:#333,stroke-width:2px
    style EmulatorUI fill:#ffa726,stroke:#333,stroke-width:2px
    style Auth fill:#ffca28,stroke:#333,stroke-width:2px
    style Firestore fill:#ffca28,stroke:#333,stroke-width:2px
    style Storage fill:#ffca28,stroke:#333,stroke-width:2px
    style Functions fill:#ffca28,stroke:#333,stroke-width:2px
    style Volume fill:#81c784,stroke:#333,stroke-width:2px
        </pre>
    </div>

    <h2>Directory Structure</h2>
    <div class="diagram-container">
        <pre class="mermaid">
graph TD
    Root["flowmaster/"] -->|Contains| Src["src/"]
    Root -->|Contains| Firebase["firebase/"]
    Root -->|Contains| Scripts["scripts/"]
    Root -->|Contains| Docs["docs/"]
    Root -->|Contains| Docker["docker-compose*.yml"]
    
    Src -->|Contains| Components["components/"]
    Src -->|Contains| Pages["pages/"]
    Src -->|Contains| Services["services/"]
    Src -->|Contains| Types["types/"]
    
    Firebase -->|Contains| Rules["rules/"]
    Firebase -->|Contains| Tests["__tests__/"]
    Firebase -->|Contains| Config["firebase.json<br/>indexes.json"]
    
    Rules -->|Contains| FSRules["firestore.rules"]
    Rules -->|Contains| STRules["storage.rules"]
    
    Scripts -->|Contains| Init["init-dev.sh"]
    Scripts -->|Contains| Cleanup["cleanup-dev.sh"]
    
    style Root fill:#e3f2fd,stroke:#333,stroke-width:2px
    style Firebase fill:#ffccbc,stroke:#333,stroke-width:2px
    style Src fill:#c8e6c9,stroke:#333,stroke-width:2px
    style Scripts fill:#fff9c4,stroke:#333,stroke-width:2px
    style Docs fill:#f5f5f5,stroke:#333,stroke-width:2px
        </pre>
    </div>

    <h2>Data Flow</h2>
    <div class="diagram-container">
        <pre class="mermaid">
sequenceDiagram
    participant D as Developer
    participant R as React App
    participant A as Auth Emulator
    participant F as Firestore Emulator
    participant S as Storage Emulator
    
    D->>R: Makes Code Changes
    Note over R: Hot Reload Updates App
    
    R->>A: Authentication Request
    A-->>R: Auth Token
    
    R->>F: Query/Update Data
    Note over F: Validates Security Rules
    F-->>R: Data Response
    
    R->>S: File Upload/Download
    Note over S: Validates Storage Rules
    S-->>R: File Response
    
    Note over A,S: All data persisted to<br/>Firebase Data Volume
        </pre>
    </div>

    <h2>Development Workflow</h2>
    <div class="diagram-container">
        <pre class="mermaid">
graph LR
    A[init-dev.sh] --> B[Environment Setup]
    B --> C[Docker Services]
    C --> D[Development]
    
    D --> E[Hot Reload] --> F[React App]
    D --> G[Emulators] --> H[Data Volume]
    D --> I[Security Rules] --> G
    
    D --> J[cleanup-dev.sh]
    
    style A fill:#4caf50,stroke:#333,stroke-width:2px
    style J fill:#f44336,stroke:#333,stroke-width:2px
    style D fill:#2196f3,stroke:#333,stroke-width:2px
    style F fill:#61dafb,stroke:#333,stroke-width:2px
    style G fill:#ffca28,stroke:#333,stroke-width:2px
    style E fill:#81c784,stroke:#333,stroke-width:2px
    style I fill:#fff176,stroke:#333,stroke-width:2px
    style H fill:#90a4ae,stroke:#333,stroke-width:2px
        </pre>
    </div>

    <h2>Security Rules Flow</h2>
    <div class="diagram-container">
        <pre class="mermaid">
flowchart TD
    Client("Client Request")
    Rules("Security Rules")
    Auth("Authentication")
    Role("User Role")
    Format("Data Format")
    Access("Resource Access")
    Decision{"Validation"}
    Allow("Allow Access")
    Deny("Deny Access")

    Client --> Rules
    Rules --> Auth
    Rules --> Role
    Rules --> Format
    Rules --> Access
    
    Auth --> Decision
    Role --> Decision
    Format --> Decision
    Access --> Decision
    
    Decision -->|Pass| Allow
    Decision -->|Fail| Deny

    style Client fill:#64b5f6,stroke:#333,stroke-width:2px
    style Rules fill:#fff176,stroke:#333,stroke-width:2px
    style Auth fill:#e3f2fd,stroke:#333,stroke-width:2px
    style Role fill:#e3f2fd,stroke:#333,stroke-width:2px
    style Format fill:#e3f2fd,stroke:#333,stroke-width:2px
    style Access fill:#e3f2fd,stroke:#333,stroke-width:2px
    style Decision fill:#90a4ae,stroke:#333,stroke-width:2px
    style Allow fill:#81c784,stroke:#333,stroke-width:2px
    style Deny fill:#e57373,stroke:#333,stroke-width:2px
        </pre>
    </div>
</body>
</html>
