<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlowMaster Architecture</title>
    <!-- Add this in the <head> section, before your existing styles -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tree-view {
            padding-left: 20px;
        }

        .tree-item {
            margin: 10px 0;
        }

        .tree-toggle {
            cursor: pointer;
            user-select: none;
        }

        .tree-toggle:before {
            content: '▶';
            display: inline-block;
            margin-right: 6px;
            transition: transform 0.2s;
        }

        .tree-toggle.active:before {
            transform: rotate(90deg);
        }

        .tree-content {
            display: none;
            margin-left: 20px;
        }

        .tree-content.active {
            display: block;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .component-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #ddd;
        }

        .port-info {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .workflow-step {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .diagram-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>FlowMaster Architecture</h1>

    <!-- Development Environment Section -->
    <div class="section">
        <h2>Development Environment</h2>
        <div class="container">
            <div class="component-card">
                <h3>App Container</h3>
                <div class="port-info">React App (port: 3000)</div>
                <div class="port-info">Hot Reloading Enabled</div>
            </div>
            <div class="component-card">
                <h3>Firebase Container</h3>
                <div class="port-info">Emulator UI (port: 4000)</div>
                <div class="port-info">Authentication (port: 9099)</div>
                <div class="port-info">Firestore (port: 8080)</div>
                <div class="port-info">Storage (port: 9199)</div>
                <div class="port-info">Functions (port: 5001)</div>
            </div>
        </div>
    </div>

    <!-- Project Structure Section -->
    <div class="section">
        <h2>Project Structure</h2>
        <div class="tree-view">
            <div class="tree-item">
                <div class="tree-toggle">flowmaster/</div>
                <div class="tree-content">
                    <div class="tree-item">
                        <div class="tree-toggle">src/</div>
                        <div class="tree-content">
                            <div>components/</div>
                            <div>pages/</div>
                            <div>services/</div>
                            <div>types/</div>
                        </div>
                    </div>
                    <div class="tree-item">
                        <div class="tree-toggle">firebase/</div>
                        <div class="tree-content">
                            <div>rules/</div>
                            <div>__tests__/</div>
                            <div>firebase.json</div>
                            <div>indexes.json</div>
                        </div>
                    </div>
                    <div>scripts/</div>
                    <div>docs/</div>
                    <div>docker-compose.yml</div>
                </div>
            </div>
        </div>
        <div class="diagram-container">
            <div class="mermaid">
                graph TD
                    flowmaster[flowmaster/] --> src[src/]
                    flowmaster --> firebase[firebase/]
                    flowmaster --> scripts[scripts/]
                    flowmaster --> docs[docs/]
                    flowmaster --> docker[docker-compose.yml]
                    
                    src --> components[components/]
                    src --> pages[pages/]
                    src --> services[services/]
                    src --> types[types/]
                    
                    firebase --> rules[rules/]
                    firebase --> tests[__tests__/]
                    firebase --> fbjson[firebase.json]
                    firebase --> idxjson[indexes.json]
                    
                    classDef default fill:#f9f9f9,stroke:#333,stroke-width:2px;
                    classDef folder fill:#e3f2fd,stroke:#333,stroke-width:2px;
                    classDef file fill:#c8e6c9,stroke:#333,stroke-width:2px;
                    
                    class flowmaster,src,firebase,scripts,docs,components,pages,services,types,rules,tests folder;
                    class docker,fbjson,idxjson file;
            </div>
        </div>
    </div>

    <!-- Development Workflow Section -->
    <div class="section">
        <h2>Development Workflow</h2>
        <div class="workflow-step">
            <h3>1. Environment Setup</h3>
            <p>Run init-dev.sh to initialize the development environment</p>
        </div>
        <div class="workflow-step">
            <h3>2. Development</h3><!-- Add this after the existing Project Structure section -->
<div class="section">
    <h2>Interactive Project Structure</h2>
    <div class="diagram-container">
        <div class="mermaid">
            graph TD
                %% Root level
                root[flowmaster] --> public
                root --> src
                root --> firebase
                root --> docs
                root --> scripts
                root --> config_files[Configuration Files]

                %% Public folder
                public --> public_files{{"index.html<br/>manifest.json"}}
                public --> locales_pub[locales]
                locales_pub --> en_pub[en]
                locales_pub --> sl_pub[sl]

                %% Src folder structure
                src --> components
                src --> config
                src --> context
                src --> hooks
                src --> i18n
                src --> locales_src[locales]
                src --> pages
                src --> routes
                src --> services
                src --> serviceWorker
                src --> styles
                src --> types
                src --> utils
                src --> app_files{{"App.tsx<br/>index.tsx<br/>routes.tsx"}}

                %% Components subfolders
                components --> auth_comp[auth]
                components --> common_comp[common]
                components --> forms_comp[forms]
                components --> layout_comp[layout]
                components --> shared_comp[shared]

                %% Pages subfolders
                pages --> auth_pages[auth]
                pages --> dashboard_pages[dashboard]
                pages --> lessons_pages[lessons]
                pages --> profile_pages[profile]
                pages --> programs_pages[programs]
                pages --> settings_pages[settings]

                %% Firebase structure
                firebase --> rules
                firebase --> tests[__tests__]
                firebase --> fb_files{{"firebase.json<br/>indexes.json"}}

                %% Styling
                classDef folder fill:#e3f2fd,stroke:#333,stroke-width:2px;
                classDef file fill:#c8e6c9,stroke:#333,stroke-width:2px;
                classDef clickable cursor:pointer;

                class root,public,src,firebase,docs,scripts,components,config,context,hooks,i18n,locales_src,pages,routes,services,serviceWorker,styles,types,utils,auth_comp,common_comp,forms_comp,layout_comp,shared_comp,auth_pages,dashboard_pages,lessons_pages,profile_pages,programs_pages,settings_pages,rules,tests folder,clickable;
                class public_files,app_files,fb_files file;

                %% Click events
                click auth_comp callback "components/auth"
                click common_comp callback "components/common"
                click forms_comp callback "components/forms"
                click layout_comp callback "components/layout"
                click shared_comp callback "components/shared"
                click auth_pages callback "pages/auth"
                click dashboard_pages callback "pages/dashboard"
                click lessons_pages callback "pages/lessons"
                click profile_pages callback "pages/profile"
                click programs_pages callback "pages/programs"
                click settings_pages callback "pages/settings"
        </div>
    </div>
</div>

<!-- Add this script after the existing scripts -->
<script>
    // Existing script content...

    // Add click handler for the diagram
    function handleDiagramClick(nodeId) {
        const contentMap = {
            'components/auth': [
                'AuthLayout.tsx',
                'Permission.tsx',
                'ProtectedRoute.tsx'
            ],
            'components/common': [
                'ErrorBoundary.tsx',
                'LoadingSpinner.tsx'
            ],
            // Add more mappings for other folders
        };

        const content = contentMap[nodeId];
        if (content) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <h3>${nodeId}</h3>
                    <ul>
                        ${content.map(file => `<li>${file}</li>`).join('')}
                    </ul>
                    <button onclick="this.parentElement.parentElement.remove()">Close</button>
                </div>
            `;
            document.body.appendChild(modal);
        }
    }

    // Add these styles to your existing styles
    const newStyles = `
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
        }

        .modal-content ul {
            list-style: none;
            padding: 0;
        }

        .modal-content li {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }

        .modal-content button {
            margin-top: 15px;
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .modal-content button:hover {
            background: #1976d2;
        }
    `;

    // Add the new styles to the document
    const styleSheet = document.createElement("style");
    styleSheet.innerText = newStyles;
    document.head.appendChild(styleSheet);
</script>
            <p>- Hot Reload enabled for React App</p>
            <p>- Firebase Emulators running</p>
            <p>- Security Rules active</p>
        </div>
        <div class="workflow-step">
            <h3>3. Cleanup</h3>
            <p>Run cleanup-dev.sh when finished</p>
        </div>
    </div>

    <script>
        document.querySelectorAll('.tree-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
                toggle.nextElementSibling.classList.toggle('active');
            });
        });
    </script>
    <!-- Add this after the Development Environment section -->
    <div class="section">
        <h2>System Architecture</h2>
        <div class="diagram-container">
            <div class="mermaid">
                graph TB
                    subgraph "Docker Environment"
                        subgraph "App Container"
                            React["React App<br/>(port: 3000)"]
                            HotReload["Hot Reloading"]
                        end
                        
                        subgraph "Firebase Container"
                            EmulatorUI["Emulator UI<br/>(port: 4000)"]
                            Auth["Authentication<br/>(port: 9099)"]
                            Firestore["Firestore<br/>(port: 8080)"]
                            Storage["Storage<br/>(port: 9199)"]
                        end
                    end
                    
                    Developer["Developer"] -->|Code Changes| HotReload
                    HotReload -->|Updates| React
                    React --> Auth
                    React --> Firestore
                    React --> Storage
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Data Flow</h2>
        <div class="diagram-container">
            <div class="mermaid">
                sequenceDiagram
                    participant D as Developer
                    participant R as React App
                    participant A as Auth
                    participant F as Firestore
                    
                    D->>R: Makes Code Changes
                    Note over R: Hot Reload Updates
                    R->>A: Authentication Request
                    A-->>R: Auth Token
                    R->>F: Query/Update Data
                    F-->>R: Data Response
            </div>
        </div>
    </div>

    <!-- Add this at the end of the body, before the existing script -->
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html>