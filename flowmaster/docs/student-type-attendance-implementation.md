# Student Type and Attendance Implementation Plan

This document outlines the plan to replace the generic student "status" with a more specific "type" field and enhance attendance tracking functionality in the FlowMaster application.

## Overview

Currently, students have a generic "status" field that doesn't provide enough context about their primary engagement with the school. By replacing this with a "type" field (program student, lesson student, or both), we can provide more targeted features and improve the user experience for managers tracking attendance.

## Goals

- Replace student "status" with "type" throughout the application
- Enhance attendance tracking for program students
- Provide comprehensive attendance overview in student profiles
- Improve reporting capabilities for managers

## Implementation Plan

### 1. Data Model Changes

- [x] Update Student model to replace "status" with "type" field
  - [x] Define type values: "program", "lesson", "both"
  - [x] Update Firestore schema
  - [x] Create migration script for existing data

- [x] Enhance attendance data model
  - [x] Link attendance records directly to students
  - [x] Add aggregation fields for quick statistics (total sessions, attended, excused, absent)

### 2. Backend Changes

- [x] Update student service functions
  - [x] Modify student form to include type field
  - [x] Add functions to fetch attendance by student

- [x] Update Firestore security rules
  - [x] Ensure proper access to student type field
  - [x] Add rules for attendance aggregation collections

- [x] Create attendance aggregation service
  - [x] Implement functions to calculate attendance statistics
  - [x] Create functions to retrieve attendance history

### 3. UI Changes - Student Management

- [x] Update Add Student form
  - [x] Add type selection dropdown
  - [x] Add appropriate help text explaining the types

- [x] Update Edit Student form
  - [x] Add type field
  - [x] Handle data migration in the UI

- [x] Update Student List
  - [x] Add type column
  - [x] Add type filter to the filter options
  - [x] Update column sorting for the new field

- [x] Update Student Profile
  - [x] Add type display
  - [x] Add conditional UI elements based on student type

### 4. UI Changes - Attendance Tracking

- [x] Create Student Attendance Summary component
  - [x] Design attendance statistics card
  - [x] Implement attendance rate visualization
  - [x] Add program-specific attendance breakdown

- [x] Enhance Student Profile with attendance section
  - [x] Add attendance summary at the top
  - [x] Create attendance history timeline
  - [x] Implement filtering by program and date range

- [x] Update Program Attendance UI
  - [x] Improve attendance taking interface
  - [x] Add quick filters for attendance status
  - [x] Implement batch operations for attendance

### 5. Reporting Features

- [x] Create Attendance Reports page
  - [x] Implement program attendance overview
  - [x] Add student-specific attendance reports
  - [x] Create exportable attendance summaries

- [x] Add Dashboard widgets
  - [x] Add attendance rate widget
  - [x] Create recent absences notification
  - [x] Implement attendance trends chart

### 6. Testing

- [x] Write unit tests for updated models and services
  - [x] Test student type validation
  - [x] Test attendance aggregation functions

- [x] Create integration tests
  - [x] Test end-to-end attendance tracking
  - [x] Verify data consistency across the application

- [x] Perform manual testing
  - [x] Verify all UI changes work as expected
  - [x] Test edge cases for attendance tracking

### 7. Documentation

- [x] Update user documentation
  - [x] Document the new student type system
  - [x] Create guide for attendance tracking

- [x] Update developer documentation
  - [x] Document data model changes
  - [x] Update API documentation

## Implementation Phases

### Phase 1: Data Model and Core Backend Changes
Focus on updating the student model and basic service functions.

### Phase 2: Essential UI Updates
Update the student forms, lists, and basic profile to use the new type field.

### Phase 3: Attendance Enhancement
Implement the attendance tracking features and student profile enhancements.

### Phase 4: Reporting and Analytics
Add the reporting features and dashboard widgets.

## Timeline

- Phase 1: 1-2 days
- Phase 2: 2-3 days
- Phase 3: 3-4 days
- Phase 4: 2-3 days

Total estimated time: 8-12 days
