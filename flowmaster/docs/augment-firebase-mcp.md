# Setting Up Firebase MCP for Augment

This guide explains how to set up and use Firebase MCP (Model Context Protocol) with the Augment VS Code extension for FlowMaster development.

## What is Firebase MCP?

Firebase MCP is a server that implements the Model Context Protocol, allowing AI assistants like Aug<PERSON> to interact directly with your Firebase services, including:

- **Firestore**: Document database operations
- **Storage**: File management with robust upload capabilities
- **Authentication**: User management and verification

## Prerequisites

1. Firebase project with service account credentials
2. Node.js environment
3. Augment VS Code extension installed

## Setup Instructions

### Step 1: Generate a Firebase Service Account Key

1. Go to the [Firebase Console](https://console.firebase.google.com/u/0/project/flowmaster-e3947/overview)
2. Click on the gear icon (⚙️) next to "Project Overview" to open Project settings
3. Go to the "Service accounts" tab
4. Click on "Generate new private key" button
5. Save the JSON file to `~/.flowmaster/firebase-service-account.json`

### Step 2: Run the Setup Script

We've provided a setup script that automates the configuration process:

```bash
chmod +x scripts/start-firebase-mcp.sh
./scripts/start-firebase-mcp.sh
```

The script will:
- Check if the service account key exists
- Prompt you to copy it if needed
- Detect your Firebase project ID
- Start the Firebase MCP server

### Step 3: Configure Augment

The Firebase MCP configuration is already set up in `.vscode/settings.json`. This configuration tells Augment to use the Firebase MCP server with your service account key.

If you need to modify the configuration, you can edit the `mcp.servers` section in `.vscode/settings.json`.

## Using Firebase MCP with Augment

Once the Firebase MCP server is running and Augment is configured, you can use Augment to interact with your Firebase services.

### Example Commands

Here are some examples of what you can ask Augment to do:

#### Firestore Examples

```
Please list all Firestore collections in the FlowMaster database.
```

```
Show me all documents in the 'schools' collection.
```

```
Add a new document to the 'students' collection with the following data:
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "grade": "A",
  "enrollmentDate": "2023-09-01"
}
```

#### Storage Examples

```
List all files in the Firebase Storage bucket.
```

```
Upload this image to Firebase Storage at 'images/logo.png':
[Base64 encoded image content]
```

#### Authentication Examples

```
Get information about the user with email '<EMAIL>'.
```

## Troubleshooting

### Common Issues

#### Service Account Key Not Found

If you see "SERVICE_ACCOUNT_KEY_PATH not set" or "Service account key not found":

1. Make sure the service account key exists at the specified path
2. Check that the path in `.vscode/settings.json` is correct
3. Try running the setup script again

#### Firebase Initialization Failed

If you see "Firebase is not initialized" error:

1. Check that your service account key is valid
2. Ensure the service account has proper permissions for Firebase services
3. Verify that the project ID in the service account key matches your Firebase project

#### Storage Bucket Not Found

If you see "The specified bucket does not exist" error:

1. Verify your bucket name in Firebase Console → Storage
2. Update the `FIREBASE_STORAGE_BUCKET` value in `.vscode/settings.json`

## Additional Resources

- [Firebase MCP GitHub Repository](https://github.com/gannonh/firebase-mcp)
- [Augment Documentation](https://docs.augmentcode.com/)
- [Firebase Documentation](https://firebase.google.com/docs)
