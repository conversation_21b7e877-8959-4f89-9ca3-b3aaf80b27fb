# FlowMaster Quick Start Guide

## Prerequisites
- Docker and Docker Compose installed
- Git installed
- Firebase CLI token (for development)

## Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd flowmaster
```

### 2. Set Up Development Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# You'll need:
# - Firebase configuration
# - Firebase CLI token
nano .env

# Initialize development environment
./scripts/init-dev.sh
```

### 3. Access the Application
- Web Application: http://localhost:3000
- Firebase Emulators:
  - Authentication: http://localhost:9099
  - Firestore: http://localhost:8080
  - Functions: http://localhost:5001
  - Storage: http://localhost:9199

### 4. Development Workflow
- Code changes will automatically reload in the browser
- Firebase emulator data persists between restarts
- Tests run in the Docker environment

### 5. Common Commands
```bash
# View logs
docker compose logs -f

# Restart services
docker compose restart

# Stop environment
docker compose down

# Rebuild containers
docker compose build --no-cache
```

## Additional Resources
- [Detailed Docker Setup Documentation](docker-setup.md)
- [Phase 1 Development Rules](../PHASE_1_DETAILED_PLAN.md)

## Troubleshooting
If you encounter issues:
1. Ensure all required ports are available
2. Check Docker logs for errors
3. Verify environment variables are set correctly
4. Try cleaning up Docker resources:
   ```bash
   docker compose down
   docker system prune
   ./scripts/init-dev.sh
   ```
