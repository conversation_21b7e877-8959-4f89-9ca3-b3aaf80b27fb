# FlowMaster - Comprehensive Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Core Features](#core-features)
4. [Technology Stack](#technology-stack)
5. [Data Models & Firestore Schema](#data-models--firestore-schema)
6. [Authentication & Authorization](#authentication--authorization)
7. [User Roles & Permissions](#user-roles--permissions)
8. [Navigation & User Flows](#navigation--user-flows)
9. [Mobile Application](#mobile-application)
10. [Firebase Integration](#firebase-integration)
11. [Development Guidelines](#development-guidelines)
12. [Deployment & Infrastructure](#deployment--infrastructure)

## Project Overview

FlowMaster is a comprehensive sports school management system designed for managing lessons, programs, students, instructors, and equipment. The application serves multiple user types including administrators, managers, instructors, and students, with role-based access control ensuring appropriate permissions for each user type.

### Key Objectives
- **Lesson Management**: Schedule and manage individual, group, and children's lessons
- **Program Management**: Handle long-term structured learning programs (yearly, seasonal, holiday)
- **Attendance Tracking**: Monitor student attendance across lessons and programs
- **Equipment Management**: Track equipment inventory and rentals
- **Multi-School Support**: Support multiple schools with data isolation
- **Mobile-First Instructor Experience**: Dedicated mobile interface for instructors

## Architecture Overview

FlowMaster follows a modern web application architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React Web App] --> B[Mobile Web App]
        A --> C[Desktop Interface]
    end
    
    subgraph "Authentication"
        D[Firebase Auth] --> E[Custom Claims]
        E --> F[Role-Based Access]
    end
    
    subgraph "Backend Services"
        G[Firebase Functions] --> H[Cloud Firestore]
        G --> I[Firebase Storage]
    end
    
    subgraph "Data Layer"
        H --> J[Users Collection]
        H --> K[Schools Collection]
        H --> L[Lessons Collection]
        H --> M[Programs Collection]
        H --> N[Equipment Collection]
    end
    
    A --> D
    B --> D
    G --> D
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style G fill:#fff3e0
    style H fill:#e8f5e8
```

### Architecture Principles
- **Component-Based Design**: Modular React components with clear responsibilities
- **State Management**: Context API for global state, local state for component-specific data
- **Type Safety**: Full TypeScript implementation with strict type checking
- **Responsive Design**: Mobile-first approach with Material-UI components
- **Performance**: Code splitting, lazy loading, and optimized bundle sizes

## Core Features

### 1. Event Scheduling System
- **Lesson Types**: Individual, Group, Children's lessons
- **Timeline Views**: Daily, Weekly, Monthly calendar views
- **Instructor Assignment**: Assign instructors to lessons and programs
- **Conflict Detection**: Prevent scheduling conflicts
- **Recurring Events**: Support for repeating lessons and program sessions

### 2. Role-Based Access Control
- **Admin**: Full system access, user management, settings
- **Manager**: School-level management, user oversight
- **Instructor**: Lesson management, attendance tracking
- **Student**: View-only access to personal data
- **Client**: Limited access for external users

### 3. Authentication System
- **Firebase Authentication**: Secure user authentication
- **Custom Claims**: Role and school-based authorization
- **Session Management**: Persistent and session-based login options
- **Email Verification**: Required for account activation

### 4. Attendance Tracking
- **Real-time Tracking**: Mark attendance during lessons/programs
- **Historical Data**: View attendance history and trends
- **Reporting**: Generate attendance reports and analytics
- **Mobile Support**: Instructor mobile app for quick attendance marking

### 5. Equipment Management
- **Inventory Tracking**: Monitor equipment status and availability
- **Rental System**: Manage equipment rentals for lessons
- **Condition Monitoring**: Track equipment condition and maintenance
- **Integration**: Link equipment to lessons and students

## Technology Stack

### Frontend
- **React 18**: Modern React with hooks and functional components
- **TypeScript**: Full type safety and enhanced developer experience
- **Material-UI (MUI)**: Comprehensive component library
- **React Router**: Client-side routing with protected routes
- **i18next**: Internationalization support (English, Slovenian)

### Backend & Infrastructure
- **Firebase Authentication**: User authentication and authorization
- **Cloud Firestore**: NoSQL database for real-time data
- **Firebase Functions**: Serverless backend functions
- **Firebase Storage**: File storage for user uploads
- **Firebase Hosting**: Static site hosting

### Development Tools
- **Vite**: Fast build tool and development server
- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting
- **Jest**: Unit testing framework
- **GitHub Actions**: CI/CD pipeline

## Data Models & Firestore Schema

### Core Collections Structure

```mermaid
erDiagram
    SCHOOLS ||--o{ USERS : contains
    SCHOOLS ||--o{ LESSONS : hosts
    SCHOOLS ||--o{ PROGRAMS : offers
    SCHOOLS ||--o{ EQUIPMENT : owns
    
    USERS ||--o{ LESSONS : instructs
    USERS ||--o{ PROGRAMS : participates
    USERS ||--o{ ATTENDANCE : records
    
    PROGRAMS ||--o{ PROGRAM_SESSIONS : contains
    PROGRAMS ||--o{ ATTENDANCE : tracks
    
    LESSONS ||--o{ ATTENDANCE : records
    LESSONS ||--o{ EQUIPMENT_ASSIGNMENTS : uses
    
    EQUIPMENT ||--o{ RENTALS : rented_in
    EQUIPMENT ||--o{ EQUIPMENT_ASSIGNMENTS : assigned_in
    
    SCHOOLS {
        string id PK
        string name
        object settings
        timestamp createdAt
        timestamp updatedAt
    }
    
    USERS {
        string id PK
        string email
        string displayName
        string roleId
        string schoolId FK
        object profile
        timestamp createdAt
        timestamp updatedAt
    }
    
    LESSONS {
        string id PK
        string title
        string type
        string instructorId FK
        array studentIds
        timestamp startTime
        number duration
        string status
        string schoolId FK
    }
    
    PROGRAMS {
        string id PK
        string name
        string type
        object schedule
        array participants
        array instructors
        object pricing
        string schoolId FK
    }
    
    EQUIPMENT {
        string id PK
        string name
        string category
        string condition
        boolean available
        string schoolId FK
    }
```

### Key Data Models

#### User Model
```typescript
interface User {
  id: string;
  email: string;
  displayName: string;
  roleId: 'admin' | 'manager' | 'instructor' | 'student';
  schoolId: string;
  profile: {
    bio?: string;
    phoneNumber?: string;
    avatar?: string;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Lesson Model
```typescript
interface Lesson {
  id: string;
  title: string;
  type: 'individual' | 'group' | 'children';
  discipline: string;
  instructorId: string;
  studentIds: string[];
  startTime: Timestamp;
  duration: number; // minutes
  level: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
  programId?: string; // if part of a program
}
```

#### Program Model
```typescript
interface Program {
  id: string;
  name: string;
  type: 'yearly' | 'seasonal' | 'camp';
  description: string;
  schedule: {
    days: WeekDay[];
    startTime: string;
    endTime: string;
    startDate: Timestamp;
    endDate: Timestamp;
  };
  participants: string[]; // student IDs
  instructors: string[]; // instructor IDs
  pricing: {
    totalFee: number;
    currency: string;
    paymentStatus: Record<string, PaymentStatus>;
  };
  status: 'active' | 'completed' | 'archived';
}
```

## Authentication & Authorization

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as React App
    participant FA as Firebase Auth
    participant FS as Firestore
    participant FC as Firebase Functions
    
    U->>A: Login Request
    A->>FA: signInWithEmailAndPassword()
    FA->>A: Firebase User + ID Token
    A->>FS: Fetch User Document
    FS->>A: User Profile Data
    A->>FC: Verify Custom Claims
    FC->>A: Role & Permissions
    A->>U: Authenticated User Session
```

### Custom Claims Structure
```typescript
interface CustomClaims {
  role: 'admin' | 'manager' | 'instructor' | 'student';
  schoolId: string;
  permissions: Permission[];
}
```

### Firebase Functions for Auth
- `createUser`: Admin function to create new users with roles
- `setCustomUserClaims`: Update user roles and permissions
- `onUserCreated`: Trigger to set default claims for new users

## User Roles & Permissions

### Permission Matrix

```mermaid
graph LR
    subgraph "Admin Permissions"
        A1[manage_users]
        A2[manage_lessons]
        A3[manage_programs]
        A4[manage_settings]
        A5[manage_equipment]
        A6[view_all]
    end
    
    subgraph "Manager Permissions"
        M1[manage_lessons]
        M2[manage_programs]
        M3[view_users]
        M4[manage_equipment]
    end
    
    subgraph "Instructor Permissions"
        I1[view_lessons]
        I2[manage_attendance]
        I3[view_programs]
        I4[view_students]
    end
    
    subgraph "Student Permissions"
        S1[view_lessons]
        S2[view_programs]
        S3[edit_profile]
    end
    
    style A1 fill:#ff6b6b
    style M1 fill:#4ecdc4
    style I1 fill:#45b7d1
    style S1 fill:#96ceb4
```

### Role Definitions
- **Admin**: Complete system control, multi-school management
- **Manager**: School-level administration, user management within school
- **Instructor**: Lesson delivery, attendance tracking, student interaction
- **Student**: Personal data access, lesson/program viewing
- **Client**: External user with limited lesson access

## Navigation & User Flows

### Main Application Flow

```mermaid
graph TD
    A[Login] --> B{Role Check}
    
    B -->|Admin| C[Admin Dashboard]
    B -->|Manager| D[Manager Dashboard]
    B -->|Instructor| E[Instructor Dashboard]
    B -->|Student| F[Student Dashboard]
    
    C --> G[User Management]
    C --> H[School Settings]
    C --> I[System Configuration]
    
    D --> J[Lesson Management]
    D --> K[Program Management]
    D --> L[Reports]
    
    E --> M[My Lessons]
    E --> N[Attendance Tracking]
    E --> O[Student Management]
    
    F --> P[My Schedule]
    F --> Q[My Programs]
    F --> R[Profile]
    
    style A fill:#e1f5fe
    style C fill:#ffebee
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#fff3e0
```

### Mobile Navigation Flow

```mermaid
graph LR
    A[Mobile Login] --> B[Bottom Navigation]
    
    B --> C[Dashboard]
    B --> D[Schedule]
    B --> E[Students]
    B --> F[Programs]
    B --> G[Notifications]
    B --> H[Settings]
    
    C --> I[Quick Actions]
    D --> J[Timeline View]
    E --> K[Student List]
    F --> L[Program List]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
```

## Mobile Application

### Mobile-First Design
- **Responsive Layout**: Adapts to mobile, tablet, and desktop screens
- **Touch-Optimized**: Large touch targets, swipe gestures
- **Offline Capability**: Service worker for offline functionality
- **Progressive Web App**: Installable on mobile devices

### Mobile-Specific Features
- **Bottom Navigation**: Easy thumb navigation on mobile devices
- **Instructor Portal**: Dedicated mobile interface for instructors
- **Quick Actions**: Rapid access to common tasks
- **Attendance Marking**: Streamlined attendance tracking interface

### Mobile Components
- `MobileLayout`: Responsive layout with bottom navigation
- `MobileDashboard`: Mobile-optimized dashboard
- `MobileSchedule`: Touch-friendly schedule interface
- `MobileAttendance`: Quick attendance marking

## Firebase Integration

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // School-based data isolation
    match /schools/{schoolId}/lessons/{lessonId} {
      allow read, write: if request.auth != null 
        && request.auth.token.schoolId == schoolId;
    }
    
    // Role-based access control
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && (request.auth.uid == userId 
        || 'admin' in request.auth.token.roles);
    }
  }
}
```

### Cloud Functions
- **Authentication Functions**: User creation, role management
- **Data Validation**: Server-side data validation
- **Notifications**: Email and push notifications
- **Scheduled Tasks**: Automated cleanup and maintenance

### Real-time Features
- **Live Updates**: Real-time lesson and attendance updates
- **Collaborative Editing**: Multiple users editing simultaneously
- **Instant Notifications**: Real-time alerts for schedule changes

## Development Guidelines

### Code Organization
```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Dashboard widgets
│   ├── lessons/        # Lesson-related components
│   ├── mobile/         # Mobile-specific components
│   └── shared/         # Shared utility components
├── pages/              # Page-level components
├── hooks/              # Custom React hooks
├── context/            # React context providers
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── config/             # Configuration files
```

### Best Practices
- **Component Design**: Single responsibility, reusable components
- **State Management**: Lift state up, use context for global state
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Performance**: Lazy loading, memoization, optimized re-renders
- **Testing**: Unit tests for utilities, integration tests for components

### Internationalization
- **Multi-language Support**: English and Slovenian translations
- **Translation Keys**: Organized by feature and component
- **Dynamic Loading**: Language files loaded on demand
- **Fallback Strategy**: English as default fallback language

## Deployment & Infrastructure

### Environment Configuration
- **Development**: Local Firebase emulators
- **Staging**: Firebase staging project
- **Production**: Firebase production project with custom domain

### CI/CD Pipeline
```mermaid
graph LR
    A[Git Push] --> B[GitHub Actions]
    B --> C[Build & Test]
    C --> D[Deploy to Staging]
    D --> E[Integration Tests]
    E --> F[Deploy to Production]
    
    style A fill:#e1f5fe
    style F fill:#e8f5e8
```

### Monitoring & Analytics
- **Firebase Analytics**: User behavior tracking
- **Performance Monitoring**: App performance metrics
- **Error Tracking**: Crash reporting and error logging
- **Usage Analytics**: Feature usage and user engagement

## Key User Flows

### Admin Flow: User Management

```mermaid
flowchart TD
    A[Admin Login] --> B[Navigate to Settings]
    B --> C[User Management]
    C --> D[Create New User]
    D --> E[Set Role & School]
    E --> F[Firebase Function: createUser]
    F --> G[User Created in Auth]
    G --> H[Custom Claims Set]
    H --> I[User Document Created]
    I --> J[Email Invitation Sent]

    style A fill:#ffebee
    style F fill:#fff3e0
    style J fill:#e8f5e8
```

### Instructor Flow: Attendance Tracking

```mermaid
flowchart TD
    A[Instructor Mobile Login] --> B[View Today's Schedule]
    B --> C[Select Lesson/Program]
    C --> D[Mark Attendance]
    D --> E[Update Firestore]
    E --> F[Real-time Sync]
    F --> G[Attendance Recorded]

    style A fill:#e8f5e8
    style D fill:#fff3e0
    style G fill:#e1f5fe
```

### Student Flow: Schedule Access

```mermaid
flowchart TD
    A[Student Login] --> B[Dashboard View]
    B --> C[My Schedule]
    C --> D[View Lessons]
    D --> E[View Programs]
    E --> F[Check Attendance]
    F --> G[Profile Management]

    style A fill:#fff3e0
    style C fill:#e1f5fe
    style G fill:#f3e5f5
```

## Equipment Management System

### Equipment Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Available
    Available --> Rented : Assign to Lesson
    Rented --> Available : Return Equipment
    Available --> Maintenance : Needs Repair
    Maintenance --> Available : Repair Complete
    Available --> Damaged : Condition Change
    Damaged --> Maintenance : Send for Repair
    Damaged --> Lost : Cannot Locate
    Lost --> [*]

    Available : Good Condition
    Rented : In Use
    Maintenance : Being Repaired
    Damaged : Needs Attention
    Lost : Missing/Stolen
```

### Equipment Integration with Lessons

```mermaid
sequenceDiagram
    participant I as Instructor
    participant L as Lesson System
    participant E as Equipment System
    participant S as Student

    I->>L: Create Lesson
    L->>E: Check Equipment Availability
    E->>L: Available Equipment List
    I->>E: Assign Equipment to Students
    E->>E: Mark Equipment as Rented
    L->>S: Lesson with Equipment Assignment
    S->>I: Lesson Complete
    I->>E: Return Equipment
    E->>E: Mark Equipment as Available
```

## Program Management Deep Dive

### Program Types and Scheduling

```mermaid
graph TB
    subgraph "Program Types"
        A[Yearly Programs]
        B[Seasonal Programs]
        C[Camp Programs]
    end

    subgraph "Yearly Programs"
        A1[School Year Courses]
        A2[Annual Memberships]
        A3[Long-term Training]
    end

    subgraph "Seasonal Programs"
        B1[Winter Sports]
        B2[Summer Activities]
        B3[Spring Training]
    end

    subgraph "Camp Programs"
        C1[Holiday Camps]
        C2[Weekend Workshops]
        C3[Intensive Courses]
    end

    A --> A1
    A --> A2
    A --> A3
    B --> B1
    B --> B2
    B --> B3
    C --> C1
    C --> C2
    C --> C3

    style A fill:#ffebee
    style B fill:#e8f5e8
    style C fill:#e1f5fe
```

### Program Session Management

```mermaid
erDiagram
    PROGRAM ||--o{ PROGRAM_SESSION : contains
    PROGRAM_SESSION ||--o{ ATTENDANCE_RECORD : tracks
    PROGRAM_SESSION ||--o{ SKILL_ASSESSMENT : evaluates

    PROGRAM {
        string id PK
        string name
        string type
        object schedule
        array participants
        array instructors
        object pricing
    }

    PROGRAM_SESSION {
        string id PK
        string programId FK
        timestamp date
        object attendance
        string notes
        object skills
        boolean isMakeup
        object makeupDetails
    }

    ATTENDANCE_RECORD {
        string id PK
        string sessionId FK
        string studentId FK
        string status
        string notes
        timestamp recordedAt
    }

    SKILL_ASSESSMENT {
        string id PK
        string sessionId FK
        string studentId FK
        array skillsCompleted
        string progressNotes
        number rating
    }
```

## Multi-School Architecture

### Data Isolation Strategy

```mermaid
graph TB
    subgraph "School A Data"
        A1[Users A]
        A2[Lessons A]
        A3[Programs A]
        A4[Equipment A]
    end

    subgraph "School B Data"
        B1[Users B]
        B2[Lessons B]
        B3[Programs B]
        B4[Equipment B]
    end

    subgraph "Shared Resources"
        S1[System Settings]
        S2[Global Templates]
        S3[Audit Logs]
    end

    subgraph "Security Layer"
        F1[Firestore Rules]
        F2[Custom Claims]
        F3[School ID Validation]
    end

    F1 --> A1
    F1 --> B1
    F2 --> A2
    F2 --> B2
    F3 --> A3
    F3 --> B3

    style A1 fill:#ffebee
    style B1 fill:#e8f5e8
    style S1 fill:#fff3e0
    style F1 fill:#e1f5fe
```

### School Management Flow

```mermaid
flowchart TD
    A[Super Admin] --> B[Create School]
    B --> C[Set School Configuration]
    C --> D[Create School Admin]
    D --> E[School Admin Login]
    E --> F[Configure School Settings]
    F --> G[Add Instructors & Staff]
    G --> H[Setup Programs & Lessons]
    H --> I[Onboard Students]
    I --> J[School Operational]

    style A fill:#ffebee
    style E fill:#e8f5e8
    style J fill:#e1f5fe
```

## Performance Optimization

### Code Splitting Strategy

```mermaid
graph TB
    subgraph "Main Bundle"
        A[Core Components]
        B[Authentication]
        C[Routing]
    end

    subgraph "Feature Bundles"
        D[Dashboard Bundle]
        E[Lessons Bundle]
        F[Programs Bundle]
        G[Equipment Bundle]
        H[Mobile Bundle]
    end

    subgraph "Vendor Bundles"
        I[React/ReactDOM]
        J[Material-UI]
        K[Firebase SDK]
    end

    A --> D
    A --> E
    A --> F
    A --> G
    A --> H

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style I fill:#f3e5f5
```

### Caching Strategy

```mermaid
graph LR
    A[User Request] --> B{Cache Check}
    B -->|Hit| C[Return Cached Data]
    B -->|Miss| D[Fetch from Firestore]
    D --> E[Update Cache]
    E --> F[Return Fresh Data]

    subgraph "Cache Layers"
        G[Browser Cache]
        H[Service Worker Cache]
        I[Memory Cache]
        J[Firestore Offline Cache]
    end

    C --> G
    F --> H

    style A fill:#e1f5fe
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

## Security Implementation

### Authentication Security Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Client App
    participant FA as Firebase Auth
    participant FS as Firestore
    participant FR as Firestore Rules

    U->>C: Login Attempt
    C->>FA: Authenticate User
    FA->>C: ID Token + Custom Claims
    C->>FS: Request Data
    FS->>FR: Validate Token & Claims
    FR->>FS: Authorization Decision
    FS->>C: Authorized Data
    C->>U: Display Data

    Note over FR: School ID validation
    Note over FR: Role-based permissions
    Note over FR: Resource ownership check
```

### Data Access Control Matrix

| Role | Users | Lessons | Programs | Equipment | Settings |
|------|-------|---------|----------|-----------|----------|
| **Admin** | Full | Full | Full | Full | Full |
| **Manager** | School | School | School | School | Limited |
| **Instructor** | View | Assigned | Assigned | View | None |
| **Student** | Own | Enrolled | Enrolled | None | Profile |

## Testing Strategy

### Testing Pyramid

```mermaid
graph TB
    subgraph "Testing Levels"
        A[Unit Tests - 70%]
        B[Integration Tests - 20%]
        C[E2E Tests - 10%]
    end

    subgraph "Unit Tests"
        A1[Utility Functions]
        A2[Custom Hooks]
        A3[Component Logic]
    end

    subgraph "Integration Tests"
        B1[API Integration]
        B2[Component Integration]
        B3[Firebase Integration]
    end

    subgraph "E2E Tests"
        C1[User Workflows]
        C2[Critical Paths]
        C3[Cross-browser Testing]
    end

    A --> A1
    A --> A2
    A --> A3
    B --> B1
    B --> B2
    B --> B3
    C --> C1
    C --> C2
    C --> C3

    style A fill:#e8f5e8
    style B fill:#fff3e0
    style C fill:#ffebee
```

## Future Roadmap

### Planned Features

```mermaid
gantt
    title FlowMaster Development Roadmap
    dateFormat  YYYY-MM-DD
    section Phase 1
    Mobile App Enhancement    :2024-01-01, 2024-03-31
    Advanced Reporting        :2024-02-01, 2024-04-30
    section Phase 2
    Payment Integration       :2024-04-01, 2024-06-30
    Advanced Analytics        :2024-05-01, 2024-07-31
    section Phase 3
    API for Third-party       :2024-07-01, 2024-09-30
    AI-powered Insights       :2024-08-01, 2024-10-31
```

### Technology Evolution
- **React 19**: Upgrade to latest React features
- **Next.js Migration**: Consider SSR for better SEO
- **GraphQL**: Implement for more efficient data fetching
- **PWA Enhancement**: Improve offline capabilities
- **AI Integration**: Smart scheduling and recommendations

---

*This comprehensive documentation serves as the definitive guide for understanding, developing, and maintaining the FlowMaster application. It should be updated regularly as the system evolves and new features are added.*
