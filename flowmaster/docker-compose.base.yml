version: '3.8'

x-logging: &default-logging
  options:
    max-size: "10m"
    max-file: "3"
  driver: json-file

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true  # Enable hot reloading
      - REACT_APP_FIREBASE_API_KEY=${REACT_APP_FIREBASE_API_KEY}
      - REACT_APP_FIREBASE_AUTH_DOMAIN=${REACT_APP_FIREBASE_AUTH_DOMAIN}
      - REACT_APP_FIREBASE_PROJECT_ID=${REACT_APP_FIREBASE_PROJECT_ID}
      - REACT_APP_FIREBASE_STORAGE_BUCKET=${REACT_APP_FIREBASE_STORAGE_BUCKET}
      - REACT_APP_FIREBASE_MESSAGING_SENDER_ID=${REACT_APP_FIREBASE_MESSAGING_SENDER_ID}
      - REACT_APP_FIREBASE_APP_ID=${REACT_APP_FIREBASE_APP_ID}
      - REACT_APP_FIREBASE_MEASUREMENT_ID=${REACT_APP_FIREBASE_measurementId}
    logging: *default-logging
    stdin_open: true  # Enable interactive mode for React DevTools
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - firebase

  firebase:
    image: andreysenov/firebase-tools
    ports:
      - "9099:9099"  # Auth
      - "5001:5001"  # Functions
      - "8080:8080"  # Firestore
      - "9199:9199"  # Storage
      - "4000:4000"  # Emulator UI
      - "9000:9000"  # Realtime Database
      - "4400:4400"  # Emulator Hub
      - "4500:4500"  # Reserved
      - "9150:9150"  # Firestore UI websocket
    volumes:
      - ./firebase:/home/<USER>/app  # Mount Firebase directory as the app root
      - firebase_data:/home/<USER>/.firebase  # Persist emulator data
    working_dir: /home/<USER>/app
    environment:
      - FIREBASE_TOKEN=${FIREBASE_TOKEN}
      - HOME=/home/<USER>
      - FIRESTORE_EMULATOR_HOST=localhost:8080
      - FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
      - FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199
      - FIREBASE_DATABASE_EMULATOR_HOST=localhost:9000
    command: firebase emulators:start --project demo-project --import=/home/<USER>/.firebase/data --export-on-exit=/home/<USER>/.firebase/data
    logging: *default-logging
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  firebase_data:  # Named volume for Firebase emulator data persistence
