version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - REACT_APP_FIREBASE_API_KEY=${FIREBASE_API_KEY}
        - REACT_APP_FIREBASE_AUTH_DOMAIN=${FIREBASE_AUTH_DOMAIN}
        - REACT_APP_FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
        - REACT_APP_FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
        - REACT_APP_FIREBASE_MESSAGING_SENDER_ID=${FIREBASE_MESSAGING_SENDER_ID}
        - REACT_APP_FIREBASE_APP_ID=${FIREBASE_APP_ID}
    ports:
      - "80:80"
    restart: unless-stopped
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

networks:
  app-network:
    driver: bridge
