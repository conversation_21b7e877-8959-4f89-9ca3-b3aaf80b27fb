{"env": {"browser": true, "es2021": true, "jest": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2021, "sourceType": "module", "project": ["./tsconfig.json", "./firebase/tsconfig.json"]}, "settings": {"import/resolver": {"typescript": {}}, "react": {"version": "detect"}}, "plugins": ["react", "@typescript-eslint", "prettier", "import"], "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "react/prop-types": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}}