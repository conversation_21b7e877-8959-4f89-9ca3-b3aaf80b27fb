#!/usr/bin/env node

/**
 * Extreme CI Build Script
 * 
 * This script takes a completely different approach to building the app in CI environments
 * by breaking down the build process into multiple smaller steps that use less memory.
 * 
 * It bypasses the standard React build process and uses webpack directly with minimal
 * configuration to avoid memory issues.
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const os = require('os');

// Log system information
console.log('CI Extreme Build - Starting');
console.log(`Node.js version: ${process.version}`);
console.log(`Total memory: ${Math.floor(os.totalmem() / (1024 * 1024))}MB`);
console.log(`Free memory: ${Math.floor(os.freemem() / (1024 * 1024))}MB`);
console.log(`CPUs: ${os.cpus().length}`);

// Clean up environment
console.log('Cleaning up environment...');
try {
  if (fs.existsSync(path.join(process.cwd(), 'build'))) {
    fs.rmSync(path.join(process.cwd(), 'build'), { recursive: true, force: true });
  }
  if (fs.existsSync(path.join(process.cwd(), 'node_modules/.cache'))) {
    fs.rmSync(path.join(process.cwd(), 'node_modules/.cache'), { recursive: true, force: true });
  }
  execSync('npm cache clean --force', { stdio: 'inherit' });
} catch (error) {
  console.error('Error cleaning up:', error);
}

// Create build directory structure
console.log('Creating build directory structure...');
const buildDir = path.join(process.cwd(), 'build');
const staticDir = path.join(buildDir, 'static');
const jsDir = path.join(staticDir, 'js');
const cssDir = path.join(staticDir, 'css');

fs.mkdirSync(buildDir, { recursive: true });
fs.mkdirSync(staticDir, { recursive: true });
fs.mkdirSync(jsDir, { recursive: true });
fs.mkdirSync(cssDir, { recursive: true });

// Copy public files
console.log('Copying public files...');
try {
  execSync('cp -r public/* build/', { stdio: 'inherit' });
} catch (error) {
  console.error('Error copying public files:', error);
}

// Create a minimal webpack config
console.log('Creating minimal webpack config...');
const webpackConfig = `
const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  mode: 'production',
  entry: './src/index.tsx',
  output: {
    path: path.resolve(__dirname, 'build/static/js'),
    filename: 'main.[contenthash:8].js',
    chunkFilename: '[name].[contenthash:8].chunk.js'
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx'],
    alias: {
      '@components': path.resolve(__dirname, 'src/components'),
      '@services': path.resolve(__dirname, 'src/services'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@context': path.resolve(__dirname, 'src/context'),
      '@pages': path.resolve(__dirname, 'src/pages'),
    }
  },
  module: {
    rules: [
      {
        test: /\\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              '@babel/preset-env',
              '@babel/preset-react',
              '@babel/preset-typescript'
            ],
            plugins: [
              '@babel/plugin-transform-runtime'
            ]
          }
        }
      },
      {
        test: /\\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  optimization: {
    minimize: true,
    minimizer: [new TerserPlugin()],
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 20000,
      cacheGroups: {
        vendor: {
          test: /[\\\\/]node_modules[\\\\/]/,
          name(module) {
            const packageName = module.context.match(/[\\\\/]node_modules[\\\\/](.*?)([\\\\/]|$)/)[1];
            return \`npm.\${packageName.replace('@', '')}\`;
          }
        }
      }
    }
  },
  performance: { hints: false }
};
`;

fs.writeFileSync(path.join(process.cwd(), 'webpack.ci.js'), webpackConfig);

// Run webpack with minimal config and increased memory
console.log('Running webpack with minimal config...');
try {
  execSync('node --max-old-space-size=4096 ./node_modules/.bin/webpack --config webpack.ci.js', {
    env: {
      ...process.env,
      NODE_OPTIONS: '--max-old-space-size=4096',
      GENERATE_SOURCEMAP: 'false',
      CI: 'false',
      DISABLE_ESLINT_PLUGIN: 'true',
      TSC_COMPILE_ON_ERROR: 'true',
      NODE_ENV: 'production',
      BABEL_ENV: 'production',
    },
    stdio: 'inherit'
  });
} catch (error) {
  console.error('Error running webpack:', error);
  process.exit(1);
}

// Create a minimal HTML file if it doesn't exist
console.log('Ensuring index.html exists...');
if (!fs.existsSync(path.join(buildDir, 'index.html'))) {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="FlowMaster Application" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>FlowMaster</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script src="%PUBLIC_URL%/static/js/main.js"></script>
  </body>
</html>
  `;
  fs.writeFileSync(path.join(buildDir, 'index.html'), htmlContent);
}

console.log('CI Extreme Build - Completed successfully');
