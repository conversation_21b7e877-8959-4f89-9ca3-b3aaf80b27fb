#!/bin/bash

# This script is used to build the application in CI/CD environments
# It disables source maps, TypeScript checking, and ESLint to reduce memory usage

# Set environment variables
export NODE_OPTIONS="--max-old-space-size=8192"
export GENERATE_SOURCEMAP=false
export DISABLE_ESLINT_PLUGIN=true
export DISABLE_TYPESCRIPT=true
export CI=false

# Run the build
echo "Running optimized build for CI/CD..."
npm run build

echo "Build process completed."
