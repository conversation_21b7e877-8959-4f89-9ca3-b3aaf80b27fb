#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Testing Firebase MCP server...${NC}"

# Check if service account key exists
SERVICE_ACCOUNT_PATH="/Users/<USER>/dev/FlowMaster/flowmaster/firebase-service-account.json"

if [ ! -f "$SERVICE_ACCOUNT_PATH" ]; then
    echo -e "${RED}Error: Service account key not found at $SERVICE_ACCOUNT_PATH${NC}"
    exit 1
fi

# Get Firebase project ID from service account key
PROJECT_ID=$(grep -o '"project_id": "[^"]*' "$SERVICE_ACCOUNT_PATH" | cut -d'"' -f4)
if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}Error: Could not detect project ID from service account key${NC}"
    exit 1
else
    echo -e "${GREEN}Detected Firebase project ID: $PROJECT_ID${NC}"
fi

# Set storage bucket name
STORAGE_BUCKET="${PROJECT_ID}.appspot.com"
echo -e "${YELLOW}Using storage bucket: $STORAGE_BUCKET${NC}"

# Test Firebase MCP server
echo -e "${GREEN}Starting Firebase MCP server for testing...${NC}"
echo -e "${YELLOW}This will run for 10 seconds and then exit${NC}"

# Run the MCP server in the background with a PID file
echo -e "${YELLOW}Starting Firebase MCP server in the background...${NC}"
SERVICE_ACCOUNT_KEY_PATH="$SERVICE_ACCOUNT_PATH" FIREBASE_STORAGE_BUCKET="$STORAGE_BUCKET" npx -y @gannonh/firebase-mcp &
MCP_PID=$!

# Wait for a few seconds to see if the server starts
echo -e "${YELLOW}Waiting for 5 seconds to check if the server starts...${NC}"
sleep 5

# Check if the process is still running
if kill -0 $MCP_PID 2>/dev/null; then
    echo -e "${GREEN}Firebase MCP server started successfully!${NC}"
    echo -e "${GREEN}Your Firebase MCP configuration is working correctly${NC}"
    echo -e "${YELLOW}Now you can use Augment with Firebase MCP${NC}"
    echo -e "${YELLOW}Try asking Augment: 'Please test all Firebase MCP tools'${NC}"

    # Kill the process
    echo -e "${YELLOW}Stopping the test server...${NC}"
    kill $MCP_PID
else
    echo -e "${RED}Error: Firebase MCP server failed to start or stopped immediately${NC}"
    echo -e "${YELLOW}Check the error messages above for more information${NC}"
fi

# Make the script executable
chmod +x "$0"
