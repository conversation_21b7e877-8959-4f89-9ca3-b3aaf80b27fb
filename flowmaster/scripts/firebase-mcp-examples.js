/**
 * Firebase MCP Examples for FlowMaster
 * 
 * This file contains examples of Firebase operations that can be performed
 * using Firebase MCP with an AI assistant like Augment Code.
 * 
 * Note: This file is for documentation purposes only and is not meant to be executed directly.
 * Instead, you should ask your AI assistant to perform these operations using the MCP tools.
 */

// ===== Firestore Examples =====

/**
 * Example 1: List all collections in Firestore
 * 
 * Ask your AI assistant:
 * "Please list all Firestore collections in the FlowMaster database."
 * 
 * The assistant will use the firestore_list_collections tool to retrieve all collections.
 */

/**
 * Example 2: Query documents in a collection
 * 
 * Ask your AI assistant:
 * "Show me all documents in the 'schools' collection."
 * 
 * The assistant will use the firestore_list_documents tool to retrieve documents.
 */

/**
 * Example 3: Get a specific document
 * 
 * Ask your AI assistant:
 * "Get the document with ID 'abc123' from the 'students' collection."
 * 
 * The assistant will use the firestore_get_document tool to retrieve the document.
 */

/**
 * Example 4: Add a new document
 * 
 * Ask your AI assistant:
 * "Add a new document to the 'students' collection with the following data:
 * {
 *   'name': '<PERSON>',
 *   'email': '<EMAIL>',
 *   'grade': 'A',
 *   'enrollmentDate': '2023-09-01'
 * }"
 * 
 * The assistant will use the firestore_add_document tool to create the document.
 */

/**
 * Example 5: Update an existing document
 * 
 * Ask your AI assistant:
 * "Update the document with ID 'abc123' in the 'students' collection to change the grade to 'B'."
 * 
 * The assistant will use the firestore_update_document tool to update the document.
 */

/**
 * Example 6: Delete a document
 * 
 * Ask your AI assistant:
 * "Delete the document with ID 'abc123' from the 'students' collection."
 * 
 * The assistant will use the firestore_delete_document tool to delete the document.
 */

/**
 * Example 7: Query documents with filtering
 * 
 * Ask your AI assistant:
 * "Find all students in the 'students' collection with a grade of 'A'."
 * 
 * The assistant will use the firestore_list_documents tool with a filter.
 */

// ===== Storage Examples =====

/**
 * Example 8: List files in Storage
 * 
 * Ask your AI assistant:
 * "List all files in the Firebase Storage bucket."
 * 
 * The assistant will use the storage_list_files tool to list files.
 */

/**
 * Example 9: Get file information
 * 
 * Ask your AI assistant:
 * "Get information about the file at 'images/logo.png' in Firebase Storage."
 * 
 * The assistant will use the storage_get_file_info tool to retrieve file metadata.
 */

/**
 * Example 10: Upload a file
 * 
 * Ask your AI assistant:
 * "Upload this text content to Firebase Storage at 'documents/readme.txt':
 * 
 * This is a sample readme file for FlowMaster.
 * It demonstrates how to use Firebase MCP to upload files.
 * "
 * 
 * The assistant will use the storage_upload tool to upload the file.
 */

/**
 * Example 11: Upload a file from URL
 * 
 * Ask your AI assistant:
 * "Upload the image from https://example.com/image.jpg to Firebase Storage at 'images/example.jpg'."
 * 
 * The assistant will use the storage_upload_from_url tool to upload the file.
 */

// ===== Authentication Examples =====

/**
 * Example 12: Get user information
 * 
 * Ask your AI assistant:
 * "Get information about the user with email '<EMAIL>'."
 * 
 * The assistant will use the auth_get_user tool to retrieve user information.
 */

// ===== FlowMaster-Specific Examples =====

/**
 * Example 13: Query students in a specific school
 * 
 * Ask your AI assistant:
 * "Find all students in the school with ID 'school123'."
 * 
 * The assistant will use the firestore_list_documents tool with a filter.
 */

/**
 * Example 14: Add a new equipment item
 * 
 * Ask your AI assistant:
 * "Add a new equipment item to the 'equipment' collection in the school with ID 'school123' with the following data:
 * {
 *   'name': 'Surfboard',
 *   'category': 'board',
 *   'condition': 'good',
 *   'available': true,
 *   'serialNumber': 'SB-12345',
 *   'purchaseDate': '2023-01-15'
 * }"
 * 
 * The assistant will use the firestore_add_document tool to create the document.
 */

/**
 * Example 15: Update lesson status
 * 
 * Ask your AI assistant:
 * "Update the lesson with ID 'lesson123' in the school with ID 'school123' to mark it as completed."
 * 
 * The assistant will use the firestore_update_document tool to update the document.
 */

/**
 * Example 16: Generate and upload a report
 * 
 * Ask your AI assistant:
 * "Generate a CSV report of all students in the 'students' collection and upload it to Firebase Storage at 'reports/students.csv'."
 * 
 * The assistant will use multiple tools to accomplish this task.
 */
