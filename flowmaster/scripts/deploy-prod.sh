#!/bin/bash

# Ensure we're in production mode
export NODE_ENV=production

# Create docker network if it doesn't exist
docker network inspect app-network >/dev/null 2>&1 || \
    docker network create app-network

# Build and start the production environment
docker compose -f docker-compose.base.yml -f docker-compose.prod.yml up --build -d

echo "Production environment is deployed!"
echo "Access the application at http://localhost"
