#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Running Firebase MCP server...${NC}"

# Set the service account key path
SERVICE_ACCOUNT_PATH="/Users/<USER>/dev/FlowMaster/flowmaster/firebase-service-account.json"

# Check if service account key exists
if [ ! -f "$SERVICE_ACCOUNT_PATH" ]; then
    echo -e "${RED}Error: Service account key not found at $SERVICE_ACCOUNT_PATH${NC}"
    exit 1
fi

# Set storage bucket name
STORAGE_BUCKET="flowmaster-e3947.appspot.com"
echo -e "${YELLOW}Using storage bucket: $STORAGE_BUCKET${NC}"

# Run the MCP server
echo -e "${GREEN}Starting Firebase MCP server...${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop the server${NC}"

# Run the MCP server with the service account key and storage bucket
SERVICE_ACCOUNT_KEY_PATH="$SERVICE_ACCOUNT_PATH" \
FIREBASE_STORAGE_BUCKET="$STORAGE_BUCKET" \
npx -y @gannonh/firebase-mcp

# Make the script executable
chmod +x "$0"
