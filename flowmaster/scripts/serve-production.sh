#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Serving FlowMaster production build...${NC}"

# Check if build directory exists
if [ ! -d "build" ]; then
  echo -e "${RED}Error: build directory not found${NC}"
  echo -e "${YELLOW}Please run 'npm run build' first${NC}"
  exit 1
fi

# Serve the production build
echo -e "${GREEN}Starting server on port 5000...${NC}"
echo -e "${YELLOW}Open http://localhost:5000 in your browser${NC}"
npx serve -s build

# Make the script executable
chmod +x "$0"
