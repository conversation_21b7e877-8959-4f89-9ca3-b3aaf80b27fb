/**
 * <PERSON><PERSON><PERSON> to import data from Firebase emulators to the real Firebase project
 * 
 * This script reads data from the emulator export directory and imports it into
 * the real Firebase project using the Firebase Admin SDK.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Path to the emulator export directory
const EMULATOR_EXPORT_DIR = path.join(__dirname, '../firebase/data');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../firebase-service-account.json');
  
  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Function to read Firestore data from emulator export
async function readFirestoreData() {
  const firestorePath = path.join(EMULATOR_EXPORT_DIR, 'firestore_export', 'firestore_export.overall_export_metadata');
  
  if (!fs.existsSync(firestorePath)) {
    console.error(`Firestore export data not found at ${firestorePath}`);
    return null;
  }

  try {
    const metadata = JSON.parse(fs.readFileSync(firestorePath, 'utf8'));
    return metadata;
  } catch (error) {
    console.error('Error reading Firestore export data:', error);
    return null;
  }
}

// Function to read document data from emulator export
function readDocumentData(documentPath) {
  try {
    const data = JSON.parse(fs.readFileSync(documentPath, 'utf8'));
    return data;
  } catch (error) {
    console.error(`Error reading document data from ${documentPath}:`, error);
    return null;
  }
}

// Function to import Firestore data to real Firebase
async function importFirestoreData() {
  console.log('Starting Firestore data import...');
  
  const metadata = await readFirestoreData();
  if (!metadata) {
    console.error('No Firestore data to import');
    return;
  }

  // Get the directory containing the document files
  const documentsDir = path.join(EMULATOR_EXPORT_DIR, 'firestore_export', 'all_namespaces', 'all_kinds');
  
  if (!fs.existsSync(documentsDir)) {
    console.error(`Documents directory not found at ${documentsDir}`);
    return;
  }

  // Read all document files
  const files = fs.readdirSync(documentsDir);
  
  // Create a batch for Firestore operations
  let batch = db.batch();
  let batchCount = 0;
  const BATCH_LIMIT = 500; // Firestore batch limit is 500 operations
  
  for (const file of files) {
    if (!file.endsWith('.export_metadata')) continue;
    
    const filePath = path.join(documentsDir, file);
    const exportMetadata = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    for (const documentMetadata of exportMetadata.documents) {
      const documentPath = path.join(documentsDir, documentMetadata.name);
      const documentData = readDocumentData(documentPath);
      
      if (!documentData) continue;
      
      // Extract collection and document ID from the path
      const pathParts = documentMetadata.name.split('_');
      const collectionId = pathParts[0];
      const documentId = pathParts[1].split('.')[0];
      
      // Create a reference to the document in Firestore
      const docRef = db.collection(collectionId).doc(documentId);
      
      // Add the document to the batch
      batch.set(docRef, documentData.fields);
      batchCount++;
      
      // If we've reached the batch limit, commit the batch and create a new one
      if (batchCount >= BATCH_LIMIT) {
        await batch.commit();
        console.log(`Committed batch of ${batchCount} documents`);
        batch = db.batch();
        batchCount = 0;
      }
    }
  }
  
  // Commit any remaining documents in the batch
  if (batchCount > 0) {
    await batch.commit();
    console.log(`Committed final batch of ${batchCount} documents`);
  }
  
  console.log('Firestore data import complete');
}

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Main function
async function main() {
  console.log('This script will import data from Firebase emulators to the real Firebase project.');
  
  const confirmed = await askForConfirmation('Are you sure you want to continue? This will overwrite data in your production Firebase project. (y/n) ');
  
  if (!confirmed) {
    console.log('Import cancelled');
    process.exit(0);
  }
  
  try {
    await importFirestoreData();
    console.log('Data import complete');
  } catch (error) {
    console.error('Error importing data:', error);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the main function
main();
