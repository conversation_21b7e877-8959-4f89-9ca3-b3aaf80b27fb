#!/usr/bin/env ts-node

/**
 * FlowMaster Test User Creation Script
 * 
 * This script creates a test admin user for accessing the seeded data.
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth';
import { getFirestore, doc, setDoc, Timestamp } from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const functions = getFunctions(app);

const SCHOOL_ID = 'mountain-biking-academy';

async function createTestUser() {
  try {
    console.log('👤 Creating test admin user for FlowMaster...');
    console.log('===============================================');

    const testUser = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      displayName: 'MTB Academy Admin',
      role: 'admin' as const,
      schoolId: SCHOOL_ID
    };

    console.log(`📧 Email: ${testUser.email}`);
    console.log(`🔑 Password: ${testUser.password}`);
    console.log(`👤 Name: ${testUser.displayName}`);
    console.log(`🏫 School: ${testUser.schoolId}`);
    console.log(`🎭 Role: ${testUser.role}`);

    // Create Firebase Auth user
    console.log('🔄 Creating Firebase Auth user...');
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      testUser.email, 
      testUser.password
    );
    const user = userCredential.user;

    console.log(`✅ Firebase Auth user created with UID: ${user.uid}`);

    // Create Firestore user document
    console.log('🔄 Creating Firestore user document...');
    const userDoc = {
      email: testUser.email,
      displayName: testUser.displayName,
      roleId: testUser.role,
      schoolId: testUser.schoolId,
      profile: {
        bio: 'Test admin user for FlowMaster mountain biking academy',
        skills: ['Administration', 'User Management', 'System Configuration'],
        certifications: ['FlowMaster Admin'],
        phoneNumber: '******-0123'
      },
      settings: {
        language: 'en',
        notifications: true,
        theme: 'light'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    await setDoc(doc(db, 'users', user.uid), userDoc);
    console.log('✅ Firestore user document created');

    // Set custom claims
    console.log('🔄 Setting custom claims...');
    try {
      const setCustomUserClaimsFunction = httpsCallable(functions, 'setCustomUserClaims');
      
      await setCustomUserClaimsFunction({
        uid: user.uid,
        claims: {
          role: testUser.role,
          schoolId: testUser.schoolId
        }
      });

      console.log('✅ Custom claims set successfully');

      // Force token refresh
      await user.getIdToken(true);
      console.log('✅ Token refreshed');

    } catch (error: any) {
      if (error.code === 'functions/not-found') {
        console.log('⚠️  Firebase Functions not available - claims will be read from Firestore');
      } else {
        console.warn('⚠️  Could not set custom claims:', error.message);
        console.log('   Claims will be read from Firestore user document');
      }
    }

    console.log('===============================================');
    console.log('🎉 Test user created successfully!');
    console.log('');
    console.log('📋 Login Credentials:');
    console.log(`   Email: ${testUser.email}`);
    console.log(`   Password: ${testUser.password}`);
    console.log('');
    console.log('🔗 You can now:');
    console.log('   1. Open FlowMaster application');
    console.log('   2. Sign in with these credentials');
    console.log('   3. Access all seeded data as admin');
    console.log('');
    console.log('✅ The user has full admin access to Mountain Biking Academy!');

  } catch (error: any) {
    if (error.code === 'auth/email-already-in-use') {
      console.log('⚠️  User already exists!');
      console.log('');
      console.log('📋 Existing Login Credentials:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: TestPassword123!');
      console.log('');
      console.log('🔗 You can sign in with these credentials.');
    } else {
      console.error('❌ Error creating test user:', error);
      throw error;
    }
  }
}

// Execute the script
createTestUser().then(() => {
  console.log('Script completed successfully');
  process.exit(0);
}).catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
