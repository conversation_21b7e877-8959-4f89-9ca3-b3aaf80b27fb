#!/usr/bin/env ts-node

/**
 * FlowMaster Database Verification Script
 * 
 * This script verifies that the seeded data was created correctly in Firestore.
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, where } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const SCHOOL_ID = 'mountain-biking-academy';

async function verifyData() {
  try {
    console.log('🔍 Verifying FlowMaster seeded data...');
    console.log('=====================================');

    // Verify school
    const schoolQuery = query(collection(db, 'schools'), where('__name__', '==', SCHOOL_ID));
    const schoolSnapshot = await getDocs(schoolQuery);
    console.log(`📚 Schools: ${schoolSnapshot.size} (expected: 1)`);

    // Verify users by role
    const usersSnapshot = await getDocs(collection(db, 'users'));
    const users = usersSnapshot.docs.map(doc => doc.data());
    
    const instructors = users.filter(user => user.roleId === 'instructor');
    const students = users.filter(user => user.roleId === 'student');
    const clients = users.filter(user => user.roleId === 'CLIENT');
    
    console.log(`👨‍🏫 Instructors: ${instructors.length} (expected: ~5)`);
    console.log(`🎓 Students: ${students.length} (expected: ~50)`);
    console.log(`👥 Clients: ${clients.length} (expected: ~30)`);
    console.log(`👤 Total Users: ${users.length} (expected: ~85)`);

    // Verify equipment
    const equipmentSnapshot = await getDocs(collection(db, 'equipment'));
    const equipment = equipmentSnapshot.docs.map(doc => doc.data());
    
    const bikes = equipment.filter(item => item.name.includes('Trek') || item.name.includes('Specialized') || item.name.includes('Giant'));
    const helmets = equipment.filter(item => item.name.includes('Helmet'));
    const gear = equipment.filter(item => item.name.includes('Pads') || item.name.includes('Gloves') || item.name.includes('Guards'));
    
    console.log(`🚵‍♂️ Mountain Bikes: ${bikes.length} (expected: ~45)`);
    console.log(`🪖 Helmets: ${helmets.length} (expected: ~20)`);
    console.log(`🛡️ Protective Gear: ${gear.length} (expected: ~45)`);
    console.log(`🔧 Total Equipment: ${equipment.length} (expected: ~110)`);

    // Verify lessons
    const lessonsSnapshot = await getDocs(collection(db, 'lessons'));
    const lessons = lessonsSnapshot.docs.map(doc => doc.data());
    
    const individualLessons = lessons.filter(lesson => lesson.type === 'individual');
    const groupLessons = lessons.filter(lesson => lesson.type === 'group');
    const childrenLessons = lessons.filter(lesson => lesson.type === 'children');
    
    console.log(`👤 Individual Lessons: ${individualLessons.length} (expected: ~30)`);
    console.log(`👥 Group Lessons: ${groupLessons.length} (expected: ~20)`);
    console.log(`👶 Children's Lessons: ${childrenLessons.length} (expected: ~15)`);
    console.log(`📚 Total Lessons: ${lessons.length} (expected: ~65)`);

    // Verify programs
    const programsSnapshot = await getDocs(collection(db, 'programs'));
    const programs = programsSnapshot.docs.map(doc => doc.data());
    
    const seasonalPrograms = programs.filter(program => program.type === 'seasonal');
    const campPrograms = programs.filter(program => program.type === 'camp');
    
    console.log(`🌱 Seasonal Programs: ${seasonalPrograms.length} (expected: 3)`);
    console.log(`🏕️ Camp Programs: ${campPrograms.length} (expected: 3)`);
    console.log(`📋 Total Programs: ${programs.length} (expected: 6)`);

    // Verify rentals
    const rentalsSnapshot = await getDocs(collection(db, 'rentals'));
    const rentals = rentalsSnapshot.docs.map(doc => doc.data());
    
    const activeRentals = rentals.filter(rental => rental.status === 'active');
    const returnedRentals = rentals.filter(rental => rental.status === 'returned');
    const overdueRentals = rentals.filter(rental => rental.status === 'overdue');
    
    console.log(`🔄 Active Rentals: ${activeRentals.length} (expected: ~15)`);
    console.log(`✅ Returned Rentals: ${returnedRentals.length} (expected: ~25)`);
    console.log(`⚠️ Overdue Rentals: ${overdueRentals.length} (expected: ~3)`);
    console.log(`📦 Total Rentals: ${rentals.length} (expected: ~43)`);

    console.log('=====================================');

    // Check for any data quality issues
    let issues = 0;

    // Check for users without school association
    const usersWithoutSchool = users.filter(user => !user.schoolId);
    if (usersWithoutSchool.length > 0) {
      console.log(`⚠️ Found ${usersWithoutSchool.length} users without school association`);
      issues++;
    }

    // Check for equipment without school association
    const equipmentWithoutSchool = equipment.filter(item => !item.schoolId);
    if (equipmentWithoutSchool.length > 0) {
      console.log(`⚠️ Found ${equipmentWithoutSchool.length} equipment items without school association`);
      issues++;
    }

    // Check for lessons without instructors
    const lessonsWithoutInstructors = lessons.filter(lesson => !lesson.instructorId);
    if (lessonsWithoutInstructors.length > 0) {
      console.log(`⚠️ Found ${lessonsWithoutInstructors.length} lessons without instructors`);
      issues++;
    }

    if (issues === 0) {
      console.log('✅ All data quality checks passed!');
    } else {
      console.log(`⚠️ Found ${issues} data quality issues`);
    }

    console.log('');
    console.log('🎉 Data verification completed!');
    console.log('🔗 You can now test the FlowMaster application with the seeded data.');

  } catch (error) {
    console.error('❌ Verification failed:', error);
    throw error;
  }
}

// Execute the verification
verifyData().then(() => {
  console.log('Verification completed successfully');
  process.exit(0);
}).catch((error) => {
  console.error('Verification failed:', error);
  process.exit(1);
});
