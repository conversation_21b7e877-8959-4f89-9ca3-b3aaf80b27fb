#!/usr/bin/env ts-node

/**
 * FlowMaster Mountain Biking School - Database Seeding Script
 *
 * This script populates the Firestore database with realistic mountain biking-focused data
 * following the existing data models and schema.
 *
 * Usage: npm run seed-db
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  doc, 
  setDoc, 
  Timestamp,
  writeBatch,
  getDocs,
  query,
  where
} from 'firebase/firestore';
// Note: We're only seeding Firestore data, not creating Firebase Auth users
import { faker } from '@faker-js/faker';

// Import types
import { RoleType } from '../src/types/role';
import { LessonFormData, LessonStatus } from '../src/types/lesson';
import { Program, ProgramType, WeekDay, ProgramStatus } from '../src/types/program';
import { EquipmentItem, EquipmentCategory, EquipmentCondition } from '../src/types/equipment';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RentalStatus } from '../src/types/equipment';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Constants
const SCHOOL_ID = 'mountain-biking-academy';
const SCHOOL_NAME = 'Mountain Biking Academy';

// Mountain biking specific data
const MTB_DISCIPLINES = [
  'Cross Country (XC)',
  'Downhill (DH)',
  'Enduro',
  'Trail Riding',
  'Freeride',
  'Dirt Jumping',
  'BMX',
  'Fat Biking'
];

const MTB_SKILLS = [
  'Basic Bike Handling',
  'Cornering Techniques',
  'Climbing Skills',
  'Descending Techniques',
  'Jumping and Air Time',
  'Technical Trail Navigation',
  'Bike Maintenance',
  'Safety and Risk Management',
  'Endurance Building',
  'Race Preparation'
];

const MTB_LEVELS = ['Beginner', 'Intermediate', 'Advanced', 'Expert'];

const BIKE_SIZES = ['XS', 'S', 'M', 'L', 'XL'];
const HELMET_SIZES = ['XS', 'S', 'M', 'L', 'XL'];

// Utility functions
const getRandomElement = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

const getRandomElements = <T>(array: T[], count: number): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

const generatePhoneNumber = (): string => {
  return faker.phone.number();
};

const generateSerialNumber = (): string => {
  return faker.string.alphanumeric(8).toUpperCase();
};

// Helper function to recursively remove undefined values from objects
const removeUndefined = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(removeUndefined);
  }

  if (typeof obj === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = removeUndefined(value);
      }
    }
    return cleaned;
  }

  return obj;
};

// Data generation functions
interface UserData {
  uid: string;
  email: string;
  displayName: string;
  roleId: RoleType;
  schoolId: string;
  profile: {
    bio?: string;
    skills: string[];
    certifications: string[];
    phoneNumber?: string;
  };
  settings: {
    language: string;
    notifications: boolean;
    theme: 'light' | 'dark';
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

const generateInstructors = (count: number): UserData[] => {
  const instructors: UserData[] = [];
  
  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const uid = `instructor_${i + 1}`;
    
    instructors.push({
      uid,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@mtbacademy.com`,
      displayName: `${firstName} ${lastName}`,
      roleId: RoleType.INSTRUCTOR,
      schoolId: SCHOOL_ID,
      profile: {
        bio: `Experienced mountain biking instructor specializing in ${getRandomElements(MTB_DISCIPLINES, 2).join(' and ')}. ${faker.lorem.sentence()}`,
        skills: getRandomElements(MTB_SKILLS, faker.number.int({ min: 3, max: 6 })),
        certifications: [
          'Certified Mountain Bike Instructor',
          'First Aid Certified',
          getRandomElement(['PMBI Level 2', 'IMBA Instructor', 'BICP Certified'])
        ],
        phoneNumber: generatePhoneNumber()
      },
      settings: {
        language: 'en',
        notifications: true,
        theme: getRandomElement(['light', 'dark'] as ('light' | 'dark')[])
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
  }
  
  return instructors;
};

const generateStudents = (count: number): UserData[] => {
  const students: UserData[] = [];
  
  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const uid = `student_${i + 1}`;
    const level = getRandomElement(MTB_LEVELS);
    
    students.push({
      uid,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      displayName: `${firstName} ${lastName}`,
      roleId: RoleType.STUDENT,
      schoolId: SCHOOL_ID,
      profile: {
        bio: `${level} mountain biker interested in ${getRandomElements(MTB_DISCIPLINES, 2).join(' and ')}.`,
        skills: getRandomElements(MTB_SKILLS, faker.number.int({ min: 1, max: 4 })),
        certifications: level === 'Advanced' || level === 'Expert' ? 
          [getRandomElement(['Basic Bike Maintenance', 'Trail Safety Certified'])] : [],
        phoneNumber: generatePhoneNumber()
      },
      settings: {
        language: 'en',
        notifications: true,
        theme: 'light'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
  }
  
  return students;
};

const generateClients = (count: number): UserData[] => {
  const clients: UserData[] = [];
  
  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const uid = `client_${i + 1}`;
    
    clients.push({
      uid,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      displayName: `${firstName} ${lastName}`,
      roleId: RoleType.CLIENT,
      schoolId: SCHOOL_ID,
      profile: {
        bio: `Occasional mountain biker looking for equipment rentals and lessons.`,
        skills: getRandomElements(MTB_SKILLS, faker.number.int({ min: 0, max: 2 })),
        certifications: [],
        phoneNumber: generatePhoneNumber()
      },
      settings: {
        language: 'en',
        notifications: false,
        theme: 'light'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
  }
  
  return clients;
};

// Equipment generation
const generateEquipment = (): EquipmentItem[] => {
  const equipment: EquipmentItem[] = [];
  let equipmentId = 1;

  // Mountain Bikes
  const bikeModels = [
    'Trek Fuel EX 9.8',
    'Specialized Stumpjumper',
    'Giant Trance X',
    'Santa Cruz Hightower',
    'Yeti SB130',
    'Pivot Mach 4 SL',
    'Cannondale Habit',
    'Scott Genius 940'
  ];

  BIKE_SIZES.forEach(size => {
    for (let i = 0; i < 3; i++) {
      equipment.push({
        id: `bike_${equipmentId++}`,
        name: `${getRandomElement(bikeModels)} (${size})`,
        category: 'other' as EquipmentCategory, // Mountain bikes categorized as 'other'
        size,
        serialNumber: generateSerialNumber(),
        condition: getRandomElement(['good', 'good', 'good', 'maintenance'] as EquipmentCondition[]),
        available: Math.random() > 0.2, // 80% available
        notes: Math.random() > 0.7 ? faker.lorem.sentence() : undefined,
        purchaseDate: Timestamp.fromDate(faker.date.past({ years: 2 })),
        lastMaintenanceDate: Timestamp.fromDate(faker.date.recent({ days: 30 })),
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }
  });

  // Helmets
  HELMET_SIZES.forEach(size => {
    for (let i = 0; i < 4; i++) {
      equipment.push({
        id: `helmet_${equipmentId++}`,
        name: `MTB Helmet (${size})`,
        category: 'helmet' as EquipmentCategory,
        size,
        serialNumber: generateSerialNumber(),
        condition: getRandomElement(['good', 'good', 'damaged'] as EquipmentCondition[]),
        available: Math.random() > 0.1, // 90% available
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }
  });

  // Protective Gear
  const protectiveGear = [
    'Knee Pads',
    'Elbow Pads',
    'Gloves',
    'Shin Guards',
    'Back Protector'
  ];

  protectiveGear.forEach(gearType => {
    ['S', 'M', 'L'].forEach(size => {
      for (let i = 0; i < 5; i++) {
        equipment.push({
          id: `gear_${equipmentId++}`,
          name: `${gearType} (${size})`,
          category: 'other' as EquipmentCategory,
          size,
          condition: getRandomElement(['good', 'good', 'good', 'damaged'] as EquipmentCondition[]),
          available: Math.random() > 0.15, // 85% available
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      }
    });
  });

  return equipment;
};

// Main seeding function
class DatabaseSeeder {
  private instructors: UserData[] = [];
  private students: UserData[] = [];
  private clients: UserData[] = [];
  private equipment: EquipmentItem[] = [];

  async checkIfDataExists(): Promise<boolean> {
    try {
      const schoolDoc = await getDocs(query(collection(db, 'schools'), where('__name__', '==', SCHOOL_ID)));
      return !schoolDoc.empty;
    } catch (error) {
      console.log('No existing data found, proceeding with seeding...');
      return false;
    }
  }

  async createSchool(): Promise<void> {
    console.log('Creating school...');
    
    const schoolData = {
      name: SCHOOL_NAME,
      sports: ['Mountain Biking'],
      programs: MTB_DISCIPLINES,
      settings: {
        language: 'en',
        currency: 'USD',
        timezone: 'America/Denver'
      },
      createdAt: Timestamp.now()
    };

    await setDoc(doc(db, 'schools', SCHOOL_ID), schoolData);
    console.log(`✅ School "${SCHOOL_NAME}" created`);
  }

  async seedUsers(): Promise<void> {
    console.log('Generating users...');
    
    this.instructors = generateInstructors(5);
    this.students = generateStudents(50);
    this.clients = generateClients(30);

    const allUsers = [...this.instructors, ...this.students, ...this.clients];
    
    console.log(`Generated ${allUsers.length} users (${this.instructors.length} instructors, ${this.students.length} students, ${this.clients.length} clients)`);

    // Use batch writes for better performance
    const batches: any[] = [];
    let currentBatch = writeBatch(db);
    let operationCount = 0;

    for (const user of allUsers) {
      const userRef = doc(db, 'users', user.uid);
      currentBatch.set(userRef, user);
      operationCount++;

      // Firestore batch limit is 500 operations
      if (operationCount === 500) {
        batches.push(currentBatch);
        currentBatch = writeBatch(db);
        operationCount = 0;
      }
    }

    if (operationCount > 0) {
      batches.push(currentBatch);
    }

    // Execute all batches
    for (let i = 0; i < batches.length; i++) {
      await batches[i].commit();
      console.log(`✅ User batch ${i + 1}/${batches.length} committed`);
    }

    console.log('✅ All users created successfully');
  }

  async seedEquipment(): Promise<void> {
    console.log('Generating equipment...');

    this.equipment = generateEquipment();
    console.log(`Generated ${this.equipment.length} equipment items`);

    const batch = writeBatch(db);

    this.equipment.forEach(item => {
      const equipmentRef = doc(db, 'equipment', item.id);
      // Filter out undefined values
      const cleanItem = Object.fromEntries(
        Object.entries({ ...item, schoolId: SCHOOL_ID }).filter(([_, value]) => value !== undefined)
      );
      batch.set(equipmentRef, cleanItem);
    });

    await batch.commit();
    console.log('✅ Equipment created successfully');
  }

  async seedLessons(): Promise<void> {
    console.log('Generating lessons...');

    const lessons: (LessonFormData & { id: string; schoolId: string })[] = [];
    let lessonId = 1;

    // Generate individual lessons
    for (let i = 0; i < 30; i++) {
      const instructor = getRandomElement(this.instructors);
      const student = getRandomElement(this.students);
      const discipline = getRandomElement(MTB_DISCIPLINES);
      const level = getRandomElement(MTB_LEVELS);

      // Generate lesson date within the next 30 days or past 30 days
      const futureDate = Math.random() > 0.5;
      const now = new Date();
      const lessonDate = futureDate
        ? new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)
        : new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);

      lessons.push({
        id: `lesson_${lessonId++}`,
        title: `${discipline} - ${level} Session`,
        type: 'individual',
        discipline,
        instructorId: instructor.uid,
        studentIds: [student.uid],
        startTime: Timestamp.fromDate(lessonDate),
        duration: faker.number.int({ min: 60, max: 180 }), // 1-3 hours
        level,
        status: futureDate ? 'scheduled' : getRandomElement(['completed', 'completed', 'cancelled'] as LessonStatus[]),
        notes: Math.random() > 0.6 ? faker.lorem.sentence() : undefined,
        createdBy: instructor.uid,
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });
    }

    // Generate group lessons
    for (let i = 0; i < 20; i++) {
      const instructor = getRandomElement(this.instructors);
      const groupSize = faker.number.int({ min: 2, max: 6 });
      const selectedStudents = getRandomElements(this.students, groupSize);
      const discipline = getRandomElement(MTB_DISCIPLINES);
      const level = getRandomElement(MTB_LEVELS);

      const futureDate = Math.random() > 0.5;
      const now = new Date();
      const lessonDate = futureDate
        ? new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)
        : new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);

      lessons.push({
        id: `lesson_${lessonId++}`,
        title: `Group ${discipline} - ${level}`,
        type: 'group',
        discipline,
        instructorId: instructor.uid,
        studentIds: selectedStudents.map(s => s.uid),
        startTime: Timestamp.fromDate(lessonDate),
        duration: faker.number.int({ min: 90, max: 240 }), // 1.5-4 hours
        level,
        status: futureDate ? 'scheduled' : getRandomElement(['completed', 'completed', 'cancelled'] as LessonStatus[]),
        notes: Math.random() > 0.6 ? faker.lorem.sentence() : undefined,
        createdBy: instructor.uid,
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });
    }

    // Generate children's lessons
    for (let i = 0; i < 15; i++) {
      const instructor = getRandomElement(this.instructors);
      const groupSize = faker.number.int({ min: 3, max: 8 });
      const selectedStudents = getRandomElements(this.students, groupSize);

      const futureDate = Math.random() > 0.5;
      const now = new Date();
      const lessonDate = futureDate
        ? new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)
        : new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);

      lessons.push({
        id: `lesson_${lessonId++}`,
        title: `Kids MTB Skills - Beginner`,
        type: 'children',
        discipline: 'Basic Bike Handling',
        instructorId: instructor.uid,
        studentIds: selectedStudents.map(s => s.uid),
        startTime: Timestamp.fromDate(lessonDate),
        duration: faker.number.int({ min: 60, max: 120 }), // 1-2 hours
        level: 'Beginner',
        status: futureDate ? 'scheduled' : getRandomElement(['completed', 'completed', 'cancelled'] as LessonStatus[]),
        notes: Math.random() > 0.6 ? faker.lorem.sentence() : undefined,
        createdBy: instructor.uid,
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });
    }

    console.log(`Generated ${lessons.length} lessons`);

    // Use batch writes
    const batches: any[] = [];
    let currentBatch = writeBatch(db);
    let operationCount = 0;

    for (const lesson of lessons) {
      const lessonRef = doc(db, 'lessons', lesson.id);
      // Filter out undefined values
      const cleanLesson = Object.fromEntries(
        Object.entries(lesson).filter(([_, value]) => value !== undefined)
      );
      currentBatch.set(lessonRef, cleanLesson);
      operationCount++;

      if (operationCount === 500) {
        batches.push(currentBatch);
        currentBatch = writeBatch(db);
        operationCount = 0;
      }
    }

    if (operationCount > 0) {
      batches.push(currentBatch);
    }

    for (let i = 0; i < batches.length; i++) {
      await batches[i].commit();
      console.log(`✅ Lesson batch ${i + 1}/${batches.length} committed`);
    }

    console.log('✅ Lessons created successfully');
  }

  async seedPrograms(): Promise<void> {
    console.log('Generating programs...');

    const programs: (Program & { schoolId: string })[] = [];

    // Seasonal Programs
    const seasonalPrograms = [
      {
        name: 'Spring Trail Mastery',
        description: 'Master the fundamentals of trail riding as the season begins. Perfect for intermediate riders looking to improve their skills.',
        season: 'spring',
        skills: ['Cornering Techniques', 'Climbing Skills', 'Technical Trail Navigation']
      },
      {
        name: 'Summer Downhill Development',
        description: 'Intensive downhill training program for advanced riders. Focus on speed, control, and safety.',
        season: 'summer',
        skills: ['Descending Techniques', 'Jumping and Air Time', 'Safety and Risk Management']
      },
      {
        name: 'Fall Enduro Training',
        description: 'Prepare for enduro racing with this comprehensive training program covering all aspects of enduro riding.',
        season: 'fall',
        skills: ['Endurance Building', 'Technical Trail Navigation', 'Race Preparation']
      }
    ];

    seasonalPrograms.forEach((programData, index) => {
      const startDate = new Date();
      startDate.setMonth(index * 4); // Spring=0, Summer=4, Fall=8
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 3);

      const instructorCount = faker.number.int({ min: 1, max: 3 });
      const selectedInstructors = getRandomElements(this.instructors, instructorCount);

      const participantCount = faker.number.int({ min: 8, max: 20 });
      const selectedStudents = getRandomElements(this.students, participantCount);

      const paymentStatus: Record<string, 'paid' | 'partial' | 'pending'> = {};
      selectedStudents.forEach(student => {
        paymentStatus[student.uid] = getRandomElement(['paid', 'paid', 'partial', 'pending']);
      });

      programs.push({
        id: `program_${index + 1}`,
        name: programData.name,
        type: 'seasonal' as ProgramType,
        description: programData.description,
        schedule: {
          days: ['tuesday', 'thursday'] as WeekDay[],
          startTime: '16:00',
          endTime: '18:00',
          startDate: Timestamp.fromDate(startDate),
          endDate: Timestamp.fromDate(endDate)
        },
        location: 'Mountain Trails Network',
        participants: selectedStudents.map(s => s.uid),
        instructors: selectedInstructors.map(i => i.uid),
        pricing: {
          totalFee: faker.number.int({ min: 300, max: 800 }),
          currency: 'USD',
          paymentStatus,
          installments: 3,
          dueDate: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
          earlyBirdDiscount: 50
        },
        progress: {
          skills: programData.skills,
          goals: `Complete ${programData.name} program and achieve proficiency in ${programData.skills.join(', ')}`
        },
        status: 'active' as ProgramStatus,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });
    });

    // Camp Programs
    const campPrograms = [
      {
        name: 'Summer Shred Camp',
        description: 'Intensive 5-day mountain biking camp for all skill levels. Includes daily rides, skills sessions, and bike maintenance workshops.',
        skills: ['Basic Bike Handling', 'Bike Maintenance', 'Trail Riding']
      },
      {
        name: 'Skills Development Weekend',
        description: 'Weekend intensive focusing on technical skills development. Perfect for riders looking to push their limits.',
        skills: ['Jumping and Air Time', 'Technical Trail Navigation', 'Advanced Cornering']
      },
      {
        name: 'Youth MTB Academy',
        description: 'Week-long program designed specifically for young riders aged 8-16. Focus on safety, fun, and skill building.',
        skills: ['Basic Bike Handling', 'Safety and Risk Management', 'Confidence Building']
      }
    ];

    campPrograms.forEach((campData, index) => {
      const startDate = new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + (campData.name.includes('Weekend') ? 2 : 5));

      const instructorCount = faker.number.int({ min: 2, max: 4 });
      const selectedInstructors = getRandomElements(this.instructors, instructorCount);

      const participantCount = faker.number.int({ min: 10, max: 25 });
      const selectedStudents = getRandomElements(this.students, participantCount);

      const paymentStatus: Record<string, 'paid' | 'partial' | 'pending'> = {};
      selectedStudents.forEach(student => {
        paymentStatus[student.uid] = getRandomElement(['paid', 'paid', 'pending']);
      });

      programs.push({
        id: `camp_${index + 1}`,
        name: campData.name,
        type: 'camp' as ProgramType,
        description: campData.description,
        schedule: {
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'] as WeekDay[],
          startTime: '09:00',
          endTime: '15:00',
          startDate: Timestamp.fromDate(startDate),
          endDate: Timestamp.fromDate(endDate)
        },
        location: 'Mountain Biking Academy Base Camp',
        participants: selectedStudents.map(s => s.uid),
        instructors: selectedInstructors.map(i => i.uid),
        pricing: {
          totalFee: faker.number.int({ min: 200, max: 600 }),
          currency: 'USD',
          paymentStatus,
          dueDate: Timestamp.fromDate(new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000)) // 1 week before
        },
        progress: {
          skills: campData.skills,
          goals: `Complete ${campData.name} and develop skills in ${campData.skills.join(', ')}`
        },
        status: 'active' as ProgramStatus,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });
    });

    console.log(`Generated ${programs.length} programs`);

    const batch = writeBatch(db);

    programs.forEach(program => {
      const programRef = doc(db, 'programs', program.id);
      // Filter out undefined values
      const cleanProgram = Object.fromEntries(
        Object.entries(program).filter(([_, value]) => value !== undefined)
      );
      batch.set(programRef, cleanProgram);
    });

    await batch.commit();
    console.log('✅ Programs created successfully');
  }

  async seedRentals(): Promise<void> {
    console.log('Generating rentals...');

    const rentals: (RentalRecord & { schoolId: string })[] = [];
    const availableEquipment = this.equipment.filter(item => item.available);
    let rentalId = 1;

    // Generate active rentals (equipment currently rented out)
    for (let i = 0; i < 15; i++) {
      const customer = Math.random() > 0.6
        ? getRandomElement(this.students)
        : getRandomElement(this.clients);

      const equipmentCount = faker.number.int({ min: 1, max: 3 });
      const selectedEquipment = getRandomElements(availableEquipment, equipmentCount);

      const rentalDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      const dueDate = new Date(rentalDate);
      dueDate.setDate(dueDate.getDate() + faker.number.int({ min: 1, max: 14 }));

      rentals.push({
        id: `rental_${rentalId++}`,
        date: Timestamp.fromDate(rentalDate),
        dueDate: Timestamp.fromDate(dueDate),
        customerId: customer.uid,
        customerName: customer.displayName,
        customerType: customer.roleId === RoleType.STUDENT ? 'student' : 'client',
        items: selectedEquipment.map(item => ({
          equipmentId: item.id,
          equipmentName: item.name,
          condition: item.condition,
          notes: Math.random() > 0.8 ? faker.lorem.sentence() : undefined
        })),
        relatedLessonId: Math.random() > 0.7 ? `lesson_${faker.number.int({ min: 1, max: 65 })}` : undefined,
        returned: false,
        status: 'active' as RentalStatus,
        notes: Math.random() > 0.7 ? faker.lorem.sentence() : undefined,
        deposit: faker.number.int({ min: 50, max: 200 }),
        depositReturned: false,
        createdBy: getRandomElement(this.instructors).uid,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });

      // Mark equipment as unavailable
      selectedEquipment.forEach(item => {
        item.available = false;
      });
    }

    // Generate completed rentals
    for (let i = 0; i < 25; i++) {
      const customer = Math.random() > 0.6
        ? getRandomElement(this.students)
        : getRandomElement(this.clients);

      const equipmentCount = faker.number.int({ min: 1, max: 4 });
      const selectedEquipment = getRandomElements(this.equipment, equipmentCount);

      const rentalDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      const dueDate = new Date(rentalDate);
      dueDate.setDate(dueDate.getDate() + faker.number.int({ min: 1, max: 7 }));

      const returnDate = new Date(dueDate);
      returnDate.setDate(returnDate.getDate() + faker.number.int({ min: -2, max: 3 }));

      rentals.push({
        id: `rental_${rentalId++}`,
        date: Timestamp.fromDate(rentalDate),
        dueDate: Timestamp.fromDate(dueDate),
        customerId: customer.uid,
        customerName: customer.displayName,
        customerType: customer.roleId === RoleType.STUDENT ? 'student' : 'client',
        items: selectedEquipment.map(item => ({
          equipmentId: item.id,
          equipmentName: item.name,
          condition: getRandomElement(['good', 'good', 'damaged'] as EquipmentCondition[]),
          notes: Math.random() > 0.8 ? faker.lorem.sentence() : undefined
        })),
        relatedLessonId: Math.random() > 0.6 ? `lesson_${faker.number.int({ min: 1, max: 65 })}` : undefined,
        returned: true,
        returnDate: Timestamp.fromDate(returnDate),
        status: 'returned' as RentalStatus,
        notes: Math.random() > 0.7 ? faker.lorem.sentence() : undefined,
        deposit: faker.number.int({ min: 50, max: 200 }),
        depositReturned: true,
        createdBy: getRandomElement(this.instructors).uid,
        createdAt: Timestamp.fromDate(rentalDate),
        updatedAt: Timestamp.fromDate(returnDate),
        schoolId: SCHOOL_ID
      });
    }

    // Generate a few overdue rentals
    for (let i = 0; i < 3; i++) {
      const customer = getRandomElement([...this.students, ...this.clients]);
      const selectedEquipment = getRandomElements(availableEquipment, 1);

      const rentalDate = new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000);
      const dueDate = new Date(rentalDate);
      dueDate.setDate(dueDate.getDate() + 3); // Due 3 days after rental

      rentals.push({
        id: `rental_${rentalId++}`,
        date: Timestamp.fromDate(rentalDate),
        dueDate: Timestamp.fromDate(dueDate),
        customerId: customer.uid,
        customerName: customer.displayName,
        customerType: customer.roleId === RoleType.STUDENT ? 'student' : 'client',
        items: selectedEquipment.map(item => ({
          equipmentId: item.id,
          equipmentName: item.name,
          condition: item.condition,
          notes: 'Overdue return'
        })),
        returned: false,
        status: 'overdue' as RentalStatus,
        notes: 'Customer has been contacted about overdue return',
        deposit: faker.number.int({ min: 50, max: 200 }),
        depositReturned: false,
        createdBy: getRandomElement(this.instructors).uid,
        createdAt: Timestamp.fromDate(rentalDate),
        updatedAt: Timestamp.now(),
        schoolId: SCHOOL_ID
      });
    }

    console.log(`Generated ${rentals.length} rentals`);

    const batch = writeBatch(db);

    rentals.forEach(rental => {
      const rentalRef = doc(db, 'rentals', rental.id);
      // Filter out undefined values
      const cleanRental = Object.fromEntries(
        Object.entries(rental).filter(([_, value]) => value !== undefined)
      );
      batch.set(rentalRef, cleanRental);
    });

    await batch.commit();
    console.log('✅ Rentals created successfully');
  }

  async run(): Promise<void> {
    try {
      console.log('🚀 Starting FlowMaster database seeding...');
      console.log('=====================================');

      // Check if data already exists
      const dataExists = await this.checkIfDataExists();
      if (dataExists) {
        console.log('⚠️  Data already exists. Do you want to continue? (This will add more data)');
        // In a real scenario, you might want to prompt the user
        // For now, we'll continue
      }

      // Create school
      await this.createSchool();

      // Seed all data
      await this.seedUsers();
      await this.seedEquipment();
      await this.seedLessons();
      await this.seedPrograms();
      await this.seedRentals();

      console.log('=====================================');
      console.log('🎉 Database seeding completed successfully!');
      console.log('');
      console.log('📊 Summary:');
      console.log(`   • School: ${SCHOOL_NAME}`);
      console.log(`   • Instructors: ${this.instructors.length}`);
      console.log(`   • Students: ${this.students.length}`);
      console.log(`   • Clients: ${this.clients.length}`);
      console.log(`   • Equipment items: ${this.equipment.length}`);
      console.log(`   • Lessons: 65 (30 individual, 20 group, 15 children)`);
      console.log(`   • Programs: 6 (3 seasonal, 3 camps)`);
      console.log(`   • Rentals: 43 (15 active, 25 completed, 3 overdue)`);
      console.log('');
      console.log('🔗 You can now access the FlowMaster application with seeded data!');

    } catch (error) {
      console.error('❌ Error during database seeding:', error);
      throw error;
    }
  }
}

// Execute the seeding script
async function main() {
  const seeder = new DatabaseSeeder();
  await seeder.run();
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}
