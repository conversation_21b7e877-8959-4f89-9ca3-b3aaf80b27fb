#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting FlowMaster with production Firebase...${NC}"

# Check if .env.development exists
if [ ! -f .env.development ]; then
    echo -e "${RED}Error: .env.development file not found.${NC}"
    exit 1
fi

# Ensure emulators are disabled
if grep -q "REACT_APP_USE_EMULATORS=true" .env.development; then
    echo -e "${YELLOW}Setting REACT_APP_USE_EMULATORS to false...${NC}"
    sed -i '' 's/REACT_APP_USE_EMULATORS=true/REACT_APP_USE_EMULATORS=false/g' .env.development
fi

# Kill any running emulators
echo -e "${YELLOW}Stopping any running Firebase emulators...${NC}"
pkill -f "firebase emulators" || true

# Clear build cache
echo -e "${YELLOW}Clearing build cache...${NC}"
rm -rf node_modules/.cache

# Restart the application
echo -e "${GREEN}Starting the application with production Firebase...${NC}"
npm start

# Make the script executable
chmod +x "$0"
