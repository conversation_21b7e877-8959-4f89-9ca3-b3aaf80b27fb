#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Building FlowMaster for production...${NC}"

# Kill any running emulators
echo -e "${YELLOW}Stopping any running Firebase emulators...${NC}"
pkill -f "firebase emulators" || true

# Clear build cache and previous build
echo -e "${YELLOW}Clearing build cache and previous build...${NC}"
rm -rf node_modules/.cache build

# Build the application in production mode
echo -e "${GREEN}Building the application with production settings...${NC}"
npm run build

# Serve the production build locally
echo -e "${GREEN}Starting local server for production build...${NC}"
echo -e "${YELLOW}Note: This is serving the production build locally, not deploying to Firebase Hosting.${NC}"
npx serve -s build

# Make the script executable
chmod +x "$0"
