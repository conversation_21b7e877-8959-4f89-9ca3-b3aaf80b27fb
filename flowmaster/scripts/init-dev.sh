#!/bin/bash
set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Initializing FlowMaster development environment...${NC}\n"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}Error: Must be run from the flowmaster directory${NC}"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file from .env.development...${NC}"
    cp .env.development .env
    echo -e "${YELLOW}Environment configuration copied from .env.development.${NC}"
fi

# Ensure Firebase directory structure exists
echo -e "${GREEN}Setting up Firebase directory structure...${NC}"
mkdir -p firebase/rules
mkdir -p firebase/__tests__/rules

# Create docker networks if they don't exist
echo -e "${GREEN}Setting up Docker networks...${NC}"
docker network inspect app-network >/dev/null 2>&1 || \
    docker network create app-network

docker network inspect dev-network >/dev/null 2>&1 || \
    docker network create dev-network

# Create named volume for Firebase emulator data
echo -e "${GREEN}Setting up Firebase data volume...${NC}"
docker volume create flowmaster_firebase_data

# Check if firebase-tools is installed globally
if ! command -v firebase &> /dev/null; then
    echo -e "${YELLOW}Firebase CLI not found. Installing firebase-tools globally...${NC}"
    npm install -g firebase-tools
fi

# Build and start the development environment
echo -e "${GREEN}Starting development environment...${NC}"
docker compose -f docker-compose.base.yml -f docker-compose.override.yml up --build -d

# Wait for services to be healthy
echo -e "${GREEN}Waiting for services to be ready...${NC}"
MAX_RETRIES=30
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s http://localhost:3000/health > /dev/null && \
       curl -s http://localhost:4000 > /dev/null; then
        break
    fi
    echo -n "."
    sleep 2
    RETRY_COUNT=$((RETRY_COUNT + 1))
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo -e "\n${RED}Services failed to start within the expected time.${NC}"
    echo "Please check docker logs for more information:"
    echo "docker compose logs"
    exit 1
fi

# Setup complete
echo -e "\n${GREEN}Development environment is ready!${NC}"
echo -e "\nAccess points:"
echo -e "  📱 Application:        ${YELLOW}http://localhost:3000${NC}"
echo -e "  🔥 Firebase Emulators:"
echo -e "    - UI Dashboard:    ${YELLOW}http://localhost:4000${NC}"
echo -e "    - Authentication:  ${YELLOW}http://localhost:9099${NC}"
echo -e "    - Firestore:      ${YELLOW}http://localhost:8080${NC}"
echo -e "    - Functions:      ${YELLOW}http://localhost:5001${NC}"
echo -e "    - Storage:        ${YELLOW}http://localhost:9199${NC}"

# Show helpful commands
echo -e "\n${GREEN}Helpful commands:${NC}"
echo -e "  📋 View logs:         ${YELLOW}docker compose logs -f${NC}"
echo -e "  🔄 Restart services:  ${YELLOW}docker compose restart${NC}"
echo -e "  ⏹️  Stop environment: ${YELLOW}docker compose down${NC}"
echo -e "  🧪 Run tests:         ${YELLOW}npm test${NC}"

# Check if .env needs to be configured
if grep -q "YOUR_" .env; then
    echo -e "\n${YELLOW}⚠️  Warning: Your .env file contains placeholder values.${NC}"
    echo -e "Please update them with your actual Firebase configuration."
fi
