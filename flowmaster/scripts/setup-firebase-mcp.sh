#!/bin/bash

# Setup script for Firebase MCP integration with FlowMaster

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up Firebase MCP for FlowMaster...${NC}"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}Error: npm is not installed. Please install Node.js and npm first.${NC}"
    exit 1
fi

# Install Firebase MCP globally
echo -e "${YELLOW}Installing Firebase MCP...${NC}"
npm install -g @gannonh/firebase-mcp

# Create directory for service account key if it doesn't exist
mkdir -p ~/.flowmaster

# Prompt for service account key path
echo -e "${YELLOW}Please enter the path to your Firebase service account key JSON file:${NC}"
read -p "Path: " SERVICE_ACCOUNT_KEY_PATH

# Validate service account key path
if [ ! -f "$SERVICE_ACCOUNT_KEY_PATH" ]; then
    echo -e "${RED}Error: Service account key file not found at $SERVICE_ACCOUNT_KEY_PATH${NC}"
    exit 1
fi

# Copy service account key to a secure location
cp "$SERVICE_ACCOUNT_KEY_PATH" ~/.flowmaster/firebase-service-account.json
echo -e "${GREEN}Service account key copied to ~/.flowmaster/firebase-service-account.json${NC}"

# Extract project ID from service account key
PROJECT_ID=$(grep -o '"project_id": "[^"]*' ~/.flowmaster/firebase-service-account.json | cut -d'"' -f4)
if [ -z "$PROJECT_ID" ]; then
    echo -e "${YELLOW}Could not automatically detect project ID. Please enter your Firebase project ID:${NC}"
    read -p "Project ID: " PROJECT_ID
fi

# Set storage bucket name
STORAGE_BUCKET="${PROJECT_ID}.appspot.com"
echo -e "${YELLOW}Using storage bucket: $STORAGE_BUCKET${NC}"
echo -e "${YELLOW}If this is not correct, you can modify it in the MCP configuration file.${NC}"

# Determine MCP configuration file location based on platform
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    AUGMENT_CONFIG_DIR="$HOME/Library/Application Support/Code/User"
    CLAUDE_CONFIG_DIR="$HOME/Library/Application Support/Claude"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    AUGMENT_CONFIG_DIR="$HOME/.config/Code/User"
    CLAUDE_CONFIG_DIR="$HOME/.config/Claude"
else
    # Windows or other
    echo -e "${RED}Unsupported operating system. Please manually configure MCP.${NC}"
    exit 1
fi

# Ask about additional configuration options
echo -e "${YELLOW}Would you like to configure additional options for Firebase MCP? (y/n)${NC}"
read -p "Configure additional options: " CONFIGURE_ADDITIONAL

# Initialize additional environment variables
DEBUG_LOG="false"
MCP_TRANSPORT="stdio"
MCP_HTTP_PORT="3000"
MCP_HTTP_HOST="localhost"
MCP_HTTP_PATH="/mcp"

if [[ "$CONFIGURE_ADDITIONAL" =~ ^[Yy]$ ]]; then
    # Ask about debug logging
    echo -e "${YELLOW}Enable debug logging? (y/n)${NC}"
    read -p "Enable debug logging: " ENABLE_DEBUG
    if [[ "$ENABLE_DEBUG" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Use default log location (~/.firebase-mcp/debug.log)? (y/n)${NC}"
        read -p "Use default location: " USE_DEFAULT_LOG
        if [[ "$USE_DEFAULT_LOG" =~ ^[Yy]$ ]]; then
            DEBUG_LOG="true"
        else
            echo -e "${YELLOW}Enter custom log file path:${NC}"
            read -p "Log file path: " CUSTOM_LOG_PATH
            DEBUG_LOG="$CUSTOM_LOG_PATH"
        fi
    fi

    # Ask about transport
    echo -e "${YELLOW}Use HTTP transport instead of stdio? (y/n)${NC}"
    read -p "Use HTTP transport: " USE_HTTP
    if [[ "$USE_HTTP" =~ ^[Yy]$ ]]; then
        MCP_TRANSPORT="http"

        # Ask about HTTP port
        echo -e "${YELLOW}Enter HTTP port (default: 3000):${NC}"
        read -p "HTTP port: " CUSTOM_PORT
        if [[ -n "$CUSTOM_PORT" ]]; then
            MCP_HTTP_PORT="$CUSTOM_PORT"
        fi

        # Ask about HTTP host
        echo -e "${YELLOW}Enter HTTP host (default: localhost):${NC}"
        read -p "HTTP host: " CUSTOM_HOST
        if [[ -n "$CUSTOM_HOST" ]]; then
            MCP_HTTP_HOST="$CUSTOM_HOST"
        fi

        # Ask about HTTP path
        echo -e "${YELLOW}Enter HTTP path (default: /mcp):${NC}"
        read -p "HTTP path: " CUSTOM_PATH
        if [[ -n "$CUSTOM_PATH" ]]; then
            MCP_HTTP_PATH="$CUSTOM_PATH"
        fi
    fi
fi

# Create Augment Code MCP configuration
mkdir -p "$AUGMENT_CONFIG_DIR"
AUGMENT_SETTINGS_FILE="$AUGMENT_CONFIG_DIR/settings.json"

# Prepare environment variables for configuration
ENV_CONFIG="{"
ENV_CONFIG+="\"SERVICE_ACCOUNT_KEY_PATH\": \"$HOME/.flowmaster/firebase-service-account.json\","
ENV_CONFIG+="\"FIREBASE_STORAGE_BUCKET\": \"$STORAGE_BUCKET\""

# Add additional environment variables if configured
if [[ "$DEBUG_LOG" != "false" ]]; then
    ENV_CONFIG+=",\"DEBUG_LOG_FILE\": \"$DEBUG_LOG\""
fi

if [[ "$MCP_TRANSPORT" == "http" ]]; then
    ENV_CONFIG+=",\"MCP_TRANSPORT\": \"http\""
    ENV_CONFIG+=",\"MCP_HTTP_PORT\": \"$MCP_HTTP_PORT\""
    ENV_CONFIG+=",\"MCP_HTTP_HOST\": \"$MCP_HTTP_HOST\""
    ENV_CONFIG+=",\"MCP_HTTP_PATH\": \"$MCP_HTTP_PATH\""
fi

ENV_CONFIG+="}"

# Check if settings.json exists and create/update it
if [ -f "$AUGMENT_SETTINGS_FILE" ]; then
    # Backup existing settings
    cp "$AUGMENT_SETTINGS_FILE" "$AUGMENT_SETTINGS_FILE.bak"
    echo -e "${GREEN}Backed up existing Augment Code settings to $AUGMENT_SETTINGS_FILE.bak${NC}"

    # Check if file already has MCP configuration
    if grep -q "\"mcp.servers\"" "$AUGMENT_SETTINGS_FILE"; then
        # Update existing MCP configuration
        echo -e "${YELLOW}Updating existing MCP configuration in Augment Code settings...${NC}"
        # This is a simplified approach - in a real scenario, you might want to use jq for proper JSON manipulation
        # For now, we'll just instruct the user to manually update
        echo -e "${YELLOW}Please manually add the Firebase MCP configuration to your Augment Code settings.${NC}"
    else
        # Add MCP configuration to existing settings
        # Again, this is simplified - in reality, you'd want to use jq
        echo -e "${YELLOW}Please manually add the Firebase MCP configuration to your Augment Code settings.${NC}"
    fi
else
    # Create new settings file with MCP configuration
    if [[ "$MCP_TRANSPORT" == "stdio" ]]; then
        cat > "$AUGMENT_SETTINGS_FILE" << EOF
{
    "mcp.servers": {
        "firebase-mcp": {
            "command": "npx",
            "args": [
                "-y",
                "@gannonh/firebase-mcp"
            ],
            "env": $ENV_CONFIG
        }
    }
}
EOF
    else
        # For HTTP transport, use URL configuration
        cat > "$AUGMENT_SETTINGS_FILE" << EOF
{
    "mcp.servers": {
        "firebase-mcp": {
            "url": "http://$MCP_HTTP_HOST:$MCP_HTTP_PORT$MCP_HTTP_PATH"
        }
    }
}
EOF
    fi
    echo -e "${GREEN}Created new Augment Code settings with Firebase MCP configuration.${NC}"
fi

# Create Claude Desktop MCP configuration if directory exists
if [ -d "$CLAUDE_CONFIG_DIR" ]; then
    CLAUDE_CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"

    # Check if config file exists and create/update it
    if [ -f "$CLAUDE_CONFIG_FILE" ]; then
        # Backup existing config
        cp "$CLAUDE_CONFIG_FILE" "$CLAUDE_CONFIG_FILE.bak"
        echo -e "${GREEN}Backed up existing Claude Desktop config to $CLAUDE_CONFIG_FILE.bak${NC}"
        echo -e "${YELLOW}Please manually add the Firebase MCP configuration to your Claude Desktop config.${NC}"
    else
        # Create new config file with MCP configuration
        if [[ "$MCP_TRANSPORT" == "stdio" ]]; then
            cat > "$CLAUDE_CONFIG_FILE" << EOF
{
    "firebase-mcp": {
        "command": "npx",
        "args": [
            "-y",
            "@gannonh/firebase-mcp"
        ],
        "env": $ENV_CONFIG
    }
}
EOF
        else
            # For HTTP transport, use URL configuration
            cat > "$CLAUDE_CONFIG_FILE" << EOF
{
    "firebase-mcp": {
        "url": "http://$MCP_HTTP_HOST:$MCP_HTTP_PORT$MCP_HTTP_PATH"
    }
}
EOF
        fi
        echo -e "${GREEN}Created new Claude Desktop config with Firebase MCP configuration.${NC}"
    fi
fi

# Create a sample MCP configuration for Cursor
mkdir -p .cursor
if [[ "$MCP_TRANSPORT" == "stdio" ]]; then
    cat > .cursor/mcp.json << EOF
{
    "firebase-mcp": {
        "command": "npx",
        "args": [
            "-y",
            "@gannonh/firebase-mcp"
        ],
        "env": $ENV_CONFIG
    }
}
EOF
else
    # For HTTP transport, use URL configuration
    cat > .cursor/mcp.json << EOF
{
    "firebase-mcp": {
        "url": "http://$MCP_HTTP_HOST:$MCP_HTTP_PORT$MCP_HTTP_PATH"
    }
}
EOF
fi
echo -e "${GREEN}Created Cursor MCP configuration in .cursor/mcp.json${NC}"

echo -e "${GREEN}Firebase MCP setup complete!${NC}"
echo -e "${YELLOW}To test the integration, ask your AI assistant: \"Please test all Firebase MCP tools.\"${NC}"

# Make the script executable
chmod +x "$0"
