#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting Firebase MCP server for Augment...${NC}"

# Check if service account key exists
SERVICE_ACCOUNT_DIR="$HOME/.flowmaster"
SERVICE_ACCOUNT_PATH="$SERVICE_ACCOUNT_DIR/firebase-service-account.json"

if [ ! -f "$SERVICE_ACCOUNT_PATH" ]; then
    echo -e "${YELLOW}Service account key not found at $SERVICE_ACCOUNT_PATH${NC}"
    echo -e "${YELLOW}Would you like to create the directory and copy your service account key? (y/n)${NC}"
    read -p "Create directory and copy key? " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        mkdir -p "$SERVICE_ACCOUNT_DIR"
        echo -e "${YELLOW}Please enter the path to your Firebase service account key JSON file:${NC}"
        read -p "Path: " KEY_PATH
        if [ -f "$KEY_PATH" ]; then
            cp "$KEY_PATH" "$SERVICE_ACCOUNT_PATH"
            echo -e "${GREEN}Service account key copied to $SERVICE_ACCOUNT_PATH${NC}"
        else
            echo -e "${RED}Error: File not found at $KEY_PATH${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}Please specify the path to your service account key:${NC}"
        read -p "Path: " SERVICE_ACCOUNT_PATH
        if [ ! -f "$SERVICE_ACCOUNT_PATH" ]; then
            echo -e "${RED}Error: Service account key not found at $SERVICE_ACCOUNT_PATH${NC}"
            exit 1
        fi
    fi
fi

# Get Firebase project ID from service account key
PROJECT_ID=$(grep -o '"project_id": "[^"]*' "$SERVICE_ACCOUNT_PATH" | cut -d'"' -f4)
if [ -z "$PROJECT_ID" ]; then
    echo -e "${YELLOW}Could not automatically detect project ID. Please enter your Firebase project ID:${NC}"
    read -p "Project ID: " PROJECT_ID
else
    echo -e "${GREEN}Detected Firebase project ID: $PROJECT_ID${NC}"
fi

# Set storage bucket name
STORAGE_BUCKET="${PROJECT_ID}.appspot.com"
echo -e "${YELLOW}Using storage bucket: $STORAGE_BUCKET${NC}"

# Start the MCP server
echo -e "${GREEN}Starting Firebase MCP server...${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop the server${NC}"

SERVICE_ACCOUNT_KEY_PATH="$SERVICE_ACCOUNT_PATH" FIREBASE_STORAGE_BUCKET="$STORAGE_BUCKET" npx -y @gannonh/firebase-mcp

# Make the script executable
chmod +x "$0"
