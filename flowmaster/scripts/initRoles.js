const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDVyQOoNRTZZU0gOxAaC8iLAEBstolTs3k",
  authDomain: "flowmaster-e3947.firebaseapp.com",
  projectId: "flowmaster-e3947",
  storageBucket: "flowmaster-e3947.firebasestorage.app",
  messagingSenderId: "350018115809",
  appId: "1:350018115809:web:4ecfdd7e77b16ebcc63fa0",
  measurementId: "G-CJSD7ZGFWJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Role types
const RoleType = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  INSTRUCTOR: 'instructor',
  STUDENT: 'student',
  CLIENT: 'CLIENT',
};

// Default roles with permissions
const DEFAULT_ROLES = {
  [RoleType.ADMIN]: {
    id: RoleType.ADMIN,
    name: 'Administrator',
    description: 'Full system access',
    permissions: [
      'manage_users',
      'manage_lessons',
      'manage_programs',
      'manage_settings',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
      'manage_equipment',
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  [RoleType.MANAGER]: {
    id: RoleType.MANAGER,
    name: 'Manager',
    description: 'School management access',
    permissions: [
      'manage_users',
      'manage_lessons',
      'manage_programs',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
      'manage_equipment',
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  [RoleType.INSTRUCTOR]: {
    id: RoleType.INSTRUCTOR,
    name: 'Instructor',
    description: 'Can manage programs and lessons',
    permissions: [
      'manage_lessons',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  [RoleType.STUDENT]: {
    id: RoleType.STUDENT,
    name: 'Student',
    description: 'Student access',
    permissions: ['view_lessons', 'view_programs', 'edit_profile'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  [RoleType.CLIENT]: {
    id: RoleType.CLIENT,
    name: 'Client',
    description: 'Client access',
    permissions: ['view_lessons'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
};

// Initialize roles in Firestore
async function initializeRoles() {
  try {
    console.log('Initializing roles...');
    
    for (const [id, role] of Object.entries(DEFAULT_ROLES)) {
      const roleRef = doc(db, 'roles', id);
      console.log(`Creating/updating role: ${id}`);
      
      await setDoc(roleRef, {
        ...role,
        updatedAt: new Date().toISOString(),
      }, { merge: true });
      
      console.log(`Role ${id} created/updated successfully`);
    }
    
    console.log('All roles initialized successfully');
  } catch (error) {
    console.error('Error initializing roles:', error);
  }
}

// Run the initialization
initializeRoles()
  .then(() => {
    console.log('Role initialization complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during role initialization:', error);
    process.exit(1);
  });
