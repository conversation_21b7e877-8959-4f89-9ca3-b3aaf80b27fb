#!/bin/bash
set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Cleaning up FlowMaster development environment...${NC}\n"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}Error: Must be run from the flowmaster directory${NC}"
    exit 1
fi

# Stop all containers
echo -e "${GREEN}Stopping containers...${NC}"
docker compose -f docker-compose.base.yml -f docker-compose.override.yml down

# Ask about removing volumes
read -p "$(echo -e ${YELLOW}Do you want to remove the Firebase emulator data volume? [y/N]${NC} )" -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}Removing Firebase data volume...${NC}"
    docker volume rm flowmaster_firebase_data
fi

# Ask about removing networks
read -p "$(echo -e ${YELLOW}Do you want to remove the Docker networks? [y/N]${NC} )" -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}Removing Docker networks...${NC}"
    docker network rm app-network dev-network 2>/dev/null || true
fi

# Clean up temporary files
echo -e "${GREEN}Cleaning up temporary files...${NC}"
rm -f firebase-debug.log

echo -e "\n${GREEN}Cleanup complete!${NC}"
echo -e "To restart the development environment, run: ${YELLOW}./scripts/init-dev.sh${NC}"
