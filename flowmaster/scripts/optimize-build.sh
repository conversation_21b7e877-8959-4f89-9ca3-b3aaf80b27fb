#!/bin/bash

# Optimize the build process by removing unnecessary files and dependencies

# Clear cache
echo "Clearing cache..."
npm cache clean --force
rm -rf node_modules/.cache

# Remove test files from node_modules
echo "Removing test files from node_modules..."
find node_modules -type d -name "test" -o -name "tests" | xargs rm -rf

# Remove documentation from node_modules
echo "Removing documentation from node_modules..."
find node_modules -type d -name "docs" -o -name "doc" | xargs rm -rf

# Remove example files from node_modules
echo "Removing example files from node_modules..."
find node_modules -type d -name "example" -o -name "examples" | xargs rm -rf

# Run build with increased memory
echo "Running build with increased memory..."
export NODE_OPTIONS="--max-old-space-size=8192"
npm run build

echo "Build process completed."
