#!/bin/bash

# Clear cache and node_modules
echo "Clearing cache..."
npm cache clean --force
rm -rf node_modules/.cache

# Install dependencies if needed
if [ "$1" == "--install" ]; then
  echo "Reinstalling dependencies..."
  rm -rf node_modules
  npm install
fi

# Run build with increased memory
echo "Running build with increased memory..."
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

echo "Build process completed."
