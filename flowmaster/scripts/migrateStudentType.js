/**
 * Migration script to update existing students with the new type field
 * 
 * This script:
 * 1. Fetches all students from all schools
 * 2. Determines the appropriate type based on their participation in programs and lessons
 * 3. Updates each student with the new type field
 * 
 * Run this script with: node src/scripts/migrateStudentType.js
 */

const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  getDocs, 
  doc, 
  updateDoc, 
  getDoc, 
  query, 
  where 
} = require('firebase/firestore');

// Initialize Firebase (replace with your config)
const firebaseConfig = {
  // Your Firebase config here
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

/**
 * Determine student type based on participation in programs and lessons
 */
async function determineStudentType(schoolId, studentId) {
  try {
    // Check if student is in any programs
    const programsRef = collection(db, 'schools', schoolId, 'programs');
    const programsQuery = query(programsRef, where('participants', 'array-contains', studentId));
    const programsSnapshot = await getDocs(programsQuery);
    const inPrograms = !programsSnapshot.empty;

    // Check if student is in any lessons
    const lessonsRef = collection(db, 'schools', schoolId, 'lessons');
    const lessonsQuery = query(lessonsRef, where('studentIds', 'array-contains', studentId));
    const lessonsSnapshot = await getDocs(lessonsQuery);
    const inLessons = !lessonsSnapshot.empty;

    // Determine type
    if (inPrograms && inLessons) {
      return 'both';
    } else if (inPrograms) {
      return 'program';
    } else if (inLessons) {
      return 'lesson';
    } else {
      // Default to both if no data is found
      return 'both';
    }
  } catch (error) {
    console.error(`Error determining type for student ${studentId}:`, error);
    return 'both'; // Default to both on error
  }
}

/**
 * Update a student with the new type field
 */
async function updateStudentType(schoolId, studentId, type) {
  try {
    const studentRef = doc(db, 'schools', schoolId, 'students', studentId);
    await updateDoc(studentRef, { type });
    console.log(`Updated student ${studentId} with type: ${type}`);
    return true;
  } catch (error) {
    console.error(`Error updating student ${studentId}:`, error);
    return false;
  }
}

/**
 * Main migration function
 */
async function migrateStudentTypes() {
  try {
    // Get all schools
    const schoolsRef = collection(db, 'schools');
    const schoolsSnapshot = await getDocs(schoolsRef);
    
    console.log(`Found ${schoolsSnapshot.size} schools`);
    
    let totalStudents = 0;
    let updatedStudents = 0;
    
    // Process each school
    for (const schoolDoc of schoolsSnapshot.docs) {
      const schoolId = schoolDoc.id;
      console.log(`Processing school: ${schoolId}`);
      
      // Get all students in this school
      const studentsRef = collection(db, 'schools', schoolId, 'students');
      const studentsSnapshot = await getDocs(studentsRef);
      
      console.log(`Found ${studentsSnapshot.size} students in school ${schoolId}`);
      totalStudents += studentsSnapshot.size;
      
      // Process each student
      for (const studentDoc of studentsSnapshot.docs) {
        const studentId = studentDoc.id;
        const studentData = studentDoc.data();
        
        // Skip if student already has a type
        if (studentData.type) {
          console.log(`Student ${studentId} already has type: ${studentData.type}`);
          continue;
        }
        
        // Determine and update student type
        const type = await determineStudentType(schoolId, studentId);
        const updated = await updateStudentType(schoolId, studentId, type);
        
        if (updated) {
          updatedStudents++;
        }
      }
    }
    
    console.log(`Migration complete. Updated ${updatedStudents} of ${totalStudents} students.`);
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
migrateStudentTypes().then(() => {
  console.log('Migration script completed');
  process.exit(0);
}).catch(error => {
  console.error('Migration script failed:', error);
  process.exit(1);
});
