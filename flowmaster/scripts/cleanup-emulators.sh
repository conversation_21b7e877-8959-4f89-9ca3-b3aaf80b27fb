#!/bin/bash

# Array of ports used by Firebase emulators
PORTS=(9099 8082 9000 5002 9199)

echo "Gracefully shutting down Firebase emulators..."

for PORT in "${PORTS[@]}"; do
    # Find process using the port
    PID=$(lsof -ti :$PORT)
    
    if [ ! -z "$PID" ]; then
        echo "Found process using port $PORT (PID: $PID)"
        echo "Sending SIGTERM for graceful shutdown..."
        kill -15 $PID
        
        # Wait for process to terminate gracefully
        echo "Waiting for process to terminate..."
        for i in {1..10}; do
            if ! kill -0 $PID 2>/dev/null; then
                echo "Process terminated gracefully"
                break
            fi
            sleep 1
        done
        
        # Force kill if still running after timeout
        if kill -0 $PID 2>/dev/null; then
            echo "Process did not terminate gracefully, forcing shutdown..."
            kill -9 $PID
        fi
    else
        echo "No process found using port $PORT"
    fi
done

echo "Cleanup complete!"