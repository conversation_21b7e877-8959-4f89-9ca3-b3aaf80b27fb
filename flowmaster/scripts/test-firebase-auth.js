/**
 * <PERSON><PERSON><PERSON> to test Firebase Authentication
 * 
 * This script tests if Firebase Authentication is working correctly
 * with the real Firebase project.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../firebase-service-account.json');
  
  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Auth instance
const auth = admin.auth();

// Function to ask for input
function askForInput(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer);
    });
  });
}

// Function to list users
async function listUsers() {
  try {
    const listUsersResult = await auth.listUsers(100);
    console.log('Users:');
    listUsersResult.users.forEach((userRecord) => {
      console.log(`- ${userRecord.uid} (${userRecord.email})`);
    });
    return listUsersResult.users;
  } catch (error) {
    console.error('Error listing users:', error);
    return [];
  }
}

// Function to get user by email
async function getUserByEmail(email) {
  try {
    const userRecord = await auth.getUserByEmail(email);
    console.log('User found:');
    console.log(`- UID: ${userRecord.uid}`);
    console.log(`- Email: ${userRecord.email}`);
    console.log(`- Email verified: ${userRecord.emailVerified}`);
    console.log(`- Display name: ${userRecord.displayName || 'Not set'}`);
    console.log(`- Phone number: ${userRecord.phoneNumber || 'Not set'}`);
    return userRecord;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

// Function to create a user
async function createUser(email, password, displayName) {
  try {
    const userRecord = await auth.createUser({
      email: email,
      emailVerified: false,
      password: password,
      displayName: displayName,
      disabled: false
    });
    console.log('User created successfully:');
    console.log(`- UID: ${userRecord.uid}`);
    console.log(`- Email: ${userRecord.email}`);
    return userRecord;
  } catch (error) {
    console.error('Error creating user:', error);
    return null;
  }
}

// Function to create a user document in Firestore
async function createUserDocument(uid, role = 'admin', schoolId = 'school1') {
  try {
    const db = admin.firestore();
    await db.collection('users').doc(uid).set({
      role: role,
      schoolId: schoolId,
      profile: {
        bio: 'Created by test script'
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('User document created successfully');
    return true;
  } catch (error) {
    console.error('Error creating user document:', error);
    return false;
  }
}

// Main function
async function main() {
  console.log('Firebase Authentication Test');
  console.log('===========================');
  
  // List existing users
  console.log('\nListing existing users...');
  const users = await listUsers();
  
  // Ask what to do
  console.log('\nWhat would you like to do?');
  console.log('1. Get user by email');
  console.log('2. Create a new user');
  console.log('3. Exit');
  
  const choice = await askForInput('Enter your choice (1-3): ');
  
  switch (choice) {
    case '1':
      const email = await askForInput('Enter email: ');
      await getUserByEmail(email);
      break;
    case '2':
      const newEmail = await askForInput('Enter email: ');
      const password = await askForInput('Enter password: ');
      const displayName = await askForInput('Enter display name: ');
      const userRecord = await createUser(newEmail, password, displayName);
      if (userRecord) {
        const createDoc = await askForInput('Create user document in Firestore? (y/n): ');
        if (createDoc.toLowerCase() === 'y') {
          const role = await askForInput('Enter role (admin, instructor, student): ');
          const schoolId = await askForInput('Enter school ID (default: school1): ') || 'school1';
          await createUserDocument(userRecord.uid, role, schoolId);
        }
      }
      break;
    case '3':
      console.log('Exiting...');
      break;
    default:
      console.log('Invalid choice');
  }
  
  // Exit the process
  process.exit(0);
}

// Run the main function
main();
