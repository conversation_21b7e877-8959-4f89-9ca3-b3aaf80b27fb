# FlowMaster Database Seeding Script

This directory contains the database seeding script for the FlowMaster mountain biking school management application.

## Overview

The seeding script (`seedDatabase.ts`) populates the Firestore database with realistic mountain biking-focused data including:

- **School**: Mountain Biking Academy
- **Users**: 5 instructors, 50 students, 30 clients
- **Equipment**: Mountain bikes, helmets, protective gear
- **Lessons**: Individual, group, and children's lessons
- **Programs**: Seasonal programs and camps
- **Rentals**: Active, completed, and overdue equipment rentals

## Prerequisites

1. **Firebase Project**: Ensure you have a Firebase project set up
2. **Environment Variables**: Configure your Firebase credentials
3. **Dependencies**: Install required npm packages

## Setup

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Configure Environment**:
   ```bash
   cp scripts/.env.example scripts/.env
   # Edit scripts/.env with your Firebase configuration
   ```

3. **Firebase Configuration**:
   - Ensure your Firebase project has Firestore enabled
   - Update Firestore security rules if needed
   - Make sure you have admin access to the project

## Running the Seeding Script

Execute the seeding script with:

```bash
npm run seed-db
```

## What Gets Created

### School Data
- **School ID**: `mountain-biking-academy`
- **Name**: Mountain Biking Academy
- **Sports**: Mountain Biking
- **Settings**: English language, USD currency, Denver timezone

### Users (85 total)
- **5 Instructors**: Experienced MTB instructors with varied specializations
- **50 Students**: Mixed skill levels (Beginner, Intermediate, Advanced, Expert)
- **30 Clients**: Occasional riders for equipment rentals

### Equipment (100+ items)
- **Mountain Bikes**: Various models and sizes (XS, S, M, L, XL)
- **Helmets**: Different sizes with safety certifications
- **Protective Gear**: Knee pads, elbow pads, gloves, shin guards

### Lessons (65 total)
- **30 Individual Lessons**: 1-on-1 coaching sessions
- **20 Group Lessons**: 2-6 participants per lesson
- **15 Children's Lessons**: Youth-focused skill building

### Programs (6 total)
- **3 Seasonal Programs**:
  - Spring Trail Mastery
  - Summer Downhill Development
  - Fall Enduro Training
- **3 Camp Programs**:
  - Summer Shred Camp
  - Skills Development Weekend
  - Youth MTB Academy

### Rentals (43 total)
- **15 Active Rentals**: Currently rented equipment
- **25 Completed Rentals**: Historical rental records
- **3 Overdue Rentals**: Equipment past due date

## Mountain Biking Specific Data

### Disciplines
- Cross Country (XC)
- Downhill (DH)
- Enduro
- Trail Riding
- Freeride
- Dirt Jumping
- BMX
- Fat Biking

### Skills Covered
- Basic Bike Handling
- Cornering Techniques
- Climbing Skills
- Descending Techniques
- Jumping and Air Time
- Technical Trail Navigation
- Bike Maintenance
- Safety and Risk Management
- Endurance Building
- Race Preparation

## Data Relationships

The script maintains proper foreign key relationships:
- Users are assigned to appropriate schools
- Lessons reference valid instructors and students
- Programs include enrolled students and assigned instructors
- Equipment rentals link to customers and optionally to lessons
- All data includes proper timestamps and school associations

## Safety Features

- **Idempotent**: Can be run multiple times safely
- **Validation**: Checks for existing data before proceeding
- **Error Handling**: Comprehensive error catching and reporting
- **Batch Operations**: Uses Firestore batch writes for performance

## Troubleshooting

### Common Issues

1. **Firebase Authentication Error**:
   - Verify your Firebase configuration in `.env`
   - Ensure your service account has proper permissions

2. **Permission Denied**:
   - Check Firestore security rules
   - Verify your user has admin access to the project

3. **Network Errors**:
   - Ensure stable internet connection
   - Check Firebase project status

### Logs

The script provides detailed logging:
- Progress indicators for each seeding phase
- Success confirmations with counts
- Error messages with context
- Final summary of created data

## Customization

To modify the seeded data:

1. **Adjust Quantities**: Change the loop counts in each seeding method
2. **Modify Data**: Update the arrays of names, skills, disciplines
3. **Add New Types**: Extend the interfaces and generation functions
4. **Change School**: Update `SCHOOL_ID` and `SCHOOL_NAME` constants

## Next Steps

After running the seeding script:

1. **Verify Data**: Check the Firebase console to confirm data creation
2. **Test Application**: Launch the FlowMaster app to see the seeded data
3. **Adjust as Needed**: Re-run with modifications if required
4. **Set Up Authentication**: Configure user authentication for the seeded users

## Support

For issues or questions about the seeding script, refer to the main project documentation or create an issue in the project repository.
