#!/usr/bin/env ts-node

/**
 * Simple Firebase connection test
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, Timestamp } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function testConnection() {
  try {
    console.log('🔥 Testing Firebase connection...');
    console.log('Project ID:', firebaseConfig.projectId);
    
    // Try to add a simple test document
    const testDoc = {
      message: 'Hello from seeding script',
      timestamp: Timestamp.now(),
      test: true
    };
    
    const docRef = await addDoc(collection(db, 'test'), testDoc);
    console.log('✅ Successfully connected to Firebase!');
    console.log('Test document ID:', docRef.id);
    
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
  }
}

testConnection().then(() => {
  console.log('Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('Test failed:', error);
  process.exit(1);
});
