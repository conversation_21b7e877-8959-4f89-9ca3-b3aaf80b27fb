#!/bin/bash

# This script builds the React application in chunks to avoid memory issues
# It uses the GENERATE_SOURCEMAP=false flag to reduce memory usage

# Clear cache
echo "Clearing cache..."
npm cache clean --force
rm -rf node_modules/.cache

# Set environment variables
export NODE_OPTIONS="--max-old-space-size=8192"
export GENERATE_SOURCEMAP=false
export CI=false

# Create a temporary build directory
echo "Creating temporary build directory..."
mkdir -p temp_build

# Run the build with reduced features
echo "Running build with reduced features..."
DISABLE_ESLINT_PLUGIN=true DISABLE_TYPESCRIPT=true npm run build

echo "Build process completed."
