#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Debugging Firebase MCP server...${NC}"

# Check if service account key exists
SERVICE_ACCOUNT_PATH="/Users/<USER>/dev/FlowMaster/flowmaster/firebase-service-account.json"

if [ ! -f "$SERVICE_ACCOUNT_PATH" ]; then
    echo -e "${RED}Error: Service account key not found at $SERVICE_ACCOUNT_PATH${NC}"
    exit 1
fi

# Verify the service account key format
echo -e "${YELLOW}Verifying service account key format...${NC}"
if ! jq . "$SERVICE_ACCOUNT_PATH" > /dev/null 2>&1; then
    echo -e "${RED}Error: Service account key is not valid JSON${NC}"
    exit 1
fi

# Check required fields in service account key
echo -e "${YELLOW}Checking required fields in service account key...${NC}"
for field in "type" "project_id" "private_key_id" "private_key" "client_email"; do
    if ! jq -e ".$field" "$SERVICE_ACCOUNT_PATH" > /dev/null 2>&1; then
        echo -e "${RED}Error: Service account key is missing required field: $field${NC}"
        exit 1
    fi
done

# Get Firebase project ID from service account key
PROJECT_ID=$(jq -r '.project_id' "$SERVICE_ACCOUNT_PATH")
if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}Error: Could not detect project ID from service account key${NC}"
    exit 1
else
    echo -e "${GREEN}Detected Firebase project ID: $PROJECT_ID${NC}"
fi

# Set storage bucket name
STORAGE_BUCKET="${PROJECT_ID}.appspot.com"
echo -e "${YELLOW}Using storage bucket: $STORAGE_BUCKET${NC}"

# Create a debug log file
DEBUG_LOG_FILE="/tmp/firebase-mcp-debug.log"
echo -e "${YELLOW}Creating debug log file at $DEBUG_LOG_FILE${NC}"
touch "$DEBUG_LOG_FILE"

# Run the MCP server with debug logging
echo -e "${GREEN}Starting Firebase MCP server with debug logging...${NC}"
echo -e "${YELLOW}This will run for 10 seconds and then exit${NC}"
echo -e "${YELLOW}Check $DEBUG_LOG_FILE for detailed logs${NC}"

# Run the MCP server with debug logging
SERVICE_ACCOUNT_KEY_PATH="$SERVICE_ACCOUNT_PATH" \
FIREBASE_STORAGE_BUCKET="$STORAGE_BUCKET" \
DEBUG_LOG_FILE="$DEBUG_LOG_FILE" \
npx -y @gannonh/firebase-mcp > /tmp/firebase-mcp-output.log 2>&1 &

MCP_PID=$!

# Wait for a few seconds to see if the server starts
echo -e "${YELLOW}Waiting for 10 seconds to check if the server starts...${NC}"
sleep 10

# Check if the process is still running
if kill -0 $MCP_PID 2>/dev/null; then
    echo -e "${GREEN}Firebase MCP server started successfully!${NC}"
    echo -e "${GREEN}Your Firebase MCP configuration is working correctly${NC}"
    echo -e "${YELLOW}Now you can use Augment with Firebase MCP${NC}"
    echo -e "${YELLOW}Try asking Augment: 'Please test all Firebase MCP tools'${NC}"
    
    # Kill the process
    echo -e "${YELLOW}Stopping the test server...${NC}"
    kill $MCP_PID
else
    echo -e "${RED}Error: Firebase MCP server failed to start or stopped immediately${NC}"
    echo -e "${YELLOW}Checking debug logs...${NC}"
    
    # Display the last few lines of the debug log
    if [ -f "$DEBUG_LOG_FILE" ]; then
        echo -e "${YELLOW}Debug log contents:${NC}"
        cat "$DEBUG_LOG_FILE"
    fi
    
    # Display the output log
    echo -e "${YELLOW}Output log contents:${NC}"
    cat /tmp/firebase-mcp-output.log
fi

# Make the script executable
chmod +x "$0"
