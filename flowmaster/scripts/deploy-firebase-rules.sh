#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Deploying Firebase rules to production...${NC}"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo -e "${RED}Error: Firebase CLI is not installed. Please install it first:${NC}"
    echo -e "npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in to Firebase
FIREBASE_USER=$(firebase login:list | grep -o 'User: .*' | head -1)
if [ -z "$FIREBASE_USER" ]; then
    echo -e "${YELLOW}You are not logged in to Firebase. Please log in:${NC}"
    firebase login
fi

# Navigate to the Firebase directory
cd "$(dirname "$0")/../firebase" || exit 1

# Confirm deployment to production
echo -e "${YELLOW}You are about to deploy Firestore and Storage rules to the production Firebase project.${NC}"
echo -e "${YELLOW}This will overwrite any existing rules in the production environment.${NC}"
read -p "Are you sure you want to continue? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${RED}Deployment cancelled.${NC}"
    exit 1
fi

# Deploy Firestore rules
echo -e "${GREEN}Deploying Firestore rules...${NC}"
firebase deploy --only firestore:rules

# Deploy Storage rules
echo -e "${GREEN}Deploying Storage rules...${NC}"
firebase deploy --only storage

echo -e "${GREEN}Firebase rules deployment complete!${NC}"

# Remind about the environment variable
echo -e "${YELLOW}Remember that you've set REACT_APP_USE_EMULATORS=false in .env.development${NC}"
echo -e "${YELLOW}This means your app will now use the real Firebase services.${NC}"
echo -e "${YELLOW}To switch back to emulators, set REACT_APP_USE_EMULATORS=true${NC}"

# Make the script executable
chmod +x "$0"
