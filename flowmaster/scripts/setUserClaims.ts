#!/usr/bin/env ts-node

/**
 * FlowMaster User Claims Setup Script
 * 
 * This script sets custom claims for existing Firebase Auth users
 * to enable access to the seeded school data.
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';
import { getFunctions, httpsCallable } from 'firebase/functions';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const functions = getFunctions(app);

const SCHOOL_ID = 'mountain-biking-academy';

async function setUserClaims() {
  try {
    console.log('🔧 Setting up user claims for FlowMaster...');
    console.log('==========================================');

    // Get current user
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log('❌ No user is currently signed in.');
      console.log('');
      console.log('Please sign in to the FlowMaster application first, then run this script.');
      console.log('');
      console.log('Steps:');
      console.log('1. Open FlowMaster application in browser');
      console.log('2. Sign in with your account');
      console.log('3. Run this script again');
      return;
    }

    console.log(`👤 Current user: ${currentUser.email}`);
    console.log(`🆔 User ID: ${currentUser.uid}`);

    // Set custom claims using Firebase Functions
    try {
      const setCustomUserClaimsFunction = httpsCallable(functions, 'setCustomUserClaims');
      
      const claims = {
        role: 'admin',
        schoolId: SCHOOL_ID
      };

      console.log('🔄 Setting custom claims...');
      console.log(`   Role: ${claims.role}`);
      console.log(`   School ID: ${claims.schoolId}`);

      await setCustomUserClaimsFunction({
        uid: currentUser.uid,
        claims
      });

      console.log('✅ Custom claims set successfully!');

      // Force token refresh to get new claims
      console.log('🔄 Refreshing authentication token...');
      await currentUser.getIdToken(true);
      
      console.log('✅ Token refreshed successfully!');

    } catch (error: any) {
      if (error.code === 'functions/not-found') {
        console.log('⚠️  Firebase Functions not available. Setting claims manually...');
        
        // Alternative approach: Update user document in Firestore
        // This will be used as fallback by the AuthContext
        console.log('📝 Note: Claims will be read from Firestore user document.');
        console.log('   The application will automatically use Firestore data as fallback.');
        
      } else {
        console.error('❌ Error setting custom claims:', error);
        throw error;
      }
    }

    console.log('==========================================');
    console.log('🎉 User setup completed!');
    console.log('');
    console.log('✅ Your account now has access to:');
    console.log(`   • School: Mountain Biking Academy (${SCHOOL_ID})`);
    console.log('   • Role: Admin (full access)');
    console.log('   • All seeded data (lessons, programs, equipment, rentals)');
    console.log('');
    console.log('🔗 You can now refresh the FlowMaster application to see the data!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    throw error;
  }
}

// Alternative function to set claims for a specific user by email
async function setClaimsForUser(email: string, password: string) {
  try {
    console.log(`🔐 Signing in as ${email}...`);
    
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    console.log(`✅ Signed in successfully as ${user.email}`);
    
    // Now set claims for this user
    await setUserClaims();
    
  } catch (error) {
    console.error('❌ Sign in failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 2) {
    // If email and password provided, sign in first
    const [email, password] = args;
    await setClaimsForUser(email, password);
  } else {
    // Use currently signed in user
    await setUserClaims();
  }
  
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { setUserClaims, setClaimsForUser };
