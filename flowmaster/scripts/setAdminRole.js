const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc, collection, getDocs } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDVyQOoNRTZZU0gOxAaC8iLAEBstolTs3k",
  authDomain: "flowmaster-e3947.firebaseapp.com",
  projectId: "flowmaster-e3947",
  storageBucket: "flowmaster-e3947.firebasestorage.app",
  messagingSenderId: "350018115809",
  appId: "1:350018115809:web:4ecfdd7e77b16ebcc63fa0",
  measurementId: "G-CJSD7ZGFWJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Role types
const RoleType = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  INSTRUCTOR: 'instructor',
  STUDENT: 'student',
  CLIENT: 'CLIENT',
};

// Set admin role for all users
async function setAdminRoleForAllUsers() {
  try {
    console.log('Setting admin role for all users...');
    
    // Get all users
    const usersRef = collection(db, 'users');
    const usersSnapshot = await getDocs(usersRef);
    
    if (usersSnapshot.empty) {
      console.log('No users found. Creating a test user...');
      
      // Create a test user with admin role
      const testUserRef = doc(db, 'users', 'test-user-id');
      await setDoc(testUserRef, {
        email: '<EMAIL>',
        displayName: 'Test User',
        roleId: RoleType.ADMIN,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
      
      console.log('Test user created with admin role');
    } else {
      // Set admin role for all existing users
      for (const userDoc of usersSnapshot.docs) {
        const userId = userDoc.id;
        const userData = userDoc.data();
        
        console.log(`Setting admin role for user: ${userId} (${userData.email || 'No email'})`);
        
        await setDoc(doc(db, 'users', userId), {
          ...userData,
          roleId: RoleType.ADMIN,
          updatedAt: new Date().toISOString(),
        }, { merge: true });
        
        console.log(`Admin role set for user: ${userId}`);
      }
      
      console.log('Admin role set for all users');
    }
  } catch (error) {
    console.error('Error setting admin role:', error);
  }
}

// Run the script
setAdminRoleForAllUsers()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
