#!/usr/bin/env node

/**
 * This script uses Node.js worker threads to parallelize the build process
 * and avoid memory issues by splitting the work across multiple processes.
 *
 * Enhanced version for CI environments with additional memory optimizations.
 */

const { Worker } = require('worker_threads');
const path = require('path');
const fs = require('fs');
const { execSync, spawn } = require('child_process');
const os = require('os');

// Clean up environment
console.log('Cleaning up environment...');
try {
  if (fs.existsSync(path.join(__dirname, '../build'))) {
    fs.rmSync(path.join(__dirname, '../build'), { recursive: true, force: true });
  }
  if (fs.existsSync(path.join(__dirname, '../node_modules/.cache'))) {
    fs.rmSync(path.join(__dirname, '../node_modules/.cache'), { recursive: true, force: true });
  }
  execSync('npm cache clean --force', { stdio: 'inherit' });
} catch (error) {
  console.error('Error cleaning up:', error);
}

// Create temporary directory for worker output
const tempDir = path.join(__dirname, '../temp_build');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// Set environment variables
process.env.GENERATE_SOURCEMAP = 'false';
process.env.CI = 'false';
process.env.DISABLE_ESLINT_PLUGIN = 'true';
process.env.TSC_COMPILE_ON_ERROR = 'true';
process.env.NODE_ENV = 'production';
process.env.BABEL_ENV = 'production';

// Check if we're running in a CI environment
const isCI = process.env.CI === 'true' || process.env.GITHUB_ACTIONS === 'true';
console.log(`Running in ${isCI ? 'CI' : 'local'} environment`);

// Get available memory
const totalMemoryMB = Math.floor(os.totalmem() / (1024 * 1024));
const freeMemoryMB = Math.floor(os.freemem() / (1024 * 1024));
console.log(`Total memory: ${totalMemoryMB}MB, Free memory: ${freeMemoryMB}MB`);

// Calculate memory to allocate (75% of total memory in CI, 80% of free memory locally)
const memoryToAllocate = isCI ? Math.floor(totalMemoryMB * 0.75) : Math.floor(freeMemoryMB * 0.8);
console.log(`Allocating ${memoryToAllocate}MB for the build process`);

// Worker script for running the build with optimized settings
const workerScript = `
  const { parentPort } = require('worker_threads');
  const { execSync, spawn } = require('child_process');
  const fs = require('fs');
  const path = require('path');

  // Function to run build with fallbacks
  async function runBuild() {
    try {
      console.log('Worker: Starting optimized build process...');

      // Disable TypeScript type checking to save memory
      process.env.TSC_COMPILE_ON_ERROR = 'true';
      process.env.DISABLE_TYPESCRIPT = 'true';

      // Disable ESLint to save memory
      process.env.DISABLE_ESLINT_PLUGIN = 'true';

      // Disable source maps to save memory
      process.env.GENERATE_SOURCEMAP = 'false';

      // Set production environment
      process.env.NODE_ENV = 'production';
      process.env.BABEL_ENV = 'production';

      // Disable CI mode
      process.env.CI = 'false';

      // Set memory limit
      process.env.NODE_OPTIONS = '--max-old-space-size=${memoryToAllocate} --no-global-gc-scheduling --optimize-for-size';

      // Try direct build first
      try {
        console.log('Attempting direct build with craco...');
        execSync('node --max-old-space-size=${memoryToAllocate} ./node_modules/.bin/craco build', {
          env: process.env,
          stdio: 'inherit'
        });
        return true;
      } catch (error) {
        console.log('Direct build failed, trying alternative approach...');
      }

      // Try with webpack directly as fallback
      try {
        console.log('Attempting build with webpack directly...');

        // Create minimal webpack config
        const webpackConfig = \`
          const path = require('path');
          const TerserPlugin = require('terser-webpack-plugin');

          module.exports = {
            mode: 'production',
            entry: './src/index.tsx',
            output: {
              path: path.resolve(__dirname, 'build'),
              filename: 'static/js/[name].[contenthash:8].js',
              chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
              publicPath: '/'
            },
            optimization: {
              minimize: true,
              minimizer: [new TerserPlugin()],
              splitChunks: {
                chunks: 'all'
              }
            },
            performance: { hints: false }
          };
        \`;

        fs.writeFileSync(path.resolve(process.cwd(), 'webpack.prod.js'), webpackConfig);

        execSync('node --max-old-space-size=${memoryToAllocate} ./node_modules/.bin/webpack --config webpack.prod.js', {
          env: process.env,
          stdio: 'inherit'
        });

        return true;
      } catch (error) {
        console.log('Alternative build failed:', error);
        return false;
      }
    } catch (error) {
      console.error('Worker: All build attempts failed:', error);
      return false;
    }
  }

  // Run the build and report back
  runBuild().then(success => {
    parentPort.postMessage({ success });
  }).catch(error => {
    console.error('Worker: Unhandled error:', error);
    parentPort.postMessage({ success: false, error: error.toString() });
  });
`;

// Write worker script to temporary file
const workerScriptPath = path.join(tempDir, 'worker.js');
fs.writeFileSync(workerScriptPath, workerScript);

console.log('Starting build process with worker thread...');

// Create worker
const worker = new Worker(workerScriptPath);

// Handle worker messages
worker.on('message', (message) => {
  if (message.success) {
    console.log('Build completed successfully!');
  } else {
    console.error('Build failed:', message.error);
    process.exit(1);
  }
});

// Handle worker errors
worker.on('error', (error) => {
  console.error('Worker error:', error);
  process.exit(1);
});

// Handle worker exit
worker.on('exit', (code) => {
  if (code !== 0) {
    console.error(`Worker stopped with exit code ${code}`);
    process.exit(code);
  }

  // Clean up temporary files
  try {
    fs.rmSync(tempDir, { recursive: true, force: true });
  } catch (error) {
    console.error('Error cleaning up temporary files:', error);
  }

  console.log('Build process completed.');
});
