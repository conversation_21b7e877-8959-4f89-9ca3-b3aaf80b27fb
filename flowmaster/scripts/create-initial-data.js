/**
 * <PERSON><PERSON><PERSON> to create initial data in the real Firebase project
 * 
 * This script creates a basic school and other essential data in your Firebase project.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../firebase-service-account.json');
  
  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Function to create a school
async function createSchool(schoolData) {
  try {
    const schoolRef = db.collection('schools').doc(schoolData.id);
    await schoolRef.set(schoolData);
    console.log(`School "${schoolData.name}" created with ID: ${schoolData.id}`);
    return schoolData.id;
  } catch (error) {
    console.error('Error creating school:', error);
    throw error;
  }
}

// Function to create a student
async function createStudent(schoolId, studentData) {
  try {
    const studentRef = db.collection('schools').doc(schoolId).collection('students').doc();
    const studentId = studentRef.id;
    await studentRef.set({
      ...studentData,
      id: studentId,
      schoolId: schoolId,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log(`Student "${studentData.name}" created with ID: ${studentId}`);
    return studentId;
  } catch (error) {
    console.error('Error creating student:', error);
    throw error;
  }
}

// Function to create an instructor
async function createInstructor(schoolId, instructorData) {
  try {
    const instructorRef = db.collection('schools').doc(schoolId).collection('instructors').doc();
    const instructorId = instructorRef.id;
    await instructorRef.set({
      ...instructorData,
      id: instructorId,
      schoolId: schoolId,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log(`Instructor "${instructorData.name}" created with ID: ${instructorId}`);
    return instructorId;
  } catch (error) {
    console.error('Error creating instructor:', error);
    throw error;
  }
}

// Function to create a program
async function createProgram(schoolId, programData) {
  try {
    const programRef = db.collection('schools').doc(schoolId).collection('programs').doc();
    const programId = programRef.id;
    await programRef.set({
      ...programData,
      id: programId,
      schoolId: schoolId,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log(`Program "${programData.name}" created with ID: ${programId}`);
    return programId;
  } catch (error) {
    console.error('Error creating program:', error);
    throw error;
  }
}

// Main function to create initial data
async function createInitialData() {
  console.log('Creating initial data in Firebase...');
  
  const confirmed = await askForConfirmation('This will create initial data in your Firebase project. Continue? (y/n) ');
  
  if (!confirmed) {
    console.log('Operation cancelled');
    process.exit(0);
  }
  
  try {
    // Create a school
    const schoolId = await createSchool({
      id: 'school1',
      name: 'FlowMaster Demo School',
      address: '123 Main St, Anytown, USA',
      phone: '************',
      email: '<EMAIL>',
      website: 'https://flowmasterdemo.com',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Create students
    const student1Id = await createStudent(schoolId, {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '************',
      type: 'program',
      status: 'active'
    });
    
    const student2Id = await createStudent(schoolId, {
      name: 'Jane Doe',
      email: '<EMAIL>',
      phone: '************',
      type: 'program',
      status: 'active'
    });
    
    // Create instructors
    const instructor1Id = await createInstructor(schoolId, {
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '************',
      specialties: ['surfing', 'kiteboarding'],
      status: 'active'
    });
    
    const instructor2Id = await createInstructor(schoolId, {
      name: 'Sarah Williams',
      email: '<EMAIL>',
      phone: '************',
      specialties: ['windsurfing', 'sailing'],
      status: 'active'
    });
    
    // Create programs
    await createProgram(schoolId, {
      name: 'Summer Surf Camp',
      description: 'Learn to surf in our intensive summer camp',
      instructorId: instructor1Id,
      capacity: 10,
      startDate: new Date('2025-06-01'),
      endDate: new Date('2025-08-31'),
      status: 'active',
      studentIds: [student1Id, student2Id]
    });
    
    await createProgram(schoolId, {
      name: 'Kiteboarding Basics',
      description: 'Introduction to kiteboarding for beginners',
      instructorId: instructor2Id,
      capacity: 5,
      startDate: new Date('2025-07-01'),
      endDate: new Date('2025-07-31'),
      status: 'active',
      studentIds: [student1Id]
    });
    
    console.log('Initial data creation complete!');
  } catch (error) {
    console.error('Error creating initial data:', error);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the main function
createInitialData();
