#!/usr/bin/env node

/**
 * Debug CI Script
 * 
 * This script is designed to help debug issues in CI environments by
 * printing out information about the environment and the build process.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

console.log('Debug CI - Starting');
console.log('===================');

// Print Node.js version
console.log(`Node.js version: ${process.version}`);

// Print environment variables
console.log('\nEnvironment Variables:');
console.log('---------------------');
Object.keys(process.env).sort().forEach(key => {
  // Filter out sensitive information
  if (!key.includes('TOKEN') && !key.includes('SECRET') && !key.includes('PASSWORD')) {
    console.log(`${key}: ${process.env[key]}`);
  }
});

// Print system information
console.log('\nSystem Information:');
console.log('------------------');
console.log(`Platform: ${os.platform()}`);
console.log(`Architecture: ${os.arch()}`);
console.log(`Total memory: ${Math.floor(os.totalmem() / (1024 * 1024))}MB`);
console.log(`Free memory: ${Math.floor(os.freemem() / (1024 * 1024))}MB`);
console.log(`CPUs: ${os.cpus().length}`);

// Print disk space
console.log('\nDisk Space:');
console.log('-----------');
try {
  const diskSpace = execSync('df -h').toString();
  console.log(diskSpace);
} catch (error) {
  console.log('Error getting disk space:', error);
}

// Print installed packages
console.log('\nInstalled Packages:');
console.log('------------------');
try {
  const packageJson = require(path.resolve(__dirname, '..', 'package.json'));
  console.log('Dependencies:');
  Object.keys(packageJson.dependencies || {}).forEach(dep => {
    console.log(`- ${dep}: ${packageJson.dependencies[dep]}`);
  });
  console.log('\nDev Dependencies:');
  Object.keys(packageJson.devDependencies || {}).forEach(dep => {
    console.log(`- ${dep}: ${packageJson.devDependencies[dep]}`);
  });
} catch (error) {
  console.log('Error getting installed packages:', error);
}

// Print available build scripts
console.log('\nAvailable Build Scripts:');
console.log('----------------------');
try {
  const packageJson = require(path.resolve(__dirname, '..', 'package.json'));
  Object.keys(packageJson.scripts || {}).forEach(script => {
    if (script.startsWith('build')) {
      console.log(`- ${script}: ${packageJson.scripts[script]}`);
    }
  });
} catch (error) {
  console.log('Error getting build scripts:', error);
}

// Print available workflow files
console.log('\nAvailable Workflow Files:');
console.log('------------------------');
try {
  const workflowsDir = path.resolve(__dirname, '..', '.github', 'workflows');
  if (fs.existsSync(workflowsDir)) {
    const workflowFiles = fs.readdirSync(workflowsDir);
    workflowFiles.forEach(file => {
      console.log(`- ${file}`);
    });
  } else {
    console.log('No workflows directory found');
  }
} catch (error) {
  console.log('Error getting workflow files:', error);
}

console.log('\nDebug CI - Completed');
console.log('====================');
