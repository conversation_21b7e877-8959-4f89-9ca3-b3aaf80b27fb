{"title": "Nastavitve", "subtitle": "Upravljajte svoj račun in nastavitve aplikacije", "categories": {"profile": "Profil", "language": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "security": "Varnost", "school": "Šola", "sport": "Šport", "booking": "Rezervacije", "financial": "Finance", "users": "Uporabniki", "system": "Sistem"}, "descriptions": {"profile": "Upravljajte svoj osebni profil in nastavitve računa", "language": "Spremenite jezik in nastavitve lokalizacije", "notifications": "Konfigurirajte svoje nastavitve obvestil", "security": "Upravljajte varnostne nastavitve in avtentikacijo", "school": "Konfigurirajte nastavitve in preference vaše <PERSON>", "sport": "Upravljajte razpoložljive športe in discipline", "booking": "Konfigurirajte pravila rezervacij in razpoložljivost", "financial": "Upravljajte finančne nastavitve in možnosti plačil", "users": "Upravljajte uporabniške račune in dovoljenja", "system": "Konfigurirajte sistemske nastavitve in preference"}, "profile": {"title": "Profil", "description": "Upravljajte svoj osebni profil in nastavitve računa", "displayName": "Prikazno ime", "email": "E-pošta", "phoneNumber": "Telefonska številka", "bio": "Biografija", "updateSuccess": "Profil je bil us<PERSON><PERSON> posodobljen", "updateError": "Napaka pri posodabljanju profila"}, "language": {"title": "<PERSON><PERSON><PERSON>", "description": "Spremenite jezik in nastavitve lokalizacije"}, "notifications": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Konfigurirajte svoje nastavitve obvestil", "emailSettings": "E-poš<PERSON>na obves<PERSON>a", "pushSettings": "<PERSON><PERSON><PERSON> obvestila", "lessons": "Posodobitve lek<PERSON>j", "programs": "Posodobitve programov", "announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reminders": "Opomniki", "updateSuccess": "Nastavitve obvestil so bile uspešno posodobljene", "updateError": "Napaka pri posodabljanju nastavitev obvestil"}, "security": {"title": "Varnost", "description": "Upravljajte varnostne nastavitve in avtentikacijo", "twoFactorAuth": "Dvostopenjska avtentikacija", "changePassword": "Spremeni geslo", "passwordSettings": "Nastavitve gesla", "loginSettings": "Nastavitve prijave", "minPasswordLength": "Minimalna dolžina gesla", "minPasswordLengthError": "Geslo mora biti dolgo vsaj 8 znakov", "requireSpecialCharacters": "Zahtevaj <PERSON>ne znake", "requireNumbers": "Zahtevaj <PERSON>", "requireUppercase": "Zahtevaj velike črke", "passwordExpiryDays": "Veljavnost gesla v dnevih", "passwordExpiryDaysError": "Veljavnost gesla mora biti 0 ali več dni", "maxLoginAttempts": "Največje število poskusov prijave", "maxLoginAttemptsError": "Največje število poskusov prijave mora biti vsaj 1", "twoFactorEnabled": "Omogoči dvostopenjsko avtentikacijo", "sessionTimeout": "Časovna omejitev seje", "minutes": "minut", "days": "dni", "fetchError": "Napaka pri pridobivanju varnostnih nastavitev", "updateError": "Napaka pri posodobitvi varnostnih nastavitev", "updateSuccess": "Varnostne nastavitve uspešno posodobljene"}, "school": {"title": "Šola", "description": "Konfigurirajte nastavitve in preference vaše smučarske šole", "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "phone": "Telefonska številka", "email": "E-pošta", "website": "Spletna stran", "fetchError": "Napaka pri pridobivanju nastavitev šole", "updateError": "Napaka pri posodabljanju nastavitev šole", "updateSuccess": "Nastavitve šole so bile uspešno posodobljene", "unauthorized": "Za dostop do nastavitev šole morate biti prijavljeni"}, "sport": {"title": "Šport", "description": "Upravljajte razpoložljive športe in discipline", "availableSports": "Razpoložljivi športi", "customSports": "Prilagojeni športi", "enterCustomSport": "Vnesite prilagojen šport", "selectedSports": "Izbrani športi", "saveChanges": "<PERSON><PERSON><PERSON> sprem<PERSON>", "disciplines": "Discipline", "addSport": "Dodaj š<PERSON>", "availableLevels": "Razpoložljivi nivoji", "customLevels": "Prilagojeni nivoji", "enterCustomLevel": "Vnesite prilagojen nivo", "selectedLevels": "Izbrani nivoji", "levels": {"beginner": "Začetnik", "intermediate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advanced": "<PERSON><PERSON><PERSON><PERSON>"}, "addDiscipline": "<PERSON><PERSON><PERSON> disciplino", "sportName": "<PERSON><PERSON>", "disciplineName": "Ime discipline", "difficulty": "Težavnost", "season": "Sezona", "sports": {"skiing": "Smučanje", "snowboarding": "Deskanje na snegu", "mountainBiking": "Gorsko kolesarstvo", "surfing": "Deskanje na valovih", "windsurfing": "J<PERSON><PERSON>je na deski", "kitesurfing": "<PERSON><PERSON><PERSON><PERSON>", "rockClimbing": "Plezanje", "kayaking": "Kajakaštvo", "hiking": "Pohodništvo"}, "equipment": "Oprema", "requirements": "<PERSON><PERSON><PERSON><PERSON>", "updateSuccess": "Nastavitve športa so bile uspešno posodobljene", "updateError": "Napaka pri posodabljanju nastavitev športa", "deleteSuccess": "Šport je bil uspešno izbrisan", "deleteError": "Napaka pri brisanju športa", "disciplineUpdateSuccess": "Disciplina je bila us<PERSON>š<PERSON> posodobljena", "disciplineUpdateError": "Napaka pri posodabljanju discipline", "disciplineDeleteSuccess": "Disciplina je bila uspešno izbrisana", "disciplineDeleteError": "Napaka pri brisanju discipline"}, "bookings": {"title": "Rezervacije", "description": "Konfigurirajte pravila rezervacij in razpoložljivost", "fetchError": "Napaka pri pridobivanju nastavitev rezervacij", "saveError": "Napaka pri shranjevanju nastavitev rezervacij", "saveSuccess": "Nastavitve rezervacij uspešno shranjene", "rules": "Pravila rezervacij", "availability": "Nastavitve razpoložljivosti", "restrictions": "Omejitve rezervacij", "cancellation": "<PERSON><PERSON>vila odpovedi", "defaults": "Privzete nastavitve", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateError": "Napaka pri posodobitvi", "minAdvanceBooking": "Minimalni čas vnaprejšnje rezervacije", "minAdvanceBookingError": "Minimalni čas vnaprejšnje rezervacije ne more biti krajši od 1 dneva", "maxAdvanceBooking": "Maksimalni čas vnaprejšnje rezervacije", "maxAdvanceBookingError": "Maksimalni čas vnaprejšnje rezervacije ne more biti daljši od 30 dni", "cancellationDeadline": "Rok za odpoved", "defaultLessonDuration": "Privzeto trajan<PERSON> le<PERSON>", "requirePaymentUpfront": "Zahtevaj plačilo vnaprej", "allowInstantBooking": "Do<PERSON><PERSON> rezervacijo", "cancellationDeadlineError": "Napaka pri roku za odpoved", "unauthorized": "Nimate dovoljenja za rezervacije", "placeholder": "Izberite termin za rezervacijo"}, "finance": {"title": "Finance", "description": "Upravljajte finančne nastavitve in možnosti plačil"}, "users": {"title": "Uporabniki", "description": "Upravljajte uporabniške račune in dovoljenja", "addUser": "<PERSON><PERSON><PERSON>", "editUser": "<PERSON><PERSON><PERSON>", "deleteUser": "Izbriši uporabnika", "name": "Ime", "email": "E-pošta", "role": "Vloga", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "confirmDelete": "<PERSON>, da želite izbrisati tega uporabnika?", "userDeleted": "Uporabnik uspešno izbrisan", "userUpdated": "Uporabnik uspešno posodobljen", "userAdded": "Uporabnik uspešno dodan", "deleteError": "Napaka pri brisanju uporabnika", "updateError": "Napaka pri posodabljanju uporabnika", "addError": "Napaka pri dodajanju uporabnika", "manageUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> po imenu ali e-po<PERSON><PERSON>...", "noSearchResults": "<PERSON>, ki bi ustrezali iskanju", "noUsers": "<PERSON> naj<PERSON>", "editRole": "Uredi vlogo", "editUserRole": "Uredi vlogo uporabnika", "primaryRole": "Primarna vloga", "additionalRoles": "Dodatne vloge", "roleExplanation": "Primarna vloga določa glavna dovoljenja, dodatne vloge pa lahko dodelijo dodatne zmožnosti.", "roleUpdateSuccess": "Vloga uporabnika je bila us<PERSON>šno posodobljena", "roleUpdateError": "Napaka pri posodabljanju vloge uporabnika", "addUserDescription": "Za dodajanje novega uporabnika uporabite konzolo Firebase Authentication za ustvarjanje računa, nato pa tukaj dodelite vloge.", "addUserNote": "Opomba: Nove uporabnike je treba ustvariti v Firebase Authentication, preden jih lahko upravljate tukaj.", "openFirebaseConsole": "Odpri Firebase konzolo", "refreshAfterAdd": "Osveži seznam uporabnikov", "createUserInFirestore": "Ustvari uporabnika v Firestore", "createUserInFirestoreDescription": "Po ustvarjanju uporabnika v Firebase Authentication vnesite njegove podatke spodaj, da ustvarite ustrezni dokument v Firestore.", "firebaseUid": "Firebase UID", "firebaseUidHelp": "UID iz Firebase Authentication", "createUser": "Ustvari upora<PERSON>a", "uidRequired": "Firebase UID je obvezen", "createSuccess": "Uporabnik je bil uspeš<PERSON> ustvarjen", "createError": "Napaka pri ustvar<PERSON><PERSON> upora<PERSON>nika", "fetchError": "Napaka pri na<PERSON><PERSON><PERSON>", "loadMoreError": "Napaka pri nalaganju ve<PERSON>"}, "system": {"title": "Sistem", "description": "Konfigurirajte sistemske nastavitve in preference"}, "account": {"title": "<PERSON><PERSON><PERSON>", "email": "E-pošta", "role": "Vloga", "deleteAccount": "Izbriši račun"}, "messages": {"success": "Nastavitve so uspešno shranjene", "error": "Napaka pri shranjevanju nastavitev"}, "settings": {"title": "Nastavitve", "save": "<PERSON><PERSON><PERSON>t<PERSON>", "reset": "Ponastavi nastavitve", "loading": "Nalag<PERSON>je nastavitev...", "welcome": {"description": "Dobrodošli v nastavitvah FlowMaster"}}, "sections": {"account": "Nastavitve računa", "accountDesc": "Upravljajte osebne nastavitve računa", "school": "Uprav<PERSON><PERSON><PERSON>", "schoolDesc": "Konfigurirajte delovanje in aktivnosti šole", "security": "Varnost in dostop", "securityDesc": "Upravljajte varnostne nastavitve in uporabniški dostop", "system": "Sistemske nastavitve", "systemDesc": "Upravljajte sistemske konfiguracije"}}