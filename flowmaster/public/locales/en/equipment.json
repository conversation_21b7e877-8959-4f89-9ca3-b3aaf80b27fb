{"tabs": {"inventory": "Inventory", "rentals": "Rentals", "equipment": "Equipment"}, "inventory": {"title": "Equipment Inventory", "addButton": "Add Equipment", "nameColumn": "Name", "categoryColumn": "Category", "sizeColumn": "Size", "serialColumn": "Serial Number", "conditionColumn": "Condition", "availabilityColumn": "Availability", "actionsColumn": "Actions", "noItems": "No equipment items found", "deleteTitle": "Delete Equipment Item", "deleteConfirmation": "Are you sure you want to delete {{name}}? This action cannot be undone."}, "form": {"addTitle": "Add Equipment Item", "editTitle": "Edit Equipment Item", "name": "Name", "category": "Category", "size": "Size", "serialNumber": "Serial Number", "condition": "Condition", "available": "Available for Rental", "additionalInfo": "Additional Information", "purchaseDate": "Purchase Date", "lastMaintenanceDate": "Last Maintenance Date", "notes": "Notes"}, "category": {"kite": "<PERSON>e", "board": "Board", "harness": "<PERSON><PERSON><PERSON>", "wetsuit": "Wetsuit", "helmet": "<PERSON><PERSON><PERSON>", "other": "Other", "Harness": "<PERSON><PERSON><PERSON>", "Helmet": "<PERSON><PERSON><PERSON>", "Kiteboard": "Kiteboard", "Surfboard": "Surfboard", "Wetsuit": "Wetsuit"}, "condition": {"good": "Good", "damaged": "Damaged", "lost": "Lost", "maintenance": "Needs Maintenance"}, "status": {"available": "Available", "unavailable": "In Use"}, "filters": {"search": "Search", "category": "Category", "condition": "Condition", "availability": "Availability", "status": "Status", "dateFrom": "From Date", "dateTo": "To Date", "apply": "Apply Filters"}, "rental": {"title": "Equipment Rentals", "addButton": "New Rental", "dateColumn": "Date", "customerColumn": "Customer", "itemsColumn": "Items", "dueDateColumn": "Due Date", "statusColumn": "Status", "actionsColumn": "Actions", "itemsCount": "items", "noItems": "No rentals found", "returnAction": "Return", "returnTitle": "Return Equipment", "returnConfirmation": "Are you sure you want to mark this rental as returned?", "confirmReturn": "Confirm Return", "customer": "Customer", "items": "Items", "status": {"active": "Active", "returned": "Returned", "overdue": "Overdue"}, "form": {"title": "New Equipment Rental", "customerSection": "Customer Information", "customerType": "Customer Type", "studentType": "Student", "clientType": "Client", "guestType": "Guest", "selectStudent": "Select Student", "selectClient": "Select Client", "guestName": "Guest Name", "rentalDetails": "Rental Details", "rentalDate": "Rental Date", "dueDate": "Due Date", "deposit": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "notes": "Notes", "equipmentSection": "Equipment Items", "selectEquipment": "Select Equipment", "addItem": "Add", "selectedItems": "Selected Items", "noItemsSelected": "No items selected"}, "detail": {"title": "Rental Details", "customer": "Customer", "dates": "Dates", "rentalDate": "Rental Date", "dueDate": "Due Date", "returnDate": "Return Date", "status": "Status", "returnButton": "Return Equipment", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositReturned": "<PERSON><PERSON><PERSON><PERSON> returned", "depositHeld": "Deposit held", "notes": "Notes", "equipmentItems": "Equipment Items", "condition": "Condition", "itemNotes": "Notes", "returnTitle": "Return Equipment", "returnInstructions": "Please check the condition of each item and add notes if needed.", "returnCondition": "Condition", "returnNotes": "Notes", "confirmReturn": "Confirm Return", "notFound": "<PERSON><PERSON> not found", "returnSuccess": "Equipment returned successfully"}}, "validation": {"nameRequired": "Name is required", "categoryRequired": "Category is required", "conditionRequired": "Condition is required", "customerRequired": "Customer is required", "dateRequired": "Date is required", "dueDateRequired": "Due date is required", "itemsRequired": "At least one item is required"}, "messages": {"fetchError": "Failed to fetch data", "saveError": "Failed to save data", "deleteError": "Failed to delete item", "returnError": "Failed to return rental", "saveSuccess": "Data saved successfully", "returnSuccess": "Equipment returned successfully"}, "lessons": {"equipmentAssignments": "Equipment Assignments", "assignEquipment": "Assign Equipment", "student": "Student", "equipment": "Equipment", "assignedAt": "Assigned At", "actions": "Actions", "noEquipment": "No equipment assigned for this lesson", "selectStudent": "Select Student", "selectEquipment": "Select Equipment"}, "dashboard": {"equipmentStatus": "Equipment Status", "overdueRentals": "Overdue Rentals", "quickRental": "New Rental", "createRental": "Create New Rental", "continueToRental": "Continue to Rental", "totalItems": "Total Items", "availability": "Availability", "condition": "Condition", "categories": "Categories", "noOverdueRentals": "No overdue rentals", "daysOverdue": "{{days}} days overdue"}, "students": {"activeRentals": "Active Rentals", "noRentals": "No equipment rentals found for this student"}}