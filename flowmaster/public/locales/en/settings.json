{"title": "Settings", "subtitle": "Manage your account and application settings", "categories": {"profile": "Profile", "language": "Language", "notifications": "Notifications", "security": "Security", "school": "School", "sport": "Sport", "booking": "Bookings", "financial": "Financial", "users": "Users", "system": "System"}, "descriptions": {"profile": "Manage your personal profile and account settings", "language": "Change language and localization settings", "notifications": "Configure your notification preferences", "security": "Manage security settings and authentication", "school": "Configure your ski school settings and preferences", "sport": "Manage available sports and disciplines", "booking": "Configure booking rules and availability", "financial": "Manage financial settings and payment options", "users": "Manage user accounts and permissions", "system": "Configure system settings and preferences"}, "profile": {"title": "Profile", "description": "Manage your personal profile and account settings"}, "language": {"title": "Language", "description": "Change language and localization settings"}, "notifications": {"title": "Notifications", "description": "Configure your notification preferences", "emailSettings": "Email Notifications", "pushSettings": "Push Notifications", "lessons": "Lesson Updates", "programs": "Program Updates", "announcements": "Announcements", "reminders": "Reminders", "updateSuccess": "Notification settings updated successfully", "updateError": "Failed to update notification settings", "weeklyDigest": "Weekly Digest"}, "security": {"title": "Security", "description": "Manage security settings and authentication", "twoFactorAuth": "Two-Factor Authentication", "changePassword": "Change Password", "passwordSettings": "Password Settings", "loginSettings": "<PERSON><PERSON>", "minPasswordLength": "Minimum Password Length", "minPasswordLengthError": "Password must be at least 8 characters long", "requireSpecialCharacters": "Require Special Characters", "requireNumbers": "Require Numbers", "requireUppercase": "Require Uppercase Letters", "passwordExpiryDays": "Password Expiry Days", "passwordExpiryDaysError": "Password expiry days must be 0 or greater", "maxLoginAttempts": "Maximum Login Attempts", "maxLoginAttemptsError": "Maximum login attempts must be at least 1", "twoFactorEnabled": "Enable Two-Factor Authentication", "sessionTimeout": "Session Timeout", "minutes": "minutes", "days": "days", "fetchError": "Failed to fetch security settings", "updateError": "Failed to update security settings", "updateSuccess": "Security settings updated successfully"}, "school": {"title": "School", "description": "Configure your ski school settings and preferences", "name": "School Name", "address": "Address", "phone": "Phone Number", "email": "Email", "website": "Website", "fetchError": "Failed to fetch school settings", "updateError": "Failed to update school settings", "updateSuccess": "School settings updated successfully", "unauthorized": "You must be logged in to access school settings"}, "sport": {"title": "Sport", "description": "Manage available sports and disciplines", "availableSports": "Available Sports", "customSports": "Custom Sports", "enterCustomSport": "Enter custom sport", "selectedSports": "Selected Sports", "saveChanges": "Save changes", "disciplines": "Disciplines", "addSport": "Add Sport", "addDiscipline": "Add Discipline", "sportName": "Sport Name", "disciplineName": "Discipline Name", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "season": "Season", "availableLevels": "Available Levels", "customLevels": "Custom Levels", "enterCustomLevel": "Enter custom level", "selectedLevels": "Selected Levels", "levels": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "sports": {"skiing": "Skiing", "snowboarding": "Snowboarding", "mountainBiking": "Mountain Biking", "surfing": "Surfing", "windsurfing": "Windsurfing", "kitesurfing": "Kitesurfing", "rockClimbing": "Rock Climbing", "kayaking": "Kayaking", "hiking": "Hiking"}, "equipment": "Equipment", "requirements": "Requirements", "updateSuccess": "Sport settings updated successfully", "updateError": "Failed to update sport settings", "deleteSuccess": "Sport deleted successfully", "deleteError": "Failed to delete sport", "disciplineUpdateSuccess": "Discipline updated successfully", "disciplineUpdateError": "Failed to update discipline", "disciplineDeleteSuccess": "Discipline deleted successfully", "disciplineDeleteError": "Failed to delete discipline"}, "bookings": {"title": "Bookings", "description": "Configure booking rules and availability", "fetchError": "Failed to fetch booking settings", "saveError": "Failed to save booking settings", "saveSuccess": "Booking settings saved successfully", "rules": "Booking Rules", "availability": "Availability Settings", "restrictions": "Booking Restrictions", "cancellation": "Cancellation Policy", "defaults": "<PERSON><PERSON><PERSON>", "updateSuccess": "Update Success", "updateError": "Update Error", "allowInstantBooking": "Allow Instant Booking", "minAdvanceBooking": "Minimum Advance Booking", "minAdvanceBookingError": "Minimum advance booking cannot be less than 1 day", "maxAdvanceBooking": "Maximum Advance Booking", "maxAdvanceBookingError": "Maximum advance booking cannot be more than 30 days", "cancellationDeadline": "Cancellation Deadline", "cancellationDeadlineError": "Cancellation Deadline Error", "defaultLessonDuration": "De<PERSON>ult Lesson <PERSON>", "requirePaymentUpfront": "Require Payment Upfront", "unauthorized": "Not authorized", "placeholder": "Coming soon"}, "finance": {"title": "Financial", "description": "Manage financial settings and payment options"}, "users": {"title": "Users", "description": "Manage user accounts and permissions", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this user?", "userDeleted": "User deleted successfully", "userUpdated": "User updated successfully", "userAdded": "User added successfully", "deleteError": "Error deleting user", "updateError": "Error updating user", "addError": "Error adding user", "manageUsers": "Manage Users", "searchPlaceholder": "Search users by name or email...", "noSearchResults": "No users match your search", "noUsers": "No users found", "editRole": "Edit Role", "editUserRole": "Edit User Role", "primaryRole": "Primary Role", "additionalRoles": "Additional Roles", "roleExplanation": "The primary role determines the main permissions, while additional roles can grant extra capabilities.", "roleUpdateSuccess": "User role updated successfully", "roleUpdateError": "Failed to update user role", "addUserDescription": "To add a new user, please use the Firebase Authentication console to create the account first, then assign roles here.", "addUserNote": "Note: New users need to be created in Firebase Authentication before they can be managed here.", "openFirebaseConsole": "Open Firebase Console", "refreshAfterAdd": "Refresh User List", "createUserInFirestore": "Create User in Firestore", "createUserInFirestoreDescription": "After creating a user in Firebase Authentication, enter their details below to create a corresponding document in Firestore.", "firebaseUid": "Firebase UID", "firebaseUidHelp": "The UID from Firebase Authentication", "createUser": "Create User", "uidRequired": "Firebase UID is required", "createSuccess": "User created successfully", "createError": "Failed to create user", "fetchError": "Failed to load users", "loadMoreError": "Failed to load more users"}, "system": {"title": "System", "description": "Configure system settings and preferences"}, "account": {"title": "Account", "email": "Email", "role": "Role", "deleteAccount": "Delete Account"}, "messages": {"success": "Settings updated successfully", "error": "Failed to update settings"}, "settings": {"title": "Settings", "save": "Save Settings", "reset": "Reset Settings", "loading": "Loading Settings...", "welcome": {"description": "Welcome to FlowMaster Settings"}}, "sections": {"account": "Account <PERSON><PERSON>", "accountDesc": "Manage your personal account preferences", "school": "School Management", "schoolDesc": "Configure school operations and activities", "security": "Security & Access", "securityDesc": "Manage security and user access settings", "system": "System Settings", "systemDesc": "Manage system-wide configurations"}}