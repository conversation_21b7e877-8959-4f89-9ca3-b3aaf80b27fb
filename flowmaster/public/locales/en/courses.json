{"courses": "Courses", "coursesDesc": "Regular courses for different age groups", "createCourse": "Create New Course", "editCourse": "Edit Course", "courseTitle": "Course Title", "courseType": "Course Type", "weekly": "Weekly Course", "daily": "Daily Course", "ageGroup": "Age Group", "children": "Children", "adults": "Adults", "date": "Date", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "selectDays": "Select Days", "timeSlots": "Time Slots", "addTimeSlot": "Add Time Slot", "startTime": "Start Time", "endTime": "End Time", "remove": "Remove", "discipline": "Discipline", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "students": "Students", "selectStudents": "Select Students", "addNewStudent": "Add New Student", "form": {"title": "Course Title", "type": "Course Type", "days": {"sunday": "Sun", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat"}, "ageGroup": {"children": "Children", "adults": "Adults"}, "instructor": "Select Instructor", "addStudent": "Add Student", "addNewStudent": "Add New Student", "selectStudents": "Select Students", "dateTime": "Date and Time", "startTime": "Start Time", "endTime": "End Time", "startDate": "Start Date", "endDate": "End Date", "duration": "Duration (minutes)", "durationHelper": "Select lesson duration", "minutes": "Minutes", "discipline": "Discipline", "level": "Level", "levels": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "students": "Students", "notes": "Notes"}, "errors": {"titleRequired": "Course title is required", "disciplineRequired": "Discipline is required", "instructorRequired": "Instructor is required", "daysRequired": "Please select at least one day", "timeSlotsRequired": "Please add at least one time slot", "endDateBeforeStartDate": "End date must be after start date", "endTimeBeforeStartTime": "End time must be after start time"}, "create": {"title": "Create New Course"}}