{"title": "Programs", "schoolPrograms": "School Programs", "schoolProgramsDesc": "Programs for schools and organized groups", "groupPrograms": "Group Programs", "groupProgramsDesc": "Group programs for schools and organized groups", "individualPrograms": "Individual Programs", "individualProgramsDesc": "Individual programs for students", "programTypes": {"yearly": "Yearly Program", "seasonal": "Seasonal Program", "camp": "Camp"}, "status": {"active": "Active", "completed": "Completed", "archived": "Archived"}, "attendance": {"present": "Present", "absent": "Absent", "excused": "Excused", "takeAttendance": "Take Attendance", "markAll": "<PERSON>", "markAllAs": "Mark <PERSON> as {{status}}", "attendanceFor": "Attendance for {{date}}", "excuseDialogTitle": "Excuse Student", "excuseDialogText": "Please provide a reason for excusing this student:", "excuseReason": "Reason"}, "labels": {"attendanceStats": "Attendance Statistics", "total": "Total", "notMarked": "Not Marked", "attendanceRate": "Attendance Rate", "makeup": "Makeup"}, "dialogs": {"excuseTitle": "Excuse Absence", "excuseDescription": "Please provide a reason for the excused absence:"}, "attendanceActions": {"saveAttendance": "Save Attendance", "exportAttendance": "Export Attendance", "selectSession": "Select a session", "markSelectedAs": "<PERSON>"}, "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "actions": {"create": "Create Program", "edit": "Edit Program", "delete": "Delete Program", "archive": "Archive Program", "view": "View Program", "generateSessions": "Generate Sessions", "exportAttendance": "Export Attendance", "addSession": "Add Session", "editSession": "Edit Session", "deleteSession": "Delete Session", "addMakeupSession": "Add Makeup Session", "back": "Back", "cancel": "Cancel", "update": "Update Program", "takeAttendance": "Take Attendance", "addParticipant": "Add Participant", "updatePayment": "Update Payment Status", "removeParticipant": "Remove Participant", "dismiss": "<PERSON><PERSON><PERSON>", "save": "Save"}, "fields": {"name": "Program Name", "type": "Program Type", "description": "Description", "location": "Location", "startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time", "endTime": "End Time", "days": "Days", "participants": "Participants", "instructors": "Instructors", "totalFee": "Total Fee", "currency": "<PERSON><PERSON><PERSON><PERSON>", "skills": "Skills", "goals": "Goals", "notes": "Notes", "date": "Date", "time": "Time", "status": "Status", "attendance": "Attendance", "actions": "Actions", "installments": "Installments", "installmentsHelp": "Number of allowed installments", "dueDate": "Due Date", "dueDateHelp": "When payment is due", "earlyBirdDiscount": "Early <PERSON> Discount", "earlyBirdDiscountHelp": "Discount for early payment", "schedule": "Schedule", "contact": "Contact", "paymentStatus": "Payment Status", "session": "Session"}, "tabs": {"overview": "Overview", "sessions": "Sessions", "participants": "Participants", "attendance": "Attendance", "progress": "Progress", "reports": "Reports", "lessons": "Lessons", "makeups": "Makeups", "payments": "Payments", "assignment": "Assignment", "schedule": "Schedule"}, "messages": {"createSuccess": "Program created successfully", "updateSuccess": "Program updated successfully", "deleteSuccess": "Program deleted successfully", "archiveSuccess": "Program archived successfully", "generateSessionsSuccess": "Sessions generated successfully", "noPrograms": "No programs found", "confirmDelete": "Are you sure you want to delete this program? This action cannot be undone.", "sessionCreated": "Session created successfully", "sessionUpdated": "Session updated successfully", "sessionDeleted": "Session deleted successfully", "attendanceSaved": "Attendance saved successfully", "saveError": "Failed to save program", "fetchError": "Failed to fetch data"}, "filters": {"all": "All Programs", "active": "Active Programs", "completed": "Completed Programs", "archived": "Archived Programs", "type": "Filter by Type", "instructor": "Filter by <PERSON><PERSON><PERSON><PERSON>", "date": "Filter by Date", "search": "Search", "status": "Filter by Status"}, "payment": {"status": {"paid": "Paid", "partial": "Partially Paid", "pending": "Payment Pending"}, "updatePayment": "Update Payment Status"}, "payments": {"notes": "Notes", "deletePayment": "Delete Payment", "deleteConfirmation": "Are you sure you want to delete this payment? This action cannot be undone."}, "placeholders": {"selectParticipants": "Select Participants", "selectInstructors": "Select Instructors"}, "studentAssignment": {"noAvailableStudents": "No available students"}, "participants": {"count": "{{count}} Students"}, "sections": {"basicInfo": "Basic Information", "schedule": "Schedule", "people": "People", "pricing": "Pricing", "progress": "Progress", "details": "Program Details", "statistics": "Statistics"}, "sessionsTabs": {"list": "Session List", "bulk": "Bulk Operations"}, "notSpecified": "Not specified", "noGoalsSpecified": "No goals specified", "noNotes": "No notes", "stats": {"participants": "Participants", "instructors": "Instructors", "sessions": "Sessions", "attendance": "Attendance"}, "sessionStatus": {"upcoming": "Upcoming", "pendingAttendance": "Pending Attendance", "completed": "Completed"}, "lessons": {"title": "Program Lessons", "linkedLessons": "Linked Lessons", "noLinkedLessons": "No linked lessons", "createLesson": "C<PERSON> <PERSON><PERSON>", "linkLesson": "<PERSON> Existing Lesson", "duration": "Duration (minutes)", "selectLesson": "Select Lesson"}, "sessions": {"date": "Date"}, "notAvailable": "Not available", "makeup": {"title": "Makeup Sessions", "scheduled": "Scheduled Makeups", "needed": "Students Needing Makeup", "create": "Create Makeup Session", "noScheduled": "No makeup sessions scheduled", "noStudentsNeedMakeup": "No students currently need makeup sessions", "selectSession": "Select a session to create makeup for", "createMakeup": "Create Makeup", "markStudents": "<PERSON>", "createMakeupSession": "Create Makeup Session", "markStudentsForMakeup": "Mark <PERSON> for Makeup"}, "reports": {"attendanceDistribution": "Attendance Distribution", "completionRate": "Completion Rate (%)", "completionPercentage": "Completion Percentage", "skills": "Skills", "skillsCompletion": "Skills Completion"}, "progress": {"personalGoal": "Personal Goal", "noPersonalGoal": "No personal goal set", "skillsProgress": "Skills Progress"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "scheduleChange": {"title": "Schedule Change", "currentSchedule": "Current Schedule", "days": "Days", "time": "Time", "dateRange": "Date Range", "newSchedule": "New Schedule", "options": "Change Options", "affectExisting": "Apply changes to existing sessions", "notifications": "Notifications", "notifyStudents": "Notify students", "notifyInstructors": "Notify instructors", "reason": "Reason for Schedule Change", "reasonPlaceholder": "Explain why the schedule is changing...", "preview": "Preview Changes", "save": "Save Changes", "previewTitle": "Preview Schedule Changes", "summary": "Summary of Changes", "notificationsSummary": "Notifications will be sent to {{count}} people", "proceed": "Proceed with Changes", "confirmTitle": "Confirm Schedule Change", "confirmText": "Are you sure you want to change the schedule for this program?", "studentsToNotify": "Students ({{count}})", "instructorsToNotify": "Instructors ({{count}})"}}