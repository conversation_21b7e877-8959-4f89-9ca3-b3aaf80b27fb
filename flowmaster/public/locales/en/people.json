{"title": "People Management", "subtitle": "Manage students, instructors, and clients", "categories": {"students": "Students", "instructors": "Instructors", "clients": "Clients"}, "descriptions": {"students": "Manage student profiles, enrollments, and progress tracking", "instructors": "Manage instructor profiles, schedules, and specializations", "clients": "Manage client information, preferences, and relationships"}, "instructors": {"title": "Instructors", "addInstructor": "Add Instructor", "editInstructor": "Edit Instructor", "deleteTitle": "Delete Instructor", "confirmDelete": "Are you sure you want to delete instructor {{firstName}} {{lastName}}?", "deleteInstructor": "Delete Instructor", "currentStudents": "Current Students", "totalLessons": "Total Lessons", "progress": "Progress", "viewProgress": "View Progress", "lessonStatistics": "Lesson Statistics", "completedLessons": "Completed", "cancelledLessons": "Cancelled", "assignedLessons": "Assigned Lessons", "assignedPrograms": "Assigned Programs", "upcomingSessions": "Upcoming Sessions", "noPrograms": "This instructor is not assigned to any programs", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "specialization": "Specialization", "disciplines": "Disciplines", "status": "Status"}}, "students": {"title": "Students", "addStudent": "Add Student", "editStudent": "Edit Student", "deleteStudent": "Delete Student", "deleteTitle": "Delete Student", "deleteConfirmation": "Are you sure you want to delete this student?", "notFound": "Student not found", "upcomingLessons": "Upcoming Lessons", "recentActivity": "Recent Activity", "equipmentRentals": "Equipment Rentals", "activeRentals": "Active Rentals", "noRentals": "No equipment rentals found for this student", "progressTracking": "Progress Tracking", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "level": "Level", "disciplines": "Disciplines", "programs": "Programs", "enrollmentDate": "Enrollment Date", "status": "Status", "type": "Student Type"}, "types": {"program": "Program Student", "lesson": "Lesson Student", "both": "Both Programs & Lessons"}, "typeHelp": "Determines what type of activities this student participates in", "attendanceStats": "Attendance Statistics", "totalClasses": "Total Classes", "attended": "Attended", "missed": "Missed", "noAttendanceData": "No attendance data available for this student", "noAttendanceHistory": "No attendance history available for this student", "enrolledPrograms": "Enrolled Programs"}, "clients": {"title": "Clients", "addClient": "Add Client", "editClient": "Edit Client", "deleteClient": "Delete Client", "deleteTitle": "Delete Client", "deleteConfirmation": "Are you sure you want to delete this client?", "notFound": "Client not found", "upcomingLessons": "Upcoming Lessons", "recentActivity": "Recent Activity", "equipmentRentals": "Equipment Rentals", "activeRentals": "Active Rentals", "noRentals": "No equipment rentals found for this client", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "disciplines": "Disciplines", "company": "Company", "type": "Type", "services": "Services", "status": "Status"}}}