{"title": "Reports", "attendance": {"title": "Attendance Reports", "programsTab": "Programs", "studentsTab": "Students", "overallStatistics": "Overall Statistics", "programsDetail": "Programs Detail", "studentsDetail": "Students Detail", "topStudents": "Top Students by Attendance", "totalSessions": "Total Sessions", "totalStudents": "Total Students", "present": "Present", "absent": "Absent", "excused": "Excused", "rate": "Attendance Rate", "noData": "No attendance data available for the selected filters"}, "messages": {"fetchError": "Failed to fetch data", "filterError": "Failed to apply filters", "exportError": "Failed to export data"}, "dashboard": {"title": "Dashboard", "attendanceWidget": "Attendance Overview", "recentAbsences": "Recent Absences", "attendanceTrends": "Attendance Trends"}, "equipment": {"title": "Equipment Reports", "overview": "Equipment Overview", "rentalTrends": "Rental Trends", "rentalStatus": "Rental Status", "customerTypes": "Customer Types", "rentalCount": "Rental Count"}, "export": "Export"}