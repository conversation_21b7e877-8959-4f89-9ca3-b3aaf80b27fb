{"app": {"name": "FlowMaster", "tagline": "Manage your sports school with ease"}, "navigation": {"dashboard": "Dashboard", "programs": "Programs", "lessons": "Lessons", "settings": "Settings", "profile": "Profile", "signOut": "Sign Out"}, "ui": {"add": "Add", "actions": "Actions", "or": "or", "search": "Search", "filter": "Filter", "loading": "Loading...", "saving": "Saving...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "today": "Today", "selectDate": "Select Date", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "submit": "Submit", "required": "Required field", "optional": "Optional", "menu": "<PERSON><PERSON>", "sort": "Sort", "home": "Home", "about": "About Us", "contact": "Contact", "help": "Help", "language": "Language", "email": "Email", "role": "Role", "view": "View", "manage": "Manage", "link": "Link", "comingSoon": "Coming soon"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "filter": "Filter", "export": "Export", "view": "View", "viewAll": "View All"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "date": "Date", "status": "Status", "type": "Type", "category": "Category", "timeRange": "Time Range", "30days": "30 Days", "60days": "60 Days", "90days": "90 Days", "apply": "Apply Filters", "reset": "Reset Filters"}, "messages": {"fetchError": "Error fetching data", "saveError": "Error saving", "deleteError": "Error deleting", "saveSuccess": "Successfully saved", "deleteSuccess": "Successfully deleted", "createSuccess": "Successfully created", "updateSuccess": "Successfully updated", "noData": "No data", "confirmDelete": "Are you sure you want to delete?", "confirmAction": "Are you sure you want to continue?", "sessionExpired": "Your session has expired. Please sign in again.", "unauthorized": "You don't have permission to access this page.", "notFound": "Page not found.", "serverError": "A server error occurred.", "networkError": "A network error occurred.", "unknownError": "An unknown error occurred."}, "status": {"active": "Active", "inactive": "Inactive"}, "menu": {"settings": "Settings", "dashboard": "Dashboard", "students": "Students", "schedule": "Schedule", "instructors": "Instructors", "clients": "Clients", "programs": "Programs", "people": "People", "lessons": "Lessons", "signOut": "Sign Out", "lessonIndividual": "Individual Lessons", "lessonGroup": "Group Lessons", "lessonChildren": "Children Lessons", "groupPrograms": "Group Programs", "individualPrograms": "Individual Programs", "weeklyCourses": "Weekly Courses", "dailyCourses": "Daily Courses", "settingsProfile": "Profile", "settingsLanguage": "Language", "settingsNotifications": "Notifications", "settingsSchool": "School", "settingsSport": "Sport", "settingsBookings": "Bookings", "settingsSecurity": "Security", "settingsUsers": "Users", "settingsFinancial": "Financial", "settingsSystem": "System", "reports": "Reports", "attendanceReports": "Attendance Reports", "equipmentReports": "Equipment Reports", "equipment": "Equipment", "equipmentInventory": "Inventory", "equipmentRentals": "Rentals"}, "update": {"newVersion": "A new version is available!", "refresh": "Refresh to update"}, "forgotPassword": "Forgot password?", "deleteConfirmation": {"title": "Confirm Deletion", "message": "Are you sure you want to delete this item?", "confirm": "Delete", "cancel": "Cancel"}, "validation": {"required": "This field is required", "email": "Enter a valid email address", "minLength": "Enter at least {{count}} characters", "maxLength": "Enter at most {{count}} characters", "passwordMatch": "Passwords do not match", "invalidCredentials": "Invalid username or password", "invalidEmail": "Invalid email address", "invalidPassword": "Invalid password", "invalidPhone": "Invalid phone number"}, "schedule": {"title": "Schedule", "tabs": {"daily": "Daily Schedule", "weekly": "Weekly Overview", "monthly": "Monthly Overview"}, "weekly": {"title": "Weekly Overview", "placeholder": "Weekly overview content will be implemented here"}, "monthly": {"title": "Monthly Overview", "placeholder": "Monthly overview content will be implemented here"}}, "reports": {"attendance": {"title": "Attendance Reports", "programsTab": "Programs", "studentsTab": "Students", "noData": "No attendance data available for the selected filters"}}}