{"title": "Lessons", "subtitle": "Manage individual, group, and children's lessons", "categories": {"individual": "Individual Lessons", "group": "Group Lessons", "children": "Children's Lessons"}, "descriptions": {"individual": "Personalized lessons tailored to your needs", "group": "Learning in a dynamic group setting", "children": "Special lessons for young learners"}, "create": {"title": "Create New Lesson"}, "edit": {"title": "<PERSON>", "duration": "{{minutes}} minut", "date": "{{date}}", "error": {"fetch": "Error fetching lesson data"}}, "form": {"title": "Lesson Title", "type": "Lesson Type", "instructor": "Select Instructor", "addStudent": "Add Student", "addNewStudent": "Add New Student", "selectStudents": "Select Students", "dateTime": "Date and Time", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration (minutes)", "durationHelper": "Select lesson duration", "discipline": "Discipline", "level": "Level", "status": "Status", "notes": "Notes", "levels": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "minutes": "Minutes", "students": "Students"}, "status": {"upcoming": "Upcoming", "completed": "Completed", "in-progress": "In Progress", "scheduled": "Scheduled", "cancelled": "Cancelled"}, "types": {"individual": "Individual Lesson", "group": "Group Lesson", "children": "Children's Lesson"}, "individual": {"title": "Individual Lessons"}, "group": {"title": "Group Lessons"}, "children": {"title": "Children's Lessons"}, "reschedule": {"title": "Reschedule <PERSON>", "button": "Reschedule", "currentDateTime": "Current Date & Time", "newDateTime": "New Date & Time", "notifications": "Notifications", "notifyStudents": "Notify Students", "notifyInstructor": "Notify Instructor", "reason": "Reason for Rescheduling", "reasonPlaceholder": "Explain why the lesson is being rescheduled...", "confirm": "Reschedule", "defaultMessage": "This lesson has been rescheduled."}, "messages": {"rescheduleError": "Failed to reschedule lesson", "rescheduleSuccess": "Lesson rescheduled successfully", "unauthorizedError": "You do not have permission to reschedule lessons", "instructorConflict": "The instructor already has a lesson scheduled during this time", "studentConflict": "One or more students already have lessons scheduled during this time"}}