{"errors": {"emailInUse": "This email is already registered", "invalidEmail": "Invalid email address", "operationNotAllowed": "Operation not allowed", "weakPassword": "Password is too weak", "userDisabled": "This account has been disabled", "userNotFound": "No account found with this email", "wrongPassword": "Invalid password", "tooManyRequests": "Too many attempts. Please try again later", "requiresRecentLogin": "Please sign in again to complete this action", "emailVerified": "Email is already verified", "unknown": "An error occurred during authentication", "resetPassword": {"error": "Failed to send password reset email. Please try again."}}, "signIn": "Sign In", "signOut": "Sign Out", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "signUp": "Sign Up", "invalidCredentials": "Invalid email or password", "login": {"title": "<PERSON><PERSON>", "subtitle": "Welcome back", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "signUp": "Sign up", "signIn": "Sign in", "errors": {"invalidEmail": "Invalid email", "invalidPassword": "Invalid password", "userNotFound": "User not found", "wrongPassword": "Wrong password", "tooManyRequests": "Too many login attempts. Please try again later.", "default": "Login error", "passwordRequirements": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}}, "register": {"title": "Register", "subtitle": "Create a new account", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "hasAccount": "Already have an account?", "signIn": "Sign in", "signUp": "Sign up", "validation": {"required": "This field is required", "invalidEmail": "Invalid email address", "passwordTooShort": "Password must be at least 8 characters", "passwordRequirements": "Password must contain at least one uppercase letter, one lowercase letter, and one number"}, "errors": {"emailInUse": "Email is already in use", "weakPassword": "Password is too weak", "passwordMismatch": "Passwords do not match", "default": "Registration error"}}, "resetPassword": {"title": "Reset Password", "subtitle": "Enter your email address", "email": "Email", "submit": "Send reset link", "backToLogin": "Back to login", "success": "Password reset email sent"}, "verifyEmail": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Check your inbox", "message": "We sent you a verification link", "resend": "Resend link", "success": "Email verified"}, "emailVerification": {"title": "<PERSON><PERSON><PERSON>"}}