# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.storage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id
REACT_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Environment
REACT_APP_ENV=development

# Firebase Emulator Configuration
REACT_APP_USE_EMULATORS=false
FIREBASE_TOKEN=${{ secrets.FIREBASE_TOKEN }}

# Production Settings
NODE_ENV=production
CHOKIDAR_USEPOLLING=true