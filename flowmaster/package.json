{"name": "flow-master", "version": "0.1.0", "private": true, "dependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@date-io/date-fns": "^2.17.0", "@date-io/dayjs": "^2.17.0", "@date-io/luxon": "^2.17.0", "@date-io/moment": "^2.17.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@firebase/analytics": "^0.10.11", "@firebase/app": "^0.10.18", "@firebase/auth": "^1.8.2", "@firebase/firestore": "^4.7.6", "@firebase/functions": "^0.12.1", "@firebase/performance": "^0.6.12", "@firebase/storage": "^0.13.5", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-date-pickers": "^7.27.0", "@types/mongoose": "^5.11.96", "@types/react-beautiful-dnd": "^13.1.8", "@types/recharts": "^1.8.29", "chart.js": "^4.4.9", "date-fns": "^2.30.0", "firebase": "^9.23.0", "firebase-admin": "^13.4.0", "formik": "^2.4.6", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^2.7.1", "mongoose": "^8.10.1", "notistack": "^3.0.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.49.3", "react-i18next": "^13.5.0", "react-router-dom": "^6.11.2", "recharts": "^2.15.3", "web-vitals": "^2.1.4", "workbox-core": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-webpack-plugin": "^7.0.0", "workbox-window": "^7.0.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-numeric-separator": "^7.23.4", "@babel/plugin-transform-optional-chaining": "^7.23.4", "@babel/plugin-transform-private-methods": "^7.23.3", "@babel/plugin-transform-private-property-in-object": "^7.23.4", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/runtime": "^7.26.7", "@craco/craco": "^7.1.0", "@faker-js/faker": "^8.4.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/aria-query": "^5.0.4", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.8", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/body-parser": "^1.19.5", "@types/bonjour": "^3.5.13", "@types/chart.js": "^2.9.41", "@types/connect": "^3.4.38", "@types/connect-history-api-fallback": "^1.5.4", "@types/cookie": "^0.6.0", "@types/eslint": "^9.6.1", "@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^20.2.3", "@types/react": "^18.2.6", "@types/react-chartjs-2": "^2.0.2", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-preset-react-app": "^10.0.1", "compression-webpack-plugin": "^10.0.0", "dotenv": "^16.5.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.8.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.7.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.5", "react-app-rewired": "^2.2.1", "react-scripts": "^5.0.1", "rimraf": "^5.0.10", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^4.9.5", "webpack-bundle-analyzer": "^4.10.2"}, "scripts": {"start": "react-app-rewired start", "start:craco": "craco start", "build": "node ./standalone-build.js", "build:craco": "node --max_old_space_size=8192 ./node_modules/.bin/craco build", "build:analyze": "ANALYZE=true node --max_old_space_size=8192 ./node_modules/.bin/craco build", "build:profile": "node --max_old_space_size=8192 ./node_modules/.bin/craco build --profile", "build:advanced": "./scripts/advanced-build.sh", "build:multi-stage": "./scripts/multi-stage-build.sh", "build:worker": "node ./scripts/worker-build.js", "build:no-source-map": "GENERATE_SOURCEMAP=false node --max_old_space_size=8192 ./node_modules/.bin/craco build", "build:ci": "DISABLE_ESLINT_PLUGIN=true DISABLE_TYPESCRIPT=true GENERATE_SOURCEMAP=false CI=false node --max_old_space_size=8192 ./node_modules/.bin/craco build", "build:extreme": "./scripts/extreme-build.sh", "build:ci-extreme": "node ./scripts/ci-extreme-build.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --maxWorkers=2", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,md}\"", "typecheck": "tsc --noEmit", "validate": "npm-run-all --parallel lint typecheck test", "clean": "rimraf build coverage", "seed-db": "ts-node scripts/seedDatabase.ts", "verify-db": "ts-node scripts/verifyData.ts", "setup-user": "ts-node scripts/setUserClaims.ts", "create-test-user": "ts-node scripts/createTestUser.ts", "preinstall": "npx only-allow npm"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}