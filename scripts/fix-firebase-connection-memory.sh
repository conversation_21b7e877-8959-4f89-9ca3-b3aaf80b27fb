#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Fixing Firebase connection issues (with memory optimization)...${NC}"

# Stop any running emulators
echo -e "${YELLOW}Stopping any running Firebase emulators...${NC}"
pkill -f "firebase emulators" || true

# Verify environment variables
echo -e "${YELLOW}Checking environment variables...${NC}"
if grep -q "REACT_APP_USE_EMULATORS=true" .env.development; then
    echo -e "${RED}Warning: REACT_APP_USE_EMULATORS is set to true in .env.development${NC}"
    echo -e "${YELLOW}Setting it to false...${NC}"
    sed -i '' 's/REACT_APP_USE_EMULATORS=true/REACT_APP_USE_EMULATORS=false/g' .env.development
    echo -e "${GREEN}Updated REACT_APP_USE_EMULATORS to false${NC}"
else
    echo -e "${GREEN}REACT_APP_USE_EMULATORS is already set to false${NC}"
fi

# Directly modify the Firebase configuration file
echo -e "${YELLOW}Modifying Firebase configuration file...${NC}"
FIREBASE_CONFIG_FILE="src/config/firebase.ts"

# Create a backup of the original file
cp "$FIREBASE_CONFIG_FILE" "${FIREBASE_CONFIG_FILE}.bak"
echo -e "${GREEN}Created backup at ${FIREBASE_CONFIG_FILE}.bak${NC}"

# Replace the emulator connection code with a more explicit check
cat > "$FIREBASE_CONFIG_FILE" << 'EOF'
import { initializeApp } from 'firebase/app';
import { connectAuthEmulator, getAuth } from 'firebase/auth';
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore';
import { connectStorageEmulator, getStorage } from 'firebase/storage';
import { connectDatabaseEmulator, getDatabase } from 'firebase/database';

const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);
const realtime = getDatabase(app);

// IMPORTANT: We're forcing the use of real Firebase services
// This overrides any environment variable settings
console.log('Using production Firebase services');

// Uncomment the following block if you want to use emulators again
/*
if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_EMULATORS === 'true') {
  connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
  connectFirestoreEmulator(db, 'localhost', 8082);
  connectStorageEmulator(storage, 'localhost', 9199);
  connectDatabaseEmulator(realtime, 'localhost', 9000);

  console.log('Connected to Firebase emulators');
}
*/

export { app, auth, db, storage, realtime };
EOF

echo -e "${GREEN}Modified Firebase configuration to force using real Firebase services${NC}"

# Clear build cache
echo -e "${YELLOW}Clearing build cache...${NC}"
rm -rf node_modules/.cache
rm -rf build

# Build the application instead of starting it
echo -e "${GREEN}Building the application...${NC}"
echo -e "${YELLOW}This will create a production build that uses real Firebase services${NC}"
NODE_OPTIONS="--max-old-space-size=4096" npm run build

echo -e "${GREEN}Build complete. You can now serve the production build:${NC}"
echo -e "${YELLOW}npx serve -s build${NC}"
