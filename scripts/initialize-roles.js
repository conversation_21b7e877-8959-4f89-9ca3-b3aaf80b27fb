/**
 * <PERSON><PERSON>t to initialize roles in the real Firebase database
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../flowmaster/firebase-service-account.json');
  
  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Default roles with their permissions
const DEFAULT_ROLES = {
  'admin': {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system access',
    permissions: [
      'manage_users',
      'manage_lessons',
      'manage_programs',
      'manage_settings',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
      'manage_equipment',
    ],
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  },
  'CLIENT': {
    id: 'CLIENT',
    name: 'Client',
    description: 'Client access',
    permissions: ['view_lessons'],
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  },
  'instructor': {
    id: 'instructor',
    name: 'Instructor',
    description: 'Can manage programs and lessons',
    permissions: [
      'manage_lessons',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
    ],
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  },
  'manager': {
    id: 'manager',
    name: 'Manager',
    description: 'School management access',
    permissions: [
      'manage_users',
      'manage_lessons',
      'manage_programs',
      'view_lessons',
      'view_programs',
      'view_users',
      'view_people',
      'edit_profile',
      'manage_attendance',
      'manage_equipment',
    ],
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  },
  'student': {
    id: 'student',
    name: 'Student',
    description: 'Student access',
    permissions: ['view_lessons', 'view_programs', 'edit_profile'],
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  },
};

// Function to initialize roles
async function initializeRoles() {
  try {
    const batch = db.batch();
    
    for (const [id, role] of Object.entries(DEFAULT_ROLES)) {
      const roleRef = db.collection('roles').doc(id);
      batch.set(roleRef, role);
      console.log(`Added role: ${id}`);
    }
    
    await batch.commit();
    console.log('Roles initialized successfully');
  } catch (error) {
    console.error('Error initializing roles:', error);
    throw error;
  }
}

// Main function
async function main() {
  console.log('Initializing roles in Firebase...');
  
  const confirmed = await askForConfirmation('This will initialize roles in your Firebase project. Continue? (y/n) ');
  
  if (!confirmed) {
    console.log('Operation cancelled');
    process.exit(0);
  }
  
  try {
    await initializeRoles();
    console.log('Role initialization complete!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

// Run the main function
main();
