/**
 * <PERSON><PERSON><PERSON> to create rental data in the real Firebase database
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../flowmaster/firebase-service-account.json');
  
  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Function to create equipment data
async function createEquipmentData() {
  try {
    const batch = db.batch();
    
    // Get the school ID
    const schoolsSnapshot = await db.collection('schools').get();
    if (schoolsSnapshot.empty) {
      console.error('No schools found. Please create a school first.');
      return;
    }
    
    const schoolId = schoolsSnapshot.docs[0].id;
    console.log(`Using school ID: ${schoolId}`);
    
    // Create equipment categories
    const categories = ['Surfboard', 'Kiteboard', 'Wetsuit', 'Harness', 'Helmet'];
    const equipmentCollection = db.collection(`schools/${schoolId}/equipment`);
    
    // Create 20 equipment items
    for (let i = 1; i <= 20; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const equipmentRef = equipmentCollection.doc();
      
      batch.set(equipmentRef, {
        id: equipmentRef.id,
        name: `${category} ${i}`,
        category,
        description: `${category} for rental`,
        status: 'available',
        condition: 'good',
        serialNumber: `SN-${Math.floor(Math.random() * 10000)}`,
        purchaseDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString(),
        purchasePrice: Math.floor(Math.random() * 500) + 100,
        schoolId,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log(`Created equipment: ${category} ${i}`);
    }
    
    await batch.commit();
    console.log('Equipment data created successfully');
    
    return schoolId;
  } catch (error) {
    console.error('Error creating equipment data:', error);
    throw error;
  }
}

// Function to create rental data
async function createRentalData(schoolId) {
  try {
    // Get equipment items
    const equipmentSnapshot = await db.collection(`schools/${schoolId}/equipment`).get();
    if (equipmentSnapshot.empty) {
      console.error('No equipment found. Please create equipment first.');
      return;
    }
    
    const equipment = equipmentSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log(`Found ${equipment.length} equipment items`);
    
    // Create rental customers
    const customers = [
      { name: 'John Smith', email: '<EMAIL>', phone: '************' },
      { name: 'Jane Doe', email: '<EMAIL>', phone: '************' },
      { name: 'Mike Johnson', email: '<EMAIL>', phone: '************' },
      { name: 'Sarah Williams', email: '<EMAIL>', phone: '************' }
    ];
    
    const rentalsCollection = db.collection(`schools/${schoolId}/rentals`);
    
    // Create active rentals
    for (let i = 0; i < 5; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const rentalItems = [];
      
      // Add 1-3 items to the rental
      const numItems = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < numItems; j++) {
        const equipmentItem = equipment[Math.floor(Math.random() * equipment.length)];
        rentalItems.push({
          equipmentId: equipmentItem.id,
          name: equipmentItem.name,
          category: equipmentItem.category,
          condition: 'good'
        });
      }
      
      const today = new Date();
      const startDate = new Date(today);
      startDate.setDate(today.getDate() - Math.floor(Math.random() * 10));
      
      const dueDate = new Date(startDate);
      dueDate.setDate(startDate.getDate() + 7);
      
      const rentalRef = rentalsCollection.doc();
      await rentalRef.set({
        id: rentalRef.id,
        customerName: customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        items: rentalItems,
        date: startDate.toISOString(),
        dueDate: dueDate.toISOString(),
        status: 'active',
        deposit: Math.floor(Math.random() * 200) + 50,
        notes: 'Created by script',
        schoolId,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log(`Created active rental for ${customer.name}`);
    }
    
    // Create overdue rentals
    for (let i = 0; i < 3; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const rentalItems = [];
      
      // Add 1-3 items to the rental
      const numItems = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < numItems; j++) {
        const equipmentItem = equipment[Math.floor(Math.random() * equipment.length)];
        rentalItems.push({
          equipmentId: equipmentItem.id,
          name: equipmentItem.name,
          category: equipmentItem.category,
          condition: 'good'
        });
      }
      
      const today = new Date();
      const startDate = new Date(today);
      startDate.setDate(today.getDate() - 15);
      
      const dueDate = new Date(startDate);
      dueDate.setDate(startDate.getDate() + 7);
      
      const rentalRef = rentalsCollection.doc();
      await rentalRef.set({
        id: rentalRef.id,
        customerName: customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        items: rentalItems,
        date: startDate.toISOString(),
        dueDate: dueDate.toISOString(),
        status: 'overdue',
        deposit: Math.floor(Math.random() * 200) + 50,
        notes: 'Created by script - Overdue',
        schoolId,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log(`Created overdue rental for ${customer.name}`);
    }
    
    // Create completed rentals
    for (let i = 0; i < 10; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const rentalItems = [];
      
      // Add 1-3 items to the rental
      const numItems = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < numItems; j++) {
        const equipmentItem = equipment[Math.floor(Math.random() * equipment.length)];
        rentalItems.push({
          equipmentId: equipmentItem.id,
          name: equipmentItem.name,
          category: equipmentItem.category,
          condition: 'good'
        });
      }
      
      const today = new Date();
      const startDate = new Date(today);
      startDate.setDate(today.getDate() - 30 - Math.floor(Math.random() * 30));
      
      const dueDate = new Date(startDate);
      dueDate.setDate(startDate.getDate() + 7);
      
      const returnDate = new Date(dueDate);
      returnDate.setDate(dueDate.getDate() - Math.floor(Math.random() * 3));
      
      const rentalRef = rentalsCollection.doc();
      await rentalRef.set({
        id: rentalRef.id,
        customerName: customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        items: rentalItems,
        date: startDate.toISOString(),
        dueDate: dueDate.toISOString(),
        returnDate: returnDate.toISOString(),
        status: 'completed',
        deposit: Math.floor(Math.random() * 200) + 50,
        notes: 'Created by script - Completed',
        schoolId,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log(`Created completed rental for ${customer.name}`);
    }
    
    console.log('Rental data created successfully');
  } catch (error) {
    console.error('Error creating rental data:', error);
    throw error;
  }
}

// Main function
async function main() {
  console.log('Creating equipment and rental data in Firebase...');
  
  const confirmed = await askForConfirmation('This will create equipment and rental data in your Firebase project. Continue? (y/n) ');
  
  if (!confirmed) {
    console.log('Operation cancelled');
    process.exit(0);
  }
  
  try {
    const schoolId = await createEquipmentData();
    await createRentalData(schoolId);
    console.log('Equipment and rental data creation complete!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

// Run the main function
main();
