/**
 * <PERSON><PERSON><PERSON> to create attendance data in the real Firebase database
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../flowmaster/firebase-service-account.json');
  
  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Function to create attendance data
async function createAttendanceData() {
  try {
    const batch = db.batch();
    
    // Get the school ID
    const schoolsSnapshot = await db.collection('schools').get();
    if (schoolsSnapshot.empty) {
      console.error('No schools found. Please create a school first.');
      return;
    }
    
    const schoolId = schoolsSnapshot.docs[0].id;
    console.log(`Using school ID: ${schoolId}`);
    
    // Get students
    const studentsSnapshot = await db.collection(`schools/${schoolId}/students`).get();
    if (studentsSnapshot.empty) {
      console.error('No students found. Please create students first.');
      return;
    }
    
    const students = studentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log(`Found ${students.length} students`);
    
    // Get programs
    const programsSnapshot = await db.collection(`schools/${schoolId}/programs`).get();
    if (programsSnapshot.empty) {
      console.error('No programs found. Please create programs first.');
      return;
    }
    
    const programs = programsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log(`Found ${programs.length} programs`);
    
    // Create attendance records for the past 30 days
    const today = new Date();
    const attendanceCollection = db.collection(`schools/${schoolId}/attendance`);
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split('T')[0];
      
      // Create attendance for each program
      for (const program of programs) {
        // Get students in this program
        const programStudentIds = Array.isArray(program.studentIds) 
          ? program.studentIds 
          : (program.studentIds ? program.studentIds.split(/,\s*/) : []);
        
        if (programStudentIds.length === 0) continue;
        
        // Create attendance record
        const attendanceId = `${dateString}-${program.id}`;
        const attendanceRef = attendanceCollection.doc(attendanceId);
        
        // Randomly mark some students as present
        const attendance = {
          id: attendanceId,
          date: dateString,
          programId: program.id,
          schoolId: schoolId,
          students: {},
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        };
        
        for (const studentId of programStudentIds) {
          // 80% chance of being present
          const isPresent = Math.random() < 0.8;
          attendance.students[studentId] = {
            present: isPresent,
            notes: isPresent ? '' : 'Absent'
          };
        }
        
        batch.set(attendanceRef, attendance);
        console.log(`Created attendance record for ${dateString} - ${program.name}`);
      }
    }
    
    await batch.commit();
    console.log('Attendance data created successfully');
  } catch (error) {
    console.error('Error creating attendance data:', error);
    throw error;
  }
}

// Main function
async function main() {
  console.log('Creating attendance data in Firebase...');
  
  const confirmed = await askForConfirmation('This will create attendance data in your Firebase project. Continue? (y/n) ');
  
  if (!confirmed) {
    console.log('Operation cancelled');
    process.exit(0);
  }
  
  try {
    await createAttendanceData();
    console.log('Attendance data creation complete!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

// Run the main function
main();
