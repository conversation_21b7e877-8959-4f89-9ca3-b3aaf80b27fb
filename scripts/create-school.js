/**
 * <PERSON><PERSON><PERSON> to create a school in the real Firebase database
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Initialize Firebase Admin SDK
try {
  // Check if service account key file exists
  const serviceAccountPath = process.env.SERVICE_ACCOUNT_KEY_PATH || path.join(__dirname, '../flowmaster/firebase-service-account.json');

  if (!fs.existsSync(serviceAccountPath)) {
    console.error(`Service account key file not found at ${serviceAccountPath}`);
    console.error('Please set the SERVICE_ACCOUNT_KEY_PATH environment variable or place the file at the default location.');
    process.exit(1);
  }

  // Initialize the app with the service account key
  admin.initializeApp({
    credential: admin.credential.cert(require(serviceAccountPath))
  });

  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// Function to create a school
async function createSchool(schoolData) {
  try {
    const schoolRef = db.collection('schools').doc(schoolData.id);
    await schoolRef.set(schoolData);
    console.log(`School "${schoolData.name}" created with ID: ${schoolData.id}`);
    return schoolData.id;
  } catch (error) {
    console.error('Error creating school:', error);
    throw error;
  }
}

// Main function to create a school
async function createSchoolData() {
  console.log('Creating school in Firebase...');

  const confirmed = await askForConfirmation('This will create a school in your Firebase project. Continue? (y/n) ');

  if (!confirmed) {
    console.log('Operation cancelled');
    process.exit(0);
  }

  try {
    // Create a school
    const schoolId = await createSchool({
      id: 'school1',
      name: 'FlowMaster Demo School',
      address: '123 Main St, Anytown, USA',
      phone: '************',
      email: '<EMAIL>',
      website: 'https://flowmasterdemo.com',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log('School creation complete!');
  } catch (error) {
    console.error('Error creating school:', error);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the main function
createSchoolData();
