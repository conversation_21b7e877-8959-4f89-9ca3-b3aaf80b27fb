# FlowMaster Phase 2 & 3: Detailed Implementation Plan

## Overview
This document outlines the detailed implementation plan for Phase 2 (Core Features Development) and Phase 3 (Lesson Management) of the FlowMaster project. The plan incorporates both MongoDB for local development and Firebase as the production database.

## Infrastructure Considerations

### Database Strategy
- [ ] Implement database service layer with MongoDB support
- [ ] Create Firebase database adapters
- [ ] Implement database switching mechanism in configuration
- [ ] Set up MongoDB schemas matching Firebase structure

### Translation Requirements
For all new features:
- [ ] Add translations to `public/locales/en/`
- [ ] Add translations to `public/locales/sl/`
- [ ] Update types in `/i18n/types`
- [ ] Update `flowmaster/src/types/i18next.d.ts`

## Database Implementation Guidelines

### Current Infrastructure

#### Firebase Configuration
- Firestore for main database
- Storage for file management
- Real-time Database for live updates
- Emulators for local development
- Security rules implementation
- Composite indexes for optimized queries

#### MongoDB Configuration
[Pending review of mongodb.ts configuration]

### Implementation Strategy

#### Service Layer Pattern
- [ ] Create abstract database interface
- [ ] Implement Firebase adapter
- [ ] Implement MongoDB adapter
- [ ] Add environment-based switching

#### Data Models
- [ ] Define shared interfaces for all models
- [ ] Implement data validation
- [ ] Create data transformation utilities
- [ ] Add type guards

#### Security Rules Update
- [ ] Update Firestore rules for new collections:
  - [ ] Lessons collection
  - [ ] Programs collection
  - [ ] Schedules collection
- [ ] Update Storage rules for new paths:
  - [ ] Lesson attachments
  - [ ] Program materials
  - [ ] User uploads

#### Index Management
- [ ] Add new composite indexes:
  - [ ] Programs by school and status
  - [ ] Lessons by instructor and date
  - [ ] Students by program and status

#### Local Development
- [ ] Configure MongoDB for local data persistence
- [ ] Set up data synchronization patterns
- [ ] Implement development utilities
- [ ] Create data seeding scripts

#### Production Deployment
- [ ] Set up Firebase production environment
- [ ] Configure backup strategies
- [ ] Implement migration scripts
- [ ] Add monitoring and logging

### Usage Guidelines

#### Creating New Features
1. Define interfaces in `types/database.ts`
2. Create service methods in `services/database.ts`
3. Implement Firebase adapter
4. Implement MongoDB adapter
5. Add security rules if needed
6. Create necessary indexes

#### Example Implementation Pattern
```typescript
// types/database.ts
interface Lesson {
  id: string;
  title: string;
  instructorId: string;
  studentIds: string[];
  startTime: Date;
  endTime: Date;
  status: LessonStatus;
}

// services/database.ts
class DatabaseService {
  private adapter: DatabaseAdapter;

  constructor() {
    this.adapter = process.env.NODE_ENV === 'development' 
      ? new MongoDBAdapter() 
      : new FirebaseAdapter();
  }

  async createLesson(lessonData: Omit<Lesson, 'id'>): Promise<string> {
    return this.adapter.createLesson(lessonData);
  }
}
```

### Security Considerations
- [ ] Implement rate limiting
- [ ] Add request validation
- [ ] Set up audit logging
- [ ] Configure backup systems
- [ ] Implement error handling

### Performance Optimization
- [ ] Configure caching strategies
- [ ] Implement query optimization
- [ ] Set up data pagination
- [ ] Add real-time sync mechanisms

### Monitoring and Maintenance
- [ ] Set up performance monitoring
- [ ] Configure error tracking
- [ ] Implement usage analytics
- [ ] Create maintenance scripts

## Phase 2: Core Features Development (4 weeks)

### Week 1-2: User Management System Enhancement

#### People Management Infrastructure
- [x] Create base people management service
  - [ ] MongoDB implementation
  - [x] Firebase implementation
- [ ] Implement people context provider
- [ ] Add people-related hooks
- [ ] Set up people routes

#### Instructor Management
- [ ] Create instructor profile components
  - [ ] Profile view
  - [ ] Profile edit
  - [ ] Availability management
- [ ] Implement instructor listing page
- [ ] Add instructor search and filtering
- [ ] Create instructor assignment system

#### Student Management
- [ ] Create student profile components
  - [ ] Profile view
  - [ ] Profile edit
  - [ ] Progress tracking
- [ ] Implement student listing page
- [ ] Add student search and filtering
- [ ] Create student group management

#### Client Management
- [ ] Create client profile components
  - [ ] Profile view
  - [ ] Profile edit
  - [ ] Billing information
- [ ] Implement client listing page
- [ ] Add client search and filtering

### Week 3-4: Calendar and Scheduling System

#### Calendar Infrastructure
- [ ] Set up calendar service
  - [ ] MongoDB implementation
  - [ ] Firebase implementation
- [ ] Create calendar context
- [ ] Implement calendar-related hooks

#### Calendar Features
- [ ] Create base calendar component
- [ ] Implement day view
- [ ] Implement week view
- [ ] Implement month view
- [ ] Add drag-and-drop support

#### Scheduling Features
- [ ] Create availability management system
- [ ] Implement booking system
  - [ ] Slot selection
  - [ ] Conflict detection
  - [ ] Booking confirmation
- [ ] Add real-time updates
- [ ] Implement notification system

## Phase 3: Lesson Management (3 weeks)

### Week 1: Core Lesson System

#### Lesson Infrastructure
- [ ] Create lesson service
  - [ ] MongoDB implementation
  - [ ] Firebase implementation
- [ ] Set up lesson context
- [ ] Implement lesson-related hooks

#### Individual Lessons
- [ ] Create lesson creation form
- [ ] Implement lesson view component
- [ ] Add lesson editing capabilities
- [ ] Create lesson status management
- [ ] Implement lesson history

#### Group Lessons
- [ ] Create group lesson management
- [ ] Implement group assignment
- [ ] Add capacity management
- [ ] Create waiting list system

### Week 2: Advanced Lesson Features

#### Lesson Templates
- [ ] Create template management system
- [ ] Implement template application
- [ ] Add template customization
- [ ] Create template library

#### Walk-in Lesson Handling
- [ ] Create walk-in registration system
- [ ] Implement quick booking
- [ ] Add capacity checking
- [ ] Create payment integration

#### Lesson Scheduling
- [ ] Implement recurring lessons
- [ ] Add schedule optimization
- [ ] Create schedule conflict resolution
- [ ] Implement schedule change notifications

### Week 3: Program Management

#### Program Infrastructure
- [x] Create program service
  - [ ] MongoDB implementation
  - [x] Firebase implementation
- [ ] Set up program context
- [ ] Implement program-related hooks

#### School Programs
- [ ] Create program creation interface
- [ ] Implement program management
- [ ] Add student assignment
- [ ] Create progress tracking

#### Regular Programs
- [ ] Implement regular program creation
- [ ] Add schedule management
- [ ] Create attendance tracking
- [ ] Implement progress reports

## Technical Implementation Details

### Database Schema Updates
- [x] Create lesson schemas
- [x] Update user schemas
- [x] Create program schemas
- [ ] Implement relationship management

### Service Layer Implementation
- [x] Update authentication service
- [x] Create lesson service
- [x] Implement program service
- [ ] Add scheduling service

### Component Structure
- [ ] Utilize `components/common` for shared components
- [ ] Implement form components using `TextField`
- [ ] Create new specialized components
- [ ] Add error boundaries and loading states

### Context and State Management
- [ ] Extend `AuthContext` for new permissions
- [ ] Update `RoleContext` for new roles
- [ ] Implement `SchoolContext` updates
- [ ] Create new context providers as needed

### Routing Updates
- [ ] Add new routes for lessons
- [ ] Create program routes
- [ ] Implement nested routes
- [ ] Update route protection

### Testing Strategy
- [ ] Unit tests for new services
- [ ] Component tests for new features
- [ ] Integration tests for workflows
- [ ] E2E tests for critical paths

## Success Criteria
- [ ] All features implemented and tested
- [ ] Database implementations working for both MongoDB and Firebase
- [ ] Translations complete for all new features
- [ ] All components properly documented
- [ ] Test coverage maintained above 80%
- [ ] Performance metrics meeting targets
- [ ] User acceptance testing passed

## Dependencies
- MongoDB for local development
- Firebase for production
- Existing project infrastructure
- Translation system
- Component library
- Testing framework

## Risk Mitigation
1. Database Synchronization
   - [x] Implement data validation
   - [ ] Create migration scripts
   - [ ] Set up backup systems

2. Performance
   - [ ] Implement lazy loading
   - [ ] Add caching where appropriate
   - [ ] Monitor real-time updates

3. Security
   - [ ] Review permission models
   - [ ] Implement rate limiting
   - [ ] Add audit logging






   -------------


   

## Next Steps
- Begin implementation of lesson management system
- Update people management features
- Start calendar system development

### MongoDB Integration

#### Current MongoDB Setup
```typescript
// Basic connection configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/flowmaster';
```

#### Required Enhancements

1. MongoDB Schema Definition
```typescript
// models/lesson.ts
const LessonSchema = new mongoose.Schema({
  title: { type: String, required: true },
  instructorId: { type: String, required: true },
  studentIds: [{ type: String }],
  startTime: { type: Date, required: true },
  endTime: { type: Date, required: true },
  status: { type: String, enum: ['scheduled', 'completed', 'cancelled'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Add similar schemas for Program, Schedule, School, etc.
```

2. MongoDB Adapter Implementation
```typescript
// services/mongodb-adapter.ts
class MongoDBAdapter implements DatabaseAdapter {
  async createLesson(lessonData: Omit<Lesson, 'id'>): Promise<string> {
    const lesson = new LessonModel(lessonData);
    await lesson.save();
    return lesson._id.toString();
  }

  async updateLesson(lessonId: string, lessonData: Partial<Lesson>): Promise<void> {
    await LessonModel.findByIdAndUpdate(lessonId, 
      { ...lessonData, updatedAt: new Date() }
    );
  }
  
  // Implement other methods...
}
```

#### Configuration Updates Needed

1. Environment Configuration
```bash
# .env.development
MONGODB_URI=mongodb://localhost:27017/flowmaster
MONGODB_DEBUG=true
```

2. Connection Options
```typescript
// config/mongodb.ts
const mongooseOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: process.env.NODE_ENV !== 'production',
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
};

mongoose.connect(MONGODB_URI, mongooseOptions);
```

3. Indexes Setup
```typescript
// models/indexes.ts
LessonSchema.index({ instructorId: 1, startTime: 1 });
LessonSchema.index({ studentIds: 1 });
ProgramSchema.index({ schoolId: 1, status: 1 });
```

#### Development Workflow

1. Local Development
- Use MongoDB for local development
- Store created data locally
- Faster development iterations
- No Firebase emulator required for basic operations

2. Production
- Use Firebase as primary database
- MongoDB optional for specific use cases
- Data synchronization if needed

#### Implementation Tasks

1. Database Models
- [ ] Create MongoDB schemas matching Firebase structure
- [ ] Add validation rules
- [ ] Set up indexes
- [ ] Implement timestamps and versioning

2. Adapter Pattern
- [ ] Create base adapter interface
- [ ] Implement MongoDB adapter
- [ ] Implement Firebase adapter
- [ ] Add adapter factory

3. Data Migration
- [ ] Create migration scripts
- [ ] Add data validation
- [ ] Implement error handling
- [ ] Add rollback capabilities

4. Testing
- [ ] Unit tests for MongoDB operations
- [ ] Integration tests with both databases
- [ ] Performance comparison tests
- [ ] Migration tests

#### Usage Example

```typescript
// services/database.ts
class DatabaseService {
  private adapter: DatabaseAdapter;

  constructor() {
    this.adapter = process.env.NODE_ENV === 'development' 
      ? new MongoDBAdapter() 
      : new FirebaseAdapter();
  }

  async createLesson(data: CreateLessonInput): Promise<string> {
    try {
      const id = await this.adapter.createLesson(data);
      return id;
    } catch (error) {
      console.error('Error creating lesson:', error);
      throw new DatabaseError('Failed to create lesson');
    }
  }
}
```

#### Monitoring and Maintenance

1. Health Checks
- [ ] Add connection monitoring
- [ ] Implement automatic reconnection
- [ ] Set up error notifications
- [ ] Monitor performance metrics

2. Backup Strategy
- [ ] Regular MongoDB dumps
- [ ] Automated backup scripts
- [ ] Backup verification
- [ ] Recovery procedures
