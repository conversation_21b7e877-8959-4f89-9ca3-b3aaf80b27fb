# FlowMaster Phase 1 Development Rules

## Code Quality and Standards

### 1. TypeScript Usage
- Use TypeScript for all new code
- Maintain strict type checking
- Define interfaces for all data structures
- Avoid using `any` type unless absolutely necessary
- Use enums for predefined sets of values

### 2. React Best Practices
- Use functional components with hooks
- Implement proper component separation and reusability
- Follow React's naming conventions:
  - Components: PascalCase
  - Files containing components: PascalCase
  - Other files: camelCase
- Keep components focused and single-responsibility
- Use proper prop typing with TypeScript

### 3. Project Structure
- Maintain the defined directory structure
- Keep related files close together
- Use barrel exports (index.ts files) for clean imports
- Follow the absolute import paths convention
- Keep component-specific styles with their components

### 4. Code Style
- Follow ESLint configuration
- Use Prettier for code formatting
- Maximum line length: 100 characters
- Use meaningful variable and function names
- Add JSDoc comments for public functions and interfaces

## Development Process

### 1. Version Control
- Use feature branches for all new development
- Branch naming convention: `feature/phase1-<feature-name>`
- Commit messages must be descriptive and follow conventional commits
- No direct commits to main or develop branches
- Regular commits with atomic changes

### 2. Testing Requirements
- Write tests alongside code development
- Maintain minimum 80% test coverage
- Required test types:
  - Unit tests for utilities and hooks
  - Component tests for UI elements
  - Integration tests for Firebase interactions
  - Authentication flow tests

### 3. Documentation
- Document all configuration steps
- Add README.md files in major directories
- Include JSDoc comments for public APIs
- Document environment setup procedures
- Keep configuration examples up to date

## Technical Requirements

### 1. Environment Setup
- Use Docker for development environment
- Maintain separate development and production configurations
- Use virtual environment for Python tools
- Keep environment variables documented
- Version lock all dependencies

### 2. Firebase Configuration
- Use Firebase Emulator for local development
- Implement proper error handling for Firebase operations
- Follow Firebase security best practices
- Keep Firebase config separate from application code
- Use Firebase Admin SDK appropriately

### 3. Database Rules
- Implement principle of least privilege
- Document all security rules
- Test security rules thoroughly
- Keep rules synchronized with application roles
- Implement proper data validation

### 4. Authentication
- Implement proper error handling
- Use loading states for authentication operations
- Maintain secure session management
- Implement proper password policies
- Handle authentication state persistence correctly

### 5. Internationalization
- Use translation keys for all text content
- Maintain separate translation files
- Follow i18n naming conventions
- Support RTL languages in styling
- Use proper date and number formatting

## Security Requirements

### 1. General Security
- No sensitive data in source code
- Proper environment variable handling
- Secure storage of API keys
- Implementation of rate limiting
- Regular security testing

### 2. Authentication Security
- Implement proper password requirements
- Secure password reset flow
- Email verification requirement
- Session timeout handling
- Failed login attempt limiting

### 3. Database Security
- Proper security rules implementation
- Data validation on both client and server
- No sensitive data exposure
- Proper error handling
- Regular security rule testing

## Performance Requirements

### 1. Application Performance
- Implement code splitting
- Optimize bundle size
- Use lazy loading where appropriate
- Implement proper caching strategies
- Monitor and optimize Firebase usage

### 2. Development Performance
- Use hot reloading
- Optimize build process
- Implement efficient testing strategy
- Use proper Docker caching
- Optimize CI/CD pipeline

## Error Handling

### 1. General Error Handling
- Implement proper error boundaries
- Use typed error handling
- Proper error logging
- User-friendly error messages
- Proper error recovery

### 2. Firebase Error Handling
- Handle authentication errors
- Handle database operation errors
- Handle network errors
- Implement retry mechanisms
- Proper error reporting

## Accessibility Requirements

### 1. General Accessibility
- Implement proper ARIA labels
- Ensure keyboard navigation
- Maintain proper contrast ratios
- Support screen readers
- Follow WCAG 2.1 guidelines

## Review Process

### 1. Code Review Requirements
- All code must be reviewed
- Follow code review checklist
- Address all review comments
- Test coverage verification
- Security review for critical components

### 2. Pre-deployment Checks
- All tests passing
- Linting passes
- Type checking passes
- Security rules tested
- Documentation updated

## Definition of Done
A feature is considered complete when:
1. Code meets all quality standards
2. Tests are written and passing
3. Documentation is complete
4. Security review is complete
5. Code review is complete
6. Feature is tested in development environment
7. Internationalization is implemented
8. Accessibility requirements are met

## translations hook
const { t } = useTranslations();
t('auth.login.title'); // Will return "Welcome Back" in English or "Dobrodošli nazaj" in Slovenian
