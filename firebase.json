{"functions": {"source": "flowmaster/firebase/functions", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"], "codebase": "default"}, "firestore": {"rules": "flowmaster/firebase/rules/firestore.rules", "indexes": "flowmaster/firebase/rules/firestore.indexes.json"}, "storage": {"rules": "flowmaster/firebase/rules/storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "ui": {"enabled": true}}}