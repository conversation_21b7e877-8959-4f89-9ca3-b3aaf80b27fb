# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
on:
  push:
    branches:
      - main
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: './flowmaster/firebase/package-lock.json'
      - name: Install dependencies
        working-directory: ./flowmaster
        run: npm ci
      - name: Build application
        working-directory: ./flowmaster
        run: npm run build
        env:
          REACT_APP_FIREBASE_API_KEY: ${{ secrets.REACT_APP_FIREBASE_API_KEY }}
          REACT_APP_FIREBASE_AUTH_DOMAIN: ${{ secrets.REACT_APP_FIREBASE_AUTH_DOMAIN }}
          REACT_APP_FIREBASE_PROJECT_ID: ${{ secrets.REACT_APP_FIREBASE_PROJECT_ID }}
          REACT_APP_FIREBASE_STORAGE_BUCKET: ${{ secrets.REACT_APP_FIREBASE_STORAGE_BUCKET }}
          REACT_APP_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.REACT_APP_FIREBASE_MESSAGING_SENDER_ID }}
          REACT_APP_FIREBASE_APP_ID: ${{ secrets.REACT_APP_FIREBASE_APP_ID }}
          REACT_APP_FIREBASE_measurementId: ${{ secrets.REACT_APP_FIREBASE_MEASUREMENT_ID }}
          REACT_APP_ENV: production
      - name: Copy build to Firebase directory
        working-directory: ./flowmaster
        run: cp -r build firebase/
      - name: Set up Firebase service account
        working-directory: ./flowmaster
        run: |
          mkdir -p firebase
          echo "${{ secrets.FIREBASE_SERVICE_ACCOUNT_FLOWMASTER_E3947 }}" > firebase/service-account.json
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_FLOWMASTER_E3947 }}
          channelId: live
          projectId: flowmaster-e3947
          entryPoint: flowmaster/firebase
