import React, { useState, ReactNode } from 'react';
import { 
  App<PERSON><PERSON>, 
  Too<PERSON>bar, 
  Typography, 
  Box, 
  BottomNavigation, 
  BottomNavigationAction, 
  Paper, 
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { 
  Menu as MenuIcon, 
  Home as HomeIcon, 
  CalendarToday as CalendarIcon, 
  People as PeopleIcon, 
  School as SchoolIcon,
  Settings as SettingsIcon,
  ExitToApp as LogoutIcon,
  ArrowBack as BackIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';

interface MobileLayoutProps {
  children: ReactNode;
  title?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ 
  children, 
  title = 'FlowMaster', 
  showBackButton = false,
  onBackClick
}) => {
  const { t } = useTranslation(['common']);
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [value, setValue] = useState(() => {
    const path = location.pathname;
    if (path.includes('/schedule')) return 1;
    if (path.includes('/students')) return 2;
    if (path.includes('/programs')) return 3;
    return 0; // Default to home
  });

  const handleNavChange = (_: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    switch (newValue) {
      case 0:
        navigate('/mobile/dashboard');
        break;
      case 1:
        navigate('/mobile/schedule');
        break;
      case 2:
        navigate('/mobile/students');
        break;
      case 3:
        navigate('/mobile/programs');
        break;
      default:
        navigate('/mobile/dashboard');
    }
  };

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      navigate(-1);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const drawer = (
    <Box sx={{ width: 250 }} role="presentation">
      <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h6">{t('common:appName', 'FlowMaster')}</Typography>
        <Typography variant="body2">{t('common:instructorPortal', 'Instructor Portal')}</Typography>
      </Box>
      <Divider />
      <List>
        <ListItem button onClick={() => { navigate('/mobile/dashboard'); setDrawerOpen(false); }}>
          <ListItemIcon><HomeIcon /></ListItemIcon>
          <ListItemText primary={t('common:dashboard', 'Dashboard')} />
        </ListItem>
        <ListItem button onClick={() => { navigate('/mobile/schedule'); setDrawerOpen(false); }}>
          <ListItemIcon><CalendarIcon /></ListItemIcon>
          <ListItemText primary={t('common:schedule', 'Schedule')} />
        </ListItem>
        <ListItem button onClick={() => { navigate('/mobile/students'); setDrawerOpen(false); }}>
          <ListItemIcon><PeopleIcon /></ListItemIcon>
          <ListItemText primary={t('common:students', 'Students')} />
        </ListItem>
        <ListItem button onClick={() => { navigate('/mobile/programs'); setDrawerOpen(false); }}>
          <ListItemIcon><SchoolIcon /></ListItemIcon>
          <ListItemText primary={t('common:programs', 'Programs')} />
        </ListItem>
      </List>
      <Divider />
      <List>
        <ListItem button onClick={() => { navigate('/mobile/settings'); setDrawerOpen(false); }}>
          <ListItemIcon><SettingsIcon /></ListItemIcon>
          <ListItemText primary={t('common:settings', 'Settings')} />
        </ListItem>
        <ListItem button onClick={handleLogout}>
          <ListItemIcon><LogoutIcon /></ListItemIcon>
          <ListItemText primary={t('common:logout', 'Logout')} />
        </ListItem>
      </List>
    </Box>
  );

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      minHeight: '100vh',
      maxWidth: '100vw',
      overflow: 'hidden'
    }}>
      <AppBar position="fixed">
        <Toolbar>
          {showBackButton ? (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="back"
              onClick={handleBackClick}
              sx={{ mr: 2 }}
            >
              <BackIcon />
            </IconButton>
          ) : (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {title}
          </Typography>
        </Toolbar>
      </AppBar>
      
      <Drawer
        anchor="left"
        open={drawerOpen}
        onClose={handleDrawerToggle}
      >
        {drawer}
      </Drawer>
      
      <Box component="main" sx={{ 
        flexGrow: 1, 
        p: 2, 
        mt: '56px', // AppBar height
        mb: '56px', // BottomNavigation height
        overflowY: 'auto',
        overflowX: 'hidden',
        WebkitOverflowScrolling: 'touch'
      }}>
        {children}
      </Box>
      
      {isMobile && (
        <Paper sx={{ position: 'fixed', bottom: 0, left: 0, right: 0 }} elevation={3}>
          <BottomNavigation
            value={value}
            onChange={handleNavChange}
            showLabels
          >
            <BottomNavigationAction label={t('common:home', 'Home')} icon={<HomeIcon />} />
            <BottomNavigationAction label={t('common:schedule', 'Schedule')} icon={<CalendarIcon />} />
            <BottomNavigationAction label={t('common:students', 'Students')} icon={<PeopleIcon />} />
            <BottomNavigationAction label={t('common:programs', 'Programs')} icon={<SchoolIcon />} />
          </BottomNavigation>
        </Paper>
      )}
    </Box>
  );
};

export default MobileLayout;
