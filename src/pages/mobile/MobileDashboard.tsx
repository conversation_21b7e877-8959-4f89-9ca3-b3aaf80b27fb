import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Box, 
  Card, 
  CardContent, 
  Grid, 
  Divider, 
  Button, 
  Skeleton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  IconButton
} from '@mui/material';
import { 
  CalendarToday as CalendarIcon, 
  People as PeopleIcon, 
  School as SchoolIcon,
  AccessTime as TimeIcon,
  ArrowForward as ArrowForwardIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { useSchool } from '../../hooks/useSchool';
import MobileLayout from '../../components/mobile/layout/MobileLayout';
import { db } from '../../services/firebase';
import { collection, query, where, getDocs, orderBy, limit, Timestamp } from 'firebase/firestore';
import { Lesson } from '../../types/lesson';

const MobileDashboard: React.FC = () => {
  const { t } = useTranslation(['common', 'lessons']);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentSchool } = useSchool();
  
  const [upcomingLessons, setUpcomingLessons] = useState<Lesson[]>([]);
  const [todayLessons, setTodayLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalLessons: 0,
    totalStudents: 0,
    programSessions: 0
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user?.uid || !currentSchool?.id) return;
      
      try {
        setLoading(true);
        
        // Get today's date at midnight
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Get tomorrow's date at midnight
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        // Get current time
        const now = new Date();
        
        // Fetch today's lessons
        const todayLessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('instructorId', '==', user.uid),
          where('startTime', '>=', Timestamp.fromDate(today)),
          where('startTime', '<', Timestamp.fromDate(tomorrow)),
          orderBy('startTime', 'asc')
        );
        
        const todayLessonsSnapshot = await getDocs(todayLessonsQuery);
        const todayLessonsData = todayLessonsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Lesson[];
        
        setTodayLessons(todayLessonsData);
        
        // Fetch upcoming lessons (future lessons, limited to 5)
        const upcomingLessonsQuery = query(
          collection(db, 'schools', currentSchool.id, 'lessons'),
          where('instructorId', '==', user.uid),
          where('startTime', '>=', Timestamp.fromDate(now)),
          orderBy('startTime', 'asc'),
          limit(5)
        );
        
        const upcomingLessonsSnapshot = await getDocs(upcomingLessonsQuery);
        const upcomingLessonsData = upcomingLessonsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Lesson[];
        
        setUpcomingLessons(upcomingLessonsData);
        
        // Calculate stats
        setStats({
          totalLessons: upcomingLessonsSnapshot.size,
          totalStudents: [...new Set(upcomingLessonsData.flatMap(lesson => lesson.studentIds || []))].length,
          programSessions: upcomingLessonsData.filter(lesson => lesson.programId).length
        });
        
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [user?.uid, currentSchool?.id]);

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'HH:mm');
  };
  
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
      
    return format(date, 'EEE, MMM d');
  };

  const getLessonStatusChip = (lesson: Lesson) => {
    switch (lesson.status) {
      case 'completed':
        return <Chip size="small" icon={<CheckCircleIcon />} label={t('lessons:status.completed')} color="success" />;
      case 'cancelled':
        return <Chip size="small" icon={<CancelIcon />} label={t('lessons:status.cancelled')} color="error" />;
      case 'scheduled':
      default:
        return <Chip size="small" icon={<TimeIcon />} label={t('lessons:status.scheduled')} color="primary" />;
    }
  };

  return (
    <MobileLayout title={t('common:instructorDashboard', 'Instructor Dashboard')}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          {t('common:welcome', 'Welcome')}, {user?.displayName?.split(' ')[0]}!
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {format(new Date(), 'EEEE, MMMM d, yyyy')}
        </Typography>
      </Box>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
              <CalendarIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{loading ? <Skeleton width="100%" /> : stats.totalLessons}</Typography>
              <Typography variant="body2" color="text.secondary">{t('common:lessons', 'Lessons')}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
              <PeopleIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{loading ? <Skeleton width="100%" /> : stats.totalStudents}</Typography>
              <Typography variant="body2" color="text.secondary">{t('common:students', 'Students')}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
              <SchoolIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{loading ? <Skeleton width="100%" /> : stats.programSessions}</Typography>
              <Typography variant="body2" color="text.secondary">{t('common:programs', 'Programs')}</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">{t('common:todaysLessons', "Today's Lessons")}</Typography>
          <Button 
            size="small" 
            endIcon={<ArrowForwardIcon />}
            onClick={() => navigate('/mobile/schedule')}
          >
            {t('common:viewAll', 'View All')}
          </Button>
        </Box>
        
        <Card>
          {loading ? (
            <CardContent>
              {[1, 2, 3].map((item) => (
                <Box key={item} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" width="100%" height={60} />
                </Box>
              ))}
            </CardContent>
          ) : todayLessons.length > 0 ? (
            <List sx={{ p: 0 }}>
              {todayLessons.map((lesson, index) => (
                <React.Fragment key={lesson.id}>
                  <ListItem 
                    button
                    onClick={() => navigate(`/mobile/lessons/${lesson.id}`)}
                    secondaryAction={
                      <IconButton edge="end" aria-label="details">
                        <ArrowForwardIcon />
                      </IconButton>
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <TimeIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText 
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1" component="span">
                            {lesson.title}
                          </Typography>
                          {getLessonStatusChip(lesson)}
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            {formatTime(lesson.startTime)} - {lesson.duration} min
                          </Typography>
                          <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                            {lesson.studentIds?.length || 0} {t('common:students', 'Students')}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                  {index < todayLessons.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <CardContent>
              <Typography variant="body2" color="text.secondary" align="center">
                {t('common:noLessonsToday', 'No lessons scheduled for today')}
              </Typography>
            </CardContent>
          )}
        </Card>
      </Box>
      
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">{t('common:upcomingLessons', 'Upcoming Lessons')}</Typography>
          <Button 
            size="small" 
            endIcon={<ArrowForwardIcon />}
            onClick={() => navigate('/mobile/schedule')}
          >
            {t('common:viewAll', 'View All')}
          </Button>
        </Box>
        
        <Card>
          {loading ? (
            <CardContent>
              {[1, 2, 3].map((item) => (
                <Box key={item} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" width="100%" height={60} />
                </Box>
              ))}
            </CardContent>
          ) : upcomingLessons.length > 0 ? (
            <List sx={{ p: 0 }}>
              {upcomingLessons.map((lesson, index) => (
                <React.Fragment key={lesson.id}>
                  <ListItem 
                    button
                    onClick={() => navigate(`/mobile/lessons/${lesson.id}`)}
                    secondaryAction={
                      <IconButton edge="end" aria-label="details">
                        <ArrowForwardIcon />
                      </IconButton>
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <TimeIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText 
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1" component="span">
                            {lesson.title}
                          </Typography>
                          {getLessonStatusChip(lesson)}
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            {formatDate(lesson.startTime)}, {formatTime(lesson.startTime)}
                          </Typography>
                          <Typography variant="body2" component="span" sx={{ display: 'block' }}>
                            {lesson.studentIds?.length || 0} {t('common:students', 'Students')}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                  {index < upcomingLessons.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <CardContent>
              <Typography variant="body2" color="text.secondary" align="center">
                {t('common:noUpcomingLessons', 'No upcoming lessons scheduled')}
              </Typography>
            </CardContent>
          )}
        </Card>
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 4 }}>
        <Button 
          variant="contained" 
          startIcon={<CalendarIcon />}
          onClick={() => navigate('/mobile/schedule')}
        >
          {t('common:viewSchedule', 'View Schedule')}
        </Button>
        <Button 
          variant="outlined" 
          startIcon={<PeopleIcon />}
          onClick={() => navigate('/mobile/students')}
        >
          {t('common:manageStudents', 'Manage Students')}
        </Button>
      </Box>
    </MobileLayout>
  );
};

export default MobileDashboard;
